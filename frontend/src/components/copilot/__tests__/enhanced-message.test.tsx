/**
 * Tests for EnhancedMessage component
 * Validates message rendering, formatting, and data visualization
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { EnhancedMessage } from '../enhanced-message';
import { CopilotResponseData } from '@/types/copilot';

describe('EnhancedMessage', () => {
  const mockTimestamp = new Date('2024-01-15T10:30:00Z');

  describe('User Messages', () => {
    test('renders user message correctly', () => {
      render(
        <EnhancedMessage
          content="Test user message"
          timestamp={mockTimestamp}
          type="user"
        />
      );

      expect(screen.getByText('Test user message')).toBeInTheDocument();
      expect(screen.getByText('10:30')).toBeInTheDocument();
    });

    test('applies correct styling for user messages', () => {
      render(
        <EnhancedMessage
          content="Test user message"
          timestamp={mockTimestamp}
          type="user"
        />
      );

      const messageContainer = screen.getByText('Test user message').closest('div');
      expect(messageContainer).toHaveClass('bg-emerald-600');
    });
  });

  describe('Assistant Messages', () => {
    test('renders assistant message correctly', () => {
      render(
        <EnhancedMessage
          content="Test assistant message"
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      expect(screen.getByText('Test assistant message')).toBeInTheDocument();
    });

    test('renders formatted content with headers', () => {
      const content = `## Portfolio Analysis
      
**Risk Assessment:**
• High risk: 15%
• Medium risk: 35%
• Low risk: 50%`;

      render(
        <EnhancedMessage
          content={content}
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      expect(screen.getByText('Portfolio Analysis')).toBeInTheDocument();
      expect(screen.getByText('Risk Assessment:')).toBeInTheDocument();
    });

    test('renders AI analytics data', () => {
      const data: CopilotResponseData = {
        response_type: 'analytics',
        confidence: 0.9,
        processing_time_ms: 150,
        topics: ['risk', 'portfolio'],
        ai_analytics: {
          portfolio_health_score: 85,
          total_msmes: 150,
          risk_distribution: {
            high: 0.1,
            medium: 0.3,
            low: 0.6,
          },
        },
      };

      render(
        <EnhancedMessage
          content="Portfolio analysis complete"
          data={data}
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      expect(screen.getByText('AI Portfolio Analytics')).toBeInTheDocument();
      expect(screen.getByText('85%')).toBeInTheDocument();
      expect(screen.getByText('150')).toBeInTheDocument();
    });

    test('renders risk distribution correctly', () => {
      const data: CopilotResponseData = {
        response_type: 'analytics',
        confidence: 0.9,
        processing_time_ms: 150,
        topics: ['risk'],
        ai_analytics: {
          portfolio_health_score: 75,
          total_msmes: 100,
          risk_distribution: {
            high: 0.2,
            medium: 0.3,
            low: 0.5,
          },
        },
      };

      render(
        <EnhancedMessage
          content="Risk analysis"
          data={data}
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      expect(screen.getByText('20%')).toBeInTheDocument(); // High risk
      expect(screen.getByText('30%')).toBeInTheDocument(); // Medium risk
      expect(screen.getByText('50%')).toBeInTheDocument(); // Low risk
    });

    test('renders predictive insights', () => {
      const data: CopilotResponseData = {
        response_type: 'insights',
        confidence: 0.85,
        processing_time_ms: 200,
        topics: ['prediction'],
        predictive_insights: [
          {
            id: '1',
            title: 'Market Trend Prediction',
            description: 'Manufacturing sector expected to improve',
            confidence: 0.8,
            impact: 'medium',
            timeline: '3 months',
            category: 'performance',
            actionable: true,
          },
        ],
      };

      render(
        <EnhancedMessage
          content="Predictive analysis"
          data={data}
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      expect(screen.getByText('Predictive Insights')).toBeInTheDocument();
      expect(screen.getByText('Market Trend Prediction')).toBeInTheDocument();
    });

    test('renders risk alerts', () => {
      const data: CopilotResponseData = {
        response_type: 'risk',
        confidence: 0.95,
        processing_time_ms: 100,
        topics: ['alerts'],
        risk_alerts: [
          {
            msme_id: '1',
            msme_name: 'ABC Manufacturing',
            risk_level: 'high',
            risk_type: 'credit',
            description: 'Payment delays detected',
            severity_score: 85,
            detected_at: new Date(),
            requires_action: true,
          },
        ],
      };

      render(
        <EnhancedMessage
          content="Risk alerts"
          data={data}
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      expect(screen.getByText('Risk Alerts')).toBeInTheDocument();
      expect(screen.getByText('ABC Manufacturing')).toBeInTheDocument();
    });
  });

  describe('Data Validation', () => {
    test('handles missing data gracefully', () => {
      render(
        <EnhancedMessage
          content="Test message"
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      expect(screen.getByText('Test message')).toBeInTheDocument();
    });

    test('filters out React event objects', () => {
      const invalidData = {
        _reactName: 'onClick',
        nativeEvent: {},
        currentTarget: {},
      };

      render(
        <EnhancedMessage
          content="Test message"
          data={invalidData as any}
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      // Should render without crashing
      expect(screen.getByText('Test message')).toBeInTheDocument();
    });

    test('handles invalid timestamp formats', () => {
      render(
        <EnhancedMessage
          content="Test message"
          timestamp={'invalid-date' as any}
          type="assistant"
        />
      );

      expect(screen.getByText('Test message')).toBeInTheDocument();
    });
  });

  describe('Content Formatting', () => {
    test('formats currency values correctly', () => {
      const content = 'Total exposure: ₹2,500,000';

      render(
        <EnhancedMessage
          content={content}
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      expect(screen.getByText(/₹2,500,000/)).toBeInTheDocument();
    });

    test('formats percentage values correctly', () => {
      const content = 'Risk level: 85.5%';

      render(
        <EnhancedMessage
          content={content}
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      expect(screen.getByText(/85.5%/)).toBeInTheDocument();
    });

    test('formats bullet points correctly', () => {
      const content = `Key findings:
• High risk MSMEs: 15
• Medium risk MSMEs: 35
• Low risk MSMEs: 50`;

      render(
        <EnhancedMessage
          content={content}
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      expect(screen.getByText('High risk MSMEs: 15')).toBeInTheDocument();
    });

    test('formats numbered lists correctly', () => {
      const content = `Action items:
1. Review high-risk accounts
2. Update compliance status
3. Generate monthly report`;

      render(
        <EnhancedMessage
          content={content}
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      expect(screen.getByText('Review high-risk accounts')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    test('memoizes expensive calculations', () => {
      const { rerender } = render(
        <EnhancedMessage
          content="Test message"
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      // Re-render with same props should not cause unnecessary recalculations
      rerender(
        <EnhancedMessage
          content="Test message"
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      expect(screen.getByText('Test message')).toBeInTheDocument();
    });

    test('handles large data objects efficiently', () => {
      const largeData: CopilotResponseData = {
        response_type: 'analytics',
        confidence: 0.9,
        processing_time_ms: 150,
        topics: Array.from({ length: 100 }, (_, i) => `topic-${i}`),
        ai_analytics: {
          portfolio_health_score: 85,
          total_msmes: 1000,
          risk_distribution: {
            high: 0.1,
            medium: 0.3,
            low: 0.6,
          },
        },
        predictive_insights: Array.from({ length: 50 }, (_, i) => ({
          id: `insight-${i}`,
          title: `Insight ${i}`,
          description: `Description ${i}`,
          confidence: 0.8,
          impact: 'medium' as const,
          timeline: '1 month',
          category: 'performance' as const,
          actionable: true,
        })),
      };

      const startTime = performance.now();
      
      render(
        <EnhancedMessage
          content="Large data test"
          data={largeData}
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      const renderTime = performance.now() - startTime;
      
      // Should render quickly even with large data
      expect(renderTime).toBeLessThan(100); // 100ms threshold
      expect(screen.getByText('Large data test')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    test('has proper semantic structure', () => {
      render(
        <EnhancedMessage
          content="## Main Heading\n\n**Subheading:**\n• List item"
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      expect(screen.getByRole('heading', { level: 3 })).toBeInTheDocument();
    });

    test('provides meaningful text content', () => {
      const data: CopilotResponseData = {
        response_type: 'analytics',
        confidence: 0.9,
        processing_time_ms: 150,
        topics: ['portfolio'],
        ai_analytics: {
          portfolio_health_score: 85,
          total_msmes: 150,
        },
      };

      render(
        <EnhancedMessage
          content="Portfolio health analysis"
          data={data}
          timestamp={mockTimestamp}
          type="assistant"
        />
      );

      // Should have descriptive text for screen readers
      expect(screen.getByText('Portfolio Health')).toBeInTheDocument();
      expect(screen.getByText('Total MSMEs')).toBeInTheDocument();
    });
  });
});
