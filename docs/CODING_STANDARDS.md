# Credit Chakra Coding Standards

## Overview

This document establishes coding standards and best practices for the Credit Chakra project to ensure consistency, maintainability, and scalability across the codebase.

## General Principles

### 1. Code Quality
- **Readability First**: Code should be self-documenting and easy to understand
- **Consistency**: Follow established patterns throughout the codebase
- **Simplicity**: Prefer simple, clear solutions over complex ones
- **Performance**: Write efficient code, but prioritize readability unless performance is critical

### 2. Documentation
- **JSDoc Comments**: All public functions and complex logic must be documented
- **README Files**: Each major module should have clear documentation
- **Type Definitions**: Use TypeScript for all new code
- **API Documentation**: Keep API docs up-to-date with implementation

## Frontend Standards (React/TypeScript)

### 1. File Naming Conventions

```
Components:     kebab-case.tsx          (e.g., portfolio-dashboard.tsx)
Hooks:          camelCase.ts            (e.g., useOptimizedData.ts)
Utilities:      kebab-case.ts           (e.g., api-client.ts)
Types:          camelCase.ts            (e.g., index.ts)
Pages:          kebab-case.tsx          (e.g., portfolio.tsx)
```

### 2. Component Structure

```typescript
/**
 * Component description and purpose
 * @param props - Component props description
 */
'use client'; // Only if needed for client components

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { ExternalLibrary } from 'external-library';
import { InternalComponent } from '@/components/internal';
import { useCustomHook } from '@/hooks/useCustomHook';
import { ComponentProps } from '@/types';

interface ComponentNameProps {
  /** Required prop description */
  requiredProp: string;
  /** Optional prop description */
  optionalProp?: number;
  /** Callback prop description */
  onAction?: (data: string) => void;
}

export function ComponentName({
  requiredProp,
  optionalProp = 10,
  onAction
}: ComponentNameProps) {
  // 1. Hooks (useState, useEffect, custom hooks)
  const [localState, setLocalState] = useState<string>('');
  const { data, loading, error } = useCustomHook();

  // 2. Memoized values
  const expensiveCalculation = useMemo(() => {
    return data?.map(item => item.value * 2) || [];
  }, [data]);

  // 3. Callback functions
  const handleAction = useCallback((value: string) => {
    setLocalState(value);
    onAction?.(value);
  }, [onAction]);

  // 4. Effects
  useEffect(() => {
    // Effect logic
  }, [dependency]);

  // 5. Early returns
  if (loading) return <LoadingComponent />;
  if (error) return <ErrorComponent error={error} />;

  // 6. Render
  return (
    <div className="component-container">
      {/* Component JSX */}
    </div>
  );
}
```

### 3. TypeScript Standards

```typescript
// Use explicit types for props
interface Props {
  id: string;
  count: number;
  isActive: boolean;
  items: Item[];
  onSelect: (item: Item) => void;
}

// Use union types for constants
type Status = 'loading' | 'success' | 'error';
type RiskBand = 'green' | 'yellow' | 'red';

// Use generics for reusable components
interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  onRowClick: (row: T) => void;
}

// Use utility types when appropriate
type PartialUser = Partial<User>;
type RequiredFields = Required<Pick<User, 'id' | 'name'>>;
```

### 4. Hook Standards

```typescript
/**
 * Custom hook description
 * @param param - Parameter description
 * @returns Hook return value description
 */
export function useCustomHook(param: string) {
  const [state, setState] = useState<StateType>(initialState);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Memoized values
  const memoizedValue = useMemo(() => {
    return expensiveCalculation(state);
  }, [state]);

  // Callback functions
  const handleAction = useCallback(async (data: ActionData) => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiCall(data);
      setState(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  }, []);

  // Return object with consistent naming
  return {
    data: state,
    loading,
    error,
    memoizedValue,
    handleAction,
    refetch: () => handleAction(param)
  };
}
```

### 5. Styling Standards

```typescript
// Use Tailwind CSS classes with consistent patterns
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-md">
  <h2 className="text-xl font-semibold text-gray-900">Title</h2>
  <Button variant="primary" size="md">Action</Button>
</div>

// Group related classes
<div className={cn(
  // Layout
  "flex items-center gap-4",
  // Spacing
  "p-6 m-4",
  // Appearance
  "bg-white border border-gray-200 rounded-lg shadow-sm",
  // Responsive
  "md:p-8 lg:gap-6",
  // Conditional
  isActive && "border-blue-500 bg-blue-50",
  className
)}>
```

## Backend Standards (Python/FastAPI)

### 1. File Naming Conventions

```
Modules:        snake_case.py           (e.g., health_score.py)
Classes:        PascalCase              (e.g., HealthScoreCalculator)
Functions:      snake_case              (e.g., calculate_health_score)
Constants:      UPPER_SNAKE_CASE        (e.g., DEFAULT_TIMEOUT)
Variables:      snake_case              (e.g., user_data)
```

### 2. Function Documentation

```python
def calculate_health_score(signals: List[Signal], weights: Dict[str, float] = None) -> ScoreDetails:
    """
    Calculate health score for an MSME based on signals.
    
    Args:
        signals: List of signals to analyze
        weights: Optional custom weights for signal types
        
    Returns:
        ScoreDetails object containing score and breakdown
        
    Raises:
        ValidationError: If signals are invalid
        CalculationError: If score calculation fails
        
    Example:
        >>> signals = [Signal(source='gst', value=1000000)]
        >>> score = calculate_health_score(signals)
        >>> print(score.score)
        750.5
    """
    if not signals:
        raise ValidationError("Signals list cannot be empty")
    
    # Implementation
    return ScoreDetails(score=calculated_score, breakdown=breakdown)
```

### 3. Class Structure

```python
class HealthScoreCalculator:
    """
    Calculates health scores for MSMEs based on various signals.
    
    Attributes:
        weights: Signal type weights for calculation
        threshold: Minimum score threshold
    """
    
    def __init__(self, weights: Dict[str, float] = None, threshold: float = 0.0):
        """Initialize calculator with optional custom weights."""
        self.weights = weights or self._get_default_weights()
        self.threshold = threshold
        self._logger = logging.getLogger(__name__)
    
    def calculate(self, signals: List[Signal]) -> ScoreDetails:
        """Calculate health score from signals."""
        self._validate_signals(signals)
        
        base_score = self._calculate_base_score(signals)
        penalties = self._calculate_penalties(signals)
        final_score = max(0, base_score - sum(penalties.values()))
        
        return ScoreDetails(
            score=final_score,
            base_score=base_score,
            penalties=penalties,
            risk_band=self._determine_risk_band(final_score)
        )
    
    def _validate_signals(self, signals: List[Signal]) -> None:
        """Validate input signals."""
        if not signals:
            raise ValidationError("Signals list cannot be empty")
        
        for signal in signals:
            if not isinstance(signal, Signal):
                raise ValidationError(f"Invalid signal type: {type(signal)}")
    
    @staticmethod
    def _get_default_weights() -> Dict[str, float]:
        """Get default signal weights."""
        return {
            'gst': 0.4,
            'upi': 0.3,
            'reviews': 0.2,
            'social': 0.1
        }
```

### 4. API Route Standards

```python
from fastapi import APIRouter, HTTPException, status, Query, Depends
from typing import List, Optional
from utils.error_handler import handle_common_exceptions, validate_msme_id
from services.data_service import data_service

router = APIRouter(prefix="/api/v1", tags=["portfolio"])

@router.get("/portfolio", response_model=PaginatedResponse[MSME])
@handle_common_exceptions
async def get_portfolio(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    risk_band: Optional[RiskBand] = Query(None, description="Filter by risk band"),
    search: Optional[str] = Query(None, description="Search term")
) -> PaginatedResponse[MSME]:
    """
    Get paginated portfolio data with filtering.
    
    Args:
        page: Page number (1-based)
        limit: Items per page (max 100)
        risk_band: Optional risk band filter
        search: Optional search term
        
    Returns:
        Paginated response with MSME data
        
    Raises:
        HTTPException: If validation fails
    """
    # Validate parameters
    if limit > 100:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Limit cannot exceed 100"
        )
    
    # Get data
    data = data_service.get_filtered_msmes(
        risk_band=risk_band,
        search=search
    )
    
    # Apply pagination
    return paginate_data(data, page, limit)
```

## Testing Standards

### 1. Frontend Testing

```typescript
// Component tests
describe('PortfolioDashboard', () => {
  const mockData = {
    msmes: [mockMSME],
    analytics: mockAnalytics
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render portfolio data correctly', () => {
    render(<PortfolioDashboard {...mockData} />);
    
    expect(screen.getByText('Portfolio Dashboard')).toBeInTheDocument();
    expect(screen.getByText(mockMSME.name)).toBeInTheDocument();
  });

  it('should handle loading state', () => {
    render(<PortfolioDashboard loading={true} />);
    
    expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument();
  });

  it('should handle error state', () => {
    const error = 'Failed to load data';
    render(<PortfolioDashboard error={error} />);
    
    expect(screen.getByText(error)).toBeInTheDocument();
  });
});

// Hook tests
describe('useOptimizedPortfolio', () => {
  it('should fetch and cache portfolio data', async () => {
    const { result } = renderHook(() => useOptimizedPortfolio());
    
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });
    
    expect(result.current.msmes).toHaveLength(20);
    expect(result.current.analytics).toBeDefined();
  });
});
```

### 2. Backend Testing

```python
import pytest
from fastapi.testclient import TestClient
from services.health_score import calculate_health_score
from models.signal import Signal

class TestHealthScore:
    """Test health score calculation."""
    
    def test_calculate_basic_score(self):
        """Test basic score calculation."""
        signals = [
            Signal(source='gst', value=1000000, normalized=0.8),
            Signal(source='upi', value=500, normalized=0.7)
        ]
        
        result = calculate_health_score(signals)
        
        assert result.score > 0
        assert result.risk_band in ['green', 'yellow', 'red']
        assert 'gst' in result.breakdown.details
    
    def test_empty_signals_raises_error(self):
        """Test that empty signals raise validation error."""
        with pytest.raises(ValidationError):
            calculate_health_score([])
    
    @pytest.mark.parametrize("score,expected_band", [
        (800, 'green'),
        (600, 'yellow'),
        (300, 'red')
    ])
    def test_risk_band_determination(self, score, expected_band):
        """Test risk band determination logic."""
        # Test implementation
        pass

class TestPortfolioAPI:
    """Test portfolio API endpoints."""
    
    def test_get_portfolio_success(self, client: TestClient):
        """Test successful portfolio retrieval."""
        response = client.get("/dashboard/portfolio")
        
        assert response.status_code == 200
        data = response.json()
        assert "data" in data
        assert "pagination" in data
    
    def test_get_portfolio_with_filters(self, client: TestClient):
        """Test portfolio with risk band filter."""
        response = client.get("/dashboard/portfolio?risk_band=green")
        
        assert response.status_code == 200
        data = response.json()
        for msme in data["data"]:
            assert msme["risk_band"] == "green"
```

## Error Handling Standards

### 1. Frontend Error Handling

```typescript
// Component error boundaries
class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Component error:', error, errorInfo);
    // Log to error tracking service
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }

    return this.props.children;
  }
}

// Hook error handling
export function useApiCall<T>(apiFunction: () => Promise<T>) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await apiFunction();
      setData(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('API call failed:', err);
    } finally {
      setLoading(false);
    }
  }, [apiFunction]);

  return { data, loading, error, execute };
}
```

### 2. Backend Error Handling

```python
# Custom exceptions
class CreditChakraError(Exception):
    """Base exception for Credit Chakra application."""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code or "GENERAL_ERROR"
        self.details = details or {}
        super().__init__(self.message)

# Error handler decorator
def handle_common_exceptions(func):
    """Decorator to handle common exceptions."""
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except ValidationError as e:
            logger.warning(f"Validation error: {e}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("VALIDATION_ERROR", str(e))
            )
        except Exception as e:
            logger.error(f"Unexpected error: {e}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=create_error_response("INTERNAL_ERROR", "An unexpected error occurred")
            )
    return wrapper
```

## Performance Standards

### 1. Frontend Performance

```typescript
// Memoization guidelines
const ExpensiveComponent = React.memo(({ data, onAction }) => {
  const processedData = useMemo(() => {
    return data.map(item => expensiveTransformation(item));
  }, [data]);

  const handleClick = useCallback((id: string) => {
    onAction(id);
  }, [onAction]);

  return <div>{/* Component JSX */}</div>;
});

// Lazy loading
const LazyComponent = React.lazy(() => import('./LazyComponent'));

function App() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <LazyComponent />
    </Suspense>
  );
}
```

### 2. Backend Performance

```python
# Caching
from functools import lru_cache
from typing import List

@lru_cache(maxsize=128)
def get_cached_calculation(msme_id: str, signal_hash: str) -> float:
    """Cache expensive calculations."""
    return expensive_calculation(msme_id, signal_hash)

# Async operations
async def process_multiple_msmes(msme_ids: List[str]) -> List[ScoreDetails]:
    """Process multiple MSMEs concurrently."""
    tasks = [process_single_msme(msme_id) for msme_id in msme_ids]
    return await asyncio.gather(*tasks)
```

## Git Standards

### 1. Commit Messages

```
feat: add portfolio pagination support
fix: resolve MSME score calculation bug
docs: update API documentation
style: format code with prettier
refactor: extract shared risk components
test: add unit tests for health score
chore: update dependencies
```

### 2. Branch Naming

```
feature/portfolio-pagination
bugfix/score-calculation-error
hotfix/critical-security-patch
release/v1.2.0
```

### 3. Pull Request Guidelines

- Clear title and description
- Link to related issues
- Include screenshots for UI changes
- Ensure all tests pass
- Request appropriate reviewers
- Update documentation if needed

## Code Review Checklist

### Frontend
- [ ] Component follows naming conventions
- [ ] TypeScript types are properly defined
- [ ] Performance optimizations are applied
- [ ] Error handling is implemented
- [ ] Tests are included
- [ ] Accessibility considerations
- [ ] Responsive design

### Backend
- [ ] Function documentation is complete
- [ ] Error handling follows standards
- [ ] Input validation is implemented
- [ ] Tests cover edge cases
- [ ] Performance considerations
- [ ] Security best practices
- [ ] API documentation is updated

This coding standards document ensures consistency and quality across the Credit Chakra codebase while supporting future development and maintenance.
