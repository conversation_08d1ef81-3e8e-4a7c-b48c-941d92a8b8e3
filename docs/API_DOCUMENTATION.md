# Credit Chakra API Documentation

## Overview

The Credit Chakra API provides comprehensive endpoints for MSME credit assessment, portfolio management, and risk monitoring. Built with FastAPI, it offers high-performance, type-safe endpoints with automatic OpenAPI documentation.

## Base URL

```
Development: http://localhost:8000
Production: https://api.creditchakra.com
```

## Authentication

Currently using development mode. Production will implement JWT-based authentication.

```http
Authorization: Bearer <jwt_token>
```

## Core Endpoints

### Portfolio Management

#### GET /dashboard/portfolio
Get paginated portfolio data with filtering and sorting.

**Parameters:**
- `page` (int, optional): Page number (default: 1)
- `limit` (int, optional): Items per page (default: 10, max: 100)
- `risk_band` (string, optional): Filter by risk band ('green', 'yellow', 'red')
- `business_type` (string, optional): Filter by business type
- `search` (string, optional): Search term for name/location
- `sort_by` (string, optional): Sort field (default: 'created_at')
- `sort_order` (string, optional): Sort order ('asc', 'desc')

**Response:**
```json
{
  "data": [
    {
      "msme_id": "msme_001",
      "name": "श्री गणेश इलेक्ट्रॉनिक्स",
      "business_type": "retail",
      "location": "Mumbai, Maharashtra",
      "current_score": 785.0,
      "risk_band": "green",
      "gst_compliance": 92,
      "banking_health": 88,
      "monthly_turnover": 2850000,
      "digital_score": 85,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "current_page": 1,
    "per_page": 10,
    "total_pages": 5,
    "total_count": 50,
    "has_next": true,
    "has_prev": false
  },
  "filters": {
    "risk_band": "green"
  },
  "total_count": 50
}
```

#### GET /dashboard/portfolio/legacy
Legacy endpoint returning all MSMEs without pagination (for backward compatibility).

**Response:**
```json
[
  {
    "msme_id": "msme_001",
    "name": "श्री गणेश इलेक्ट्रॉनिक्स",
    "business_type": "retail",
    "location": "Mumbai, Maharashtra",
    "current_score": 785.0,
    "risk_band": "green",
    "score_trend": "stable",
    "signals_count": 12,
    "recent_nudges": 0,
    "last_signal_date": "2024-01-15T10:30:00Z",
    "created_at": "2024-01-15T10:30:00Z",
    "tags": ["retail", "verified", "active"],
    "gst_compliance": 92,
    "banking_health": 88,
    "monthly_turnover": 2850000,
    "digital_score": 85
  }
]
```

#### GET /dashboard/portfolio/summary
Get portfolio summary statistics.

**Response:**
```json
{
  "total_count": 20,
  "risk_distribution": {
    "green": 12,
    "yellow": 5,
    "red": 3
  },
  "business_type_distribution": {
    "retail": 8,
    "manufacturing": 6,
    "services": 4,
    "b2b": 2
  },
  "average_score": 678.5,
  "score_range": {
    "min": 298.0,
    "max": 785.0
  }
}
```

### Analytics

#### GET /dashboard/analytics
Get comprehensive dashboard analytics.

**Response:**
```json
{
  "total_msmes": 20,
  "risk_distribution": {
    "green": 12,
    "yellow": 5,
    "red": 3
  },
  "business_type_distribution": {
    "retail": 8,
    "manufacturing": 6,
    "services": 4,
    "b2b": 2
  },
  "average_score": 678.5,
  "average_gst_compliance": 82.1,
  "average_banking_health": 79.3,
  "total_signals": 240,
  "last_updated": "2024-01-15T10:30:00Z"
}
```

### MSME Management

#### GET /msme/{msme_id}
Get detailed information for a specific MSME.

**Parameters:**
- `msme_id` (string): Unique MSME identifier

**Response:**
```json
{
  "msme_id": "msme_001",
  "name": "श्री गणेश इलेक्ट्रॉनिक्स",
  "business_type": "retail",
  "location": "Mumbai, Maharashtra",
  "current_score": 785.0,
  "risk_band": "green",
  "score_trend": "stable",
  "signals_count": 12,
  "recent_nudges": 0,
  "last_signal_date": "2024-01-15T10:30:00Z",
  "created_at": "2024-01-15T10:30:00Z",
  "tags": ["retail", "verified", "active"],
  "gst_compliance": 92,
  "banking_health": 88,
  "monthly_turnover": 2850000,
  "digital_score": 85,
  "gst_number": "27AABCS1234C1Z5"
}
```

#### GET /msme/{msme_id}/score
Get detailed score breakdown for an MSME.

**Response:**
```json
{
  "msme_id": "msme_001",
  "msme_name": "श्री गणेश इलेक्ट्रॉनिक्स",
  "current_score": 785.0,
  "risk_band": "green",
  "score_breakdown": {
    "base_score": 800.0,
    "gst_penalty": -5.0,
    "reviews_penalty": -7.0,
    "upi_penalty": -3.0,
    "details": {
      "gst_compliance": "Excellent GST filing record",
      "digital_payments": "High UPI transaction volume",
      "market_presence": "Strong online reviews"
    }
  },
  "signals_count": 12,
  "last_updated": "2024-01-15T10:30:00Z"
}
```

#### GET /msme/{msme_id}/signals
Get signals for a specific MSME.

**Parameters:**
- `limit` (int, optional): Number of signals to return (default: 10)

**Response:**
```json
[
  {
    "signal_id": "sig_001",
    "msme_id": "msme_001",
    "source": "gst",
    "type": "turnover",
    "value": 2850000,
    "normalized": 0.85,
    "confidence": 0.95,
    "timestamp": "2024-01-15T10:30:00Z",
    "metadata": {
      "period": "2024-01",
      "filing_status": "on_time"
    }
  }
]
```

#### GET /msme/{msme_id}/nudges
Get nudges for a specific MSME.

**Response:**
```json
[
  {
    "nudge_id": "nudge_001",
    "msme_id": "msme_001",
    "type": "reminder",
    "title": "GST Filing Reminder",
    "message": "Your GST filing is due in 3 days",
    "priority": "medium",
    "status": "sent",
    "sent_at": "2024-01-15T10:30:00Z"
  }
]
```

### Error Handling

All endpoints use standardized error responses:

**Success Response:**
```json
{
  "success": true,
  "message": "Success",
  "data": { ... },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Error Response:**
```json
{
  "success": false,
  "error": "MSME_NOT_FOUND",
  "message": "MSME profile not found: msme_999",
  "details": {
    "msme_id": "msme_999"
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### HTTP Status Codes

- `200 OK`: Successful request
- `400 Bad Request`: Invalid request parameters
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation error
- `500 Internal Server Error`: Server error
- `503 Service Unavailable`: External service error

### Rate Limiting

- Development: No rate limiting
- Production: 1000 requests per hour per API key

### Data Models

#### MSME Model
```python
{
  "msme_id": "string",
  "name": "string",
  "business_type": "retail|manufacturing|services|b2b",
  "location": "string",
  "current_score": "number (0-1000)",
  "risk_band": "green|yellow|red",
  "score_trend": "improving|stable|declining",
  "signals_count": "integer",
  "recent_nudges": "integer",
  "last_signal_date": "ISO 8601 datetime",
  "created_at": "ISO 8601 datetime",
  "tags": ["string"],
  "gst_compliance": "number (0-100)",
  "banking_health": "number (0-100)",
  "monthly_turnover": "number",
  "digital_score": "number (0-100)",
  "gst_number": "string (optional)"
}
```

#### Signal Model
```python
{
  "signal_id": "string",
  "msme_id": "string",
  "source": "gst|upi|bank|social|manual",
  "type": "string",
  "value": "any",
  "normalized": "number (0-1)",
  "confidence": "number (0-1)",
  "timestamp": "ISO 8601 datetime",
  "metadata": "object (optional)"
}
```

### Pagination

All paginated endpoints support:
- `page`: Page number (1-based)
- `limit`: Items per page (1-100)

Response includes pagination metadata:
```json
{
  "pagination": {
    "current_page": 1,
    "per_page": 10,
    "total_pages": 5,
    "total_count": 50,
    "has_next": true,
    "has_prev": false,
    "next_page": 2,
    "prev_page": null
  }
}
```

### Filtering and Sorting

Most list endpoints support:
- **Filtering**: Query parameters for common fields
- **Sorting**: `sort_by` and `sort_order` parameters
- **Search**: `search` parameter for text search

### Future Enhancements

Planned API enhancements:
1. **Real-time Updates**: WebSocket endpoints for live data
2. **Bulk Operations**: Batch processing endpoints
3. **Advanced Analytics**: ML-powered insights endpoints
4. **External Integrations**: GST API, Account Aggregator endpoints
5. **Audit Trails**: Comprehensive logging and tracking
