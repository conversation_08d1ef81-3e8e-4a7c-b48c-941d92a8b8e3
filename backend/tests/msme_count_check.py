#!/usr/bin/env python3
"""
Test script to verify MSME count and data consistency
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.data_service import data_service

def test_msme_count():
    """Test that we have exactly 20 MSMEs with correct distribution"""
    print("=== Testing MSME Count and Distribution ===\n")
    
    # Initialize data
    data_service.initialize_data()
    
    # Get all MSMEs
    all_msmes = data_service.get_all_msmes()
    total_count = len(all_msmes)
    
    print(f"Total MSMEs: {total_count}")
    
    # Check risk distribution
    green_msmes = data_service.get_msmes_by_risk_band('green')
    yellow_msmes = data_service.get_msmes_by_risk_band('yellow')
    red_msmes = data_service.get_msmes_by_risk_band('red')
    
    print(f"Green (Low Risk): {len(green_msmes)}")
    print(f"Yellow (Medium Risk): {len(yellow_msmes)}")
    print(f"Red (High Risk): {len(red_msmes)}")
    
    print(f"\nRisk Distribution:")
    print(f"Green: {len(green_msmes)/total_count*100:.1f}%")
    print(f"Yellow: {len(yellow_msmes)/total_count*100:.1f}%")
    print(f"Red: {len(red_msmes)/total_count*100:.1f}%")
    
    # List all MSME IDs and names
    print(f"\n=== All {total_count} MSMEs ===")
    for i, msme in enumerate(all_msmes, 1):
        print(f"{i:2d}. {msme.msme_id} - {msme.name} ({msme.risk_band}) - Score: {msme.score}")
    
    # Check for expected count
    if total_count == 20:
        print(f"\n✅ SUCCESS: Found exactly 20 MSMEs")
    else:
        print(f"\n❌ ERROR: Expected 20 MSMEs, found {total_count}")
    
    # Check expected distribution
    expected_green = 12
    expected_yellow = 5
    expected_red = 3
    
    if len(green_msmes) == expected_green and len(yellow_msmes) == expected_yellow and len(red_msmes) == expected_red:
        print(f"✅ SUCCESS: Risk distribution is correct (12 green, 5 yellow, 3 red)")
    else:
        print(f"❌ ERROR: Risk distribution is incorrect")
        print(f"   Expected: {expected_green} green, {expected_yellow} yellow, {expected_red} red")
        print(f"   Actual: {len(green_msmes)} green, {len(yellow_msmes)} yellow, {len(red_msmes)} red")
    
    return total_count == 20

if __name__ == "__main__":
    test_msme_count()
