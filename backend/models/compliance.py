from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from enum import Enum

class ComplianceType(str, Enum):
    RBI_GUIDELINES = "RBI_GUIDELINES"
    DATA_PRIVACY = "DATA_PRIVACY"
    CREDIT_REPORTING = "CREDIT_REPORTING"
    ANTI_MONEY_LAUNDERING = "ANTI_MONEY_LAUNDERING"
    KYC_COMPLIANCE = "KYC_COMPLIANCE"
    CYBER_SECURITY = "CYBER_SECURITY"

class ComplianceStatus(str, Enum):
    COMPLIANT = "COMPLIANT"
    NON_COMPLIANT = "NON_COMPLIANT"
    PARTIAL_COMPLIANCE = "PARTIAL_COMPLIANCE"
    UNDER_REVIEW = "UNDER_REVIEW"
    EXEMPTED = "EXEMPTED"

class RiskLevel(str, Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class DataClassification(str, Enum):
    PUBLIC = "PUBLIC"
    INTERNAL = "INTERNAL"
    CONFIDENTIAL = "CONFIDENTIAL"
    RESTRICTED = "RESTRICTED"

class RBICompliance(BaseModel):
    """RBI compliance tracking and monitoring"""
    compliance_id: str = Field(..., description="Compliance record identifier")
    msme_id: str = Field(..., description="MSME identifier")
    
    # RBI Guidelines Compliance
    credit_policy_compliance: bool = Field(True, description="Credit policy compliance")
    fair_practices_code: bool = Field(True, description="Fair practices code adherence")
    interest_rate_guidelines: bool = Field(True, description="Interest rate guidelines compliance")
    loan_recovery_guidelines: bool = Field(True, description="Loan recovery guidelines compliance")
    
    # Credit Information Compliance
    cibil_reporting_compliance: bool = Field(True, description="CIBIL reporting compliance")
    credit_bureau_guidelines: bool = Field(True, description="Credit bureau guidelines compliance")
    borrower_consent_obtained: bool = Field(True, description="Borrower consent for credit checks")
    
    # Data Protection Compliance
    data_localization_compliance: bool = Field(True, description="Data localization compliance")
    customer_data_protection: bool = Field(True, description="Customer data protection measures")
    data_retention_policy: bool = Field(True, description="Data retention policy compliance")
    
    # KYC and AML Compliance
    kyc_compliance: bool = Field(True, description="KYC compliance status")
    aml_compliance: bool = Field(True, description="AML compliance status")
    suspicious_transaction_reporting: bool = Field(True, description="STR compliance")
    
    # Cyber Security Compliance
    cyber_security_framework: bool = Field(True, description="Cyber security framework compliance")
    data_breach_protocols: bool = Field(True, description="Data breach response protocols")
    security_audit_compliance: bool = Field(True, description="Security audit compliance")
    
    # Overall Compliance Score
    overall_compliance_score: float = Field(0.0, description="Overall compliance score (0-100)")
    compliance_risk_level: RiskLevel = Field(RiskLevel.LOW, description="Compliance risk level")
    
    # Compliance Dates
    last_compliance_review: date = Field(..., description="Last compliance review date")
    next_compliance_review: date = Field(..., description="Next compliance review date")
    
    # Non-compliance Issues
    non_compliance_issues: List[str] = Field(default_factory=list, description="Non-compliance issues")
    remediation_actions: List[str] = Field(default_factory=list, description="Remediation actions")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class DataPrivacyCompliance(BaseModel):
    """Data privacy and protection compliance"""
    privacy_id: str = Field(..., description="Privacy compliance identifier")
    msme_id: str = Field(..., description="MSME identifier")
    
    # Consent Management
    consent_obtained: bool = Field(False, description="Data processing consent obtained")
    consent_date: Optional[datetime] = Field(None, description="Consent obtained date")
    consent_expiry: Optional[datetime] = Field(None, description="Consent expiry date")
    consent_scope: List[str] = Field(default_factory=list, description="Scope of consent")
    
    # Data Processing
    data_minimization: bool = Field(True, description="Data minimization principle followed")
    purpose_limitation: bool = Field(True, description="Purpose limitation compliance")
    data_accuracy: bool = Field(True, description="Data accuracy maintained")
    storage_limitation: bool = Field(True, description="Storage limitation compliance")
    
    # Data Subject Rights
    right_to_access: bool = Field(True, description="Right to access implemented")
    right_to_rectification: bool = Field(True, description="Right to rectification implemented")
    right_to_erasure: bool = Field(True, description="Right to erasure implemented")
    right_to_portability: bool = Field(True, description="Right to data portability implemented")
    
    # Security Measures
    encryption_at_rest: bool = Field(True, description="Data encryption at rest")
    encryption_in_transit: bool = Field(True, description="Data encryption in transit")
    access_controls: bool = Field(True, description="Access controls implemented")
    audit_logging: bool = Field(True, description="Audit logging enabled")
    
    # Data Sharing
    third_party_sharing: bool = Field(False, description="Data shared with third parties")
    sharing_agreements: List[str] = Field(default_factory=list, description="Data sharing agreements")
    cross_border_transfer: bool = Field(False, description="Cross-border data transfer")
    
    # Breach Management
    breach_detection: bool = Field(True, description="Breach detection mechanisms")
    breach_response_plan: bool = Field(True, description="Breach response plan in place")
    breach_notification_process: bool = Field(True, description="Breach notification process")
    
    privacy_score: float = Field(0.0, description="Privacy compliance score (0-100)")
    privacy_risk_level: RiskLevel = Field(RiskLevel.LOW, description="Privacy risk level")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class SecurityAudit(BaseModel):
    """Security audit and assessment"""
    audit_id: str = Field(..., description="Audit identifier")
    audit_type: str = Field(..., description="Type of security audit")
    audit_date: date = Field(..., description="Audit date")
    auditor: str = Field(..., description="Auditor name/organization")
    
    # Audit Scope
    scope_description: str = Field(..., description="Audit scope description")
    systems_audited: List[str] = Field(default_factory=list, description="Systems audited")
    data_sources_audited: List[str] = Field(default_factory=list, description="Data sources audited")
    
    # Findings
    critical_findings: List[str] = Field(default_factory=list, description="Critical findings")
    high_findings: List[str] = Field(default_factory=list, description="High priority findings")
    medium_findings: List[str] = Field(default_factory=list, description="Medium priority findings")
    low_findings: List[str] = Field(default_factory=list, description="Low priority findings")
    
    # Recommendations
    immediate_actions: List[str] = Field(default_factory=list, description="Immediate actions required")
    short_term_actions: List[str] = Field(default_factory=list, description="Short-term actions")
    long_term_actions: List[str] = Field(default_factory=list, description="Long-term actions")
    
    # Compliance Assessment
    compliance_gaps: List[str] = Field(default_factory=list, description="Compliance gaps identified")
    regulatory_violations: List[str] = Field(default_factory=list, description="Regulatory violations")
    
    # Overall Assessment
    overall_security_score: float = Field(0.0, description="Overall security score (0-100)")
    risk_rating: RiskLevel = Field(RiskLevel.MEDIUM, description="Overall risk rating")
    
    # Follow-up
    remediation_deadline: date = Field(..., description="Remediation deadline")
    next_audit_date: date = Field(..., description="Next audit date")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)

class DataGovernance(BaseModel):
    """Data governance and management"""
    governance_id: str = Field(..., description="Governance record identifier")
    
    # Data Classification
    data_inventory: Dict[str, DataClassification] = Field(default_factory=dict, description="Data inventory with classifications")
    sensitive_data_identified: bool = Field(True, description="Sensitive data identified")
    data_flow_mapping: bool = Field(True, description="Data flow mapping completed")
    
    # Data Quality
    data_quality_score: float = Field(0.0, description="Data quality score (0-100)")
    data_completeness: float = Field(0.0, description="Data completeness percentage")
    data_accuracy: float = Field(0.0, description="Data accuracy percentage")
    data_consistency: float = Field(0.0, description="Data consistency percentage")
    
    # Data Lifecycle Management
    data_retention_policy: bool = Field(True, description="Data retention policy in place")
    data_archival_process: bool = Field(True, description="Data archival process defined")
    data_disposal_process: bool = Field(True, description="Data disposal process defined")
    
    # Access Management
    role_based_access: bool = Field(True, description="Role-based access control")
    privileged_access_management: bool = Field(True, description="Privileged access management")
    access_review_process: bool = Field(True, description="Regular access reviews")
    
    # Monitoring and Reporting
    data_usage_monitoring: bool = Field(True, description="Data usage monitoring")
    compliance_reporting: bool = Field(True, description="Compliance reporting")
    incident_tracking: bool = Field(True, description="Data incident tracking")
    
    governance_score: float = Field(0.0, description="Data governance score (0-100)")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class ComplianceAlert(BaseModel):
    """Compliance alerts and notifications"""
    alert_id: str = Field(..., description="Alert identifier")
    alert_type: ComplianceType = Field(..., description="Type of compliance alert")
    severity: RiskLevel = Field(..., description="Alert severity")
    
    # Alert Details
    title: str = Field(..., description="Alert title")
    description: str = Field(..., description="Alert description")
    affected_systems: List[str] = Field(default_factory=list, description="Affected systems")
    affected_data: List[str] = Field(default_factory=list, description="Affected data")
    
    # Timeline
    detected_at: datetime = Field(default_factory=datetime.utcnow, description="Detection timestamp")
    due_date: Optional[datetime] = Field(None, description="Resolution due date")
    resolved_at: Optional[datetime] = Field(None, description="Resolution timestamp")
    
    # Status
    status: str = Field("OPEN", description="Alert status")
    assigned_to: Optional[str] = Field(None, description="Assigned team/person")
    
    # Actions
    required_actions: List[str] = Field(default_factory=list, description="Required actions")
    completed_actions: List[str] = Field(default_factory=list, description="Completed actions")
    
    # Impact Assessment
    business_impact: str = Field("MEDIUM", description="Business impact level")
    regulatory_impact: str = Field("MEDIUM", description="Regulatory impact level")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class ComplianceReport(BaseModel):
    """Comprehensive compliance reporting"""
    report_id: str = Field(..., description="Report identifier")
    report_type: str = Field(..., description="Type of compliance report")
    reporting_period: str = Field(..., description="Reporting period")
    
    # Overall Compliance Status
    overall_compliance_score: float = Field(0.0, description="Overall compliance score")
    compliance_trend: str = Field("stable", description="Compliance trend")
    
    # Category Scores
    rbi_compliance_score: float = Field(0.0, description="RBI compliance score")
    data_privacy_score: float = Field(0.0, description="Data privacy score")
    security_score: float = Field(0.0, description="Security score")
    governance_score: float = Field(0.0, description="Governance score")
    
    # Risk Assessment
    high_risk_areas: List[str] = Field(default_factory=list, description="High risk areas")
    compliance_gaps: List[str] = Field(default_factory=list, description="Compliance gaps")
    improvement_recommendations: List[str] = Field(default_factory=list, description="Improvement recommendations")
    
    # Metrics
    total_msmes_assessed: int = Field(0, description="Total MSMEs assessed")
    compliant_msmes: int = Field(0, description="Compliant MSMEs")
    non_compliant_msmes: int = Field(0, description="Non-compliant MSMEs")
    
    # Incidents and Breaches
    security_incidents: int = Field(0, description="Security incidents count")
    data_breaches: int = Field(0, description="Data breaches count")
    regulatory_violations: int = Field(0, description="Regulatory violations count")
    
    # Remediation Status
    open_issues: int = Field(0, description="Open compliance issues")
    resolved_issues: int = Field(0, description="Resolved compliance issues")
    overdue_issues: int = Field(0, description="Overdue compliance issues")
    
    generated_at: datetime = Field(default_factory=datetime.utcnow)
    generated_by: str = Field(..., description="Report generator")

class ComplianceRequest(BaseModel):
    """Request model for compliance assessment"""
    msme_id: Optional[str] = Field(None, description="MSME identifier")
    compliance_types: List[ComplianceType] = Field(default_factory=list, description="Types of compliance to assess")
    include_recommendations: bool = Field(True, description="Include recommendations")
    include_risk_assessment: bool = Field(True, description="Include risk assessment")

class ComplianceResponse(BaseModel):
    """Response model for compliance assessment"""
    msme_id: Optional[str] = None
    
    rbi_compliance: Optional[RBICompliance] = None
    data_privacy: Optional[DataPrivacyCompliance] = None
    security_audit: Optional[SecurityAudit] = None
    governance: Optional[DataGovernance] = None
    alerts: List[ComplianceAlert] = Field(default_factory=list)
    
    overall_compliance_score: float = Field(0.0, description="Overall compliance score")
    compliance_status: ComplianceStatus = Field(ComplianceStatus.UNDER_REVIEW)
    risk_level: RiskLevel = Field(RiskLevel.MEDIUM)
    
    recommendations: List[str] = Field(default_factory=list)
    next_review_date: date = Field(default_factory=lambda: date.today())
    
    data_freshness: datetime = Field(default_factory=datetime.utcnow)
    status: str = Field("success", description="Response status")
    message: Optional[str] = None
