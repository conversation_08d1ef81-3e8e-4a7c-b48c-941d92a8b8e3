"""
Export service for Credit Chakra reports.

This service handles PDF and CSV export functionality for all report types with:
- Professional PDF formatting with Credit Chakra branding
- CSV exports with proper data structure
- File management and cleanup
- Download URL generation

Author: Credit Chakra Team
Version: 1.0.0
"""
from __future__ import annotations

import os
import uuid
from datetime import datetime, timezone, timedelta
from typing import Any, Dict, List, Optional, Union
import logging
import tempfile
import pandas as pd
from io import BytesIO

from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.platypus.flowables import HRFlowable
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT

from models.reports import (
    ReportFormat, ReportExportResponse, ReportStatus,
    PortfolioSummaryReport, RiskExposureReport, ComplianceRegulatoryReport,
    PerformanceTrendsReport, DetailedMSMEProfileReport
)

logger = logging.getLogger(__name__)


class ExportService:
    """Service for exporting reports in various formats"""
    
    def __init__(self):
        self.export_dir = os.path.join(tempfile.gettempdir(), "credit_chakra_exports")
        os.makedirs(self.export_dir, exist_ok=True)
        
        # Define Credit Chakra brand colors
        self.brand_colors = {
            'primary': colors.Color(0.059, 0.522, 0.459),  # Emerald-600
            'secondary': colors.Color(0.031, 0.412, 0.365),  # Emerald-700
            'light': colors.Color(0.922, 0.992, 0.976),  # Emerald-50
            'text': colors.Color(0.047, 0.047, 0.047),  # Gray-950
            'muted': colors.Color(0.374, 0.374, 0.374)  # Gray-600
        }
    
    def _get_pdf_styles(self):
        """Get custom PDF styles for Credit Chakra branding"""
        styles = getSampleStyleSheet()
        
        # Custom title style
        styles.add(ParagraphStyle(
            name='CreditChakraTitle',
            parent=styles['Title'],
            fontSize=24,
            textColor=self.brand_colors['primary'],
            spaceAfter=20,
            alignment=TA_CENTER,
            fontName='Helvetica-Bold'
        ))
        
        # Custom heading style
        styles.add(ParagraphStyle(
            name='CreditChakraHeading',
            parent=styles['Heading1'],
            fontSize=16,
            textColor=self.brand_colors['secondary'],
            spaceAfter=12,
            spaceBefore=20,
            fontName='Helvetica-Bold'
        ))
        
        # Custom subheading style
        styles.add(ParagraphStyle(
            name='CreditChakraSubheading',
            parent=styles['Heading2'],
            fontSize=14,
            textColor=self.brand_colors['secondary'],
            spaceAfter=8,
            spaceBefore=12,
            fontName='Helvetica-Bold'
        ))
        
        # Custom body style
        styles.add(ParagraphStyle(
            name='CreditChakraBody',
            parent=styles['Normal'],
            fontSize=10,
            textColor=self.brand_colors['text'],
            spaceAfter=6,
            fontName='Helvetica'
        ))
        
        return styles
    
    def _create_pdf_header(self, doc, styles):
        """Create PDF header with Credit Chakra branding"""
        story = []
        
        # Title
        title = Paragraph("Credit Chakra", styles['CreditChakraTitle'])
        story.append(title)
        
        # Subtitle
        subtitle = Paragraph("MSME Credit Monitoring Dashboard", styles['CreditChakraBody'])
        story.append(subtitle)
        
        # Horizontal line
        story.append(HRFlowable(width="100%", thickness=2, color=self.brand_colors['primary']))
        story.append(Spacer(1, 20))
        
        return story
    
    def _create_pdf_footer(self, report_metadata, styles):
        """Create PDF footer with generation info"""
        story = []
        
        story.append(Spacer(1, 20))
        story.append(HRFlowable(width="100%", thickness=1, color=self.brand_colors['muted']))
        
        footer_text = f"""
        <para align="center">
        Generated on: {report_metadata.generated_at.strftime('%B %d, %Y at %I:%M %p')} | 
        Generated by: {report_metadata.generated_by} | 
        Report ID: {report_metadata.report_id}
        </para>
        """
        
        footer = Paragraph(footer_text, styles['CreditChakraBody'])
        story.append(footer)
        
        return story
    
    async def export_portfolio_summary_pdf(self, report: PortfolioSummaryReport) -> str:
        """Export Portfolio Summary Report as PDF"""
        try:
            filename = f"portfolio_summary_{report.metadata.report_id}.pdf"
            filepath = os.path.join(self.export_dir, filename)
            
            doc = SimpleDocTemplate(filepath, pagesize=A4)
            styles = self._get_pdf_styles()
            story = []
            
            # Header
            story.extend(self._create_pdf_header(doc, styles))
            
            # Report title
            story.append(Paragraph("Portfolio Summary Report", styles['CreditChakraHeading']))
            story.append(Paragraph(f"Reporting Period: {report.metadata.reporting_period_start} to {report.metadata.reporting_period_end}", styles['CreditChakraBody']))
            story.append(Spacer(1, 20))
            
            # Key Metrics
            story.append(Paragraph("Key Portfolio Metrics", styles['CreditChakraSubheading']))
            
            metrics_data = [
                ['Metric', 'Value'],
                ['Total MSMEs', str(report.total_msmes)],
                ['Active MSMEs', str(report.active_msmes)],
                ['Average Credit Score', f"{report.average_credit_score:.1f}"],
                ['Portfolio Health Score', f"{report.overall_portfolio_health_score:.1f}%"],
                ['Portfolio Growth Rate', f"{report.portfolio_growth_rate:.1f}%"]
            ]
            
            metrics_table = Table(metrics_data, colWidths=[3*inch, 2*inch])
            metrics_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), self.brand_colors['primary']),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('GRID', (0, 0), (-1, -1), 1, self.brand_colors['muted'])
            ]))
            
            story.append(metrics_table)
            story.append(Spacer(1, 20))
            
            # Risk Distribution
            story.append(Paragraph("Risk Distribution", styles['CreditChakraSubheading']))
            
            risk_data = [
                ['Risk Level', 'Count', 'Percentage'],
                ['High Risk', str(report.risk_distribution.high_risk_count), f"{report.risk_distribution.high_risk_percentage:.1f}%"],
                ['Medium Risk', str(report.risk_distribution.medium_risk_count), f"{report.risk_distribution.medium_risk_percentage:.1f}%"],
                ['Low Risk', str(report.risk_distribution.low_risk_count), f"{report.risk_distribution.low_risk_percentage:.1f}%"]
            ]
            
            risk_table = Table(risk_data, colWidths=[2*inch, 1.5*inch, 1.5*inch])
            risk_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), self.brand_colors['primary']),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('GRID', (0, 0), (-1, -1), 1, self.brand_colors['muted'])
            ]))
            
            story.append(risk_table)
            story.append(Spacer(1, 20))
            
            # SMA Classification
            story.append(Paragraph("SMA Classification Summary", styles['CreditChakraSubheading']))
            
            sma_data = [
                ['Classification', 'Count', 'Amount (₹)'],
                ['Standard Accounts', str(report.sma_accounts_summary.standard_accounts), '-'],
                ['SMA-0 (1-30 days)', str(report.sma_accounts_summary.sma_0_count), '-'],
                ['SMA-1 (31-60 days)', str(report.sma_accounts_summary.sma_1_count), '-'],
                ['SMA-2 (61-90 days)', str(report.sma_accounts_summary.sma_2_count), '-'],
                ['Total Overdue', '-', f"₹{report.sma_accounts_summary.total_overdue_amount:,.0f}"],
                ['Provision Required', '-', f"₹{report.sma_accounts_summary.provision_required:,.0f}"]
            ]
            
            sma_table = Table(sma_data, colWidths=[2.5*inch, 1.5*inch, 1.5*inch])
            sma_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), self.brand_colors['primary']),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('GRID', (0, 0), (-1, -1), 1, self.brand_colors['muted'])
            ]))
            
            story.append(sma_table)
            story.append(Spacer(1, 20))
            
            # Key Insights
            if report.key_insights:
                story.append(Paragraph("Key Insights", styles['CreditChakraSubheading']))
                for insight in report.key_insights:
                    story.append(Paragraph(f"• {insight}", styles['CreditChakraBody']))
                story.append(Spacer(1, 10))
            
            # Recommendations
            if report.recommendations:
                story.append(Paragraph("Recommendations", styles['CreditChakraSubheading']))
                for recommendation in report.recommendations:
                    story.append(Paragraph(f"• {recommendation}", styles['CreditChakraBody']))
            
            # Footer
            story.extend(self._create_pdf_footer(report.metadata, styles))
            
            # Build PDF
            doc.build(story)
            
            logger.info(f"Successfully exported Portfolio Summary Report to PDF: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error exporting Portfolio Summary Report to PDF: {str(e)}")
            raise
    
    async def export_portfolio_summary_csv(self, report: PortfolioSummaryReport) -> str:
        """Export Portfolio Summary Report as CSV"""
        try:
            filename = f"portfolio_summary_{report.metadata.report_id}.csv"
            filepath = os.path.join(self.export_dir, filename)
            
            # Create comprehensive data structure
            data = {
                'Report_ID': [report.metadata.report_id],
                'Generated_At': [report.metadata.generated_at.isoformat()],
                'Generated_By': [report.metadata.generated_by],
                'Reporting_Period_Start': [report.metadata.reporting_period_start.isoformat()],
                'Reporting_Period_End': [report.metadata.reporting_period_end.isoformat()],
                'Total_MSMEs': [report.total_msmes],
                'Active_MSMEs': [report.active_msmes],
                'Inactive_MSMEs': [report.inactive_msmes],
                'Average_Credit_Score': [report.average_credit_score],
                'Portfolio_Health_Score': [report.overall_portfolio_health_score],
                'Portfolio_Growth_Rate': [report.portfolio_growth_rate],
                'High_Risk_Count': [report.risk_distribution.high_risk_count],
                'High_Risk_Percentage': [report.risk_distribution.high_risk_percentage],
                'Medium_Risk_Count': [report.risk_distribution.medium_risk_count],
                'Medium_Risk_Percentage': [report.risk_distribution.medium_risk_percentage],
                'Low_Risk_Count': [report.risk_distribution.low_risk_count],
                'Low_Risk_Percentage': [report.risk_distribution.low_risk_percentage],
                'Standard_Accounts': [report.sma_accounts_summary.standard_accounts],
                'SMA_0_Count': [report.sma_accounts_summary.sma_0_count],
                'SMA_1_Count': [report.sma_accounts_summary.sma_1_count],
                'SMA_2_Count': [report.sma_accounts_summary.sma_2_count],
                'Total_Overdue_Amount': [report.sma_accounts_summary.total_overdue_amount],
                'Provision_Required': [report.sma_accounts_summary.provision_required],
                'Retail_Count': [report.business_type_distribution.retail_count],
                'Manufacturing_Count': [report.business_type_distribution.manufacturing_count],
                'Services_Count': [report.business_type_distribution.services_count],
                'B2B_Count': [report.business_type_distribution.b2b_count]
            }
            
            df = pd.DataFrame(data)
            df.to_csv(filepath, index=False)
            
            logger.info(f"Successfully exported Portfolio Summary Report to CSV: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error exporting Portfolio Summary Report to CSV: {str(e)}")
            raise
    
    def cleanup_old_exports(self, max_age_hours: int = 24):
        """Clean up old export files"""
        try:
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=max_age_hours)
            
            for filename in os.listdir(self.export_dir):
                filepath = os.path.join(self.export_dir, filename)
                if os.path.isfile(filepath):
                    file_time = datetime.fromtimestamp(os.path.getctime(filepath))
                    if file_time < cutoff_time:
                        os.remove(filepath)
                        logger.info(f"Cleaned up old export file: {filename}")
                        
        except Exception as e:
            logger.error(f"Error cleaning up old exports: {str(e)}")


# Create singleton instance
export_service = ExportService()
