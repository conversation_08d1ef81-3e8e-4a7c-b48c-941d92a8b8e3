'use client';

import { memo, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { QuerySuggestionsProps } from '@/types/copilot';

// Enhanced suggestions with categories
const defaultSuggestions = [
  "Show me high risk MSMEs that need attention",
  "Which MSMEs had significant score drops recently?",
  "What compliance deadlines are coming up?",
  "Analyze risk distribution across branches",
  "Show manufacturing sector performance trends",
  "List overdue compliance tasks"
];

const QuerySuggestionsComponent = memo(function QuerySuggestions({
  onSuggestionClick,
  suggestions = defaultSuggestions,
  category = 'general'
}: QuerySuggestionsProps) {

  // Memoized click handler
  const handleSuggestionClick = useCallback((suggestion: string) => {
    onSuggestionClick(suggestion);
  }, [onSuggestionClick]);
  return (
    <div className="max-w-2xl mx-auto space-y-4">
      <div className="text-center">
        <h3 className="text-lg font-medium text-foreground mb-2">
          Popular questions
        </h3>
        <p className="text-sm text-muted-foreground">
          Click on any question below or type your own
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {suggestions.map((suggestion, index) => (
          <Button
            key={`suggestion-${category}-${index}`}
            variant="outline"
            className="h-auto p-3 text-left justify-start hover:bg-muted/50 transition-colors focus:outline-none focus:ring-2 focus:ring-emerald-500"
            onClick={() => handleSuggestionClick(suggestion)}
            aria-label={`Suggestion: ${suggestion}`}
          >
            <div className="text-sm text-foreground">
              {suggestion}
            </div>
          </Button>
        ))}
      </div>
    </div>
  );
});

// Export the memoized component
export { QuerySuggestionsComponent as QuerySuggestions };
