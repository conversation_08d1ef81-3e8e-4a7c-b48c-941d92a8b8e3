"""
AI Business Profile Analyzer for Credit Chakra
Advanced analysis of MSME business profiles using machine learning
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

logger = logging.getLogger(__name__)

class BusinessHealthCategory(Enum):
    EXCELLENT = "excellent"
    GOOD = "good"
    AVERAGE = "average"
    POOR = "poor"
    CRITICAL = "critical"

class GrowthStage(Enum):
    STARTUP = "startup"
    GROWTH = "growth"
    MATURE = "mature"
    DECLINE = "decline"
    TURNAROUND = "turnaround"

class RiskCategory(Enum):
    FINANCIAL = "financial"
    OPERATIONAL = "operational"
    MARKET = "market"
    REGULATORY = "regulatory"
    TECHNOLOGICAL = "technological"

@dataclass
class BusinessInsight:
    category: str
    insight: str
    confidence: float
    impact_level: str
    actionable: bool
    recommendations: List[str]

@dataclass
class AIBusinessProfile:
    msme_id: str
    business_name: str
    overall_health_score: float
    health_category: BusinessHealthCategory
    growth_stage: GrowthStage
    financial_strength: Dict[str, float]
    operational_efficiency: Dict[str, float]
    market_position: Dict[str, float]
    digital_maturity: Dict[str, float]
    risk_assessment: Dict[RiskCategory, Dict[str, Any]]
    growth_potential: Dict[str, float]
    competitive_advantages: List[str]
    improvement_areas: List[str]
    ai_insights: List[BusinessInsight]
    benchmarking: Dict[str, Any]
    predictions: Dict[str, Any]
    created_at: datetime

class AIBusinessAnalyzer:
    """
    Advanced AI-powered business profile analyzer
    """
    
    def __init__(self):
        self.analysis_models = self._initialize_analysis_models()
        self.benchmarking_data = self._load_benchmarking_data()
        self.insight_generators = self._initialize_insight_generators()
        
    def _initialize_analysis_models(self) -> Dict[str, Any]:
        """Initialize AI analysis models"""
        return {
            'financial_analyzer': {
                'revenue_stability': {'weight': 0.25, 'indicators': ['revenue_trend', 'seasonality']},
                'profitability': {'weight': 0.20, 'indicators': ['margin_trends', 'cost_efficiency']},
                'liquidity': {'weight': 0.20, 'indicators': ['cash_flow', 'working_capital']},
                'leverage': {'weight': 0.15, 'indicators': ['debt_ratio', 'interest_coverage']},
                'growth': {'weight': 0.20, 'indicators': ['revenue_growth', 'market_expansion']}
            },
            'operational_analyzer': {
                'efficiency': {'weight': 0.30, 'indicators': ['productivity', 'automation_level']},
                'quality': {'weight': 0.25, 'indicators': ['defect_rates', 'customer_satisfaction']},
                'supply_chain': {'weight': 0.20, 'indicators': ['supplier_diversity', 'inventory_management']},
                'innovation': {'weight': 0.25, 'indicators': ['r_and_d_investment', 'product_development']}
            },
            'market_analyzer': {
                'market_share': {'weight': 0.30, 'indicators': ['relative_position', 'brand_strength']},
                'customer_base': {'weight': 0.25, 'indicators': ['customer_retention', 'acquisition_rate']},
                'competitive_position': {'weight': 0.25, 'indicators': ['differentiation', 'pricing_power']},
                'market_trends': {'weight': 0.20, 'indicators': ['industry_growth', 'disruption_risk']}
            },
            'digital_analyzer': {
                'digital_presence': {'weight': 0.25, 'indicators': ['online_visibility', 'social_media']},
                'digital_payments': {'weight': 0.30, 'indicators': ['upi_adoption', 'digital_transactions']},
                'automation': {'weight': 0.25, 'indicators': ['process_automation', 'digital_tools']},
                'data_analytics': {'weight': 0.20, 'indicators': ['data_usage', 'analytics_maturity']}
            }
        }
    
    def _load_benchmarking_data(self) -> Dict[str, Any]:
        """Load industry benchmarking data"""
        return {
            'Manufacturing': {
                'avg_health_score': 68.5,
                'financial_benchmarks': {'profitability': 0.12, 'liquidity': 1.8, 'leverage': 0.45},
                'operational_benchmarks': {'efficiency': 0.75, 'quality': 0.92},
                'digital_benchmarks': {'digital_payments': 0.65, 'automation': 0.45}
            },
            'Retail': {
                'avg_health_score': 72.3,
                'financial_benchmarks': {'profitability': 0.08, 'liquidity': 2.1, 'leverage': 0.35},
                'operational_benchmarks': {'efficiency': 0.80, 'quality': 0.88},
                'digital_benchmarks': {'digital_payments': 0.85, 'automation': 0.60}
            },
            'Services': {
                'avg_health_score': 75.8,
                'financial_benchmarks': {'profitability': 0.15, 'liquidity': 2.5, 'leverage': 0.25},
                'operational_benchmarks': {'efficiency': 0.85, 'quality': 0.90},
                'digital_benchmarks': {'digital_payments': 0.90, 'automation': 0.70}
            },
            'Technology': {
                'avg_health_score': 81.2,
                'financial_benchmarks': {'profitability': 0.20, 'liquidity': 3.0, 'leverage': 0.20},
                'operational_benchmarks': {'efficiency': 0.90, 'quality': 0.95},
                'digital_benchmarks': {'digital_payments': 0.95, 'automation': 0.85}
            }
        }
    
    def _initialize_insight_generators(self) -> Dict[str, Any]:
        """Initialize AI insight generation models"""
        return {
            'pattern_recognition': {
                'seasonal_patterns': True,
                'growth_patterns': True,
                'risk_patterns': True,
                'opportunity_patterns': True
            },
            'predictive_models': {
                'revenue_forecasting': True,
                'risk_prediction': True,
                'growth_prediction': True,
                'market_trend_prediction': True
            },
            'recommendation_engine': {
                'improvement_recommendations': True,
                'growth_recommendations': True,
                'risk_mitigation_recommendations': True,
                'digital_transformation_recommendations': True
            }
        }
    
    async def analyze_business_profile(self, msme_data: Dict[str, Any]) -> AIBusinessProfile:
        """
        Comprehensive AI-powered business profile analysis
        """
        try:
            msme_id = msme_data.get('id', 'unknown')
            business_name = msme_data.get('business_name', 'Unknown Business')
            
            logger.info(f"Starting AI business analysis for: {business_name}")
            
            # Parallel analysis tasks
            tasks = [
                self._analyze_financial_strength(msme_data),
                self._analyze_operational_efficiency(msme_data),
                self._analyze_market_position(msme_data),
                self._analyze_digital_maturity(msme_data),
                self._assess_risks(msme_data),
                self._evaluate_growth_potential(msme_data)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            financial_strength = results[0] if not isinstance(results[0], Exception) else {}
            operational_efficiency = results[1] if not isinstance(results[1], Exception) else {}
            market_position = results[2] if not isinstance(results[2], Exception) else {}
            digital_maturity = results[3] if not isinstance(results[3], Exception) else {}
            risk_assessment = results[4] if not isinstance(results[4], Exception) else {}
            growth_potential = results[5] if not isinstance(results[5], Exception) else {}
            
            # Calculate overall health score
            overall_health_score = self._calculate_overall_health_score(
                financial_strength, operational_efficiency, market_position, digital_maturity
            )
            
            # Determine health category and growth stage
            health_category = self._determine_health_category(overall_health_score)
            growth_stage = self._determine_growth_stage(msme_data, growth_potential)
            
            # Generate AI insights
            ai_insights = await self._generate_ai_insights(
                msme_data, financial_strength, operational_efficiency, 
                market_position, digital_maturity, risk_assessment
            )
            
            # Generate benchmarking analysis
            benchmarking = await self._generate_benchmarking_analysis(msme_data, overall_health_score)
            
            # Generate predictions
            predictions = await self._generate_predictions(msme_data, ai_insights)
            
            # Identify competitive advantages and improvement areas
            competitive_advantages = self._identify_competitive_advantages(
                financial_strength, operational_efficiency, market_position, digital_maturity
            )
            improvement_areas = self._identify_improvement_areas(
                financial_strength, operational_efficiency, market_position, digital_maturity
            )
            
            return AIBusinessProfile(
                msme_id=msme_id,
                business_name=business_name,
                overall_health_score=overall_health_score,
                health_category=health_category,
                growth_stage=growth_stage,
                financial_strength=financial_strength,
                operational_efficiency=operational_efficiency,
                market_position=market_position,
                digital_maturity=digital_maturity,
                risk_assessment=risk_assessment,
                growth_potential=growth_potential,
                competitive_advantages=competitive_advantages,
                improvement_areas=improvement_areas,
                ai_insights=ai_insights,
                benchmarking=benchmarking,
                predictions=predictions,
                created_at=datetime.now(timezone.utc)
            )
            
        except Exception as e:
            logger.error(f"Business profile analysis failed for {msme_data.get('id')}: {str(e)}")
            return self._get_fallback_profile(msme_data)
    
    async def _analyze_financial_strength(self, msme_data: Dict[str, Any]) -> Dict[str, float]:
        """Analyze financial strength indicators"""
        score = msme_data.get('score', 50)
        
        # Mock financial analysis - would use actual financial data
        return {
            'revenue_stability': min(100, score + 10) / 100,
            'profitability': min(100, score + 5) / 100,
            'liquidity': min(100, score + 15) / 100,
            'leverage': max(0, 100 - score) / 100,
            'growth_rate': min(100, score + 20) / 100,
            'overall_score': score / 100
        }
    
    async def _analyze_operational_efficiency(self, msme_data: Dict[str, Any]) -> Dict[str, float]:
        """Analyze operational efficiency metrics"""
        score = msme_data.get('score', 50)
        
        return {
            'productivity': min(100, score + 8) / 100,
            'quality_metrics': min(100, score + 12) / 100,
            'supply_chain_efficiency': min(100, score + 5) / 100,
            'innovation_index': min(100, score + 15) / 100,
            'overall_score': (score + 10) / 100
        }
    
    async def _analyze_market_position(self, msme_data: Dict[str, Any]) -> Dict[str, float]:
        """Analyze market position and competitive standing"""
        score = msme_data.get('score', 50)
        business_type = msme_data.get('business_type', 'Unknown')
        
        # Adjust based on business type
        type_multiplier = {
            'Technology': 1.2,
            'Services': 1.1,
            'Retail': 1.0,
            'Manufacturing': 0.9
        }.get(business_type, 1.0)
        
        adjusted_score = min(100, score * type_multiplier)
        
        return {
            'market_share': adjusted_score / 100,
            'customer_satisfaction': min(100, adjusted_score + 10) / 100,
            'brand_strength': min(100, adjusted_score + 5) / 100,
            'competitive_advantage': min(100, adjusted_score + 8) / 100,
            'overall_score': adjusted_score / 100
        }
    
    async def _analyze_digital_maturity(self, msme_data: Dict[str, Any]) -> Dict[str, float]:
        """Analyze digital transformation and technology adoption"""
        score = msme_data.get('score', 50)
        
        # Digital maturity often correlates with overall business health
        digital_base = min(100, score * 0.8 + 20)
        
        return {
            'digital_presence': digital_base / 100,
            'payment_digitization': min(100, digital_base + 15) / 100,
            'process_automation': min(100, digital_base + 10) / 100,
            'data_analytics_usage': min(100, digital_base + 5) / 100,
            'overall_score': digital_base / 100
        }
    
    async def _assess_risks(self, msme_data: Dict[str, Any]) -> Dict[RiskCategory, Dict[str, Any]]:
        """Assess various risk categories"""
        score = msme_data.get('score', 50)
        
        # Higher scores indicate lower risk
        base_risk = max(0, 100 - score) / 100
        
        return {
            RiskCategory.FINANCIAL: {
                'level': base_risk,
                'factors': ['cash_flow_volatility', 'debt_burden'],
                'mitigation_strategies': ['improve_cash_management', 'debt_restructuring']
            },
            RiskCategory.OPERATIONAL: {
                'level': base_risk * 0.8,
                'factors': ['supply_chain_disruption', 'key_person_dependency'],
                'mitigation_strategies': ['diversify_suppliers', 'succession_planning']
            },
            RiskCategory.MARKET: {
                'level': base_risk * 0.9,
                'factors': ['market_competition', 'demand_volatility'],
                'mitigation_strategies': ['market_diversification', 'product_innovation']
            }
        }
    
    async def _evaluate_growth_potential(self, msme_data: Dict[str, Any]) -> Dict[str, float]:
        """Evaluate growth potential and opportunities"""
        score = msme_data.get('score', 50)
        
        return {
            'market_expansion_potential': min(100, score + 20) / 100,
            'product_diversification_potential': min(100, score + 15) / 100,
            'digital_transformation_potential': min(100, score + 25) / 100,
            'scaling_readiness': score / 100,
            'overall_potential': min(100, score + 18) / 100
        }
    
    def _calculate_overall_health_score(self, financial: Dict, operational: Dict, market: Dict, digital: Dict) -> float:
        """Calculate weighted overall health score"""
        weights = {'financial': 0.35, 'operational': 0.25, 'market': 0.25, 'digital': 0.15}
        
        financial_score = financial.get('overall_score', 0.5) * 100
        operational_score = operational.get('overall_score', 0.5) * 100
        market_score = market.get('overall_score', 0.5) * 100
        digital_score = digital.get('overall_score', 0.5) * 100
        
        overall_score = (
            financial_score * weights['financial'] +
            operational_score * weights['operational'] +
            market_score * weights['market'] +
            digital_score * weights['digital']
        )
        
        return round(overall_score, 1)
    
    def _determine_health_category(self, score: float) -> BusinessHealthCategory:
        """Determine business health category based on score"""
        if score >= 85:
            return BusinessHealthCategory.EXCELLENT
        elif score >= 70:
            return BusinessHealthCategory.GOOD
        elif score >= 55:
            return BusinessHealthCategory.AVERAGE
        elif score >= 40:
            return BusinessHealthCategory.POOR
        else:
            return BusinessHealthCategory.CRITICAL
    
    def _determine_growth_stage(self, msme_data: Dict[str, Any], growth_potential: Dict[str, float]) -> GrowthStage:
        """Determine business growth stage"""
        # Mock implementation - would use more sophisticated analysis
        potential = growth_potential.get('overall_potential', 0.5)
        
        if potential > 0.8:
            return GrowthStage.GROWTH
        elif potential > 0.6:
            return GrowthStage.MATURE
        elif potential > 0.4:
            return GrowthStage.STARTUP
        else:
            return GrowthStage.DECLINE
    
    async def _generate_ai_insights(self, msme_data: Dict, financial: Dict, operational: Dict, market: Dict, digital: Dict, risks: Dict) -> List[BusinessInsight]:
        """Generate AI-powered business insights"""
        insights = []
        
        # Financial insights
        if financial.get('liquidity', 0) < 0.6:
            insights.append(BusinessInsight(
                category="Financial",
                insight="Liquidity position needs improvement to handle unexpected cash flow challenges",
                confidence=0.85,
                impact_level="High",
                actionable=True,
                recommendations=["Establish credit line", "Improve receivables collection", "Optimize inventory levels"]
            ))
        
        # Digital transformation insights
        if digital.get('overall_score', 0) < 0.5:
            insights.append(BusinessInsight(
                category="Digital",
                insight="Significant digital transformation opportunity to improve efficiency and customer reach",
                confidence=0.78,
                impact_level="Medium",
                actionable=True,
                recommendations=["Implement digital payment systems", "Develop online presence", "Automate key processes"]
            ))
        
        return insights
    
    async def _generate_benchmarking_analysis(self, msme_data: Dict[str, Any], health_score: float) -> Dict[str, Any]:
        """Generate benchmarking analysis against industry peers"""
        business_type = msme_data.get('business_type', 'Manufacturing')
        benchmark_data = self.benchmarking_data.get(business_type, self.benchmarking_data['Manufacturing'])
        
        return {
            'industry_average': benchmark_data['avg_health_score'],
            'performance_vs_peers': health_score - benchmark_data['avg_health_score'],
            'percentile_ranking': min(95, max(5, (health_score / benchmark_data['avg_health_score']) * 50)),
            'industry': business_type
        }
    
    async def _generate_predictions(self, msme_data: Dict[str, Any], insights: List[BusinessInsight]) -> Dict[str, Any]:
        """Generate AI-powered predictions"""
        return {
            'score_trend_6_months': 'stable',
            'growth_probability': 0.65,
            'risk_level_change': 'decreasing',
            'recommended_actions': len([i for i in insights if i.actionable])
        }
    
    def _identify_competitive_advantages(self, financial: Dict, operational: Dict, market: Dict, digital: Dict) -> List[str]:
        """Identify competitive advantages"""
        advantages = []
        
        if financial.get('profitability', 0) > 0.8:
            advantages.append("Strong profitability margins")
        if operational.get('quality_metrics', 0) > 0.85:
            advantages.append("Superior quality standards")
        if digital.get('payment_digitization', 0) > 0.8:
            advantages.append("Advanced digital payment adoption")
        if market.get('customer_satisfaction', 0) > 0.85:
            advantages.append("High customer satisfaction")
        
        return advantages or ["Stable business operations"]
    
    def _identify_improvement_areas(self, financial: Dict, operational: Dict, market: Dict, digital: Dict) -> List[str]:
        """Identify areas for improvement"""
        improvements = []
        
        if financial.get('liquidity', 0) < 0.6:
            improvements.append("Cash flow management")
        if operational.get('productivity', 0) < 0.7:
            improvements.append("Operational efficiency")
        if digital.get('overall_score', 0) < 0.6:
            improvements.append("Digital transformation")
        if market.get('market_share', 0) < 0.5:
            improvements.append("Market penetration")
        
        return improvements or ["Continue current performance"]
    
    def _get_fallback_profile(self, msme_data: Dict[str, Any]) -> AIBusinessProfile:
        """Provide fallback profile when analysis fails"""
        return AIBusinessProfile(
            msme_id=msme_data.get('id', 'unknown'),
            business_name=msme_data.get('business_name', 'Unknown Business'),
            overall_health_score=65.0,
            health_category=BusinessHealthCategory.AVERAGE,
            growth_stage=GrowthStage.MATURE,
            financial_strength={'overall_score': 0.65},
            operational_efficiency={'overall_score': 0.65},
            market_position={'overall_score': 0.65},
            digital_maturity={'overall_score': 0.65},
            risk_assessment={},
            growth_potential={'overall_potential': 0.65},
            competitive_advantages=["Stable operations"],
            improvement_areas=["Digital transformation"],
            ai_insights=[],
            benchmarking={'industry_average': 65.0},
            predictions={'score_trend_6_months': 'stable'},
            created_at=datetime.now(timezone.utc)
        )

# Global instance
ai_business_analyzer = AIBusinessAnalyzer()
