from fastapi import APIRouter, HTTPException, status, BackgroundTasks
from typing import List, Optional
from datetime import datetime, timezone, timedelta

from models.gst_data import (
    GSTDataRequest, GSTDataResponse, GSTTurnoverData, 
    GSTComplianceRecord, GSTPaymentPattern, GSTAnalytics,
    GSTHealthCheck, GSTReturnType
)
from services.gst_service import GSTService

router = APIRouter()
gst_service = GSTService()

@router.post("/gst/sync", response_model=GSTDataResponse, status_code=status.HTTP_200_OK)
async def sync_gst_data(request: GSTDataRequest, background_tasks: BackgroundTasks):
    """Sync GST data for an MSME"""
    try:
        # Validate GSTIN format
        if not _validate_gstin(request.gstin):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid GSTIN format"
            )
        
        # Fetch turnover data
        turnover_data = []
        periods = gst_service._generate_period_list(request.from_period, request.to_period)
        
        for period in periods:
            for return_type in request.return_types:
                data = await gst_service.fetch_gst_turnover_data(
                    request.gstin, period, return_type
                )
                if data:
                    turnover_data.append(data)
        
        # Fetch compliance data
        compliance_records = await gst_service.fetch_gst_compliance_data(
            request.gstin, request.from_period, request.to_period
        )
        
        # Analyze payment patterns
        payment_patterns = await gst_service.analyze_gst_payment_patterns(
            request.gstin, periods
        )
        
        # Calculate analytics if requested
        analytics = None
        if request.include_analytics:
            # This would typically require msme_id, for now using gstin
            analytics = await gst_service.calculate_gst_analytics(
                request.gstin, request.gstin
            )
        
        # Schedule background sync for real-time updates
        background_tasks.add_task(
            _schedule_periodic_sync, request.gstin
        )
        
        return GSTDataResponse(
            gstin=request.gstin,
            turnover_data=turnover_data,
            compliance_records=compliance_records,
            payment_patterns=payment_patterns,
            analytics=analytics,
            status="success",
            message=f"Successfully synced GST data for {len(periods)} periods"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to sync GST data: {str(e)}"
        )

@router.get("/gst/{gstin}/turnover", response_model=List[GSTTurnoverData])
async def get_gst_turnover_data(gstin: str, from_period: str, to_period: str):
    """Get GST turnover data for a specific period range"""
    try:
        if not _validate_gstin(gstin):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid GSTIN format"
            )
        
        turnover_data = await gst_service._get_stored_turnover_data(gstin)
        
        # Filter by period range
        filtered_data = [
            data for data in turnover_data
            if from_period <= data.period <= to_period
        ]
        
        return filtered_data
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get GST turnover data: {str(e)}"
        )

@router.get("/gst/{gstin}/compliance", response_model=List[GSTComplianceRecord])
async def get_gst_compliance_data(gstin: str, from_period: str, to_period: str):
    """Get GST compliance data for a specific period range"""
    try:
        if not _validate_gstin(gstin):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid GSTIN format"
            )
        
        compliance_records = await gst_service._get_stored_compliance_records(gstin)
        
        # Filter by period range
        filtered_records = [
            record for record in compliance_records
            if from_period <= record.period <= to_period
        ]
        
        return filtered_records
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get GST compliance data: {str(e)}"
        )

@router.get("/gst/{gstin}/payments", response_model=List[GSTPaymentPattern])
async def get_gst_payment_patterns(gstin: str, from_period: str, to_period: str):
    """Get GST payment patterns for a specific period range"""
    try:
        if not _validate_gstin(gstin):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid GSTIN format"
            )
        
        payment_patterns = await gst_service._get_stored_payment_patterns(gstin)
        
        # Filter by period range
        filtered_patterns = [
            pattern for pattern in payment_patterns
            if from_period <= pattern.period <= to_period
        ]
        
        return filtered_patterns
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get GST payment patterns: {str(e)}"
        )

@router.get("/gst/{gstin}/analytics", response_model=GSTAnalytics)
async def get_gst_analytics(gstin: str):
    """Get comprehensive GST analytics for an MSME"""
    try:
        if not _validate_gstin(gstin):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid GSTIN format"
            )
        
        analytics = await gst_service.calculate_gst_analytics(gstin, gstin)
        
        if not analytics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="GST analytics not found. Please sync GST data first."
            )
        
        return analytics
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get GST analytics: {str(e)}"
        )

@router.get("/gst/{gstin}/health", response_model=GSTHealthCheck)
async def get_gst_health_check(gstin: str):
    """Get GST data source health check"""
    try:
        if not _validate_gstin(gstin):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid GSTIN format"
            )
        
        health_check = await gst_service.get_gst_health_check(gstin)
        return health_check
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get GST health check: {str(e)}"
        )

@router.post("/gst/{gstin}/refresh", status_code=status.HTTP_202_ACCEPTED)
async def refresh_gst_data(gstin: str, background_tasks: BackgroundTasks):
    """Trigger manual refresh of GST data"""
    try:
        if not _validate_gstin(gstin):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid GSTIN format"
            )
        
        # Schedule background refresh
        background_tasks.add_task(
            _refresh_gst_data_background, gstin
        )
        
        return {"message": "GST data refresh initiated", "gstin": gstin}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initiate GST data refresh: {str(e)}"
        )

@router.get("/gst/{gstin}/score", response_model=dict)
async def get_gst_credit_score_impact(gstin: str):
    """Get GST data impact on credit score"""
    try:
        if not _validate_gstin(gstin):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid GSTIN format"
            )
        
        analytics = await gst_service.calculate_gst_analytics(gstin, gstin)
        
        if not analytics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="GST analytics not found"
            )
        
        # Calculate GST contribution to credit score
        score_impact = {
            "gst_score": analytics.overall_gst_score,
            "score_components": {
                "filing_compliance": {
                    "value": analytics.filing_compliance_rate,
                    "weight": 25,
                    "contribution": analytics.filing_compliance_rate * 0.25
                },
                "payment_compliance": {
                    "value": analytics.payment_compliance_rate,
                    "weight": 25,
                    "contribution": analytics.payment_compliance_rate * 0.25
                },
                "turnover_stability": {
                    "value": max(0, 100 - analytics.turnover_volatility),
                    "weight": 20,
                    "contribution": max(0, 100 - analytics.turnover_volatility) * 0.20
                },
                "itc_efficiency": {
                    "value": analytics.itc_efficiency,
                    "weight": 15,
                    "contribution": analytics.itc_efficiency * 0.15
                },
                "growth_trend": {
                    "value": min(100, max(0, 50 + analytics.turnover_growth_rate)),
                    "weight": 15,
                    "contribution": min(100, max(0, 50 + analytics.turnover_growth_rate)) * 0.15
                }
            },
            "risk_indicators": {
                "compliance_risk": analytics.compliance_risk_score,
                "payment_risk": analytics.payment_risk_score
            },
            "trends": {
                "turnover_trend": analytics.turnover_trend,
                "compliance_trend": analytics.compliance_trend
            },
            "recommendations": _generate_gst_recommendations(analytics)
        }
        
        return score_impact
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate GST score impact: {str(e)}"
        )

# Helper functions
def _validate_gstin(gstin: str) -> bool:
    """Validate GSTIN format"""
    if not gstin or len(gstin) != 15:
        return False
    
    # Basic GSTIN format validation
    # Format: 2 digits (state code) + 10 alphanumeric (PAN) + 1 digit (entity number) + 1 alphabet (Z) + 1 check digit
    import re
    pattern = r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}[Z]{1}[0-9A-Z]{1}$'
    return bool(re.match(pattern, gstin))

async def _schedule_periodic_sync(gstin: str):
    """Schedule periodic GST data sync"""
    # Implementation for scheduling periodic sync
    # This could use Celery, APScheduler, or similar
    pass

async def _refresh_gst_data_background(gstin: str):
    """Background task to refresh GST data"""
    try:
        # Get last 12 months of data
        from_period = (datetime.now(timezone.utc) - timedelta(days=365)).strftime("%m%Y")
        to_period = datetime.now(timezone.utc).strftime("%m%Y")
        
        request = GSTDataRequest(
            gstin=gstin,
            from_period=from_period,
            to_period=to_period,
            return_types=[GSTReturnType.GSTR3B],
            include_analytics=True
        )
        
        # Perform sync
        await sync_gst_data(request, BackgroundTasks())
        
    except Exception as e:
        # Log error
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Background GST refresh failed for {gstin}: {str(e)}")

def _generate_gst_recommendations(analytics: GSTAnalytics) -> List[str]:
    """Generate recommendations based on GST analytics"""
    recommendations = []
    
    if analytics.filing_compliance_rate < 80:
        recommendations.append("Improve GST filing compliance by setting up automated reminders")
    
    if analytics.payment_compliance_rate < 80:
        recommendations.append("Enhance payment discipline to avoid penalties and interest")
    
    if analytics.turnover_volatility > 30:
        recommendations.append("Work on stabilizing business turnover through diversification")
    
    if analytics.itc_efficiency < 70:
        recommendations.append("Optimize Input Tax Credit utilization to improve cash flow")
    
    if analytics.turnover_growth_rate < 0:
        recommendations.append("Focus on business growth strategies to increase turnover")
    
    if analytics.avg_filing_delay > 5:
        recommendations.append("Implement better accounting processes to reduce filing delays")
    
    return recommendations
