"""
Tests for the centralized data service.

This module tests the CentralizedDataService class which provides
consistent MSME data access patterns and mock data generation.

Author: Credit Chakra Team
Version: 1.0.0
"""
import pytest
from unittest.mock import Mock, patch
from datetime import datetime

from services.data_service import CentralizedDataService, MSMEData


class TestCentralizedDataService:
    """Test cases for CentralizedDataService."""
    
    def test_initialization(self):
        """Test service initialization."""
        service = CentralizedDataService()
        assert service._msme_data_cache == {}
        assert service._initialized is False
    
    def test_initialize_data(self):
        """Test data initialization."""
        service = CentralizedDataService()
        service.initialize_data()
        
        assert service._initialized is True
        assert len(service._msme_data_cache) == 20  # Should have 20 MSMEs
        
        # Test idempotency - calling again should not regenerate data
        original_cache = service._msme_data_cache.copy()
        service.initialize_data()
        assert service._msme_data_cache == original_cache
    
    def test_get_all_msmes(self):
        """Test getting all MSMEs."""
        service = CentralizedDataService()
        msmes = service.get_all_msmes()
        
        assert len(msmes) == 20
        assert all(isinstance(msme, MSMEData) for msme in msmes)
        
        # Verify risk distribution (60% green, 25% yellow, 15% red)
        risk_counts = {"green": 0, "yellow": 0, "red": 0}
        for msme in msmes:
            risk_counts[msme.risk_band] += 1
        
        assert risk_counts["green"] == 12  # 60% of 20
        assert risk_counts["yellow"] == 5   # 25% of 20
        assert risk_counts["red"] == 3      # 15% of 20
    
    def test_get_msme_by_id(self):
        """Test getting MSME by ID."""
        service = CentralizedDataService()
        msmes = service.get_all_msmes()
        
        # Test with existing ID
        first_msme = msmes[0]
        found_msme = service.get_msme_by_id(first_msme.msme_id)
        assert found_msme is not None
        assert found_msme.msme_id == first_msme.msme_id
        assert found_msme.name == first_msme.name
        
        # Test with non-existing ID
        not_found = service.get_msme_by_id("non_existing_id")
        assert not_found is None
    
    def test_get_msmes_by_risk_band(self):
        """Test filtering MSMEs by risk band."""
        service = CentralizedDataService()
        
        # Test green risk band
        green_msmes = service.get_msmes_by_risk_band("green")
        assert len(green_msmes) == 12
        assert all(msme.risk_band == "green" for msme in green_msmes)
        
        # Test yellow risk band
        yellow_msmes = service.get_msmes_by_risk_band("yellow")
        assert len(yellow_msmes) == 5
        assert all(msme.risk_band == "yellow" for msme in yellow_msmes)
        
        # Test red risk band
        red_msmes = service.get_msmes_by_risk_band("red")
        assert len(red_msmes) == 3
        assert all(msme.risk_band == "red" for msme in red_msmes)
        
        # Test invalid risk band
        invalid_msmes = service.get_msmes_by_risk_band("invalid")
        assert len(invalid_msmes) == 0
    
    def test_get_msmes_by_business_type(self):
        """Test filtering MSMEs by business type."""
        service = CentralizedDataService()
        all_msmes = service.get_all_msmes()
        
        # Count business types
        business_type_counts = {}
        for msme in all_msmes:
            business_type_counts[msme.business_type] = business_type_counts.get(msme.business_type, 0) + 1
        
        # Test each business type
        for business_type, expected_count in business_type_counts.items():
            filtered_msmes = service.get_msmes_by_business_type(business_type)
            assert len(filtered_msmes) == expected_count
            assert all(msme.business_type == business_type for msme in filtered_msmes)
        
        # Test invalid business type
        invalid_msmes = service.get_msmes_by_business_type("invalid")
        assert len(invalid_msmes) == 0
    
    def test_get_portfolio_analytics(self):
        """Test portfolio analytics generation."""
        service = CentralizedDataService()
        analytics = service.get_portfolio_analytics()
        
        # Verify basic structure
        assert "total_msmes" in analytics
        assert "risk_distribution" in analytics
        assert "business_type_distribution" in analytics
        assert "average_score" in analytics
        assert "average_gst_compliance" in analytics
        assert "average_banking_health" in analytics
        assert "total_signals" in analytics
        assert "triggered_escalations" in analytics
        assert "last_updated" in analytics
        
        # Verify values
        assert analytics["total_msmes"] == 20
        assert analytics["risk_distribution"]["green"] == 12
        assert analytics["risk_distribution"]["yellow"] == 5
        assert analytics["risk_distribution"]["red"] == 3
        
        # Verify averages are reasonable
        assert 0 <= analytics["average_score"] <= 100
        assert 0 <= analytics["average_gst_compliance"] <= 100
        assert 0 <= analytics["average_banking_health"] <= 100
        
        # Verify business type distribution normalization
        business_types = analytics["business_type_distribution"]
        expected_types = ["Retail", "Manufacturing", "Services", "B2B"]
        for business_type in business_types.keys():
            assert business_type in expected_types
        
        # Verify triggered escalations calculation
        assert isinstance(analytics["triggered_escalations"], int)
        assert analytics["triggered_escalations"] >= 0
    
    def test_msme_data_quality(self):
        """Test the quality and consistency of generated MSME data."""
        service = CentralizedDataService()
        msmes = service.get_all_msmes()
        
        for msme in msmes:
            # Test required fields
            assert msme.msme_id is not None and len(msme.msme_id) > 0
            assert msme.name is not None and len(msme.name) > 0
            assert msme.business_type in ["retail", "manufacturing", "services", "b2b"]
            assert msme.location is not None and len(msme.location) > 0
            assert msme.risk_band in ["green", "yellow", "red"]
            
            # Test score ranges
            assert 0 <= msme.score <= 100
            assert 0 <= msme.gst_compliance <= 100
            assert 0 <= msme.banking_health <= 100
            assert 0 <= msme.digital_score <= 100
            assert msme.monthly_turnover >= 0
            
            # Test GST number format
            assert msme.gst_number is not None
            assert len(msme.gst_number) == 15  # Standard GST number length
            
            # Test created_at format
            assert msme.created_at is not None
            # Should be valid ISO format
            datetime.fromisoformat(msme.created_at.replace('Z', '+00:00'))
            
            # Test tags
            assert isinstance(msme.tags, list)
            assert len(msme.tags) > 0
    
    def test_risk_score_consistency(self):
        """Test that risk bands are consistent with scores."""
        service = CentralizedDataService()
        msmes = service.get_all_msmes()
        
        for msme in msmes:
            if msme.risk_band == "green":
                assert msme.score >= 70, f"Green risk MSME {msme.msme_id} has score {msme.score} < 70"
            elif msme.risk_band == "yellow":
                assert 40 <= msme.score < 70, f"Yellow risk MSME {msme.msme_id} has score {msme.score} not in [40, 70)"
            elif msme.risk_band == "red":
                assert msme.score < 40, f"Red risk MSME {msme.msme_id} has score {msme.score} >= 40"
    
    def test_indian_business_data_authenticity(self):
        """Test that generated data reflects authentic Indian business characteristics."""
        service = CentralizedDataService()
        msmes = service.get_all_msmes()
        
        # Check for Indian city names
        indian_cities = [
            "Mumbai", "Delhi", "Bangalore", "Hyderabad", "Chennai", "Kolkata",
            "Pune", "Ahmedabad", "Surat", "Jaipur", "Lucknow", "Kanpur",
            "Nagpur", "Indore", "Bhopal", "Visakhapatnam", "Patna", "Vadodara"
        ]
        
        locations_found = [msme.location for msme in msmes]
        has_indian_cities = any(
            any(city in location for city in indian_cities)
            for location in locations_found
        )
        assert has_indian_cities, "No Indian cities found in MSME locations"
        
        # Check for Hindi/Indian business names
        has_hindi_names = any(
            any(char in msme.name for char in "श्रीगणेशराजेशकुमारअनिलसुनीलविकासप्रकाशदीपकमनीषरमेशसुरेशमहेशनरेशदिनेशजितेंद्रराजेंद्रसुरेंद्रमहेंद्रनरेंद्र")
            for msme in msmes
        )
        assert has_hindi_names, "No Hindi/Indian business names found"
        
        # Check GST number format (should start with state codes)
        valid_state_codes = [
            "01", "02", "03", "04", "05", "06", "07", "08", "09", "10",
            "11", "12", "13", "14", "15", "16", "17", "18", "19", "20",
            "21", "22", "23", "24", "25", "26", "27", "28", "29", "30",
            "31", "32", "33", "34", "35", "36", "37"
        ]
        
        for msme in msmes:
            state_code = msme.gst_number[:2]
            assert state_code in valid_state_codes, f"Invalid GST state code: {state_code}"
    
    def test_concurrent_access(self):
        """Test thread safety of data service."""
        import threading
        import time
        
        service = CentralizedDataService()
        results = []
        errors = []
        
        def access_data():
            try:
                msmes = service.get_all_msmes()
                results.append(len(msmes))
            except Exception as e:
                errors.append(e)
        
        # Create multiple threads accessing data simultaneously
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=access_data)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify no errors and consistent results
        assert len(errors) == 0, f"Errors occurred: {errors}"
        assert len(results) == 10
        assert all(result == 20 for result in results), "Inconsistent results from concurrent access"
