from fastapi import APIRouter

router = APIRouter()

@router.get("/hello")
async def hello_world():
    """Simple hello world endpoint"""
    return {"message": "Hello World"}

@router.get("/portfolio-test")
async def portfolio_test():
    """Test portfolio endpoint"""
    return [
        {
            "msme_id": "test-1",
            "name": "Test MSME",
            "business_type": "retail",
            "location": "Mumbai",
            "current_score": 75,
            "risk_band": "green"
        }
    ]
