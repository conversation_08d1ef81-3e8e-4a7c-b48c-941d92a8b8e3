from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from enum import Enum

class MetricCategory(str, Enum):
    LIQUIDITY = "LIQUIDITY"
    PROFITABILITY = "PROFITABILITY"
    LEVERAGE = "LEVERAGE"
    EFFICIENCY = "EFFICIENCY"
    MARKET = "MARKET"

class MetricTrend(str, Enum):
    IMPROVING = "IMPROVING"
    DECLINING = "DECLINING"
    STABLE = "STABLE"

class RiskLevel(str, Enum):
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

class FinancialMetric(BaseModel):
    """Individual financial metric"""
    metric_id: str = Field(..., description="Metric identifier")
    metric_name: str = Field(..., description="Metric name")
    category: MetricCategory = Field(..., description="Metric category")
    
    # Metric value and calculation
    value: float = Field(..., description="Metric value")
    formula: str = Field(..., description="Calculation formula")
    unit: str = Field(..., description="Unit of measurement")
    
    # Benchmarking
    industry_benchmark: Optional[float] = Field(None, description="Industry benchmark")
    peer_average: Optional[float] = Field(None, description="Peer group average")
    target_value: Optional[float] = Field(None, description="Target value")
    
    # Analysis
    trend: MetricTrend = Field(MetricTrend.STABLE, description="Trend direction")
    risk_level: RiskLevel = Field(RiskLevel.MEDIUM, description="Risk level")
    score: float = Field(0.0, description="Metric score (0-100)")
    
    # Metadata
    calculation_date: date = Field(..., description="Calculation date")
    data_quality: float = Field(100.0, description="Data quality score")
    confidence_level: float = Field(100.0, description="Confidence level")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)

class LiquidityMetrics(BaseModel):
    """Liquidity ratio metrics"""
    msme_id: str = Field(..., description="MSME identifier")
    calculation_date: date = Field(..., description="Calculation date")
    
    # Current Ratio = Current Assets / Current Liabilities
    current_ratio: float = Field(0.0, description="Current ratio")
    current_ratio_score: float = Field(0.0, description="Current ratio score")
    
    # Quick Ratio = (Current Assets - Inventory) / Current Liabilities
    quick_ratio: float = Field(0.0, description="Quick ratio")
    quick_ratio_score: float = Field(0.0, description="Quick ratio score")
    
    # Cash Ratio = Cash and Cash Equivalents / Current Liabilities
    cash_ratio: float = Field(0.0, description="Cash ratio")
    cash_ratio_score: float = Field(0.0, description="Cash ratio score")
    
    # Working Capital = Current Assets - Current Liabilities
    working_capital: float = Field(0.0, description="Working capital")
    working_capital_score: float = Field(0.0, description="Working capital score")
    
    # Operating Cash Flow Ratio = Operating Cash Flow / Current Liabilities
    operating_cash_flow_ratio: float = Field(0.0, description="Operating cash flow ratio")
    operating_cash_flow_ratio_score: float = Field(0.0, description="Operating cash flow ratio score")
    
    # Overall liquidity score
    overall_liquidity_score: float = Field(0.0, description="Overall liquidity score")
    liquidity_risk_level: RiskLevel = Field(RiskLevel.MEDIUM, description="Liquidity risk level")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)

class ProfitabilityMetrics(BaseModel):
    """Profitability ratio metrics"""
    msme_id: str = Field(..., description="MSME identifier")
    calculation_date: date = Field(..., description="Calculation date")
    
    # Gross Profit Margin = (Revenue - COGS) / Revenue
    gross_profit_margin: float = Field(0.0, description="Gross profit margin (%)")
    gross_profit_margin_score: float = Field(0.0, description="Gross profit margin score")
    
    # Net Profit Margin = Net Income / Revenue
    net_profit_margin: float = Field(0.0, description="Net profit margin (%)")
    net_profit_margin_score: float = Field(0.0, description="Net profit margin score")
    
    # Operating Profit Margin = Operating Income / Revenue
    operating_profit_margin: float = Field(0.0, description="Operating profit margin (%)")
    operating_profit_margin_score: float = Field(0.0, description="Operating profit margin score")
    
    # EBITDA Margin = EBITDA / Revenue
    ebitda_margin: float = Field(0.0, description="EBITDA margin (%)")
    ebitda_margin_score: float = Field(0.0, description="EBITDA margin score")
    
    # Return on Assets (ROA) = Net Income / Total Assets
    return_on_assets: float = Field(0.0, description="Return on assets (%)")
    roa_score: float = Field(0.0, description="ROA score")
    
    # Return on Equity (ROE) = Net Income / Shareholders' Equity
    return_on_equity: float = Field(0.0, description="Return on equity (%)")
    roe_score: float = Field(0.0, description="ROE score")
    
    # Return on Investment (ROI) = (Gain - Cost) / Cost
    return_on_investment: float = Field(0.0, description="Return on investment (%)")
    roi_score: float = Field(0.0, description="ROI score")
    
    # Overall profitability score
    overall_profitability_score: float = Field(0.0, description="Overall profitability score")
    profitability_trend: MetricTrend = Field(MetricTrend.STABLE, description="Profitability trend")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)

class LeverageMetrics(BaseModel):
    """Leverage/Debt ratio metrics"""
    msme_id: str = Field(..., description="MSME identifier")
    calculation_date: date = Field(..., description="Calculation date")
    
    # Debt-to-Equity Ratio = Total Debt / Total Equity
    debt_to_equity_ratio: float = Field(0.0, description="Debt-to-equity ratio")
    debt_to_equity_score: float = Field(0.0, description="Debt-to-equity score")
    
    # Debt-to-Assets Ratio = Total Debt / Total Assets
    debt_to_assets_ratio: float = Field(0.0, description="Debt-to-assets ratio")
    debt_to_assets_score: float = Field(0.0, description="Debt-to-assets score")
    
    # Interest Coverage Ratio = EBIT / Interest Expense
    interest_coverage_ratio: float = Field(0.0, description="Interest coverage ratio")
    interest_coverage_score: float = Field(0.0, description="Interest coverage score")
    
    # Debt Service Coverage Ratio (DSCR) = Operating Income / Total Debt Service
    debt_service_coverage_ratio: float = Field(0.0, description="Debt service coverage ratio")
    dscr_score: float = Field(0.0, description="DSCR score")
    
    # Cash Coverage Ratio = (EBIT + Depreciation) / Interest Expense
    cash_coverage_ratio: float = Field(0.0, description="Cash coverage ratio")
    cash_coverage_score: float = Field(0.0, description="Cash coverage score")
    
    # Equity Multiplier = Total Assets / Total Equity
    equity_multiplier: float = Field(0.0, description="Equity multiplier")
    equity_multiplier_score: float = Field(0.0, description="Equity multiplier score")
    
    # Overall leverage score
    overall_leverage_score: float = Field(0.0, description="Overall leverage score")
    leverage_risk_level: RiskLevel = Field(RiskLevel.MEDIUM, description="Leverage risk level")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)

class EfficiencyMetrics(BaseModel):
    """Efficiency/Activity ratio metrics"""
    msme_id: str = Field(..., description="MSME identifier")
    calculation_date: date = Field(..., description="Calculation date")
    
    # Asset Turnover = Revenue / Average Total Assets
    asset_turnover: float = Field(0.0, description="Asset turnover ratio")
    asset_turnover_score: float = Field(0.0, description="Asset turnover score")
    
    # Inventory Turnover = COGS / Average Inventory
    inventory_turnover: float = Field(0.0, description="Inventory turnover ratio")
    inventory_turnover_score: float = Field(0.0, description="Inventory turnover score")
    
    # Receivables Turnover = Revenue / Average Accounts Receivable
    receivables_turnover: float = Field(0.0, description="Receivables turnover ratio")
    receivables_turnover_score: float = Field(0.0, description="Receivables turnover score")
    
    # Payables Turnover = COGS / Average Accounts Payable
    payables_turnover: float = Field(0.0, description="Payables turnover ratio")
    payables_turnover_score: float = Field(0.0, description="Payables turnover score")
    
    # Working Capital Turnover = Revenue / Average Working Capital
    working_capital_turnover: float = Field(0.0, description="Working capital turnover ratio")
    working_capital_turnover_score: float = Field(0.0, description="Working capital turnover score")
    
    # Days Sales Outstanding (DSO) = (Accounts Receivable / Revenue) * 365
    days_sales_outstanding: float = Field(0.0, description="Days sales outstanding")
    dso_score: float = Field(0.0, description="DSO score")
    
    # Days Inventory Outstanding (DIO) = (Inventory / COGS) * 365
    days_inventory_outstanding: float = Field(0.0, description="Days inventory outstanding")
    dio_score: float = Field(0.0, description="DIO score")
    
    # Days Payable Outstanding (DPO) = (Accounts Payable / COGS) * 365
    days_payable_outstanding: float = Field(0.0, description="Days payable outstanding")
    dpo_score: float = Field(0.0, description="DPO score")
    
    # Cash Conversion Cycle = DSO + DIO - DPO
    cash_conversion_cycle: float = Field(0.0, description="Cash conversion cycle (days)")
    cash_conversion_cycle_score: float = Field(0.0, description="Cash conversion cycle score")
    
    # Overall efficiency score
    overall_efficiency_score: float = Field(0.0, description="Overall efficiency score")
    efficiency_trend: MetricTrend = Field(MetricTrend.STABLE, description="Efficiency trend")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)

class ComprehensiveFinancialMetrics(BaseModel):
    """Comprehensive financial metrics dashboard"""
    msme_id: str = Field(..., description="MSME identifier")
    calculation_date: date = Field(..., description="Calculation date")
    
    # Component metrics
    liquidity_metrics: LiquidityMetrics
    profitability_metrics: ProfitabilityMetrics
    leverage_metrics: LeverageMetrics
    efficiency_metrics: EfficiencyMetrics
    
    # Overall scores
    overall_financial_score: float = Field(0.0, description="Overall financial health score")
    financial_grade: str = Field("C", description="Financial grade (A-F)")
    
    # Risk assessment
    overall_risk_level: RiskLevel = Field(RiskLevel.MEDIUM, description="Overall risk level")
    risk_factors: List[str] = Field(default_factory=list, description="Key risk factors")
    
    # Trend analysis
    financial_trend: MetricTrend = Field(MetricTrend.STABLE, description="Overall financial trend")
    trend_analysis: Dict[str, Any] = Field(default_factory=dict, description="Detailed trend analysis")
    
    # Benchmarking
    industry_percentile: float = Field(50.0, description="Industry percentile ranking")
    peer_comparison: Dict[str, Any] = Field(default_factory=dict, description="Peer comparison data")
    
    # Recommendations
    improvement_areas: List[str] = Field(default_factory=list, description="Areas for improvement")
    action_items: List[str] = Field(default_factory=list, description="Recommended actions")
    
    # Data quality
    data_completeness: float = Field(0.0, description="Data completeness percentage")
    calculation_confidence: float = Field(0.0, description="Calculation confidence level")
    
    last_updated: datetime = Field(default_factory=datetime.utcnow)

class FinancialMetricsRequest(BaseModel):
    """Request model for financial metrics calculation"""
    msme_id: str = Field(..., description="MSME identifier")
    calculation_date: Optional[date] = Field(None, description="Calculation date")
    include_benchmarking: bool = Field(True, description="Include industry benchmarking")
    include_trends: bool = Field(True, description="Include trend analysis")
    include_recommendations: bool = Field(True, description="Include recommendations")

class FinancialMetricsResponse(BaseModel):
    """Response model for financial metrics"""
    msme_id: str
    
    comprehensive_metrics: Optional[ComprehensiveFinancialMetrics] = None
    individual_metrics: List[FinancialMetric] = Field(default_factory=list)
    
    data_freshness: datetime = Field(default_factory=datetime.utcnow)
    source: str = Field("FINANCIAL_METRICS_ENGINE", description="Data source")
    status: str = Field("success", description="Response status")
    message: Optional[str] = None
