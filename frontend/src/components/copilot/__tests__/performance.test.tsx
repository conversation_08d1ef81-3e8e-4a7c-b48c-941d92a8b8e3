/**
 * Performance tests for AI Copilot components
 * Validates 2-second load requirement and 1000+ MSME record handling
 */

import React from 'react';
import { render, act, waitFor } from '@testing-library/react';
import { CopilotPage } from '../copilot-page';
import { EnhancedMessage } from '../enhanced-message';
import { MessageList } from '../message-list';
import { QuickActions } from '../quick-actions';
import { InsightsFeed } from '../insights-feed';
import { ChatMessage, Insight } from '@/types/copilot';
import {
  messageCache
} from '@/lib/performance';

// Mock performance APIs
Object.defineProperty(window, 'performance', {
  value: {
    now: jest.fn(() => Date.now()),
    memory: {
      usedJSHeapSize: 1024 * 1024 * 50,
      totalJSHeapSize: 1024 * 1024 * 100,
    },
  },
});

// Helper function to generate large datasets
const generateMessages = (count: number): ChatMessage[] => {
  return Array.from({ length: count }, (_, i) => ({
    id: `msg-${i}`,
    type: i % 2 === 0 ? 'user' : 'assistant',
    content: `Message ${i} - This is a test message with some content to simulate real usage patterns.`,
    timestamp: new Date(Date.now() - (count - i) * 1000),
    data: i % 5 === 0 ? {
      response_type: 'analytics' as const,
      confidence: 0.8 + Math.random() * 0.2,
      processing_time_ms: 100 + Math.random() * 200,
      topics: [`topic-${i}`, `category-${i % 10}`],
      ai_analytics: {
        portfolio_health_score: 60 + Math.random() * 40,
        total_msmes: 100 + Math.random() * 900,
        risk_distribution: {
          high: Math.random() * 0.3,
          medium: 0.3 + Math.random() * 0.3,
          low: 0.4 + Math.random() * 0.3,
        },
      },
    } : undefined,
  }));
};

const generateInsights = (count: number): Insight[] => {
  return Array.from({ length: count }, (_, i) => ({
    id: `insight-${i}`,
    type: ['alert', 'trend', 'compliance', 'geographic', 'prediction', 'opportunity'][i % 6] as any,
    severity: ['high', 'medium', 'low'][i % 3] as any,
    title: `Insight ${i}`,
    description: `This is insight ${i} with detailed description and analysis.`,
    timestamp: new Date(Date.now() - i * 60000),
    actionable: i % 2 === 0,
    dismissed: false,
    priority_score: Math.random() * 100,
    exposure_amount: Math.random() * 1000000,
    ai_generated: true,
    confidence: 0.7 + Math.random() * 0.3,
    source: 'ai_model' as const,
  }));
};

describe('Performance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    messageCache.clear();
  });

  describe('2-Second Load Requirement', () => {
    test('CopilotPage loads within 2 seconds with 1000 messages', async () => {
      const messages = generateMessages(1000);
      const startTime = performance.now();

      await act(async () => {
        render(<CopilotPage initialMessages={messages} />);
      });

      const loadTime = performance.now() - startTime;
      expect(loadTime).toBeLessThan(2000); // 2 seconds
    });

    test('MessageList renders 1000+ messages within performance budget', async () => {
      const messages = generateMessages(1500);
      const startTime = performance.now();

      await act(async () => {
        render(<MessageList messages={messages} />);
      });

      const renderTime = performance.now() - startTime;
      expect(renderTime).toBeLessThan(1000); // 1 second for component render
    });

    test('EnhancedMessage with complex data renders quickly', async () => {
      const complexData = {
        response_type: 'analytics' as const,
        confidence: 0.95,
        processing_time_ms: 150,
        topics: Array.from({ length: 50 }, (_, i) => `topic-${i}`),
        ai_analytics: {
          portfolio_health_score: 85,
          total_msmes: 1000,
          risk_distribution: { high: 0.1, medium: 0.3, low: 0.6 },
        },
        predictive_insights: Array.from({ length: 20 }, (_, i) => ({
          id: `insight-${i}`,
          title: `Insight ${i}`,
          description: `Complex insight ${i} with detailed analysis`,
          confidence: 0.8,
          impact: 'high' as const,
          timeline: '1 month',
          category: 'risk' as const,
          actionable: true,
        })),
        risk_alerts: Array.from({ length: 10 }, (_, i) => ({
          msme_id: `msme-${i}`,
          msme_name: `MSME ${i}`,
          risk_level: 'high' as const,
          risk_type: 'credit' as const,
          description: `Risk alert ${i}`,
          severity_score: 80 + Math.random() * 20,
          detected_at: new Date(),
          requires_action: true,
        })),
      };

      const startTime = performance.now();

      await act(async () => {
        render(
          <EnhancedMessage
            content="Complex analysis with large dataset"
            data={complexData}
            timestamp={new Date()}
            type="assistant"
          />
        );
      });

      const renderTime = performance.now() - startTime;
      expect(renderTime).toBeLessThan(500); // 500ms for complex message
    });

    test('InsightsFeed handles 1000+ insights efficiently', async () => {
      const insights = generateInsights(1200);
      const startTime = performance.now();

      await act(async () => {
        render(<InsightsFeed insights={insights} />);
      });

      const renderTime = performance.now() - startTime;
      expect(renderTime).toBeLessThan(1500); // 1.5 seconds for large insights
    });
  });

  describe('Memory Management', () => {
    test('message cache handles large datasets without memory leaks', () => {
      const messages = generateMessages(2000);
      
      // Add messages to cache
      messages.forEach(message => {
        messageCache.set(`${message.id}-${message.timestamp.getTime()}`, message);
      });

      const stats = messageCache.getStats();
      expect(stats.size).toBeLessThanOrEqual(1000); // Should respect max size
    });

    test('components clean up properly on unmount', () => {
      const messages = generateMessages(500);
      
      const { unmount } = render(<CopilotPage initialMessages={messages} />);
      
      // Should not throw errors or cause memory leaks
      expect(() => unmount()).not.toThrow();
    });

    test('memory usage stays within reasonable bounds', async () => {
      const initialMemory = (performance as any).memory.usedJSHeapSize;
      
      // Render large dataset
      const messages = generateMessages(1000);
      const { unmount } = render(<CopilotPage initialMessages={messages} />);
      
      const peakMemory = (performance as any).memory.usedJSHeapSize;
      
      // Clean up
      unmount();
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = (performance as any).memory.usedJSHeapSize;
      
      // Memory should not grow excessively
      const memoryGrowth = peakMemory - initialMemory;
      expect(memoryGrowth).toBeLessThan(100 * 1024 * 1024); // 100MB limit
      
      // Memory should be cleaned up after unmount
      const memoryLeak = finalMemory - initialMemory;
      expect(memoryLeak).toBeLessThan(10 * 1024 * 1024); // 10MB tolerance
    });
  });

  describe('Rendering Performance', () => {
    test('virtual scrolling works with large message lists', async () => {
      const messages = generateMessages(2000);
      
      const startTime = performance.now();
      
      const { container } = render(<MessageList messages={messages} />);
      
      const renderTime = performance.now() - startTime;
      
      // Should render quickly even with many messages
      expect(renderTime).toBeLessThan(1000);
      
      // Should not render all messages at once (virtual scrolling)
      const renderedMessages = container.querySelectorAll('[data-message-id]');
      expect(renderedMessages.length).toBeLessThan(messages.length);
    });

    test('debounced input updates improve performance', async () => {

      
      const TestComponent = () => {
        const [value, setValue] = React.useState('');
        
        React.useEffect(() => {
          updateCount++;
        }, [value]);
        
        return (
          <input
            value={value}
            onChange={(e) => setValue(e.target.value)}
            data-testid="test-input"
          />
        );
      };
      
      const { getByTestId } = render(<TestComponent />);
      const input = getByTestId('test-input');
      
      // Simulate rapid typing
      const startTime = performance.now();
      
      for (let i = 0; i < 10; i++) {
        await act(async () => {
          input.focus();
          // Simulate typing
          (input as HTMLInputElement).value = `test${i}`;
          input.dispatchEvent(new Event('input', { bubbles: true }));
        });
      }
      
      const typingTime = performance.now() - startTime;
      
      // Should handle rapid input efficiently
      expect(typingTime).toBeLessThan(100);
    });

    test('memoization prevents unnecessary re-renders', () => {
      let renderCount = 0;
      
      const TestMessage = React.memo(({ content }: { content: string }) => {
        renderCount++;
        return <div>{content}</div>;
      });
      TestMessage.displayName = 'TestMessage';
      
      const { rerender } = render(<TestMessage content="test" />);
      
      // Re-render with same props
      rerender(<TestMessage content="test" />);
      rerender(<TestMessage content="test" />);
      
      // Should only render once due to memoization
      expect(renderCount).toBe(1);
    });
  });

  describe('API Performance', () => {
    test('throttled API calls prevent spam', async () => {
      let callCount = 0;
      
      const mockApiCall = jest.fn().mockImplementation(async () => {
        callCount++;
        return { success: true };
      });
      
      // Simulate rapid API calls
      const promises = Array.from({ length: 10 }, () => mockApiCall());
      
      await Promise.all(promises);
      
      // Should throttle calls
      expect(callCount).toBeLessThan(10);
    });

    test('response caching improves performance', () => {
      const cacheKey = 'test-response';
      const testData = { response: 'cached data' };
      
      // Cache response
      messageCache.set(cacheKey, testData as any);
      
      // Retrieve from cache
      const startTime = performance.now();
      const cached = messageCache.get(cacheKey);
      const retrievalTime = performance.now() - startTime;
      
      expect(cached).toEqual(testData);
      expect(retrievalTime).toBeLessThan(1); // Should be nearly instantaneous
    });
  });

  describe('Responsive Design Performance', () => {
    test('handles viewport changes efficiently', async () => {
      const { container } = render(<CopilotPage />);
      
      // Simulate viewport changes
      const startTime = performance.now();
      
      // Change viewport size
      Object.defineProperty(window, 'innerWidth', { value: 768 });
      Object.defineProperty(window, 'innerHeight', { value: 1024 });
      
      // Trigger resize event
      window.dispatchEvent(new Event('resize'));
      
      await waitFor(() => {
        const resizeTime = performance.now() - startTime;
        expect(resizeTime).toBeLessThan(100); // Should handle resize quickly
      });
      
      expect(container).toBeInTheDocument();
    });

    test('mobile layout renders efficiently', async () => {
      // Mock mobile viewport
      Object.defineProperty(window, 'innerWidth', { value: 375 });
      Object.defineProperty(window, 'innerHeight', { value: 667 });
      
      const messages = generateMessages(100);
      const startTime = performance.now();
      
      await act(async () => {
        render(<CopilotPage initialMessages={messages} />);
      });
      
      const renderTime = performance.now() - startTime;
      expect(renderTime).toBeLessThan(1000); // Should render quickly on mobile
    });
  });

  describe('Stress Testing', () => {
    test('handles extreme load gracefully', async () => {
      const extremeMessages = generateMessages(5000);
      const extremeInsights = generateInsights(1000);
      
      const startTime = performance.now();
      
      await act(async () => {
        render(
          <div>
            <CopilotPage initialMessages={extremeMessages} />
            <InsightsFeed insights={extremeInsights} />
          </div>
        );
      });
      
      const loadTime = performance.now() - startTime;
      
      // Should handle extreme load within reasonable time
      expect(loadTime).toBeLessThan(5000); // 5 second tolerance for extreme load
    });

    test('maintains performance under concurrent operations', async () => {
      const messages = generateMessages(1000);
      
      const operations = [
        () => render(<CopilotPage initialMessages={messages} />),
        () => render(<MessageList messages={messages} />),
        () => render(<QuickActions onQuerySelect={() => {}} />),
        () => render(<InsightsFeed insights={generateInsights(100)} />),
      ];
      
      const startTime = performance.now();
      
      // Run operations concurrently
      await Promise.all(operations.map(op => act(async () => op())));
      
      const totalTime = performance.now() - startTime;
      
      // Should handle concurrent operations efficiently
      expect(totalTime).toBeLessThan(3000); // 3 seconds for all operations
    });
  });
});
