#!/usr/bin/env node

/**
 * Cleanup script to remove unused imports and variables from TypeScript/JavaScript files
 * This script processes the ESLint output and automatically fixes common issues
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Files to clean up based on ESLint warnings
const filesToClean = [
  'src/components/dashboard/analytics.tsx',
  'src/components/msme/data-source-overview.tsx',
  'src/components/msme/msme-overview-cards.tsx',
  'src/components/msme/tabs/banking-data-tab.tsx',
  'src/components/msme/tabs/cash-flow-tab.tsx',
  'src/components/msme/tabs/enhanced-data-sources-tab.tsx',
  'src/components/msme/tabs/enhanced-score-analysis-tab.tsx',
  'src/components/msme/tabs/forecasts-tab.tsx',
  'src/components/msme/tabs/unified-signal-history-tab.tsx',
  'src/components/notifications/notification-panel.tsx',
  'src/components/portfolio/optimized-portfolio-dashboard.tsx',
  'src/components/portfolio/portfolio-list.tsx',
  'src/components/reports/reports-analytics.tsx',
  'src/components/reports/reports-dashboard.tsx',
  'src/components/risk/advanced-risk-monitor.tsx',
  'src/components/risk/risk-monitor.tsx',
  'src/components/risk-monitor/RiskMonitorDashboard.tsx',
  'src/components/risk-monitor/SpecialMentionAccounts.tsx',
  'src/components/security/security-monitor.tsx',
  'src/components/shared/data-fetcher.tsx',
  'src/components/trends/trends-analysis.tsx'
];

// Common unused imports to remove
const commonUnusedImports = {
  'DollarSign': true,
  'ArrowUpRight': true,
  'ArrowDownRight': true,
  'CheckCircle': true,
  'Shield': true,
  'Building': true,
  'Target': true,
  'Eye': true,
  'Activity': true,
  'AlertTriangle': true,
  'CreditCard': true,
  'Building2': true,
  'TrendingDown': true,
  'Settings': true,
  'Mail': true,
  'Calendar': true,
  'MapPin': true,
  'Users': true,
  'Filter': true,
  'FileText': true,
  'RefreshCw': true,
  'ScrollArea': true,
  'Tabs': true,
  'TabsContent': true,
  'TabsList': true,
  'TabsTrigger': true,
  'Select': true,
  'SelectContent': true,
  'SelectItem': true,
  'SelectTrigger': true,
  'SelectValue': true,
  'Skeleton': true,
  'Tooltip': true,
  'TooltipContent': true,
  'TooltipTrigger': true,
  'Button': true,
  'Badge': true,
  'CardDescription': true,
  'CardHeader': true,
  'CardTitle': true,
  'Search': true,
  'useEffect': true,
  'useMemo': true,
  'Bell': true,
  'Download': true,
  'Zap': true,
  'Database': true,
  'Server': true,
  'Wifi': true,
  'XCircle': true,
  'Separator': true
};

function cleanupFile(filePath) {
  console.log(`Cleaning up: ${filePath}`);
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    let lines = content.split('\n');
    let modified = false;
    
    // Remove unused import lines
    lines = lines.filter(line => {
      const trimmed = line.trim();
      
      // Skip empty lines and non-import lines
      if (!trimmed.startsWith('import') && !trimmed.includes('from \'lucide-react\'')) {
        return true;
      }
      
      // Check if line contains only unused imports
      const hasUsedImport = Object.keys(commonUnusedImports).some(unusedImport => {
        return trimmed.includes(unusedImport) && !isImportUsed(content, unusedImport);
      });
      
      if (hasUsedImport) {
        modified = true;
        return false;
      }
      
      return true;
    });
    
    if (modified) {
      fs.writeFileSync(filePath, lines.join('\n'));
      console.log(`✓ Cleaned up ${filePath}`);
    } else {
      console.log(`- No changes needed for ${filePath}`);
    }
    
  } catch (error) {
    console.error(`Error cleaning ${filePath}:`, error.message);
  }
}

function isImportUsed(content, importName) {
  // Simple check if import is used in the file
  const regex = new RegExp(`\\b${importName}\\b`, 'g');
  const matches = content.match(regex);
  return matches && matches.length > 1; // More than just the import declaration
}

function main() {
  console.log('🧹 Starting cleanup of unused imports...\n');
  
  // Change to frontend directory
  process.chdir(path.join(__dirname, '..', 'frontend'));
  
  filesToClean.forEach(cleanupFile);
  
  console.log('\n✅ Cleanup completed!');
  console.log('\nRunning ESLint to verify improvements...');
  
  try {
    execSync('npm run lint', { stdio: 'inherit' });
  } catch (error) {
    console.log('ESLint completed with warnings (this is expected)');
  }
}

if (require.main === module) {
  main();
}

module.exports = { cleanupFile, isImportUsed };
