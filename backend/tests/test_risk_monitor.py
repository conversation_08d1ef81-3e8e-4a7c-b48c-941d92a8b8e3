"""
Comprehensive test suite for Credit Chakra Risk Monitor functionality.

This module provides comprehensive testing for the enhanced risk monitoring
system including SMA classification, DPD calculation, real-time risk events,
and RBI compliance features.

Author: Credit Chakra Team
Version: 1.0.0
"""

import pytest
import asyncio
from datetime import datetime, date, timedelta
from unittest.mock import Mock, AsyncMock, patch
from fastapi.testclient import TestClient

from main import app
from models.risk_monitoring import (
    SMAClassification, SMAProgressionData, RiskEvent, RiskEventType,
    SeverityLevel, DPDCalculationResult, AuditAction,
    calculate_days_past_due, get_sma_classification_from_dpd,
    calculate_provision_amount, generate_early_warning_signals
)
from services.realtime_risk_monitor import realtime_monitor


class TestSMAClassification:
    """Test SMA classification and DPD calculation functionality."""
    
    def test_dpd_calculation_standard(self):
        """Test DPD calculation for standard accounts."""
        due_date = date.today() - timedelta(days=0)
        result = calculate_days_past_due(due_date=due_date)
        
        assert result.days_past_due == 0
        assert result.sma_classification == SMAClassification.STANDARD
        assert result.calculation_valid is True
        assert result.requires_provision is False
        assert result.rbi_reporting_required is False
    
    def test_dpd_calculation_sma_0(self):
        """Test DPD calculation for SMA-0 classification."""
        due_date = date.today() - timedelta(days=15)
        result = calculate_days_past_due(due_date=due_date)
        
        assert result.days_past_due == 15
        assert result.sma_classification == SMAClassification.SMA_0
        assert result.calculation_valid is True
        assert result.requires_provision is False
        assert result.rbi_reporting_required is True
    
    def test_dpd_calculation_sma_1(self):
        """Test DPD calculation for SMA-1 classification."""
        due_date = date.today() - timedelta(days=45)
        result = calculate_days_past_due(due_date=due_date)
        
        assert result.days_past_due == 45
        assert result.sma_classification == SMAClassification.SMA_1
        assert result.calculation_valid is True
        assert result.requires_provision is True
        assert result.rbi_reporting_required is True
    
    def test_dpd_calculation_sma_2(self):
        """Test DPD calculation for SMA-2 classification."""
        due_date = date.today() - timedelta(days=75)
        result = calculate_days_past_due(due_date=due_date)
        
        assert result.days_past_due == 75
        assert result.sma_classification == SMAClassification.SMA_2
        assert result.calculation_valid is True
        assert result.requires_provision is True
        assert result.rbi_reporting_required is True
    
    def test_dpd_calculation_npa(self):
        """Test DPD calculation for NPA classification."""
        due_date = date.today() - timedelta(days=95)
        result = calculate_days_past_due(due_date=due_date)
        
        assert result.days_past_due == 95
        assert result.sma_classification == SMAClassification.NPA
        assert result.calculation_valid is True
        assert result.requires_provision is True
        assert result.rbi_reporting_required is True
    
    def test_dpd_calculation_with_grace_period(self):
        """Test DPD calculation with grace period."""
        due_date = date.today() - timedelta(days=10)
        result = calculate_days_past_due(due_date=due_date, grace_period_days=5)
        
        assert result.days_past_due == 5
        assert result.sma_classification == SMAClassification.SMA_0
        assert "Grace period of 5 days applied" in result.business_rules_applied
    
    def test_sma_classification_from_dpd(self):
        """Test SMA classification utility function."""
        assert get_sma_classification_from_dpd(0) == SMAClassification.STANDARD
        assert get_sma_classification_from_dpd(15) == SMAClassification.SMA_0
        assert get_sma_classification_from_dpd(45) == SMAClassification.SMA_1
        assert get_sma_classification_from_dpd(75) == SMAClassification.SMA_2
        assert get_sma_classification_from_dpd(95) == SMAClassification.NPA


class TestProvisionCalculation:
    """Test provision calculation functionality."""
    
    def test_provision_calculation_standard(self):
        """Test provision calculation for standard accounts."""
        amount = calculate_provision_amount(1000000, SMAClassification.STANDARD)
        assert amount == 0.0
    
    def test_provision_calculation_sma_0(self):
        """Test provision calculation for SMA-0 accounts."""
        amount = calculate_provision_amount(1000000, SMAClassification.SMA_0)
        assert amount == 2500.0  # 0.25%
    
    def test_provision_calculation_sma_1(self):
        """Test provision calculation for SMA-1 accounts."""
        amount = calculate_provision_amount(1000000, SMAClassification.SMA_1)
        assert amount == 5000.0  # 0.5%
    
    def test_provision_calculation_sma_2(self):
        """Test provision calculation for SMA-2 accounts."""
        amount = calculate_provision_amount(1000000, SMAClassification.SMA_2)
        assert amount == 10000.0  # 1%
    
    def test_provision_calculation_npa(self):
        """Test provision calculation for NPA accounts."""
        amount = calculate_provision_amount(1000000, SMAClassification.NPA)
        assert amount == 150000.0  # 15%


class TestSMAProgressionData:
    """Test SMA progression data model functionality."""
    
    def test_sma_progression_creation(self):
        """Test SMA progression data creation."""
        sma_data = SMAProgressionData(
            msme_id="test_001",
            msme_name="Test MSME",
            business_type="retail",
            location="Mumbai",
            current_classification=SMAClassification.SMA_1,
            days_past_due=45,
            outstanding_amount=1000000,
            credit_limit=1200000,
            next_review_date=date.today() + timedelta(days=7)
        )
        
        assert sma_data.msme_id == "test_001"
        assert sma_data.current_classification == SMAClassification.SMA_1
        assert sma_data.utilization_ratio == 1000000 / 1200000
        assert sma_data.severity_score > 0
    
    def test_severity_score_calculation(self):
        """Test severity score calculation."""
        sma_data = SMAProgressionData(
            msme_id="test_001",
            msme_name="Test MSME",
            business_type="retail",
            location="Mumbai",
            current_classification=SMAClassification.SMA_2,
            days_past_due=75,
            outstanding_amount=********,  # 1.5Cr
            credit_limit=********,
            next_review_date=date.today() + timedelta(days=7),
            stress_indicators=["GST delays", "Banking issues"]
        )
        
        severity = sma_data.severity_score
        assert severity > 50  # Should be high due to SMA-2, high exposure, and stress indicators
        assert severity <= 100
    
    def test_audit_trail_entry(self):
        """Test audit trail entry addition."""
        sma_data = SMAProgressionData(
            msme_id="test_001",
            msme_name="Test MSME",
            business_type="retail",
            location="Mumbai",
            current_classification=SMAClassification.SMA_1,
            days_past_due=45,
            outstanding_amount=1000000,
            credit_limit=1200000,
            next_review_date=date.today() + timedelta(days=7)
        )
        
        sma_data.add_audit_entry(
            action=AuditAction.CLASSIFICATION_CHANGED,
            description="Classification changed from SMA-0 to SMA-1",
            performed_by="system_monitor"
        )
        
        assert len(sma_data.audit_trail) == 1
        assert sma_data.audit_trail[0].action == AuditAction.CLASSIFICATION_CHANGED
        assert sma_data.audit_trail[0].regulatory_impact is True


class TestEarlyWarningSignals:
    """Test early warning signal generation."""
    
    def test_early_warning_signals_generation(self):
        """Test early warning signals generation."""
        sma_data = SMAProgressionData(
            msme_id="test_001",
            msme_name="Test MSME",
            business_type="retail",
            location="Mumbai",
            current_classification=SMAClassification.SMA_1,
            days_past_due=45,
            outstanding_amount=1800000,
            credit_limit=2000000,  # 90% utilization
            next_review_date=date.today() + timedelta(days=7),
            risk_score=35.0,  # Low score
            dpd_trend_7d=5.0  # Increasing trend
        )
        
        signals = generate_early_warning_signals(sma_data)
        
        assert len(signals) > 0
        assert any("Payment overdue" in signal for signal in signals)
        assert any("High credit utilization" in signal for signal in signals)
        assert any("Low credit score" in signal for signal in signals)
        assert any("Increasing DPD trend" in signal for signal in signals)


class TestRealTimeRiskMonitor:
    """Test real-time risk monitoring functionality."""
    
    @pytest.mark.asyncio
    async def test_sma_classification_service(self):
        """Test SMA classification service."""
        due_date = date.today() - timedelta(days=45)
        outstanding_amount = 1000000.0
        
        sma_data = await realtime_monitor.classify_msme_sma_status(
            msme_id="test_001",
            due_date=due_date,
            outstanding_amount=outstanding_amount
        )
        
        assert sma_data.msme_id == "test_001"
        assert sma_data.current_classification == SMAClassification.SMA_1
        assert sma_data.days_past_due == 45
        assert sma_data.outstanding_amount == outstanding_amount
        assert sma_data.provision_required > 0
    
    @pytest.mark.asyncio
    async def test_sma_heatmap_generation(self):
        """Test SMA heatmap data generation."""
        heatmap_data = await realtime_monitor.get_sma_heatmap_data()
        
        assert 'sma_distribution' in heatmap_data
        assert 'business_type_sma' in heatmap_data
        assert 'geographic_sma' in heatmap_data
        assert 'exposure_by_sma' in heatmap_data
        assert heatmap_data['total_exposure'] > 0
        assert 0 <= heatmap_data['npa_ratio'] <= 1
        assert 0 <= heatmap_data['sma_ratio'] <= 1
    
    @pytest.mark.asyncio
    async def test_portfolio_sma_summary(self):
        """Test portfolio SMA summary generation."""
        summary = await realtime_monitor.get_portfolio_sma_summary()
        
        assert 'total_msmes' in summary
        assert 'sma_accounts' in summary
        assert 'npa_accounts' in summary
        assert 'provision_required' in summary
        assert 'regulatory_alerts' in summary
        assert summary['total_msmes'] > 0
    
    @pytest.mark.asyncio
    async def test_sma_progression_monitoring(self):
        """Test SMA progression monitoring."""
        alerts = await realtime_monitor.monitor_sma_progression()
        
        assert isinstance(alerts, list)
        for alert in alerts:
            assert hasattr(alert, 'alert_id')
            assert hasattr(alert, 'msme_id')
            assert hasattr(alert, 'severity')
            assert alert.severity in ['low', 'medium', 'high', 'critical']
    
    @pytest.mark.asyncio
    async def test_risk_events_generation(self):
        """Test risk events generation."""
        events = await realtime_monitor.generate_risk_events()
        
        assert isinstance(events, list)
        assert len(events) > 0
        
        for event in events:
            assert hasattr(event, 'event_id')
            assert hasattr(event, 'event_type')
            assert hasattr(event, 'severity')
            assert hasattr(event, 'impact_score')
            assert 0 <= event.impact_score <= 100


class TestAPIEndpoints:
    """Test Risk Monitor API endpoints."""
    
    def setup_method(self):
        """Set up test client."""
        self.client = TestClient(app)
    
    def test_sma_heatmap_endpoint(self):
        """Test SMA heatmap API endpoint."""
        response = self.client.get("/dashboard/risk-monitor/sma-heatmap")
        if response.status_code != 200:
            print(f"Error response: {response.text}")
        assert response.status_code == 200

        data = response.json()
        assert data['status'] == 'success'
        assert 'data' in data
        assert 'timestamp' in data
    
    def test_portfolio_summary_endpoint(self):
        """Test portfolio SMA summary API endpoint."""
        response = self.client.get("/dashboard/risk-monitor/portfolio-summary")
        assert response.status_code == 200

        data = response.json()
        assert data['status'] == 'success'
        assert 'data' in data
        assert 'total_msmes' in data['data']

    def test_sma_progression_endpoint(self):
        """Test SMA progression alerts API endpoint."""
        response = self.client.get("/dashboard/risk-monitor/sma-progression")
        assert response.status_code == 200

        data = response.json()
        assert isinstance(data, list)

    def test_real_time_events_endpoint(self):
        """Test real-time risk events API endpoint."""
        response = self.client.get("/dashboard/risk-monitor/real-time-events")
        assert response.status_code == 200

        data = response.json()
        assert isinstance(data, list)

    def test_portfolio_metrics_endpoint(self):
        """Test portfolio risk metrics API endpoint."""
        response = self.client.get("/dashboard/risk-monitor/portfolio-metrics")
        assert response.status_code == 200

        data = response.json()
        assert data['status'] == 'success'
        assert 'data' in data
    
    def test_classify_sma_endpoint(self):
        """Test SMA classification API endpoint."""
        due_date = (date.today() - timedelta(days=45)).strftime("%Y-%m-%d")
        outstanding_amount = 1000000

        response = self.client.post(
            f"/dashboard/risk-monitor/classify-sma/test_001?due_date={due_date}&outstanding_amount={outstanding_amount}"
        )
        assert response.status_code == 200

        data = response.json()
        assert data['status'] == 'success'
        assert 'data' in data

    def test_classify_sma_invalid_date(self):
        """Test SMA classification with invalid date format."""
        response = self.client.post(
            "/dashboard/risk-monitor/classify-sma/test_001?due_date=invalid-date&outstanding_amount=1000000"
        )
        assert response.status_code == 400


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
