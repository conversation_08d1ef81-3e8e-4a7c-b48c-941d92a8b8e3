"""
Centralized data service for Credit Chakra application.

This module provides a centralized service for managing realistic Indian MSME data,
ensuring consistency across all components and eliminating data duplication.
It supports the portfolio dashboard, analytics, and risk monitoring features.

Author: Credit Chakra Team
Version: 1.0.0
"""
from __future__ import annotations

import random
import uuid
from dataclasses import dataclass
from datetime import datetime, timedelta, UTC
from typing import Any, Dict, List, Optional

@dataclass
class MSMEData:
    """
    Standardized MSME data structure for consistent data representation.

    This dataclass represents a Micro, Small, and Medium Enterprise (MSME)
    with all the essential fields required for credit assessment and risk monitoring.

    Attributes:
        msme_id: Unique identifier for the MSME
        name: Business name (supports Hindi/English)
        business_type: Type of business (retail, manufacturing, services, b2b)
        location: Business location (city, state format)
        score: Credit score on 100-point scale (0-100)
        risk_band: Risk classification (green, yellow, red)
        gst_compliance: GST compliance score (0-100)
        banking_health: Banking relationship health score (0-100)
        monthly_turnover: Monthly GST turnover in INR
        digital_score: Digital payment adoption score (0-100)
        gst_number: GST registration number
        created_at: ISO format timestamp of creation
        tags: List of business tags/categories
        # RBI Compliance fields
        sma_classification: str  # standard, sma_0, sma_1, sma_2, npa
        days_past_due: int  # Days past due for SMA classification
        outstanding_amount: float  # Outstanding loan amount
        last_payment_date: str  # Last payment date in ISO format
        provision_required: float  # Provision amount required as per RBI norms
    """
    msme_id: str
    name: str
    business_type: str
    location: str
    score: float
    risk_band: str
    gst_compliance: int
    banking_health: int
    monthly_turnover: int
    digital_score: int
    gst_number: str
    created_at: str
    tags: List[str]
    # RBI Compliance fields
    sma_classification: str = "standard"
    days_past_due: int = 0
    outstanding_amount: float = 0.0
    last_payment_date: str = ""
    provision_required: float = 0.0

class CentralizedDataService:
    """
    Centralized service for managing realistic Indian MSME data.

    This service ensures consistency across all components and eliminates data duplication.
    It provides a single source of truth for MSME data used throughout the application.

    The service generates 20 realistic Indian MSMEs with proper risk distribution:
    - 60% green risk (12 MSMEs) - Score 70-100
    - 25% yellow risk (5 MSMEs) - Score 40-69
    - 15% red risk (3 MSMEs) - Score 0-39

    Attributes:
        _msme_data_cache: Internal cache storing MSME data by ID
        _initialized: Flag indicating if data has been initialized
    """

    def __init__(self) -> None:
        """Initialize the data service with empty cache."""
        self._msme_data_cache: Dict[str, MSMEData] = {}
        self._initialized: bool = False

    def initialize_data(self) -> None:
        """
        Initialize the centralized data store with realistic Indian MSMEs.

        This method is idempotent - calling it multiple times will not
        regenerate the data if it's already initialized.
        """
        if self._initialized:
            return

        # Generate 20 realistic Indian MSMEs with proper risk distribution
        # 60% green (12), 25% yellow (5), 15% red (3)
        self._msme_data_cache = self._generate_realistic_msmes()
        self._initialized = True
    
    def get_all_msmes(self) -> List[MSMEData]:
        """
        Get all MSME data from the centralized cache.

        Returns:
            List of all MSMEData objects in the system
        """
        if not self._initialized:
            self.initialize_data()
        return list(self._msme_data_cache.values())

    def get_msme_by_id(self, msme_id: str) -> Optional[MSMEData]:
        """
        Get specific MSME by its unique identifier.

        Args:
            msme_id: Unique identifier for the MSME

        Returns:
            MSMEData object if found, None otherwise
        """
        if not self._initialized:
            self.initialize_data()
        return self._msme_data_cache.get(msme_id)

    def get_msmes_by_risk_band(self, risk_band: str) -> List[MSMEData]:
        """
        Get MSMEs filtered by risk band classification.

        Args:
            risk_band: Risk classification ('green', 'yellow', 'red')

        Returns:
            List of MSMEData objects matching the risk band
        """
        if not self._initialized:
            self.initialize_data()
        return [msme for msme in self._msme_data_cache.values() if msme.risk_band == risk_band]

    def get_msmes_by_business_type(self, business_type: str) -> List[MSMEData]:
        """
        Get MSMEs filtered by business type.

        Args:
            business_type: Type of business ('retail', 'manufacturing', 'services', 'b2b')

        Returns:
            List of MSMEData objects matching the business type
        """
        if not self._initialized:
            self.initialize_data()
        return [msme for msme in self._msme_data_cache.values() if msme.business_type == business_type]
    
    def get_portfolio_analytics(self) -> Dict[str, Any]:
        """Get portfolio analytics summary"""
        if not self._initialized:
            self.initialize_data()
            
        msmes = list(self._msme_data_cache.values())
        total_msmes = len(msmes)
        
        # Risk distribution
        risk_distribution = {
            'green': len([m for m in msmes if m.risk_band == 'green']),
            'yellow': len([m for m in msmes if m.risk_band == 'yellow']),
            'red': len([m for m in msmes if m.risk_band == 'red'])
        }
        
        # Business type distribution with normalized names
        business_types = {}
        business_type_mapping = {
            'retail': 'Retail',
            'manufacturing': 'Manufacturing',
            'services': 'Services',
            'service': 'Services',  # Handle any legacy inconsistencies
            'mfg': 'Manufacturing',  # Handle any legacy inconsistencies
            'b2b': 'B2B'
        }

        for msme in msmes:
            normalized_type = business_type_mapping.get(msme.business_type.lower(), msme.business_type.title())
            business_types[normalized_type] = business_types.get(normalized_type, 0) + 1
        
        # Calculate averages
        avg_score = sum(msme.score for msme in msmes) / total_msmes if total_msmes > 0 else 0
        avg_gst_compliance = sum(msme.gst_compliance for msme in msmes) / total_msmes if total_msmes > 0 else 0
        avg_banking_health = sum(msme.banking_health for msme in msmes) / total_msmes if total_msmes > 0 else 0
        
        # Calculate total signals and average per MSME
        total_signals = total_msmes * random.randint(8, 15)  # Mock signal count
        average_signals_per_msme = total_signals / total_msmes if total_msmes > 0 else 0

        # Calculate triggered escalations based on risk factors
        # Escalations are triggered for high-risk MSMEs, score drops, and compliance issues
        high_risk_escalations = risk_distribution['red']  # All red risk MSMEs
        medium_risk_escalations = max(0, risk_distribution['yellow'] // 3)  # 1/3 of yellow risk MSMEs
        compliance_escalations = max(0, len([m for m in msmes if m.gst_compliance < 60]))  # Low GST compliance
        triggered_escalations = min(high_risk_escalations + medium_risk_escalations + compliance_escalations, total_msmes)

        return {
            'total_msmes': total_msmes,
            'risk_distribution': risk_distribution,
            'business_type_distribution': business_types,
            'average_score': round(avg_score, 1),
            'average_gst_compliance': round(avg_gst_compliance, 1),
            'average_banking_health': round(avg_banking_health, 1),
            'total_signals': total_signals,
            'triggered_escalations': triggered_escalations,
            'average_signals_per_msme': round(average_signals_per_msme, 1),
            'last_updated': datetime.now(UTC).isoformat()
        }
    
    def _generate_realistic_msmes(self) -> Dict[str, MSMEData]:
        """Generate realistic Indian MSME data with proper distribution"""
        msmes = {}
        
        # GREEN RISK MSMEs (12 out of 20 = 60%) - Enhanced with realistic Indian business data
        green_msmes = [
            {
                "name": "श्री गणेश इलेक्ट्रॉनिक्स",
                "business_type": "retail",
                "location": "Mumbai, Maharashtra",
                "score": 78.5,
                "gst_compliance": 92,
                "banking_health": 88,
                "monthly_turnover": 2850000,
                "digital_score": 85,
                "gst_number": "27AABCS1234C1Z5"
            },
            {
                "name": "Rajesh Textiles Pvt Ltd",
                "business_type": "manufacturing",
                "location": "Surat, Gujarat",
                "score": 74.2,
                "gst_compliance": 89,
                "banking_health": 91,
                "monthly_turnover": 4200000,
                "digital_score": 78,
                "gst_number": "24AABCT5678D1Z8"
            },
            {
                "name": "Annapurna Catering Services",
                "business_type": "services",
                "location": "Pune, Maharashtra",
                "score": 72.8,
                "gst_compliance": 85,
                "banking_health": 82,
                "monthly_turnover": 1850000,
                "digital_score": 72,
                "gst_number": "27AABCA9876E1Z2"
            },
            {
                "name": "Infosys Business Solutions",
                "business_type": "b2b",
                "location": "Bangalore, Karnataka",
                "score": 82.1,
                "gst_compliance": 96,
                "banking_health": 94,
                "monthly_turnover": 5200000,
                "digital_score": 98,
                "gst_number": "29AABCI1234F1Z9"
            },
            {
                "name": "Saravana Stores",
                "business_type": "retail",
                "location": "Chennai, Tamil Nadu",
                "score": 76.8,
                "gst_compliance": 91,
                "banking_health": 87,
                "monthly_turnover": 3100000,
                "digital_score": 83,
                "gst_number": "33AABCS5678G1Z3"
            },
            {
                "name": "Tata Steel Components",
                "business_type": "manufacturing",
                "location": "Jamshedpur, Jharkhand",
                "score": 80.4,
                "gst_compliance": 95,
                "banking_health": 92,
                "monthly_turnover": 6800000,
                "digital_score": 81,
                "gst_number": "20AABCT9876H1Z7"
            },
            {
                "name": "Zomato Delivery Partners",
                "business_type": "services",
                "location": "Gurgaon, Haryana",
                "score": 73.9,
                "gst_compliance": 88,
                "banking_health": 85,
                "monthly_turnover": 2200000,
                "digital_score": 95,
                "gst_number": "06AABCZ1234I1Z1"
            },
            {
                "name": "Reliance Retail Partners",
                "business_type": "b2b",
                "location": "Ahmedabad, Gujarat",
                "score": 79.2,
                "gst_compliance": 93,
                "banking_health": 89,
                "monthly_turnover": 4500000,
                "digital_score": 86,
                "gst_number": "24AABCR5678J1Z5"
            },
            {
                "name": "Big Bazaar Franchise",
                "business_type": "retail",
                "location": "Hyderabad, Telangana",
                "score": 75.6,
                "gst_compliance": 90,
                "banking_health": 86,
                "monthly_turnover": 2900000,
                "digital_score": 82,
                "gst_number": "36AABCB9876K1Z9"
            },
            {
                "name": "Mahindra Auto Components",
                "business_type": "manufacturing",
                "location": "Aurangabad, Maharashtra",
                "score": 77.3,
                "gst_compliance": 92,
                "banking_health": 88,
                "monthly_turnover": 3800000,
                "digital_score": 79,
                "gst_number": "27AABCM1234L1Z3"
            },
            {
                "name": "Urban Company Services",
                "business_type": "services",
                "location": "Delhi, Delhi",
                "score": 74.7,
                "gst_compliance": 89,
                "banking_health": 84,
                "monthly_turnover": 1950000,
                "digital_score": 91,
                "gst_number": "07AABCU5678M1Z7"
            },
            {
                "name": "Flipkart Logistics Hub",
                "business_type": "b2b",
                "location": "Kolkata, West Bengal",
                "score": 78.1,
                "gst_compliance": 94,
                "banking_health": 90,
                "monthly_turnover": 4100000,
                "digital_score": 88,
                "gst_number": "19AABCF9876N1Z1"
            }
        ]
        
        # Add green MSMEs with SMA data
        for i, msme_data in enumerate(green_msmes):
            msme_id = f"msme_{str(i+1).zfill(3)}"

            # Green MSMEs mostly have standard classification with occasional SMA-0
            sma_classification = "standard" if i < 10 else "sma_0"  # 2 out of 12 in SMA-0
            days_past_due = 0 if sma_classification == "standard" else random.randint(1, 30)
            outstanding_amount = msme_data["monthly_turnover"] * random.uniform(1.5, 3.0)
            last_payment_date = (datetime.now(UTC) - timedelta(days=days_past_due)).isoformat()
            provision_required = outstanding_amount * 0.0025 if sma_classification == "sma_0" else 0.0

            msmes[msme_id] = MSMEData(
                msme_id=msme_id,
                risk_band="green",
                created_at=(datetime.now(UTC) - timedelta(days=random.randint(30, 365))).isoformat(),
                tags=[msme_data["business_type"], "verified", "active"],
                sma_classification=sma_classification,
                days_past_due=days_past_due,
                outstanding_amount=outstanding_amount,
                last_payment_date=last_payment_date,
                provision_required=provision_required,
                **msme_data
            )
        
        # YELLOW RISK MSMEs (5 out of 20 = 25%) - Enhanced with realistic Indian business data
        yellow_msmes = [
            {
                "name": "Sharma Medical Store",
                "business_type": "retail",
                "location": "Lucknow, Uttar Pradesh",
                "score": 58.2,
                "gst_compliance": 72,
                "banking_health": 68,
                "monthly_turnover": 1200000,
                "digital_score": 65,
                "gst_number": "09AABCS1234O1Z5"
            },
            {
                "name": "Rajasthan Handicrafts Ltd",
                "business_type": "manufacturing",
                "location": "Jaipur, Rajasthan",
                "score": 63.4,
                "gst_compliance": 76,
                "banking_health": 71,
                "monthly_turnover": 1800000,
                "digital_score": 58,
                "gst_number": "08AABCR5678P1Z9"
            },
            {
                "name": "Ola Cab Services",
                "business_type": "services",
                "location": "Indore, Madhya Pradesh",
                "score": 59.8,
                "gst_compliance": 74,
                "banking_health": 69,
                "monthly_turnover": 950000,
                "digital_score": 62,
                "gst_number": "23AABCO9876Q1Z3"
            },
            {
                "name": "Patanjali Distributors",
                "business_type": "b2b",
                "location": "Haridwar, Uttarakhand",
                "score": 61.5,
                "gst_compliance": 78,
                "banking_health": 73,
                "monthly_turnover": 1650000,
                "digital_score": 67,
                "gst_number": "05AABCP1234R1Z7"
            },
            {
                "name": "More Supermarket",
                "business_type": "retail",
                "location": "Bhopal, Madhya Pradesh",
                "score": 56.7,
                "gst_compliance": 70,
                "banking_health": 66,
                "monthly_turnover": 1100000,
                "digital_score": 63,
                "gst_number": "23AABCM5678S1Z1"
            }
        ]

        # RED RISK MSMEs (3 out of 20 = 15%) - Enhanced with realistic Indian business data
        red_msmes = [
            {
                "name": "Kanpur Leather Works",
                "business_type": "manufacturing",
                "location": "Kanpur, Uttar Pradesh",
                "score": 29.8,
                "gst_compliance": 42,
                "banking_health": 35,
                "monthly_turnover": 380000,
                "digital_score": 28,
                "gst_number": "09AABCK1234U1Z9"
            },
            {
                "name": "Varanasi Silk Weavers",
                "business_type": "manufacturing",
                "location": "Varanasi, Uttar Pradesh",
                "score": 32.4,
                "gst_compliance": 38,
                "banking_health": 41,
                "monthly_turnover": 420000,
                "digital_score": 22,
                "gst_number": "09AABCV5678H1Z2"
            },
            {
                "name": "Bihar Transport Co-op",
                "business_type": "services",
                "location": "Patna, Bihar",
                "score": 35.6,
                "gst_compliance": 45,
                "banking_health": 38,
                "monthly_turnover": 480000,
                "digital_score": 31,
                "gst_number": "10AABCB1234T1Z7"
            }
        ]

        # Add yellow MSMEs with SMA data
        for i, msme_data in enumerate(yellow_msmes):
            msme_id = f"msme_{str(i+13).zfill(3)}"

            # Yellow MSMEs have mix of SMA-0 and SMA-1 classifications
            if i < 2:
                sma_classification = "sma_0"
                days_past_due = random.randint(15, 30)
                provision_rate = 0.0025
            elif i < 4:
                sma_classification = "sma_1"
                days_past_due = random.randint(31, 60)
                provision_rate = 0.01
            else:
                sma_classification = "standard"
                days_past_due = 0
                provision_rate = 0.0

            outstanding_amount = msme_data["monthly_turnover"] * random.uniform(2.0, 4.0)
            last_payment_date = (datetime.now(UTC) - timedelta(days=days_past_due)).isoformat()
            provision_required = outstanding_amount * provision_rate

            msmes[msme_id] = MSMEData(
                msme_id=msme_id,
                risk_band="yellow",
                created_at=(datetime.now(UTC) - timedelta(days=random.randint(30, 365))).isoformat(),
                tags=[msme_data["business_type"], "monitoring", "active"],
                sma_classification=sma_classification,
                days_past_due=days_past_due,
                outstanding_amount=outstanding_amount,
                last_payment_date=last_payment_date,
                provision_required=provision_required,
                **msme_data
            )

        # Add red MSMEs with SMA data
        for i, msme_data in enumerate(red_msmes):
            msme_id = f"msme_{str(i+18).zfill(3)}"

            # Red MSMEs have SMA-2 or NPA classifications
            if i == 0:
                sma_classification = "sma_2"
                days_past_due = random.randint(61, 90)
                provision_rate = 0.025
            elif i == 1:
                sma_classification = "npa"
                days_past_due = random.randint(91, 180)
                provision_rate = 0.15
            else:
                sma_classification = "sma_1"
                days_past_due = random.randint(45, 60)
                provision_rate = 0.01

            outstanding_amount = msme_data["monthly_turnover"] * random.uniform(3.0, 6.0)
            last_payment_date = (datetime.now(UTC) - timedelta(days=days_past_due)).isoformat()
            provision_required = outstanding_amount * provision_rate

            msmes[msme_id] = MSMEData(
                msme_id=msme_id,
                risk_band="red",
                created_at=(datetime.now(UTC) - timedelta(days=random.randint(30, 365))).isoformat(),
                tags=[msme_data["business_type"], "high_risk", "requires_attention"],
                sma_classification=sma_classification,
                days_past_due=days_past_due,
                outstanding_amount=outstanding_amount,
                last_payment_date=last_payment_date,
                provision_required=provision_required,
                **msme_data
            )

        return msmes

# Global instance
data_service = CentralizedDataService()
