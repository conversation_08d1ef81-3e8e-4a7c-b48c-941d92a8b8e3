/**
 * Optimized Portfolio Dashboard Component
 * Uses performance optimizations, shared components, and efficient data patterns
 */
'use client';

import React, { useState, useEffect, useMemo, useCallback, memo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';

import { RiskDistribution, RiskBadge } from '@/components/shared/risk-card';
import { DataFetcher, LoadingSkeleton } from '@/components/shared/data-fetcher';
import { BulkNudgeModal } from '@/components/msme/bulk-nudge-modal';
import { TopAction } from '@/types';

// Interface for MSME data in portfolio
interface PortfolioMSME {
  msme_id: string;
  name: string;
  created_at: string;
  risk_band: 'green' | 'yellow' | 'red';
  current_score: number;
  gst_compliance?: number;
  banking_health?: number;
  business_type: string;
  location: string;
  signals_count?: number;
}

// Interface for analytics data
interface PortfolioAnalytics {
  risk_distribution: {
    green: number;
    yellow: number;
    red: number;
  };
  total_msmes: number;
}
import { useDebouncedState, usePerformanceMonitor } from '@/lib/performance';
import { useInputValidation } from '@/hooks/useValidation';
import { cn } from '@/lib/utils';
import {
  Building2, MapPin, Calendar, Search, ArrowUpRight, Target,
  ChevronLeft, ChevronRight
} from 'lucide-react';

// Memoized MSME Row Component for better performance
const MSMETableRow = memo(({ msme, onClick }: { msme: PortfolioMSME; onClick: (id: string) => void }) => {
  const handleClick = useCallback(() => onClick(msme.msme_id), [msme.msme_id, onClick]);

  const handleViewClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onClick(msme.msme_id);
  }, [msme.msme_id, onClick]);

  return (
    <TableRow
      className="hover:bg-muted/50 cursor-pointer transition-colors"
      onClick={handleClick}
    >
      <TableCell>
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Building2 className="h-4 w-4 text-primary" />
          </div>
          <div>
            <div className="font-medium">{msme.name}</div>
            <div className="text-sm text-muted-foreground flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              {new Date(msme.created_at).toLocaleDateString()}
            </div>
          </div>
        </div>
      </TableCell>
      <TableCell className="text-center">
        <RiskBadge riskLevel={msme.risk_band} size="sm" />
      </TableCell>
      <TableCell className="text-center">
        <div className="font-semibold">{Math.round(msme.current_score)}</div>
      </TableCell>
      <TableCell className="text-center">
        <div className="font-medium">{msme.gst_compliance || 'N/A'}%</div>
      </TableCell>
      <TableCell className="text-center">
        <div className="font-medium">{msme.banking_health || 'N/A'}%</div>
      </TableCell>
      <TableCell className="text-center">
        <Badge variant="outline" className="capitalize">
          {msme.business_type}
        </Badge>
      </TableCell>
      <TableCell className="text-center">
        <div className="flex items-center justify-center gap-1 text-sm text-muted-foreground">
          <MapPin className="h-3 w-3" />
          {msme.location}
        </div>
      </TableCell>
      <TableCell className="text-center">
        <Button
          size="sm"
          variant="outline"
          onClick={handleViewClick}
        >
          View
        </Button>
      </TableCell>
    </TableRow>
  );
});

MSMETableRow.displayName = 'MSMETableRow';

export function OptimizedPortfolioDashboard() {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Use debounced search for better performance
  const [searchTerm, setSearchTerm, debouncedSearchTerm] = useDebouncedState('', 300);
  const [showBulkNudgeModal, setShowBulkNudgeModal] = useState(false);

  // Search validation for security
  const searchValidation = useInputValidation('searchTerm', { required: false, maxLength: 100 });

  // Performance monitoring
  usePerformanceMonitor('OptimizedPortfolioDashboard');

  // Direct data fetching for debugging
  const [msmes, setMsmes] = useState<PortfolioMSME[]>([]);
  const [analytics, setAnalytics] = useState<PortfolioAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Simplified pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 20;
  const filteredMsmes = msmes;
  const paginatedMsmes = msmes;
  const totalPages = Math.ceil(msmes.length / pageSize);

  const setFilter = useCallback((filter: Record<string, unknown>) => {
    console.log('Filter applied:', filter);
  }, []);

  const clearFilter = useCallback(() => {
    console.log('Filter cleared');
  }, []);

  const refetch = async () => {
    try {
      setLoading(true);
      const [portfolioResponse, analyticsResponse] = await Promise.all([
        fetch('http://localhost:8000/api/dashboard/portfolio/legacy'),
        fetch('http://localhost:8000/api/dashboard/analytics')
      ]);

      const portfolioData = await portfolioResponse.json();
      const analyticsData = await analyticsResponse.json();

      setMsmes(portfolioData);
      setAnalytics(analyticsData);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refetch();
  }, []);

  const filterRisk = searchParams?.get('filter');

  // Fetch AI insights separately to avoid blocking main data
  useEffect(() => {
    // AI insights functionality removed for optimization
  }, []);

  // Apply filters when URL params change
  useEffect(() => {
    if (filterRisk) {
      setFilter({ risk_band: filterRisk as 'green' | 'yellow' | 'red' });
    } else {
      clearFilter();
    }
  }, [filterRisk, setFilter, clearFilter]);

  // Optimized search filtering with debounced search term
  const searchFilteredMsmes = useMemo(() => {
    if (!debouncedSearchTerm.trim()) return paginatedMsmes;

    const term = debouncedSearchTerm.toLowerCase();
    return paginatedMsmes.filter(msme =>
      msme.name.toLowerCase().includes(term) ||
      msme.location.toLowerCase().includes(term) ||
      msme.business_type.toLowerCase().includes(term)
    );
  }, [paginatedMsmes, debouncedSearchTerm]);

  // Memoized top actions generation
  const topActions = useMemo((): TopAction[] => {
    if (!analytics || msmes.length === 0) return [];

    const actions: TopAction[] = [];

    // High risk MSMEs requiring immediate attention
    const highRiskCount = analytics.risk_distribution.red;
    if (highRiskCount > 0) {
      actions.push({
        id: 'review_high_risk',
        title: 'Review High-Risk MSMEs',
        description: `${highRiskCount} MSMEs require immediate attention due to high credit risk`,
        impact: 'high',
        type: 'risk_reduction',
        msme_count: highRiskCount,
        potential_value: `₹${(highRiskCount * 2.5).toFixed(1)}L exposure`,
        action_items: ['Review credit scores', 'Assess collateral', 'Update risk mitigation'],
        priority_score: 95,
        estimated_completion_days: 3
      });
    }

    // Data collection opportunities
    const lowDataMsmes = msmes.filter(msme => (msme.signals_count || 0) < 5).length;
    if (lowDataMsmes > 0) {
      actions.push({
        id: 'enhance_data',
        title: 'Enhance Data Collection',
        description: `${lowDataMsmes} MSMEs have insufficient data for accurate scoring`,
        impact: 'medium',
        type: 'efficiency',
        msme_count: lowDataMsmes,
        potential_value: `${Math.round((lowDataMsmes / msmes.length) * 100)}% accuracy gain`,
        action_items: ['Request additional documents', 'Enable digital integrations', 'Schedule data reviews'],
        priority_score: 75,
        estimated_completion_days: 7
      });
    }

    // Growth opportunities
    const mediumRiskCount = analytics.risk_distribution.yellow;
    if (mediumRiskCount > 0) {
      actions.push({
        id: 'growth_opportunities',
        title: 'Unlock Growth Opportunities',
        description: `${mediumRiskCount} MSMEs show potential for credit limit increases`,
        impact: 'medium',
        type: 'ltv_optimization',
        msme_count: mediumRiskCount,
        potential_value: `₹${(mediumRiskCount * 1.8).toFixed(1)}L revenue potential`,
        action_items: ['Analyze growth patterns', 'Review credit limits', 'Propose limit increases'],
        priority_score: 65,
        estimated_completion_days: 5
      });
    }

    return actions.slice(0, 3); // Top 3 actions
  }, [analytics, msmes]);

  const handleMSMEClick = useCallback((msmeId: string) => {
    router.push(`/msme/${msmeId}`);
  }, [router]);

  const handleRiskFilterClick = useCallback((riskLevel: string) => {
    if (filterRisk === riskLevel) {
      router.push('/');
    } else {
      router.push(`/?filter=${riskLevel}`);
    }
  }, [filterRisk, router]);

  // Debug logging
  console.log('Portfolio Debug Info:', {
    totalMsmes: msmes.length,
    filteredMsmes: filteredMsmes.length,
    paginatedMsmes: paginatedMsmes.length,
    searchFilteredMsmes: searchFilteredMsmes.length,
    currentPage,
    totalPages,
    loading,
    error
  });

  // Utility functions removed for optimization

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-3">
          <LoadingSkeleton type="card" />
          <LoadingSkeleton type="card" />
          <LoadingSkeleton type="card" />
        </div>
        <LoadingSkeleton type="table" count={5} />
      </div>
    );
  }

  if (error) {
    return (
      <DataFetcher
        data={null}
        loading={false}
        error={error}
        onRetry={refetch}
      >
        {() => null}
      </DataFetcher>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-2">
          <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
            Portfolio Dashboard
          </h1>
          <p className="text-lg text-muted-foreground">
            Monitor and manage your MSME credit portfolio
          </p>
          <div className="mt-2 p-2 bg-blue-100 rounded text-sm">
            <strong>Debug:</strong> Total MSMEs loaded: {msmes.length} |
            Filtered: {filteredMsmes.length} |
            Displayed: {paginatedMsmes.length}
          </div>
        </div>
        <div className="flex gap-3">
          <Button onClick={refetch} variant="outline" size="lg">
            <ArrowUpRight className="mr-2 h-4 w-4" />
            Refresh Data
          </Button>
        </div>
      </div>

      {/* Simplified Top Actions Section */}
      {topActions.length > 0 && (
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4 text-emerald-500" />
                <h3 className="font-medium text-sm">Priority Actions</h3>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {topActions.length} items
                </Badge>
              </div>
            </div>
            <div className="space-y-2">
              <div className="space-y-2">
                {topActions.slice(0, 3).map((action) => (
                  <div key={action.id} className="flex items-center justify-between p-2 rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors">
                    <div className="flex items-center gap-3">
                      <div className={`w-2 h-2 rounded-full ${
                        action.impact === 'high' ? 'bg-red-500' :
                        action.impact === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                      }`} />
                      <div>
                        <p className="text-sm font-medium">{action.title}</p>
                        <p className="text-xs text-muted-foreground">{action.msme_count} MSMEs • {action.potential_value}</p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">
                      View
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Risk Distribution */}
      {analytics && (
        <RiskDistribution
          riskDistribution={analytics.risk_distribution}
          totalCount={analytics.total_msmes}
          onRiskClick={handleRiskFilterClick}
        />
      )}

      {/* Portfolio Overview */}
      <Card className="border-0 shadow-lg">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Building2 className="h-5 w-5 text-primary" />
                </div>
                Portfolio Overview
                <Badge variant="secondary" className="ml-2">
                  {filteredMsmes.length} MSMEs
                </Badge>
              </CardTitle>
              <CardDescription>
                Click on any MSME row to view detailed information
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search MSMEs..."
                  value={searchTerm}
                  onChange={(e) => {
                    const value = e.target.value;
                    searchValidation.validate(value);
                    if (searchValidation.isValid || !value.trim()) {
                      setSearchTerm(searchValidation.sanitizedValue || value);
                    }
                  }}
                  className={cn(
                    "pl-10 w-64",
                    searchValidation.touched && !searchValidation.isValid
                      ? "border-red-300 focus:border-red-500"
                      : ""
                  )}
                  aria-invalid={searchValidation.touched && !searchValidation.isValid}
                />
                {searchValidation.touched && !searchValidation.isValid && (
                  <div className="absolute top-full left-0 mt-1 text-xs text-red-600 bg-white px-2 py-1 rounded shadow-sm border">
                    {searchValidation.errors.join(', ')}
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="bg-muted/50">
                  <TableHead className="font-semibold">Business Name</TableHead>
                  <TableHead className="font-semibold text-center">Risk Band</TableHead>
                  <TableHead className="font-semibold text-center">Credit Score</TableHead>
                  <TableHead className="font-semibold text-center">GST Compliance</TableHead>
                  <TableHead className="font-semibold text-center">Banking Health</TableHead>
                  <TableHead className="font-semibold text-center">Business Type</TableHead>
                  <TableHead className="font-semibold text-center">Location</TableHead>
                  <TableHead className="font-semibold text-center">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {searchFilteredMsmes.map((msme) => (
                  <MSMETableRow
                    key={msme.msme_id}
                    msme={msme}
                    onClick={handleMSMEClick}
                  />
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                Page {currentPage} of {totalPages} ({filteredMsmes.length} total MSMEs)
              </div>
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage - 1)}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(currentPage + 1)}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Bulk Nudge Modal */}
      <BulkNudgeModal
        isOpen={showBulkNudgeModal}
        onClose={() => setShowBulkNudgeModal(false)}
      />
    </div>
  );
}
