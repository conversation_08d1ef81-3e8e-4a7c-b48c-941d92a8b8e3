'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  Calendar,
  Database,
  TrendingUp,
  Shield,
  Building,
  CreditCard,
  FileText,
  Activity,
  Clock,
  Zap
} from 'lucide-react';

interface DataSourceStatus {
  source: string;
  name: string;
  icon: React.ReactNode;
  status: 'connected' | 'disconnected' | 'error' | 'pending';
  lastUpdated: string;
  dataQuality: number;
  recordCount: number;
  healthScore: number;
  description: string;
}

interface DataSourceOverviewProps {
  msmeId: string;
}

export default function DataSourceOverview({ msmeId }: DataSourceOverviewProps) {
  const [dataSources, setDataSources] = useState<DataSourceStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchDataSourceStatus();
  }, [msmeId]);

  const fetchDataSourceStatus = async () => {
    try {
      setLoading(true);
      // Mock data - in production, this would call the actual API
      const mockData: DataSourceStatus[] = [
        {
          source: 'gst',
          name: 'GST Data',
          icon: <FileText className="h-5 w-5" />,
          status: 'connected',
          lastUpdated: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
          dataQuality: 95.2,
          recordCount: 156,
          healthScore: 92.8,
          description: 'GST turnover, compliance, and payment data'
        },
        {
          source: 'banking',
          name: 'Banking Data',
          icon: <CreditCard className="h-5 w-5" />,
          status: 'connected',
          lastUpdated: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
          dataQuality: 88.7,
          recordCount: 2847,
          healthScore: 85.4,
          description: 'Bank statements and transaction analysis'
        },
        {
          source: 'udyam',
          name: 'Udyam Registration',
          icon: <Building className="h-5 w-5" />,
          status: 'connected',
          lastUpdated: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
          dataQuality: 98.5,
          recordCount: 1,
          healthScore: 96.2,
          description: 'MSME registration and verification data'
        },
        {
          source: 'cashflow',
          name: 'Cash Flow Analysis',
          icon: <Activity className="h-5 w-5" />,
          status: 'connected',
          lastUpdated: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
          dataQuality: 82.3,
          recordCount: 72,
          healthScore: 78.9,
          description: 'Cash flow statements and forecasting'
        },
        {
          source: 'financial',
          name: 'Financial Metrics',
          icon: <TrendingUp className="h-5 w-5" />,
          status: 'connected',
          lastUpdated: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
          dataQuality: 91.4,
          recordCount: 48,
          healthScore: 87.6,
          description: 'Financial ratios and performance metrics'
        },
        {
          source: 'compliance',
          name: 'Compliance Data',
          icon: <Shield className="h-5 w-5" />,
          status: 'pending',
          lastUpdated: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(), // 12 hours ago
          dataQuality: 75.8,
          recordCount: 12,
          healthScore: 72.1,
          description: 'Regulatory compliance and audit data'
        }
      ];
      
      setDataSources(mockData);
    } catch (error) {
      console.error('Error fetching data source status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDataSourceStatus();
    setRefreshing(false);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge variant="default" className="bg-green-100 text-green-800">Connected</Badge>;
      case 'disconnected':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800">Disconnected</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-yellow-600';
    if (score >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  const formatLastUpdated = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const calculateOverallHealth = () => {
    if (dataSources.length === 0) return 0;
    const totalScore = dataSources.reduce((sum, source) => sum + source.healthScore, 0);
    return totalScore / dataSources.length;
  };

  const getConnectedCount = () => {
    return dataSources.filter(source => source.status === 'connected').length;
  };

  const getTotalRecords = () => {
    return dataSources.reduce((sum, source) => sum + source.recordCount, 0);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Refresh */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Data Source Overview</h2>
          <p className="text-muted-foreground">Real-time status of all integrated data sources</p>
        </div>
        <Button 
          variant="outline" 
          onClick={handleRefresh} 
          disabled={refreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh All
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <Database className="h-5 w-5" />
              Data Sources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 mb-2">
              {getConnectedCount()}/{dataSources.length}
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-blue-600">Connected</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700">
              <TrendingUp className="h-5 w-5" />
              Overall Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 mb-2">
              {calculateOverallHealth().toFixed(0)}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Progress value={calculateOverallHealth()} className="h-2 flex-1" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <FileText className="h-5 w-5" />
              Total Records
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 mb-2">
              {getTotalRecords().toLocaleString()}
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-purple-600">Data Points</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <Zap className="h-5 w-5" />
              Data Quality
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 mb-2">
              {(dataSources.reduce((sum, source) => sum + source.dataQuality, 0) / dataSources.length).toFixed(0)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-orange-600">Average</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Data Source Details */}
      <div className="grid gap-6 md:grid-cols-2">
        {dataSources.map((source) => (
          <Card key={source.source} className="border-0 shadow-lg">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    {source.icon}
                  </div>
                  {source.name}
                </CardTitle>
                <div className="flex items-center gap-2">
                  {getStatusIcon(source.status)}
                  {getStatusBadge(source.status)}
                </div>
              </div>
              <CardDescription>{source.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Health Score</span>
                    <span className={`font-medium ${getHealthScoreColor(source.healthScore)}`}>
                      {source.healthScore.toFixed(0)}/100
                    </span>
                  </div>
                  <Progress value={source.healthScore} className="h-2" />
                  
                  <div className="flex items-center justify-between text-sm">
                    <span>Data Quality</span>
                    <span className="font-medium">{source.dataQuality.toFixed(1)}%</span>
                  </div>
                  <Progress value={source.dataQuality} className="h-2" />
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Records</span>
                    <span className="font-medium">{source.recordCount.toLocaleString()}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span>Last Updated</span>
                    <span className="font-medium">{formatLastUpdated(source.lastUpdated)}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span>Status</span>
                    <span className="font-medium capitalize">{source.status}</span>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="flex items-center justify-between">
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <RefreshCw className="h-3 w-3" />
                  Refresh
                </Button>
                <Button variant="ghost" size="sm" className="flex items-center gap-2">
                  <FileText className="h-3 w-3" />
                  View Details
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* System Health Summary */}
      <Card className="bg-muted/30">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                System last checked: {new Date().toLocaleString()}
              </span>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Overall Status:</span>
                <Badge variant={getConnectedCount() === dataSources.length ? "default" : "secondary"}>
                  {getConnectedCount() === dataSources.length ? "All Systems Operational" : "Some Issues Detected"}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
