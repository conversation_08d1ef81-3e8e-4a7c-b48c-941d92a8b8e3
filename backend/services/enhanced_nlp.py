"""
Enhanced Natural Language Processing for Credit Domain
Industry-leading query understanding and response generation
"""

import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class QueryIntent(Enum):
    RISK_ANALYSIS = "risk_analysis"
    PORTFOLIO_OVERVIEW = "portfolio_overview"
    COMPLIANCE_CHECK = "compliance_check"
    SCORE_ANALYSIS = "score_analysis"
    GEOGRAPHIC_ANALYSIS = "geographic_analysis"
    SECTOR_ANALYSIS = "sector_analysis"
    TREND_ANALYSIS = "trend_analysis"
    ALERT_MANAGEMENT = "alert_management"
    RECOMMENDATION_REQUEST = "recommendation_request"
    COMPARATIVE_ANALYSIS = "comparative_analysis"

class EntityType(Enum):
    BUSINESS_TYPE = "business_type"
    LOCATION = "location"
    TIME_PERIOD = "time_period"
    RISK_LEVEL = "risk_level"
    SCORE_RANGE = "score_range"
    AMOUNT_RANGE = "amount_range"
    COMPLIANCE_TYPE = "compliance_type"
    METRIC = "metric"

@dataclass
class QueryEntity:
    entity_type: EntityType
    value: str
    confidence: float
    normalized_value: Any

@dataclass
class QueryContext:
    intent: QueryIntent
    entities: List[QueryEntity]
    confidence: float
    complexity_score: float
    requires_data_analysis: bool
    suggested_visualizations: List[str]

class EnhancedCreditNLP:
    """
    Advanced NLP processor for credit domain queries
    Understands complex multi-step credit analysis requests
    """
    
    def __init__(self):
        self.intent_patterns = self._initialize_intent_patterns()
        self.entity_patterns = self._initialize_entity_patterns()
        self.domain_vocabulary = self._initialize_domain_vocabulary()
        self.query_templates = self._initialize_query_templates()
        
    def _initialize_intent_patterns(self) -> Dict[QueryIntent, List[str]]:
        """Initialize intent recognition patterns"""
        return {
            QueryIntent.RISK_ANALYSIS: [
                r"(?:show|find|identify|list).*(?:high|medium|low).*risk",
                r"(?:risk|risky).*(?:msme|borrower|account|customer)",
                r"(?:alert|warning|flag).*(?:risk|default|problem)",
                r"(?:concentration|exposure).*risk",
                r"(?:early|advance).*warning"
            ],
            QueryIntent.PORTFOLIO_OVERVIEW: [
                r"(?:portfolio|overall).*(?:summary|overview|status|health)",
                r"(?:total|all).*(?:msme|borrower|account)",
                r"(?:dashboard|snapshot).*(?:portfolio|credit)",
                r"(?:performance|health).*(?:portfolio|book)"
            ],
            QueryIntent.COMPLIANCE_CHECK: [
                r"(?:compliance|regulatory|rbi).*(?:status|check|requirement)",
                r"(?:crilc|npa|sma|ews).*(?:filing|report|submission)",
                r"(?:deadline|due|overdue).*(?:compliance|filing|report)",
                r"(?:audit|examination).*(?:ready|preparation)"
            ],
            QueryIntent.SCORE_ANALYSIS: [
                r"(?:score|rating).*(?:drop|decline|deteriorat|improv|increas)",
                r"(?:credit|risk).*score.*(?:trend|pattern|change)",
                r"(?:declining|improving|stable).*(?:score|performance)",
                r"(?:score|rating).*(?:distribution|range|band)"
            ],
            QueryIntent.GEOGRAPHIC_ANALYSIS: [
                r"(?:branch|location|region|state|city).*(?:wise|analysis|distribution)",
                r"(?:geographic|geographical).*(?:risk|performance|analysis)",
                r"(?:mumbai|delhi|bangalore|chennai|pune|hyderabad|gujarat|maharashtra)",
                r"(?:area|zone|territory).*(?:performance|risk)"
            ],
            QueryIntent.SECTOR_ANALYSIS: [
                r"(?:sector|industry|business.*type).*(?:wise|analysis|performance)",
                r"(?:manufacturing|retail|service|trading|agriculture)",
                r"(?:industry|sector).*(?:trend|pattern|risk|performance)",
                r"(?:business.*category|vertical).*analysis"
            ],
            QueryIntent.TREND_ANALYSIS: [
                r"(?:trend|pattern|movement).*(?:analysis|over.*time)",
                r"(?:quarter|month|year).*(?:performance|trend|comparison)",
                r"(?:historical|past|previous).*(?:performance|data|trend)",
                r"(?:forecast|predict|projection).*(?:trend|performance)"
            ]
        }
    
    def _initialize_entity_patterns(self) -> Dict[EntityType, List[Tuple[str, str]]]:
        """Initialize entity extraction patterns"""
        return {
            EntityType.BUSINESS_TYPE: [
                (r"(?:manufacturing|factory|production)", "manufacturing"),
                (r"(?:retail|shop|store|trading)", "retail"),
                (r"(?:service|consulting|software)", "services"),
                (r"(?:agriculture|farming|agri)", "agriculture"),
                (r"(?:textile|garment|fabric)", "textile"),
                (r"(?:food|restaurant|catering)", "food_beverage"),
                (r"(?:construction|building|real.*estate)", "construction")
            ],
            EntityType.LOCATION: [
                (r"(?:mumbai|bombay)", "mumbai"),
                (r"(?:delhi|new.*delhi)", "delhi"),
                (r"(?:bangalore|bengaluru)", "bangalore"),
                (r"(?:chennai|madras)", "chennai"),
                (r"(?:pune|poona)", "pune"),
                (r"(?:hyderabad)", "hyderabad"),
                (r"(?:gujarat)", "gujarat"),
                (r"(?:maharashtra)", "maharashtra"),
                (r"(?:karnataka)", "karnataka"),
                (r"(?:tamil.*nadu)", "tamil_nadu")
            ],
            EntityType.TIME_PERIOD: [
                (r"(?:last|past|previous).*(\d+).*(?:day|days)", "days"),
                (r"(?:last|past|previous).*(\d+).*(?:week|weeks)", "weeks"),
                (r"(?:last|past|previous).*(\d+).*(?:month|months)", "months"),
                (r"(?:last|past|previous).*(\d+).*(?:quarter|quarters)", "quarters"),
                (r"(?:last|past|previous).*(\d+).*(?:year|years)", "years"),
                (r"(?:this|current).*(?:month|quarter|year)", "current_period"),
                (r"(?:today|yesterday)", "recent")
            ],
            EntityType.RISK_LEVEL: [
                (r"(?:high|critical|severe).*risk", "high"),
                (r"(?:medium|moderate).*risk", "medium"),
                (r"(?:low|minimal).*risk", "low"),
                (r"(?:red|danger)", "high"),
                (r"(?:yellow|amber|orange)", "medium"),
                (r"(?:green|safe)", "low")
            ],
            EntityType.SCORE_RANGE: [
                (r"(?:score|rating).*(?:above|over|greater.*than).*(\d+)", "above"),
                (r"(?:score|rating).*(?:below|under|less.*than).*(\d+)", "below"),
                (r"(?:score|rating).*(?:between).*(\d+).*(?:and|to).*(\d+)", "range"),
                (r"(?:drop|decline|fall).*(?:by|of).*(\d+).*(?:point|points)", "drop")
            ],
            EntityType.COMPLIANCE_TYPE: [
                (r"(?:crilc)", "crilc"),
                (r"(?:npa|non.*performing)", "npa"),
                (r"(?:sma|special.*mention)", "sma"),
                (r"(?:ews|early.*warning)", "ews"),
                (r"(?:rbi|reserve.*bank)", "rbi_general"),
                (r"(?:audit|examination)", "audit")
            ]
        }
    
    def _initialize_domain_vocabulary(self) -> Dict[str, List[str]]:
        """Initialize credit domain vocabulary for better understanding"""
        return {
            "risk_indicators": [
                "default", "delinquency", "npa", "sma", "overdue", "delay",
                "deterioration", "stress", "concentration", "exposure"
            ],
            "financial_metrics": [
                "turnover", "revenue", "profit", "loss", "cash_flow", "debt",
                "equity", "ratio", "margin", "growth", "decline"
            ],
            "compliance_terms": [
                "filing", "submission", "deadline", "requirement", "regulation",
                "guideline", "circular", "notification", "compliance", "audit"
            ],
            "time_expressions": [
                "daily", "weekly", "monthly", "quarterly", "annually",
                "recent", "latest", "current", "historical", "trending"
            ]
        }

    def _initialize_query_templates(self) -> Dict[QueryIntent, List[str]]:
        """Initialize query response templates"""
        return {
            QueryIntent.RISK_ANALYSIS: [
                "Based on current portfolio analysis, here are the risk insights:",
                "Risk assessment shows the following patterns:",
                "Current risk distribution across your portfolio:"
            ],
            QueryIntent.PORTFOLIO_OVERVIEW: [
                "Portfolio summary and key metrics:",
                "Here's your current portfolio overview:",
                "Portfolio health and performance indicators:"
            ],
            QueryIntent.COMPLIANCE_CHECK: [
                "Compliance status and regulatory updates:",
                "Current compliance health and upcoming deadlines:",
                "Regulatory requirements and filing status:"
            ],
            QueryIntent.SCORE_ANALYSIS: [
                "Credit score analysis and trends:",
                "Score performance and improvement opportunities:",
                "Detailed scoring breakdown and insights:"
            ],
            QueryIntent.GEOGRAPHIC_ANALYSIS: [
                "Geographic distribution and regional insights:",
                "Location-based risk and performance analysis:",
                "Regional portfolio breakdown:"
            ],
            QueryIntent.SECTOR_ANALYSIS: [
                "Sector-wise performance and risk analysis:",
                "Industry trends and sector insights:",
                "Business type distribution and analysis:"
            ],
            QueryIntent.TREND_ANALYSIS: [
                "Trend analysis and predictive insights:",
                "Historical patterns and future projections:",
                "Performance trends and forecasting:"
            ]
        }

    def parse_complex_query(self, query: str) -> QueryContext:
        """
        Parse complex credit domain queries with high accuracy
        Example: "Show me manufacturing MSMEs in Gujarat with declining cash flows and upcoming loan renewals"
        """
        query_lower = query.lower()
        
        # Detect primary intent
        intent = self._detect_intent(query_lower)
        
        # Extract entities
        entities = self._extract_entities(query_lower)
        
        # Calculate confidence and complexity
        confidence = self._calculate_confidence(query_lower, intent, entities)
        complexity_score = self._calculate_complexity(query_lower, entities)
        
        # Determine if data analysis is required
        requires_analysis = self._requires_data_analysis(intent, entities)
        
        # Suggest visualizations
        visualizations = self._suggest_visualizations(intent, entities)
        
        return QueryContext(
            intent=intent,
            entities=entities,
            confidence=confidence,
            complexity_score=complexity_score,
            requires_data_analysis=requires_analysis,
            suggested_visualizations=visualizations
        )
    
    def _detect_intent(self, query: str) -> QueryIntent:
        """Detect the primary intent of the query"""
        intent_scores = {}
        
        for intent, patterns in self.intent_patterns.items():
            score = 0
            for pattern in patterns:
                if re.search(pattern, query, re.IGNORECASE):
                    score += 1
            intent_scores[intent] = score
        
        # Return intent with highest score, default to PORTFOLIO_OVERVIEW
        if intent_scores:
            return max(intent_scores, key=intent_scores.get)
        return QueryIntent.PORTFOLIO_OVERVIEW
    
    def _extract_entities(self, query: str) -> List[QueryEntity]:
        """Extract entities from the query"""
        entities = []
        
        for entity_type, patterns in self.entity_patterns.items():
            for pattern, normalized_value in patterns:
                match = re.search(pattern, query, re.IGNORECASE)
                if match:
                    entities.append(QueryEntity(
                        entity_type=entity_type,
                        value=match.group(0),
                        confidence=0.8,  # Base confidence
                        normalized_value=normalized_value
                    ))
        
        return entities
    
    def _calculate_confidence(self, query: str, intent: QueryIntent, entities: List[QueryEntity]) -> float:
        """Calculate confidence score for the query understanding"""
        base_confidence = 0.6
        
        # Boost confidence based on domain vocabulary matches
        domain_matches = 0
        for category, terms in self.domain_vocabulary.items():
            for term in terms:
                if term in query:
                    domain_matches += 1
        
        vocabulary_boost = min(0.3, domain_matches * 0.05)
        
        # Boost confidence based on entity extraction
        entity_boost = min(0.2, len(entities) * 0.05)
        
        return min(1.0, base_confidence + vocabulary_boost + entity_boost)
    
    def _calculate_complexity(self, query: str, entities: List[QueryEntity]) -> float:
        """Calculate query complexity score"""
        # Base complexity factors
        word_count = len(query.split())
        entity_count = len(entities)
        
        # Complex query indicators
        complex_indicators = [
            "and", "or", "but", "with", "having", "where",
            "compare", "versus", "between", "across", "within"
        ]
        
        complexity_words = sum(1 for word in complex_indicators if word in query)
        
        # Calculate complexity score (0-1)
        complexity = min(1.0, (word_count * 0.02) + (entity_count * 0.1) + (complexity_words * 0.15))
        
        return complexity
    
    def _requires_data_analysis(self, intent: QueryIntent, entities: List[QueryEntity]) -> bool:
        """Determine if the query requires data analysis"""
        analysis_intents = [
            QueryIntent.RISK_ANALYSIS,
            QueryIntent.SCORE_ANALYSIS,
            QueryIntent.TREND_ANALYSIS,
            QueryIntent.COMPARATIVE_ANALYSIS,
            QueryIntent.GEOGRAPHIC_ANALYSIS,
            QueryIntent.SECTOR_ANALYSIS
        ]
        
        return intent in analysis_intents or len(entities) > 2
    
    def _suggest_visualizations(self, intent: QueryIntent, entities: List[QueryEntity]) -> List[str]:
        """Suggest appropriate visualizations for the query"""
        visualization_map = {
            QueryIntent.RISK_ANALYSIS: ["risk_heatmap", "distribution_chart", "alert_timeline"],
            QueryIntent.PORTFOLIO_OVERVIEW: ["portfolio_summary", "pie_chart", "kpi_cards"],
            QueryIntent.SCORE_ANALYSIS: ["trend_line", "score_distribution", "comparison_chart"],
            QueryIntent.GEOGRAPHIC_ANALYSIS: ["geographic_map", "branch_comparison", "regional_chart"],
            QueryIntent.SECTOR_ANALYSIS: ["sector_breakdown", "performance_comparison", "bubble_chart"],
            QueryIntent.TREND_ANALYSIS: ["time_series", "trend_analysis", "forecast_chart"],
            QueryIntent.COMPLIANCE_CHECK: ["compliance_dashboard", "deadline_calendar", "status_indicators"]
        }
        
        return visualization_map.get(intent, ["table", "summary_card"])
    
    def generate_contextual_response(self, query_context: QueryContext, data_results: Dict[str, Any], user_role: str = "credit_manager") -> str:
        """
        Generate contextual responses based on user role and query complexity
        """
        if user_role == "executive":
            return self._generate_executive_response(query_context, data_results)
        elif user_role == "risk_analyst":
            return self._generate_analyst_response(query_context, data_results)
        else:
            return self._generate_manager_response(query_context, data_results)
    
    def _generate_executive_response(self, context: QueryContext, data: Dict[str, Any]) -> str:
        """Generate executive-focused response with high-level insights"""
        response = "**Executive Summary**\n\n"
        
        if context.intent == QueryIntent.RISK_ANALYSIS:
            response += f"Portfolio risk assessment shows {data.get('high_risk_count', 0)} high-risk accounts requiring attention. "
            response += f"Total exposure at risk: ₹{data.get('total_exposure', 0)/********:.1f}Cr.\n\n"
            response += "**Key Actions Required:**\n"
            response += "• Immediate review of top 5 high-risk accounts\n"
            response += "• Enhanced monitoring protocols activation\n"
            response += "• Risk committee briefing recommended"
        
        return response
    
    def _generate_analyst_response(self, context: QueryContext, data: Dict[str, Any]) -> str:
        """Generate analyst-focused response with detailed metrics"""
        response = "**Detailed Analysis**\n\n"
        
        if context.intent == QueryIntent.RISK_ANALYSIS:
            response += f"**Risk Distribution Analysis:**\n"
            response += f"• High Risk: {data.get('high_risk_count', 0)} accounts\n"
            response += f"• Medium Risk: {data.get('medium_risk_count', 0)} accounts\n"
            response += f"• Low Risk: {data.get('low_risk_count', 0)} accounts\n\n"
            response += f"**Statistical Insights:**\n"
            response += f"• Portfolio concentration ratio: {data.get('concentration_ratio', 0):.2f}\n"
            response += f"• Average PD: {data.get('avg_pd', 0):.2%}\n"
            response += f"• Risk-adjusted return: {data.get('raroc', 0):.2%}"
        
        return response
    
    def _generate_manager_response(self, context: QueryContext, data: Dict[str, Any]) -> str:
        """Generate manager-focused response with actionable insights"""
        response = "**Portfolio Insights**\n\n"
        
        if context.intent == QueryIntent.RISK_ANALYSIS:
            response += f"Found {data.get('high_risk_count', 0)} MSMEs requiring immediate attention:\n\n"
            
            # Add specific recommendations
            response += "**Recommended Actions:**\n"
            response += "• Schedule field visits for top 3 accounts\n"
            response += "• Review credit limits and collateral\n"
            response += "• Initiate recovery procedures if needed\n\n"
            
            response += "**Next Steps:**\n"
            response += "• Update risk assessment within 48 hours\n"
            response += "• Prepare management report\n"
            response += "• Monitor daily for changes"
        
        return response

# Global instance for use across the application
enhanced_nlp = EnhancedCreditNLP()
