'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  ScatterChart,
  Scatter,
  AreaChart,
  Area,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';

interface PortfolioFinancialChartsProps {
  data: {
    portfolio_summary: Record<string, unknown>;
    risk_distribution: Record<string, unknown>[];
    performance_trends: Record<string, unknown>[];
    sector_analysis: Record<string, unknown>[];
    correlation_matrix: Record<string, unknown>[];
    outliers: Record<string, unknown>[];
  };
}

export default function PortfolioFinancialCharts({ data }: PortfolioFinancialChartsProps) {
  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899'];

  // Prepare risk distribution data
  const riskDistributionData = data.risk_distribution || [
    { risk_level: 'Low', count: 45, percentage: 60, avg_score: 85.2 },
    { risk_level: 'Medium', count: 22, percentage: 29.3, avg_score: 72.8 },
    { risk_level: 'High', count: 8, percentage: 10.7, avg_score: 58.4 }
  ];

  // Prepare sector performance data
  const sectorData = data.sector_analysis || [
    { sector: 'Manufacturing', avg_score: 78.5, count: 25, liquidity: 82, profitability: 75, leverage: 70, efficiency: 85 },
    { sector: 'Services', avg_score: 82.1, count: 30, liquidity: 85, profitability: 80, leverage: 78, efficiency: 85 },
    { sector: 'Trading', avg_score: 71.3, count: 15, liquidity: 75, profitability: 68, leverage: 65, efficiency: 77 },
    { sector: 'Technology', avg_score: 85.7, count: 20, liquidity: 88, profitability: 85, leverage: 82, efficiency: 88 },
    { sector: 'Agriculture', avg_score: 69.2, count: 10, liquidity: 72, profitability: 65, leverage: 68, efficiency: 72 }
  ];

  // Prepare performance trends data
  const trendsData = data.performance_trends || [
    { month: 'Jan', avg_score: 75.2, liquidity: 78.5, profitability: 72.1, leverage: 70.8, efficiency: 79.4 },
    { month: 'Feb', avg_score: 76.8, liquidity: 79.2, profitability: 74.3, leverage: 72.1, efficiency: 81.2 },
    { month: 'Mar', avg_score: 78.1, liquidity: 80.1, profitability: 75.8, leverage: 73.5, efficiency: 82.9 },
    { month: 'Apr', avg_score: 77.9, liquidity: 79.8, profitability: 76.2, leverage: 74.1, efficiency: 81.5 },
    { month: 'May', avg_score: 79.3, liquidity: 81.2, profitability: 77.5, leverage: 75.2, efficiency: 83.1 },
    { month: 'Jun', avg_score: 80.1, liquidity: 82.1, profitability: 78.9, leverage: 76.8, efficiency: 82.7 }
  ];

  // Prepare correlation scatter data
  const correlationData = data.correlation_matrix || [
    { score: 85, liquidity: 88, profitability: 82, size: 15, sector: 'Technology' },
    { score: 78, liquidity: 80, profitability: 76, size: 25, sector: 'Manufacturing' },
    { score: 82, liquidity: 85, profitability: 79, size: 30, sector: 'Services' },
    { score: 71, liquidity: 75, profitability: 68, size: 15, sector: 'Trading' },
    { score: 69, liquidity: 72, profitability: 66, size: 10, sector: 'Agriculture' }
  ];

  // Prepare outliers data
  const outliersData = data.outliers || [
    { name: 'MSME_001', score: 95, category: 'High Performer', sector: 'Technology' },
    { name: 'MSME_045', score: 35, category: 'Underperformer', sector: 'Trading' },
    { name: 'MSME_078', score: 92, category: 'High Performer', sector: 'Services' },
    { name: 'MSME_123', score: 28, category: 'Underperformer', sector: 'Manufacturing' }
  ];

  return (
    <div className="space-y-6">
      {/* Portfolio Overview */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Risk Distribution</CardTitle>
            <CardDescription>Portfolio risk level breakdown</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={riskDistributionData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ risk_level, percentage }) => `${risk_level}: ${percentage}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="count"
                >
                  {riskDistributionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value, name) => [`${value} MSMEs`, name]} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Sector Performance</CardTitle>
            <CardDescription>Average scores by business sector</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={sectorData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="sector" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="avg_score" fill="#3B82F6" name="Average Score" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Performance Trends */}
      <Card>
        <CardHeader>
          <CardTitle>Portfolio Performance Trends</CardTitle>
          <CardDescription>6-month portfolio performance evolution</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={trendsData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="avg_score" stroke="#3B82F6" strokeWidth={3} name="Overall Score" />
              <Line type="monotone" dataKey="liquidity" stroke="#10B981" strokeWidth={2} name="Liquidity" />
              <Line type="monotone" dataKey="profitability" stroke="#F59E0B" strokeWidth={2} name="Profitability" />
              <Line type="monotone" dataKey="leverage" stroke="#EF4444" strokeWidth={2} name="Leverage" />
              <Line type="monotone" dataKey="efficiency" stroke="#8B5CF6" strokeWidth={2} name="Efficiency" />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Sector Analysis Radar */}
      <Card>
        <CardHeader>
          <CardTitle>Sector Financial Health Radar</CardTitle>
          <CardDescription>Multi-dimensional sector comparison</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <RadarChart data={sectorData}>
              <PolarGrid />
              <PolarAngleAxis dataKey="sector" />
              <PolarRadiusAxis angle={90} domain={[0, 100]} />
              <Radar
                name="Liquidity"
                dataKey="liquidity"
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.1}
                strokeWidth={2}
              />
              <Radar
                name="Profitability"
                dataKey="profitability"
                stroke="#10B981"
                fill="#10B981"
                fillOpacity={0.1}
                strokeWidth={2}
              />
              <Radar
                name="Leverage"
                dataKey="leverage"
                stroke="#F59E0B"
                fill="#F59E0B"
                fillOpacity={0.1}
                strokeWidth={2}
              />
              <Radar
                name="Efficiency"
                dataKey="efficiency"
                stroke="#EF4444"
                fill="#EF4444"
                fillOpacity={0.1}
                strokeWidth={2}
              />
              <Tooltip />
            </RadarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Correlation Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Score vs Liquidity Correlation</CardTitle>
            <CardDescription>Relationship between overall score and liquidity</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <ScatterChart data={correlationData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="liquidity" name="Liquidity Score" />
                <YAxis dataKey="score" name="Overall Score" />
                <Tooltip cursor={{ strokeDasharray: '3 3' }} />
                <Scatter name="MSMEs" dataKey="score" fill="#3B82F6" />
              </ScatterChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Score vs Profitability Correlation</CardTitle>
            <CardDescription>Relationship between overall score and profitability</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <ScatterChart data={correlationData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="profitability" name="Profitability Score" />
                <YAxis dataKey="score" name="Overall Score" />
                <Tooltip cursor={{ strokeDasharray: '3 3' }} />
                <Scatter name="MSMEs" dataKey="score" fill="#10B981" />
              </ScatterChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Portfolio Health Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Portfolio Health Distribution</CardTitle>
          <CardDescription>Distribution of financial health scores across portfolio</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={trendsData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Area 
                type="monotone" 
                dataKey="liquidity" 
                stackId="1" 
                stroke="#3B82F6" 
                fill="#3B82F6" 
                fillOpacity={0.6}
                name="Liquidity"
              />
              <Area 
                type="monotone" 
                dataKey="profitability" 
                stackId="1" 
                stroke="#10B981" 
                fill="#10B981" 
                fillOpacity={0.6}
                name="Profitability"
              />
              <Area 
                type="monotone" 
                dataKey="leverage" 
                stackId="1" 
                stroke="#F59E0B" 
                fill="#F59E0B" 
                fillOpacity={0.6}
                name="Leverage"
              />
              <Area 
                type="monotone" 
                dataKey="efficiency" 
                stackId="1" 
                stroke="#EF4444" 
                fill="#EF4444" 
                fillOpacity={0.6}
                name="Efficiency"
              />
            </AreaChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Outliers Analysis */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-green-700">Top Performers</CardTitle>
            <CardDescription>MSMEs with exceptional financial performance</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {outliersData
                .filter(msme => msme.category === 'High Performer')
                .map((msme, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">{String(msme.name || 'Unknown')}</p>
                      <p className="text-xs text-green-600">{String(msme.sector || 'Unknown')}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-green-700">{String(msme.score || 0)}/100</p>
                      <Badge variant="default" className="bg-green-100 text-green-800 text-xs">
                        Top 5%
                      </Badge>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-red-700">Attention Required</CardTitle>
            <CardDescription>MSMEs requiring immediate attention</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {outliersData
                .filter(msme => msme.category === 'Underperformer')
                .map((msme, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div>
                      <p className="font-medium text-sm">{String(msme.name || 'Unknown')}</p>
                      <p className="text-xs text-red-600">{String(msme.sector || 'Unknown')}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-red-700">{String(msme.score || 0)}/100</p>
                      <Badge variant="destructive" className="text-xs">
                        Bottom 10%
                      </Badge>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sector Comparison Table */}
      <Card>
        <CardHeader>
          <CardTitle>Sector Performance Summary</CardTitle>
          <CardDescription>Detailed sector-wise financial metrics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Sector</th>
                  <th className="text-right p-2">Count</th>
                  <th className="text-right p-2">Avg Score</th>
                  <th className="text-right p-2">Liquidity</th>
                  <th className="text-right p-2">Profitability</th>
                  <th className="text-right p-2">Leverage</th>
                  <th className="text-right p-2">Efficiency</th>
                </tr>
              </thead>
              <tbody>
                {sectorData.map((sector, index) => (
                  <tr key={index} className="border-b">
                    <td className="p-2 font-medium">{String(sector.sector || 'Unknown')}</td>
                    <td className="p-2 text-right">{String(sector.count || 0)}</td>
                    <td className="p-2 text-right font-semibold">{Number(sector.avg_score || 0).toFixed(1)}</td>
                    <td className="p-2 text-right">{String(sector.liquidity || 'N/A')}</td>
                    <td className="p-2 text-right">{String(sector.profitability || 'N/A')}</td>
                    <td className="p-2 text-right">{String(sector.leverage || 'N/A')}</td>
                    <td className="p-2 text-right">{String(sector.efficiency || 'N/A')}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
