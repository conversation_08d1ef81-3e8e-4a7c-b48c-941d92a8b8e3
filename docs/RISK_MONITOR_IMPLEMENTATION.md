# Credit Chakra Risk Monitor Implementation

## Overview

The Credit Chakra Risk Monitor is a comprehensive RBI-compliant early warning system for MSME lending that provides real-time risk assessment, SMA (Special Mention Account) classification, and regulatory compliance monitoring.

## Architecture

### Backend Components

#### Enhanced Risk Data Models (`backend/models/risk_monitoring.py`)

**Key Features:**
- RBI-compliant SMA classifications (Standard, SMA-0, SMA-1, SMA-2, NPA)
- Automated DPD (Days Past Due) calculation with business rules
- Comprehensive audit trail for regulatory compliance
- Provision calculation based on RBI guidelines
- Early warning signal generation

**Core Models:**
```python
class SMAClassification(str, Enum):
    STANDARD = "standard"    # 0 days past due
    SMA_0 = "sma_0"         # 1-30 days past due
    SMA_1 = "sma_1"         # 31-60 days past due
    SMA_2 = "sma_2"         # 61-90 days past due
    NPA = "npa"             # 90+ days past due

class SMAProgressionData(BaseModel):
    # Enhanced SMA tracking with audit trail
    # Automatic severity scoring
    # Utilization ratio calculation
    # Early warning signal integration
```

**Utility Functions:**
- `calculate_days_past_due()`: RBI-compliant DPD calculation
- `get_sma_classification_from_dpd()`: Automatic SMA classification
- `calculate_provision_amount()`: Provision requirement calculation
- `generate_early_warning_signals()`: Risk signal generation

#### Real-time Risk Classification Service (`backend/services/realtime_risk_monitor.py`)

**Enhanced Features:**
- Automatic MSME SMA status classification
- Real-time portfolio risk metrics calculation
- SMA heatmap data generation for visualization
- Portfolio-level SMA summary with trends
- Risk event generation with severity scoring
- RBI compliance monitoring

**Key Methods:**
```python
async def classify_msme_sma_status(msme_id, due_date, outstanding_amount)
async def get_sma_heatmap_data()
async def get_portfolio_sma_summary()
async def monitor_sma_progression()
async def generate_risk_events()
```

#### API Endpoints (`backend/routes/dashboard.py`)

**New Risk Monitor Endpoints:**
- `GET /api/dashboard/risk-monitor/sma-heatmap` - SMA distribution visualization data
- `GET /api/dashboard/risk-monitor/portfolio-summary` - Portfolio-level SMA metrics
- `GET /api/dashboard/risk-monitor/real-time-events` - Live risk events (includes SMA progression alerts)
- `GET /api/dashboard/risk-monitor/portfolio-metrics` - Real-time portfolio metrics
- `POST /api/dashboard/risk-monitor/classify-sma/{msme_id}` - Individual SMA classification

### Frontend Components

#### Special Mention Accounts Component (`frontend/src/components/risk-monitor/SpecialMentionAccounts.tsx`)

**Features:**
- Interactive SMA heatmap visualization
- Business type and geographic risk distribution
- Prioritized action list for credit officers
- Real-time data synchronization
- 2-second load requirement compliance
- Emerald color palette consistency

**Component Structure:**
```tsx
export function SpecialMentionAccounts() {
  // State management for SMA data
  // Real-time API integration
  // Interactive tabs: Overview, Heatmap, Actions
  // Responsive design with skeleton loading
}
```

#### Risk Monitor Dashboard (`frontend/src/components/risk-monitor/RiskMonitorDashboard.tsx`)

**Features:**
- Comprehensive risk monitoring dashboard
- Portfolio health overview cards
- Real-time risk event streaming
- RBI compliance status monitoring
- Cross-component data synchronization

**Tab Structure:**
1. **SMA Monitor** - Special Mention Accounts component
2. **Real-time Events** - Live risk event monitoring
3. **Compliance** - RBI compliance dashboard

## RBI Compliance Features

### SMA Classification System

**Automatic Classification:**
- Standard: 0 days past due
- SMA-0: 1-30 days past due
- SMA-1: 31-60 days past due  
- SMA-2: 61-90 days past due
- NPA: 90+ days past due

**Provision Requirements:**
- Standard: 0%
- SMA-0: 0.25%
- SMA-1: 0.5%
- SMA-2: 1.0%
- NPA: 15.0%

### Audit Trail System

**Comprehensive Tracking:**
- All SMA classification changes
- Provision updates
- User actions and acknowledgments
- Regulatory impact flagging
- Timestamp and user attribution

### Early Warning Signals

**Automated Detection:**
- Payment overdue alerts
- High credit utilization warnings
- Declining credit score trends
- SMA progression risk indicators
- Stress indicator accumulation

## Performance Requirements

### 2-Second Load Requirement

**Implementation:**
- Optimized API endpoints with caching
- Efficient database queries
- Skeleton loading screens
- Minimum 2-second load time enforcement for smooth UX
- Real-time updates every 30 seconds

**Performance Monitoring:**
- Load time tracking for all endpoints
- Concurrent request handling
- Memory usage optimization
- Response size efficiency

### Scalability Features

**Architecture Preparation:**
- Support for 1000+ MSME records
- Batch processing capabilities
- Real-time WebSocket integration ready
- ML-powered recommendations framework
- Extensible component architecture

## Testing Coverage

### Backend Tests (`backend/tests/test_risk_monitor.py`)

**Test Categories:**
- SMA classification accuracy
- DPD calculation validation
- Provision calculation verification
- API endpoint functionality
- Real-time service performance
- RBI compliance features

### Frontend Tests (`frontend/src/components/risk-monitor/__tests__/`)

**Test Coverage:**
- Component rendering
- User interaction handling
- API integration
- Error state management
- Performance requirements
- Accessibility compliance

### Performance Tests (`backend/tests/test_performance.py`)

**Performance Validation:**
- 2-second load requirement verification
- Concurrent request handling
- Memory usage efficiency
- Scalability under load
- RBI compliance calculation speed

## Integration Points

### Existing System Integration

**Portfolio Dashboard:**
- Cross-component data synchronization
- Consistent emerald color palette
- Modal popup patterns
- Navigation integration

**API Client Updates:**
- New risk monitoring endpoints
- Error handling consistency
- Response format standardization

### Data Flow

```
MSME Data → DPD Calculation → SMA Classification → Risk Events → Dashboard Visualization
     ↓              ↓               ↓              ↓              ↓
Audit Trail → Provision Calc → Early Warnings → Alerts → User Actions
```

## Deployment Considerations

### Environment Setup

**Backend Requirements:**
- Python 3.8+
- FastAPI framework
- Pydantic models
- Async/await support

**Frontend Requirements:**
- Next.js 14+
- TypeScript
- ShadCN UI components
- React 18+

### Configuration

**Environment Variables:**
- API endpoints configuration
- Database connection settings
- Real-time update intervals
- Performance monitoring settings

## Future Enhancements

### Planned Features

1. **Advanced ML Integration**
   - Predictive SMA progression models
   - Risk score forecasting
   - Behavioral pattern analysis

2. **Enhanced Visualization**
   - Interactive risk heatmaps
   - Trend analysis charts
   - Geographic risk mapping

3. **Regulatory Reporting**
   - Automated RBI report generation
   - Compliance dashboard enhancements
   - Audit trail export functionality

4. **Real-time Notifications**
   - WebSocket integration
   - Push notification system
   - Email/SMS alert integration

## Maintenance and Support

### Monitoring

**Key Metrics:**
- API response times
- SMA classification accuracy
- User engagement metrics
- System performance indicators

### Documentation Updates

**Regular Reviews:**
- RBI guideline changes
- Performance optimization
- Feature enhancement documentation
- User feedback integration

---

**Author:** Credit Chakra Team  
**Version:** 1.0.0  
**Last Updated:** 2024-01-20
