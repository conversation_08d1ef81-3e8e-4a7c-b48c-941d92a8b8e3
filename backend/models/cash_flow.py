from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from enum import Enum

class CashFlowCategory(str, Enum):
    OPERATING = "OPERATING"
    INVESTING = "INVESTING"
    FINANCING = "FINANCING"

class CashFlowType(str, Enum):
    INFLOW = "INFLOW"
    OUTFLOW = "OUTFLOW"

class SeasonalPattern(str, Enum):
    HIGH = "HIGH"
    MEDIUM = "MEDIUM"
    LOW = "LOW"

class CashFlowItem(BaseModel):
    """Individual cash flow item"""
    item_id: str = Field(..., description="Unique item identifier")
    category: CashFlowCategory = Field(..., description="Cash flow category")
    flow_type: CashFlowType = Field(..., description="Inflow or outflow")
    
    # Item details
    description: str = Field(..., description="Item description")
    amount: float = Field(..., description="Cash flow amount")
    transaction_date: date = Field(..., description="Transaction date")
    
    # Classification
    subcategory: Optional[str] = Field(None, description="Subcategory")
    account_source: Optional[str] = Field(None, description="Source account")
    counterparty: Optional[str] = Field(None, description="Counterparty")
    
    # Metadata
    is_recurring: bool = Field(False, description="Is recurring item")
    frequency: Optional[str] = Field(None, description="Frequency if recurring")
    confidence_score: float = Field(100.0, description="Classification confidence")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)

class CashFlowStatement(BaseModel):
    """Comprehensive cash flow statement"""
    statement_id: str = Field(..., description="Statement identifier")
    msme_id: str = Field(..., description="MSME identifier")
    
    # Period information
    period_start: date = Field(..., description="Period start date")
    period_end: date = Field(..., description="Period end date")
    period_type: str = Field(..., description="Period type (monthly/quarterly/annual)")
    
    # Opening and closing cash
    opening_cash: float = Field(0.0, description="Opening cash balance")
    closing_cash: float = Field(0.0, description="Closing cash balance")
    net_change_in_cash: float = Field(0.0, description="Net change in cash")
    
    # Operating activities
    operating_cash_inflows: float = Field(0.0, description="Operating cash inflows")
    operating_cash_outflows: float = Field(0.0, description="Operating cash outflows")
    net_operating_cash_flow: float = Field(0.0, description="Net operating cash flow")
    
    # Operating subcategories
    revenue_collections: float = Field(0.0, description="Revenue collections")
    supplier_payments: float = Field(0.0, description="Payments to suppliers")
    employee_payments: float = Field(0.0, description="Employee payments")
    tax_payments: float = Field(0.0, description="Tax payments")
    interest_payments: float = Field(0.0, description="Interest payments")
    other_operating_inflows: float = Field(0.0, description="Other operating inflows")
    other_operating_outflows: float = Field(0.0, description="Other operating outflows")
    
    # Investing activities
    investing_cash_inflows: float = Field(0.0, description="Investing cash inflows")
    investing_cash_outflows: float = Field(0.0, description="Investing cash outflows")
    net_investing_cash_flow: float = Field(0.0, description="Net investing cash flow")
    
    # Investing subcategories
    asset_sales: float = Field(0.0, description="Proceeds from asset sales")
    asset_purchases: float = Field(0.0, description="Asset purchases")
    investment_income: float = Field(0.0, description="Investment income")
    investment_purchases: float = Field(0.0, description="Investment purchases")
    
    # Financing activities
    financing_cash_inflows: float = Field(0.0, description="Financing cash inflows")
    financing_cash_outflows: float = Field(0.0, description="Financing cash outflows")
    net_financing_cash_flow: float = Field(0.0, description="Net financing cash flow")
    
    # Financing subcategories
    loan_proceeds: float = Field(0.0, description="Loan proceeds")
    loan_repayments: float = Field(0.0, description="Loan repayments")
    equity_contributions: float = Field(0.0, description="Equity contributions")
    dividend_payments: float = Field(0.0, description="Dividend payments")
    
    # Quality metrics
    data_completeness: float = Field(0.0, description="Data completeness percentage")
    classification_accuracy: float = Field(0.0, description="Classification accuracy")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class SeasonalAnalysis(BaseModel):
    """Seasonal cash flow pattern analysis"""
    msme_id: str = Field(..., description="MSME identifier")
    analysis_period: str = Field(..., description="Analysis period (e.g., 2023)")
    
    # Monthly patterns
    monthly_patterns: Dict[int, SeasonalPattern] = Field(default_factory=dict, description="Monthly seasonal patterns")
    monthly_averages: Dict[int, float] = Field(default_factory=dict, description="Monthly average cash flows")
    monthly_volatility: Dict[int, float] = Field(default_factory=dict, description="Monthly volatility")
    
    # Seasonal metrics
    seasonal_index: float = Field(0.0, description="Overall seasonal variation index")
    peak_months: List[int] = Field(default_factory=list, description="Peak cash flow months")
    low_months: List[int] = Field(default_factory=list, description="Low cash flow months")
    
    # Business cycle analysis
    cycle_length: Optional[int] = Field(None, description="Business cycle length in months")
    cycle_amplitude: float = Field(0.0, description="Cycle amplitude")
    trend_direction: str = Field("stable", description="Overall trend direction")
    
    # Predictability metrics
    predictability_score: float = Field(0.0, description="Cash flow predictability score")
    volatility_score: float = Field(0.0, description="Cash flow volatility score")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)

class CashFlowForecast(BaseModel):
    """Cash flow forecast model"""
    forecast_id: str = Field(..., description="Forecast identifier")
    msme_id: str = Field(..., description="MSME identifier")
    
    # Forecast parameters
    forecast_start: date = Field(..., description="Forecast start date")
    forecast_end: date = Field(..., description="Forecast end date")
    forecast_horizon: int = Field(..., description="Forecast horizon in months")
    
    # Forecast methodology
    model_type: str = Field(..., description="Forecasting model type")
    confidence_level: float = Field(0.0, description="Forecast confidence level")
    
    # Monthly forecasts
    monthly_forecasts: List[Dict[str, Any]] = Field(default_factory=list, description="Monthly forecast data")
    
    # Forecast summary
    total_inflow_forecast: float = Field(0.0, description="Total forecasted inflows")
    total_outflow_forecast: float = Field(0.0, description="Total forecasted outflows")
    net_cash_flow_forecast: float = Field(0.0, description="Net cash flow forecast")
    
    # Risk scenarios
    optimistic_scenario: float = Field(0.0, description="Optimistic scenario forecast")
    pessimistic_scenario: float = Field(0.0, description="Pessimistic scenario forecast")
    most_likely_scenario: float = Field(0.0, description="Most likely scenario forecast")
    
    # Cash flow gaps
    projected_gaps: List[Dict[str, Any]] = Field(default_factory=list, description="Projected cash flow gaps")
    surplus_periods: List[Dict[str, Any]] = Field(default_factory=list, description="Projected surplus periods")
    
    # Model performance
    historical_accuracy: float = Field(0.0, description="Historical forecast accuracy")
    mean_absolute_error: float = Field(0.0, description="Mean absolute error")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class WorkingCapitalAnalysis(BaseModel):
    """Working capital cycle analysis"""
    msme_id: str = Field(..., description="MSME identifier")
    analysis_date: date = Field(..., description="Analysis date")
    
    # Working capital components
    current_assets: float = Field(0.0, description="Current assets")
    current_liabilities: float = Field(0.0, description="Current liabilities")
    working_capital: float = Field(0.0, description="Working capital")
    
    # Cycle components
    inventory_days: float = Field(0.0, description="Days inventory outstanding")
    receivables_days: float = Field(0.0, description="Days sales outstanding")
    payables_days: float = Field(0.0, description="Days payable outstanding")
    
    # Cash conversion cycle
    cash_conversion_cycle: float = Field(0.0, description="Cash conversion cycle in days")
    operating_cycle: float = Field(0.0, description="Operating cycle in days")
    
    # Efficiency metrics
    working_capital_turnover: float = Field(0.0, description="Working capital turnover ratio")
    working_capital_to_sales: float = Field(0.0, description="Working capital to sales ratio")
    
    # Trend analysis
    cycle_trend: str = Field("stable", description="Cash conversion cycle trend")
    efficiency_score: float = Field(0.0, description="Working capital efficiency score")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)

class CashFlowAnalytics(BaseModel):
    """Comprehensive cash flow analytics"""
    msme_id: str = Field(..., description="MSME identifier")
    
    # Cash flow health metrics
    operating_cash_flow_ratio: float = Field(0.0, description="Operating cash flow ratio")
    cash_flow_coverage_ratio: float = Field(0.0, description="Cash flow coverage ratio")
    cash_flow_to_debt_ratio: float = Field(0.0, description="Cash flow to debt ratio")
    free_cash_flow: float = Field(0.0, description="Free cash flow")
    
    # Liquidity metrics
    cash_ratio: float = Field(0.0, description="Cash ratio")
    quick_ratio: float = Field(0.0, description="Quick ratio")
    current_ratio: float = Field(0.0, description="Current ratio")
    
    # Efficiency metrics
    cash_turnover_ratio: float = Field(0.0, description="Cash turnover ratio")
    receivables_turnover: float = Field(0.0, description="Receivables turnover")
    inventory_turnover: float = Field(0.0, description="Inventory turnover")
    payables_turnover: float = Field(0.0, description="Payables turnover")
    
    # Quality metrics
    cash_flow_quality: float = Field(0.0, description="Cash flow quality score")
    earnings_quality: float = Field(0.0, description="Earnings quality score")
    accruals_ratio: float = Field(0.0, description="Accruals ratio")
    
    # Volatility and risk
    cash_flow_volatility: float = Field(0.0, description="Cash flow volatility")
    cash_flow_predictability: float = Field(0.0, description="Cash flow predictability")
    liquidity_risk_score: float = Field(0.0, description="Liquidity risk score")
    
    # Growth metrics
    cash_flow_growth_rate: float = Field(0.0, description="Cash flow growth rate")
    operating_leverage: float = Field(0.0, description="Operating leverage")
    
    # Overall scores
    cash_flow_health_score: float = Field(0.0, description="Overall cash flow health score")
    liquidity_score: float = Field(0.0, description="Liquidity score")
    efficiency_score: float = Field(0.0, description="Efficiency score")
    
    last_updated: datetime = Field(default_factory=datetime.utcnow)

class CashFlowRequest(BaseModel):
    """Request model for cash flow analysis"""
    msme_id: str = Field(..., description="MSME identifier")
    period_start: date = Field(..., description="Analysis start date")
    period_end: date = Field(..., description="Analysis end date")
    include_forecast: bool = Field(True, description="Include forecast analysis")
    include_seasonal: bool = Field(True, description="Include seasonal analysis")
    include_working_capital: bool = Field(True, description="Include working capital analysis")
    forecast_horizon: int = Field(12, description="Forecast horizon in months")

class CashFlowResponse(BaseModel):
    """Response model for cash flow analysis"""
    msme_id: str
    
    cash_flow_statements: List[CashFlowStatement] = Field(default_factory=list)
    seasonal_analysis: Optional[SeasonalAnalysis] = None
    forecast: Optional[CashFlowForecast] = None
    working_capital_analysis: Optional[WorkingCapitalAnalysis] = None
    analytics: Optional[CashFlowAnalytics] = None
    
    data_freshness: datetime = Field(default_factory=datetime.utcnow)
    source: str = Field("CASH_FLOW_ENGINE", description="Data source")
    status: str = Field("success", description="Response status")
    message: Optional[str] = None
