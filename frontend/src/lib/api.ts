import { MSME, Analytics, ScoreDetails, Signal, SignalInput, Nudge, NudgeRequest, EnhancedScoreDetails } from '@/types';

// Enhanced AI Copilot types with better type safety
export interface ConversationContext {
  conversation_history?: ConversationMessage[];
  user_intent?: 'analysis' | 'query' | 'action' | 'insight';
  timestamp?: string;
  conversation_context?: Record<string, unknown>;
  user_id?: string;
  session_id?: string;
  query_metadata?: QueryMetadata;
}

export interface QueryMetadata {
  query_length: number;
  query_type: 'question' | 'command';
  contains_numbers: boolean;
  urgency_indicators: boolean;
}

export interface ConversationMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

export interface CopilotQuery {
  query: string;
  context?: ConversationContext;
}

export interface AIAnalytics {
  portfolio_health_score: number;
  total_msmes: number;
  risk_distribution?: {
    high: number;
    medium: number;
    low: number;
  };
}

export interface PredictiveInsight {
  title: string;
  description: string;
  confidence?: number;
  impact?: string;
  timeline?: string;
}

export interface CopilotResponseData {
  response_type?: 'general' | 'analytics' | 'risk' | 'compliance' | 'insights';
  confidence?: number;
  topics?: string[];
  ai_analytics?: AIAnalytics;
  predictive_insights?: PredictiveInsight[];
  ai_recommendations?: string[];
  msme_data?: MSME[];
  risk_alerts?: Array<{
    msme_id: string;
    risk_level: 'high' | 'medium' | 'low';
    description: string;
  }>;
}

export interface CopilotResponse {
  response: string;
  data?: CopilotResponseData;
  suggestions?: string[];
  query_id: string;
  timestamp: string;
}

export interface ChatMessage {
  id: string;
  query: string;
  response: string;
  timestamp: string;
  user_id?: string;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

class ApiError extends Error {
  constructor(message: string, public status: number) {
    super(message);
    this.name = 'ApiError';
  }
}

async function fetchApi<T>(endpoint: string, options?: {
  method?: string;
  body?: unknown;
}): Promise<T> {
  try {
    const fetchOptions: RequestInit = {
      method: options?.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    };

    if (options?.body) {
      fetchOptions.body = JSON.stringify(options.body);
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, fetchOptions);

    if (!response.ok) {
      const errorText = await response.text();
      throw new ApiError(`HTTP error! status: ${response.status} - ${errorText}`, response.status);
    }

    return await response.json();
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }

    // Handle specific error types
    if (error instanceof TypeError && error.message.includes('circular structure')) {
      throw new ApiError('Data serialization error: Please check your request data', 400);
    }

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new ApiError(`Network error: ${errorMessage}`, 0);
  }
}

export const api = {
  // Dashboard Analytics
  async getAnalytics(): Promise<Analytics> {
    return fetchApi<Analytics>('/api/dashboard/analytics');
  },

  // Portfolio Management
  async getPortfolio(): Promise<MSME[]> {
    return fetchApi<MSME[]>('/api/dashboard/portfolio/legacy');
  },

  // MSME Management
  async getMSME(id: string): Promise<MSME> {
    return fetchApi<MSME>(`/msme/${id}`);
  },

  async getMSMEScore(id: string): Promise<ScoreDetails> {
    return fetchApi<ScoreDetails>(`/msme/${id}/score`);
  },

  async getMSMEEnhancedScore(id: string): Promise<EnhancedScoreDetails> {
    return fetchApi<EnhancedScoreDetails>(`/msme/${id}/enhanced-score`);
  },

  // Signal Management
  async getMSMESignals(id: string, limit?: number): Promise<Signal[]> {
    const endpoint = `/msme/${id}/signals${limit ? `?limit=${limit}` : ''}`;
    return fetchApi<Signal[]>(endpoint);
  },

  async addSignalToMSME(msmeId: string, signalInput: SignalInput): Promise<{
    signal_id: string;
    msme_id: string;
    signal_data: Signal;
    score_update: Record<string, unknown>;
  }> {
    return fetchApi(`/msme/${msmeId}/signals`, {
      method: 'POST',
      body: signalInput,
    });
  },

  // Nudge Management
  async getMSMENudges(id: string, limit?: number): Promise<Nudge[]> {
    const endpoint = `/msme/${id}/nudges${limit ? `?limit=${limit}` : ''}`;
    return fetchApi<Nudge[]>(endpoint);
  },

  async sendNudgeToMSME(msmeId: string, nudgeRequest: NudgeRequest): Promise<Nudge> {
    return fetchApi<Nudge>(`/msme/${msmeId}/nudges`, {
      method: 'POST',
      body: nudgeRequest,
    });
  },

  async sendBulkNudges(msmeIds: string[], nudgeRequest: NudgeRequest): Promise<{
    total_requested: number;
    successful: number;
    failed: number;
    nudge_ids: string[];
    errors: Array<{ msme_id: string; error: string }>;
  }> {
    return fetchApi('/nudges/bulk', {
      method: 'POST',
      body: {
        msme_ids: msmeIds,
        trigger_type: nudgeRequest.trigger_type,
        message: nudgeRequest.message,
        medium: nudgeRequest.medium,
        metadata: nudgeRequest.metadata,
      },
    });
  },

  // Health check
  async healthCheck(): Promise<{ status: string; service: string }> {
    return fetchApi<{ status: string; service: string }>('/health');
  },

  // AI Copilot
  async askCopilot(query: CopilotQuery): Promise<CopilotResponse> {
    return fetchApi<CopilotResponse>('/api/copilot/ask', {
      method: 'POST',
      body: query,
    });
  },

  async getCopilotHistory(limit?: number, offset?: number): Promise<ChatMessage[]> {
    const params = new URLSearchParams();
    if (limit) params.append('limit', limit.toString());
    if (offset) params.append('offset', offset.toString());

    const endpoint = `/api/copilot/history${params.toString() ? `?${params.toString()}` : ''}`;
    return fetchApi<ChatMessage[]>(endpoint);
  },

  async submitCopilotFeedback(queryId: string, rating: number, feedback?: string): Promise<{ message: string }> {
    return fetchApi<{ message: string }>('/api/copilot/feedback', {
      method: 'POST',
      body: {
        query_id: queryId,
        rating,
        feedback,
      },
    });
  },

  async getCopilotInsights(): Promise<{ insights: Record<string, unknown>[] }> {
    return fetchApi<{ insights: Record<string, unknown>[] }>('/api/copilot/insights');
  },

  async getComplianceHealth(): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>('/api/copilot/compliance-health');
  },

  async getAdvancedRiskAnalysis(msmeId?: string): Promise<Record<string, unknown>> {
    const endpoint = msmeId
      ? `/api/copilot/advanced-risk-analysis?msme_id=${msmeId}`
      : '/api/copilot/advanced-risk-analysis';
    return fetchApi<Record<string, unknown>>(endpoint);
  },

  async getRealtimePortfolioMetrics(): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>('/api/copilot/realtime-portfolio-metrics');
  },

  async getBehavioralAnalysis(msmeId: string): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>(`/api/copilot/behavioral-analysis/${msmeId}`);
  },

  // Enhanced AI Copilot methods
  async getPredictiveAnalytics(msmeId?: string, forecastType?: string, timeHorizon?: string): Promise<Record<string, unknown>> {
    const params = new URLSearchParams();
    if (msmeId) params.append('msme_id', msmeId);
    if (forecastType) params.append('forecast_type', forecastType);
    if (timeHorizon) params.append('time_horizon', timeHorizon);

    const endpoint = `/api/copilot/predictive-analytics${params.toString() ? `?${params.toString()}` : ''}`;
    return fetchApi<Record<string, unknown>>(endpoint);
  },

  async getIntelligentInsights(userId?: string): Promise<Record<string, unknown>> {
    const params = new URLSearchParams();
    if (userId) params.append('user_id', userId);

    const endpoint = `/api/copilot/intelligent-insights${params.toString() ? `?${params.toString()}` : ''}`;
    return fetchApi<Record<string, unknown>>(endpoint);
  },

  // Enhanced portfolio analytics
  async getPortfolioForecast(timeHorizon: string = '6_months'): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>(`/api/copilot/predictive-analytics?time_horizon=${timeHorizon}`);
  },

  async getStressTestResults(): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>('/api/copilot/stress-test');
  },

  // Enhanced AI-powered endpoints
  async generateSmartNudges(msmeId: string): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>(`/api/copilot/generate-smart-nudges?msme_id=${msmeId}`, {
      method: 'POST'
    });
  },

  async getAIBusinessAnalysis(msmeId: string): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>(`/api/copilot/ai-business-analysis/${msmeId}`);
  },

  async getAdvancedPortfolioAnalytics(): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>('/api/copilot/intelligent-insights');
  },

  async getPatternAnalysis(): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>('/api/copilot/pattern-analysis');
  },

  // Real-time monitoring
  async getRealtimeAlerts(): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>('/api/copilot/realtime-alerts');
  },

  async getModelPerformanceMetrics(): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>('/api/copilot/model-performance');
  },

  // Risk Monitoring
  async getSMAHeatmap(): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>('/api/dashboard/risk-monitor/sma-heatmap');
  },

  async getPortfolioSMASummary(): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>('/api/dashboard/risk-monitor/portfolio-summary');
  },

  async getRealTimeRiskEvents(): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>('/api/dashboard/risk-monitor/real-time-events');
  },

  async getPortfolioRiskMetrics(): Promise<Record<string, unknown>> {
    return fetchApi<Record<string, unknown>>('/api/dashboard/risk-monitor/portfolio-metrics');
  },

  async classifyMSMESMA(msmeId: string, dueDate: string, outstandingAmount: number, lastPaymentDate?: string): Promise<Record<string, unknown>> {
    const params = new URLSearchParams({
      due_date: dueDate,
      outstanding_amount: outstandingAmount.toString(),
    });

    if (lastPaymentDate) {
      params.append('last_payment_date', lastPaymentDate);
    }

    return fetchApi<Record<string, unknown>>(`/api/dashboard/risk-monitor/classify-sma/${msmeId}?${params.toString()}`, {
      method: 'POST',
    });
  },

  // Reports Management
  async getAvailableReports(): Promise<{
    available_reports: string[];
    description: Record<string, string>;
  }> {
    return fetchApi('/api/reports/');
  },

  async generatePortfolioSummaryReport(startDate: string, endDate: string, generatedBy?: string): Promise<Record<string, unknown>> {
    const params = new URLSearchParams({
      start_date: startDate,
      end_date: endDate,
    });

    if (generatedBy) {
      params.append('generated_by', generatedBy);
    }

    return fetchApi(`/api/reports/portfolio-summary?${params.toString()}`, {
      method: 'POST',
    });
  },

  async generateRiskExposureReport(startDate: string, endDate: string, generatedBy?: string): Promise<Record<string, unknown>> {
    const params = new URLSearchParams({
      start_date: startDate,
      end_date: endDate,
    });

    if (generatedBy) {
      params.append('generated_by', generatedBy);
    }

    return fetchApi(`/api/reports/risk-exposure?${params.toString()}`, {
      method: 'POST',
    });
  },

  async generateComplianceRegulatoryReport(startDate: string, endDate: string, generatedBy?: string): Promise<Record<string, unknown>> {
    const params = new URLSearchParams({
      start_date: startDate,
      end_date: endDate,
    });

    if (generatedBy) {
      params.append('generated_by', generatedBy);
    }

    return fetchApi(`/api/reports/compliance-regulatory?${params.toString()}`, {
      method: 'POST',
    });
  },

  async generatePerformanceTrendsReport(startDate: string, endDate: string, generatedBy?: string): Promise<Record<string, unknown>> {
    const params = new URLSearchParams({
      start_date: startDate,
      end_date: endDate,
    });

    if (generatedBy) {
      params.append('generated_by', generatedBy);
    }

    return fetchApi(`/api/reports/performance-trends?${params.toString()}`, {
      method: 'POST',
    });
  },

  async generateDetailedMSMEProfileReport(msmeId: string, startDate: string, endDate: string, generatedBy?: string): Promise<Record<string, unknown>> {
    const params = new URLSearchParams({
      msme_id: msmeId,
      start_date: startDate,
      end_date: endDate,
    });

    if (generatedBy) {
      params.append('generated_by', generatedBy);
    }

    return fetchApi(`/api/reports/detailed-msme-profile?${params.toString()}`, {
      method: 'POST',
    });
  },

  async exportReport(
    reportType: string,
    exportFormat: 'pdf' | 'csv',
    startDate: string,
    endDate: string,
    msmeId?: string,
    generatedBy?: string
  ): Promise<{
    report_id: string;
    export_format: string;
    file_url: string;
    file_size: number;
    expires_at: string;
    status: string;
  }> {
    const params = new URLSearchParams({
      export_format: exportFormat,
      start_date: startDate,
      end_date: endDate,
    });

    if (msmeId) {
      params.append('msme_id', msmeId);
    }

    if (generatedBy) {
      params.append('generated_by', generatedBy);
    }

    return fetchApi(`/api/reports/export/${reportType}?${params.toString()}`, {
      method: 'POST',
    });
  }
};
