# Credit Chakra Firestore Schema

## Collections Structure

### 1. MSMEs Collection (`/msmes/{msme_id}`)

Main collection storing MSME business profiles.

```javascript
{
  msme_id: string,           // Unique identifier
  name: string,              // Business name
  business_type: string,     // "retail" | "b2b" | "manufacturing" | "services"
  location: string,          // Business location
  created_at: timestamp,     // Profile creation date
  score: number,             // Credit score (0-1000)
  risk_band: string,         // "green" | "yellow" | "red"
  tags: string[]             // Business tags/categories
}
```

### 2. Signals Subcollection (`/msmes/{msme_id}/signals/{signal_id}`)

Stores data signals from various sources for each MSME.

```javascript
{
  signal_id: string,         // Unique signal identifier
  msme_id: string,          // Parent MSME ID
  source: string,           // "gst" | "upi" | "reviews" | "justdial" | "instagram" | "maps"
  value: any,               // Raw signal value (varies by source)
  normalized: number,       // Normalized score (0-1)
  timestamp: timestamp,     // Signal collection time
  metadata: object          // Additional source-specific data
}
```

#### Signal Value Formats by Source

**GST Signals:**
```javascript
value: number  // Monthly turnover amount
metadata: {
  month: string,      // "YYYY-MM"
  verified: boolean   // GST verification status
}
```

**UPI Signals:**
```javascript
value: {
  transaction_count: number,  // Number of transactions
  volume: number             // Total transaction volume
}
metadata: {
  merchant_id: string,  // UPI merchant identifier
  period: string        // Data collection period
}
```

**Reviews Signals:**
```javascript
value: {
  average_rating: number,  // Average rating (1-5)
  review_count: number     // Total number of reviews
}
metadata: {
  platform: string,    // "google_maps" | "zomato" | "justdial"
  last_updated: timestamp
}
```

**Social Media Signals (Instagram/JustDial):**
```javascript
value: {
  followers: number,        // Follower count
  engagement_rate: number   // Engagement rate (0-1)
}
metadata: {
  handle: string,          // Social media handle
  verified: boolean,       // Account verification status
  last_post: timestamp     // Last activity timestamp
}
```

### 3. Nudges Subcollection (`/msmes/{msme_id}/nudges/{nudge_id}`)

Stores notification/nudge records for each MSME.

```javascript
{
  nudge_id: string,          // Unique nudge identifier
  msme_id: string,          // Parent MSME ID
  trigger_type: string,     // "score_drop" | "score_improvement" | "new_signal" | "risk_band_change"
  message: string,          // Notification message
  medium: string,           // "whatsapp" | "email" | "sms" | "push_notification"
  sent_at: timestamp,       // When nudge was sent
  status: string,           // "pending" | "sent" | "failed" | "delivered"
  metadata: object          // Additional nudge-specific data
}
```

### 4. System Collection (`/system/rules`)

Stores system-wide configuration and rules.

```javascript
{
  scoring_rules: {
    signal_weights: {
      gst: 0.3,
      upi: 0.25,
      reviews: 0.2,
      justdial: 0.1,
      instagram: 0.1,
      maps: 0.05
    },
    risk_bands: {
      green: { min_score: 700, max_score: 1000 },
      yellow: { min_score: 400, max_score: 699 },
      red: { min_score: 0, max_score: 399 }
    },
    normalization_rules: { ... }
  },
  nudge_triggers: { ... },
  business_categories: { ... }
}
```

## Indexes

### Composite Indexes Required

1. **Signals by MSME and Source:**
   - Collection: `msmes/{msme_id}/signals`
   - Fields: `source` (Ascending), `timestamp` (Descending)

2. **Signals by MSME and Timestamp:**
   - Collection: `msmes/{msme_id}/signals`
   - Fields: `timestamp` (Descending)

3. **Nudges by MSME and Status:**
   - Collection: `msmes/{msme_id}/nudges`
   - Fields: `status` (Ascending), `sent_at` (Descending)

4. **MSMEs by Risk Band:**
   - Collection: `msmes`
   - Fields: `risk_band` (Ascending), `score` (Descending)

5. **MSMEs by Business Type:**
   - Collection: `msmes`
   - Fields: `business_type` (Ascending), `created_at` (Descending)

## Security Rules

- **Authentication Required**: All operations require authenticated users
- **MSME Access**: Users can read/write their own MSME data
- **Admin Access**: Admin users can access all data
- **Public Access**: Limited public read access for specific endpoints

## Data Flow

1. **MSME Creation**: New business profile created in `/msmes/{msme_id}`
2. **Signal Addition**: Data signals added to `/msmes/{msme_id}/signals/{signal_id}`
3. **Score Calculation**: Automatic score recalculation triggered by new signals
4. **Risk Band Update**: Risk band updated based on new score
5. **Nudge Generation**: Automatic nudges triggered by score/band changes
6. **Nudge Delivery**: Nudges sent via configured medium and status updated

## Query Patterns

### Common Queries

```javascript
// Get MSME with latest signals
db.collection('msmes').doc(msmeId)
  .collection('signals')
  .orderBy('timestamp', 'desc')
  .limit(10)

// Get MSMEs by risk band
db.collection('msmes')
  .where('risk_band', '==', 'red')
  .orderBy('score', 'asc')

// Get pending nudges
db.collection('msmes').doc(msmeId)
  .collection('nudges')
  .where('status', '==', 'pending')
  .orderBy('sent_at', 'desc')

// Get signals by source
db.collection('msmes').doc(msmeId)
  .collection('signals')
  .where('source', '==', 'gst')
  .orderBy('timestamp', 'desc')
```

## Backup and Migration

- **Daily Backups**: Automated daily backups of all collections
- **Data Export**: Support for JSON export of MSME data
- **Migration Scripts**: Version-controlled migration scripts for schema changes
