"""
Reports API routes for Credit Chakra MSME credit monitoring dashboard.

This module provides API endpoints for generating and exporting reports including:
- Portfolio Summary Report
- Risk Exposure Report
- Compliance & Regulatory Report
- Performance Trends Report
- Detailed MSME Profile Report

Author: Credit Chakra Team
Version: 1.0.0
"""
from __future__ import annotations

from datetime import date, timezone, datetime, timedelta
from typing import Any, Dict, List, Optional
import logging
import os

from fastapi import APIRouter, HTTPException, Query, status, BackgroundTasks
from fastapi.responses import FileResponse
from pydantic import BaseModel

from models.reports import (
    ReportType, ReportFormat, ReportGenerationRequest, ReportExportResponse,
    PortfolioSummaryReport, RiskExposureReport, ComplianceRegulatoryReport,
    PerformanceTrendsReport, DetailedMSMEProfileReport
)
from services.reports_service import reports_service
from services.export_service import export_service
from utils.error_handler import handle_common_exceptions

logger = logging.getLogger(__name__)
router = APIRouter()


class ReportListResponse(BaseModel):
    """Response model for listing available reports"""
    available_reports: List[str]
    description: Dict[str, str]


@router.get("/", response_model=ReportListResponse)
@handle_common_exceptions
async def list_available_reports():
    """Get list of available report types"""
    return ReportListResponse(
        available_reports=[
            "portfolio_summary",
            "risk_exposure", 
            "compliance_regulatory",
            "performance_trends",
            "detailed_msme_profile"
        ],
        description={
            "portfolio_summary": "Comprehensive portfolio overview with risk distribution and SMA analysis",
            "risk_exposure": "Geographic and sector-wise risk exposure analysis with regulatory alerts",
            "compliance_regulatory": "RBI compliance status and regulatory reporting readiness",
            "performance_trends": "Portfolio performance trends and MSME lifecycle analytics",
            "detailed_msme_profile": "Individual MSME comprehensive risk assessment and history"
        }
    )


@router.post("/portfolio-summary", response_model=PortfolioSummaryReport)
@handle_common_exceptions
async def generate_portfolio_summary_report(
    start_date: date = Query(..., description="Start date of reporting period"),
    end_date: date = Query(..., description="End date of reporting period"),
    generated_by: str = Query("system", description="User generating the report")
):
    """Generate Portfolio Summary Report"""
    try:
        if start_date > end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        if end_date > date.today():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="End date cannot be in the future"
            )
        
        report = await reports_service.generate_portfolio_summary_report(
            start_date=start_date,
            end_date=end_date,
            generated_by=generated_by
        )
        
        return report
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating portfolio summary report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate portfolio summary report: {str(e)}"
        )


@router.post("/risk-exposure", response_model=RiskExposureReport)
@handle_common_exceptions
async def generate_risk_exposure_report(
    start_date: date = Query(..., description="Start date of reporting period"),
    end_date: date = Query(..., description="End date of reporting period"),
    generated_by: str = Query("system", description="User generating the report")
):
    """Generate Risk Exposure Report"""
    try:
        if start_date > end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        report = await reports_service.generate_risk_exposure_report(
            start_date=start_date,
            end_date=end_date,
            generated_by=generated_by
        )
        
        return report
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating risk exposure report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate risk exposure report: {str(e)}"
        )


@router.post("/compliance-regulatory", response_model=ComplianceRegulatoryReport)
@handle_common_exceptions
async def generate_compliance_regulatory_report(
    start_date: date = Query(..., description="Start date of reporting period"),
    end_date: date = Query(..., description="End date of reporting period"),
    generated_by: str = Query("system", description="User generating the report")
):
    """Generate Compliance & Regulatory Report"""
    try:
        if start_date > end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        report = await reports_service.generate_compliance_regulatory_report(
            start_date=start_date,
            end_date=end_date,
            generated_by=generated_by
        )
        
        return report
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating compliance regulatory report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate compliance regulatory report: {str(e)}"
        )


@router.post("/performance-trends", response_model=PerformanceTrendsReport)
@handle_common_exceptions
async def generate_performance_trends_report(
    start_date: date = Query(..., description="Start date of reporting period"),
    end_date: date = Query(..., description="End date of reporting period"),
    generated_by: str = Query("system", description="User generating the report")
):
    """Generate Performance Trends Report"""
    try:
        if start_date > end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        report = await reports_service.generate_performance_trends_report(
            start_date=start_date,
            end_date=end_date,
            generated_by=generated_by
        )
        
        return report
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating performance trends report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate performance trends report: {str(e)}"
        )


@router.post("/detailed-msme-profile", response_model=DetailedMSMEProfileReport)
@handle_common_exceptions
async def generate_detailed_msme_profile_report(
    msme_id: str = Query(..., description="MSME ID for detailed report"),
    start_date: date = Query(..., description="Start date of reporting period"),
    end_date: date = Query(..., description="End date of reporting period"),
    generated_by: str = Query("system", description="User generating the report")
):
    """Generate Detailed MSME Profile Report"""
    try:
        if start_date > end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )
        
        if not msme_id or msme_id.strip() == "":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="MSME ID is required for detailed profile report"
            )
        
        report = await reports_service.generate_detailed_msme_profile_report(
            msme_id=msme_id,
            start_date=start_date,
            end_date=end_date,
            generated_by=generated_by
        )
        
        return report
        
    except HTTPException:
        raise
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error generating detailed MSME profile report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate detailed MSME profile report: {str(e)}"
        )


@router.post("/export/{report_type}", response_model=ReportExportResponse)
@handle_common_exceptions
async def export_report(
    report_type: str,
    export_format: ReportFormat = Query(ReportFormat.PDF, description="Export format"),
    start_date: date = Query(..., description="Start date of reporting period"),
    end_date: date = Query(..., description="End date of reporting period"),
    msme_id: Optional[str] = Query(None, description="MSME ID for detailed reports"),
    generated_by: str = Query("system", description="User generating the report")
):
    """Export report in specified format (PDF/CSV)"""
    try:
        # Validate report type
        valid_types = ["portfolio_summary", "risk_exposure", "compliance_regulatory",
                      "performance_trends", "detailed_msme_profile"]
        if report_type not in valid_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid report type. Must be one of: {', '.join(valid_types)}"
            )

        if start_date > end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Start date must be before end date"
            )

        # Generate the report first
        report = None
        if report_type == "portfolio_summary":
            report = await reports_service.generate_portfolio_summary_report(
                start_date=start_date, end_date=end_date, generated_by=generated_by
            )
        elif report_type == "risk_exposure":
            report = await reports_service.generate_risk_exposure_report(
                start_date=start_date, end_date=end_date, generated_by=generated_by
            )
        elif report_type == "compliance_regulatory":
            report = await reports_service.generate_compliance_regulatory_report(
                start_date=start_date, end_date=end_date, generated_by=generated_by
            )
        elif report_type == "performance_trends":
            report = await reports_service.generate_performance_trends_report(
                start_date=start_date, end_date=end_date, generated_by=generated_by
            )
        elif report_type == "detailed_msme_profile":
            if not msme_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="MSME ID is required for detailed profile report"
                )
            report = await reports_service.generate_detailed_msme_profile_report(
                msme_id=msme_id, start_date=start_date, end_date=end_date, generated_by=generated_by
            )

        if not report:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate report"
            )

        # Export the report
        filepath = None
        if export_format == ReportFormat.PDF:
            if report_type == "portfolio_summary":
                filepath = await export_service.export_portfolio_summary_pdf(report)
            else:
                # For now, only portfolio summary PDF is implemented
                raise HTTPException(
                    status_code=status.HTTP_501_NOT_IMPLEMENTED,
                    detail=f"PDF export for {report_type} not yet implemented"
                )
        elif export_format == ReportFormat.CSV:
            if report_type == "portfolio_summary":
                filepath = await export_service.export_portfolio_summary_csv(report)
            else:
                # For now, only portfolio summary CSV is implemented
                raise HTTPException(
                    status_code=status.HTTP_501_NOT_IMPLEMENTED,
                    detail=f"CSV export for {report_type} not yet implemented"
                )

        if not filepath:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to export report"
            )

        # Get file size
        file_size = os.path.getsize(filepath) if os.path.exists(filepath) else 0
        filename = os.path.basename(filepath)

        return ReportExportResponse(
            report_id=report.metadata.report_id,
            export_format=export_format,
            file_url=f"/api/reports/download/{filename}",
            file_size=file_size,
            expires_at=datetime.now(timezone.utc) + timedelta(hours=24),
            status="completed"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting report: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export report: {str(e)}"
        )


@router.get("/download/{filename}")
@handle_common_exceptions
async def download_report_file(filename: str):
    """Download exported report file"""
    try:
        filepath = os.path.join(export_service.export_dir, filename)

        if not os.path.exists(filepath):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found or has expired"
            )

        # Determine media type based on file extension
        if filename.endswith('.pdf'):
            media_type = 'application/pdf'
        elif filename.endswith('.csv'):
            media_type = 'text/csv'
        else:
            media_type = 'application/octet-stream'

        return FileResponse(
            path=filepath,
            filename=filename,
            media_type=media_type
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to download file: {str(e)}"
        )
