'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';

export default function TestAPIPage() {
  const [portfolioData, setPortfolioData] = useState<Record<string, unknown>[]>([]);
  const [analyticsData, setAnalyticsData] = useState<Record<string, unknown> | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function testAPI() {
      try {
        setLoading(true);
        setError(null);

        const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
        
        // Test portfolio endpoint
        const portfolioResponse = await fetch(`${API_URL}/api/dashboard/portfolio/legacy`);
        if (!portfolioResponse.ok) {
          throw new Error(`Portfolio API failed: ${portfolioResponse.status}`);
        }
        const portfolio = await portfolioResponse.json();
        setPortfolioData(portfolio);

        // Test analytics endpoint
        const analyticsResponse = await fetch(`${API_URL}/api/dashboard/analytics`);
        if (!analyticsResponse.ok) {
          throw new Error(`Analytics API failed: ${analyticsResponse.status}`);
        }
        const analytics = await analyticsResponse.json();
        setAnalyticsData(analytics);

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    }

    testAPI();
  }, []);

  if (loading) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">Testing API Connection...</h1>
        <div className="animate-pulse">Loading...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4 text-red-600">API Test Failed</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          Error: {error}
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">API Test Results</h1>
      
      <div className="grid gap-6">
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          ✅ API Connection Successful!
        </div>

        <div className="bg-white border rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Portfolio Data</h2>
          <p className="mb-2">Total MSMEs: <strong>{portfolioData.length}</strong></p>
          <div className="bg-gray-100 p-4 rounded text-sm">
            <pre>{JSON.stringify(portfolioData.slice(0, 2), null, 2)}</pre>
            {portfolioData.length > 2 && <p className="mt-2 text-gray-600">... and {portfolioData.length - 2} more</p>}
          </div>
        </div>

        {analyticsData && (
          <div className="bg-white border rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">Analytics Data</h2>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p>Total MSMEs: <strong>{(analyticsData as any).total_msmes}</strong></p>
                <p>Average Score: <strong>{(analyticsData as any).average_score}</strong></p>
              </div>
              <div>
                <p>Risk Distribution:</p>
                <ul className="ml-4">
                  <li>🟢 Green: {(analyticsData as any).risk_distribution?.green} ({Math.round(((analyticsData as any).risk_distribution?.green / (analyticsData as any).total_msmes) * 100)}%)</li>
                  <li>🟡 Yellow: {(analyticsData as any).risk_distribution?.yellow} ({Math.round(((analyticsData as any).risk_distribution?.yellow / (analyticsData as any).total_msmes) * 100)}%)</li>
                  <li>🔴 Red: {(analyticsData as any).risk_distribution?.red} ({Math.round(((analyticsData as any).risk_distribution?.red / (analyticsData as any).total_msmes) * 100)}%)</li>
                </ul>
              </div>
            </div>
            <div className="bg-gray-100 p-4 rounded text-sm">
              <pre>{JSON.stringify(analyticsData, null, 2)}</pre>
            </div>
          </div>
        )}

        <div className="mt-6">
          <Link
            href="/"
            className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          >
            Back to Portfolio Dashboard
          </Link>
        </div>
      </div>
    </div>
  );
}
