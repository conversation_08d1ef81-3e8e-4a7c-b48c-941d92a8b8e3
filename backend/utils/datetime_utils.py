"""
Datetime utilities for Credit Chakra.

This module provides timezone-aware datetime utilities to replace deprecated
datetime.now(datetime.UTC) usage throughout the codebase.

Author: Credit Chakra Team
Version: 1.0.0
"""

from datetime import datetime, timezone


def utc_now() -> datetime:
    """
    Get current UTC datetime.
    
    This replaces the deprecated datetime.now(datetime.UTC) with the recommended
    timezone-aware datetime.now(datetime.UTC).
    
    Returns:
        datetime: Current UTC datetime with timezone information
    """
    return datetime.now(timezone.utc)


def utc_timestamp() -> float:
    """
    Get current UTC timestamp.
    
    Returns:
        float: Current UTC timestamp
    """
    return utc_now().timestamp()


def utc_isoformat() -> str:
    """
    Get current UTC datetime in ISO format.
    
    Returns:
        str: Current UTC datetime in ISO format
    """
    return utc_now().isoformat()
