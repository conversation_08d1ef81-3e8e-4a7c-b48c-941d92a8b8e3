"""
Comprehensive tests for AI Copilot enhancements
Tests all new AI features, performance optimizations, and integrations
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock
import json

# Import the services we're testing
from services.ai_analytics import ai_analytics_engine, AnalyticsType
from services.ai_nudge_engine import ai_nudge_engine, NudgeType, NudgePriority
from services.ai_business_analyzer import ai_business_analyzer, BusinessHealthCategory
from services.ai_performance_optimizer import ai_performance_optimizer, CacheType
from services.llm_integration import LLMIntegrationService

class TestAIAnalyticsEngine:
    """Test the AI Analytics Engine"""
    
    @pytest.fixture
    def sample_portfolio_data(self):
        return {
            'msmes': [
                {
                    'id': 'test_msme_1',
                    'business_name': 'Test Manufacturing Co',
                    'business_type': 'Manufacturing',
                    'score': 75,
                    'location': 'Mumbai',
                    'exposure': 5000000
                },
                {
                    'id': 'test_msme_2',
                    'business_name': 'Test Retail Store',
                    'business_type': 'Retail',
                    'score': 45,
                    'location': 'Delhi',
                    'exposure': 2000000
                }
            ]
        }
    
    @pytest.mark.asyncio
    async def test_portfolio_analysis(self, sample_portfolio_data):
        """Test comprehensive portfolio analysis"""
        analysis = await ai_analytics_engine.analyze_portfolio(sample_portfolio_data)
        
        assert analysis.total_msmes == 2
        assert 'high' in analysis.risk_distribution
        assert 'medium' in analysis.risk_distribution
        assert 'low' in analysis.risk_distribution
        assert analysis.overall_health_score > 0
        assert analysis.overall_health_score <= 100
        
    @pytest.mark.asyncio
    async def test_risk_distribution_calculation(self, sample_portfolio_data):
        """Test risk distribution calculation accuracy"""
        analysis = await ai_analytics_engine.analyze_portfolio(sample_portfolio_data)
        
        # With scores 75 and 45, we should have 1 medium risk and 1 low risk
        assert analysis.risk_distribution['high'] == 0.0  # No scores < 40
        assert analysis.risk_distribution['medium'] == 0.5  # 1 out of 2 (score 45)
        assert analysis.risk_distribution['low'] == 0.5  # 1 out of 2 (score 75)
        
    @pytest.mark.asyncio
    async def test_sector_performance_analysis(self, sample_portfolio_data):
        """Test sector-wise performance analysis"""
        analysis = await ai_analytics_engine.analyze_portfolio(sample_portfolio_data)
        
        assert 'Manufacturing' in analysis.sector_performance
        assert 'Retail' in analysis.sector_performance
        
        manufacturing_data = analysis.sector_performance['Manufacturing']
        assert 'average_score' in manufacturing_data
        assert 'account_count' in manufacturing_data
        assert manufacturing_data['account_count'] == 1

class TestAINudgeEngine:
    """Test the AI Nudge Engine"""
    
    @pytest.fixture
    def sample_msme_data(self):
        return {
            'id': 'test_msme_nudge',
            'business_name': 'Test Business for Nudges',
            'business_type': 'Manufacturing',
            'score': 35,  # Low score to trigger nudges
            'location': 'Mumbai'
        }
    
    @pytest.mark.asyncio
    async def test_nudge_generation(self, sample_msme_data):
        """Test intelligent nudge generation"""
        nudges = await ai_nudge_engine.generate_intelligent_nudges(sample_msme_data)
        
        assert isinstance(nudges, list)
        assert len(nudges) <= 5  # Should return top 5 nudges
        
        if nudges:
            nudge = nudges[0]
            assert hasattr(nudge, 'id')
            assert hasattr(nudge, 'type')
            assert hasattr(nudge, 'priority')
            assert hasattr(nudge, 'confidence_score')
            assert 0 <= nudge.confidence_score <= 1
    
    @pytest.mark.asyncio
    async def test_behavioral_profile_analysis(self, sample_msme_data):
        """Test behavioral profile analysis"""
        behavioral_profile = await ai_nudge_engine._analyze_behavioral_profile(sample_msme_data)
        
        assert 'payment_behavior' in behavioral_profile
        assert 'digital_engagement' in behavioral_profile
        assert 'risk_response' in behavioral_profile
        assert 'business_type' in behavioral_profile
        
        # Low score should result in 'late' or 'irregular' payment behavior
        assert behavioral_profile['payment_behavior'] in ['late', 'irregular']
    
    @pytest.mark.asyncio
    async def test_personalization_profile_creation(self, sample_msme_data):
        """Test personalization profile creation"""
        behavioral_profile = await ai_nudge_engine._analyze_behavioral_profile(sample_msme_data)
        personalization = await ai_nudge_engine._create_personalization_profile(sample_msme_data, behavioral_profile)
        
        assert hasattr(personalization, 'communication_style')
        assert hasattr(personalization, 'preferred_language')
        assert hasattr(personalization, 'optimal_time_of_day')
        assert personalization.communication_style in ['formal', 'friendly', 'urgent', 'supportive']

class TestAIBusinessAnalyzer:
    """Test the AI Business Analyzer"""
    
    @pytest.fixture
    def sample_business_data(self):
        return {
            'id': 'test_business_analysis',
            'business_name': 'Test Business Analysis Co',
            'business_type': 'Technology',
            'score': 82,
            'location': 'Bangalore'
        }
    
    @pytest.mark.asyncio
    async def test_business_profile_analysis(self, sample_business_data):
        """Test comprehensive business profile analysis"""
        profile = await ai_business_analyzer.analyze_business_profile(sample_business_data)
        
        assert profile.msme_id == 'test_business_analysis'
        assert profile.business_name == 'Test Business Analysis Co'
        assert isinstance(profile.overall_health_score, float)
        assert 0 <= profile.overall_health_score <= 100
        assert isinstance(profile.health_category, BusinessHealthCategory)
        
    @pytest.mark.asyncio
    async def test_financial_strength_analysis(self, sample_business_data):
        """Test financial strength analysis"""
        financial_strength = await ai_business_analyzer._analyze_financial_strength(sample_business_data)
        
        assert 'revenue_stability' in financial_strength
        assert 'profitability' in financial_strength
        assert 'liquidity' in financial_strength
        assert 'overall_score' in financial_strength
        
        # All scores should be between 0 and 1
        for key, value in financial_strength.items():
            assert 0 <= value <= 1
    
    @pytest.mark.asyncio
    async def test_digital_maturity_analysis(self, sample_business_data):
        """Test digital maturity analysis"""
        digital_maturity = await ai_business_analyzer._analyze_digital_maturity(sample_business_data)
        
        assert 'digital_presence' in digital_maturity
        assert 'payment_digitization' in digital_maturity
        assert 'process_automation' in digital_maturity
        assert 'overall_score' in digital_maturity
        
        # Technology businesses should have higher digital scores
        assert digital_maturity['overall_score'] > 0.5
    
    @pytest.mark.asyncio
    async def test_ai_insights_generation(self, sample_business_data):
        """Test AI insights generation"""
        # Create mock data that would trigger insights
        financial = {'liquidity': 0.4}  # Low liquidity to trigger insight
        operational = {'overall_score': 0.7}
        market = {'overall_score': 0.8}
        digital = {'overall_score': 0.3}  # Low digital score to trigger insight
        risks = {}
        
        insights = await ai_business_analyzer._generate_ai_insights(
            sample_business_data, financial, operational, market, digital, risks
        )
        
        assert isinstance(insights, list)
        
        if insights:
            insight = insights[0]
            assert hasattr(insight, 'category')
            assert hasattr(insight, 'insight')
            assert hasattr(insight, 'confidence')
            assert hasattr(insight, 'actionable')
            assert hasattr(insight, 'recommendations')

class TestAIPerformanceOptimizer:
    """Test the AI Performance Optimizer"""
    
    @pytest.mark.asyncio
    async def test_cache_functionality(self):
        """Test caching functionality"""
        # Mock AI function
        async def mock_ai_function(data):
            return {"result": "test_data", "processed": True}
        
        cache_key = "test_cache_key"
        cache_type = CacheType.INSIGHTS
        
        # First call should miss cache and execute function
        result1, metadata1 = await ai_performance_optimizer.optimize_ai_request(
            cache_key, cache_type, mock_ai_function, {"test": "data"}
        )
        
        assert metadata1['cache_hit'] == False
        assert result1['result'] == "test_data"
        
        # Second call should hit cache
        result2, metadata2 = await ai_performance_optimizer.optimize_ai_request(
            cache_key, cache_type, mock_ai_function, {"test": "data"}
        )
        
        assert metadata2['cache_hit'] == True
        assert result2['result'] == "test_data"
    
    @pytest.mark.asyncio
    async def test_batch_processing_small(self):
        """Test small batch processing"""
        msme_list = [
            {'id': f'msme_{i}', 'business_type': 'Manufacturing', 'score': 70 + i}
            for i in range(50)  # Small batch
        ]
        
        result = await ai_performance_optimizer.batch_optimize_portfolio_analysis(msme_list)
        
        assert result['total_msmes'] == 50
        assert 'processing_time_ms' in result
        assert 'business_type_analysis' in result
        assert result['processing_time_ms'] < 5000  # Should be fast for small batch
    
    @pytest.mark.asyncio
    async def test_batch_processing_large(self):
        """Test large batch processing with chunking"""
        msme_list = [
            {'id': f'msme_{i}', 'business_type': 'Manufacturing', 'score': 70}
            for i in range(250)  # Large batch
        ]
        
        result = await ai_performance_optimizer.batch_optimize_portfolio_analysis(msme_list)
        
        assert result['total_msmes'] == 250
        assert 'chunks_processed' in result
        assert result['chunks_processed'] > 1  # Should be chunked
        assert 'performance_optimizations' in result
        assert result['performance_optimizations']['chunking_used'] == True
    
    def test_performance_metrics_tracking(self):
        """Test performance metrics tracking"""
        # Simulate some requests
        ai_performance_optimizer._update_performance_metrics(0.5, cache_hit=True)
        ai_performance_optimizer._update_performance_metrics(1.0, cache_hit=False)
        ai_performance_optimizer._update_performance_metrics(0.8, error=True)
        
        summary = ai_performance_optimizer.get_performance_summary()
        
        assert 'avg_response_time_ms' in summary
        assert 'cache_hit_rate_percent' in summary
        assert 'error_rate_percent' in summary
        assert 'total_requests' in summary
        assert summary['total_requests'] > 0

class TestLLMIntegrationEnhancements:
    """Test enhanced LLM integration"""
    
    @pytest.fixture
    def llm_service(self):
        return LLMIntegrationService()
    
    @pytest.mark.asyncio
    async def test_domain_knowledge_enhancement(self, llm_service):
        """Test domain knowledge enhancement"""
        query = "What is NPA and how does it affect credit scoring?"
        context = {"user_id": "test_user"}
        
        enhanced_context = llm_service._enhance_context_with_domain_knowledge(query, context)
        
        assert 'relevant_credit_terms' in enhanced_context
        assert 'npa' in enhanced_context['relevant_credit_terms']
        assert enhanced_context['relevant_credit_terms']['npa'] == "Non-Performing Asset - loans overdue by 90+ days"
    
    @pytest.mark.asyncio
    async def test_conversation_memory_update(self, llm_service):
        """Test conversation memory functionality"""
        user_id = "test_user"
        query = "Show me risk analysis for my portfolio"
        context = {"user_intent": "analysis"}
        
        llm_service._update_conversation_memory(user_id, query, context)
        
        assert user_id in llm_service.conversation_memory
        memory = llm_service.conversation_memory[user_id]
        assert len(memory['recent_queries']) == 1
        assert 'risk' in memory['topics_discussed']
    
    @pytest.mark.asyncio
    async def test_enhanced_mock_response_generation(self, llm_service):
        """Test enhanced mock response generation"""
        query = "Show me high risk alerts and compliance issues"
        context = {"user_id": "test_user"}
        
        response = await llm_service._generate_enhanced_mock_response(query, context)
        
        assert hasattr(response, 'content')
        assert hasattr(response, 'confidence')
        assert hasattr(response, 'reasoning')
        assert hasattr(response, 'suggested_actions')
        assert response.confidence > 0
        assert len(response.suggested_actions) > 0

class TestIntegrationScenarios:
    """Test integration scenarios between different AI components"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_portfolio_analysis(self):
        """Test end-to-end portfolio analysis workflow"""
        # Sample portfolio data
        portfolio_data = {
            'msmes': [
                {
                    'id': 'integration_test_1',
                    'business_name': 'Integration Test Co',
                    'business_type': 'Manufacturing',
                    'score': 65,
                    'location': 'Mumbai'
                }
            ]
        }
        
        # Test AI analytics
        analysis = await ai_analytics_engine.analyze_portfolio(portfolio_data)
        assert analysis.total_msmes == 1
        
        # Test business profile analysis for the MSME
        msme_data = portfolio_data['msmes'][0]
        business_profile = await ai_business_analyzer.analyze_business_profile(msme_data)
        assert business_profile.msme_id == 'integration_test_1'
        
        # Test nudge generation
        nudges = await ai_nudge_engine.generate_intelligent_nudges(msme_data)
        assert isinstance(nudges, list)
    
    @pytest.mark.asyncio
    async def test_performance_optimization_integration(self):
        """Test performance optimization with AI services"""
        # Mock AI function
        async def mock_analysis_function(data):
            await asyncio.sleep(0.1)  # Simulate processing time
            return {"analysis": "completed", "data": data}
        
        # Test with performance optimizer
        cache_key = "integration_test_cache"
        result, metadata = await ai_performance_optimizer.optimize_ai_request(
            cache_key,
            CacheType.PORTFOLIO_ANALYSIS,
            mock_analysis_function,
            {"test": "integration"}
        )
        
        assert result['analysis'] == "completed"
        assert 'response_time_ms' in metadata
        assert metadata['response_time_ms'] > 0
    
    def test_error_handling_and_fallbacks(self):
        """Test error handling and fallback mechanisms"""
        # Test fallback portfolio analysis
        fallback_analysis = ai_analytics_engine._get_fallback_analysis()
        assert fallback_analysis.total_msmes == 0
        assert fallback_analysis.overall_health_score == 75.0
        
        # Test fallback business profile
        fallback_profile = ai_business_analyzer._get_fallback_profile({'id': 'test'})
        assert fallback_profile.msme_id == 'test'
        assert fallback_profile.health_category == BusinessHealthCategory.AVERAGE

# Performance benchmarks
class TestPerformanceBenchmarks:
    """Test performance benchmarks to ensure 2-second SLA"""
    
    @pytest.mark.asyncio
    async def test_portfolio_analysis_performance(self):
        """Test portfolio analysis meets performance requirements"""
        # Create larger dataset
        portfolio_data = {
            'msmes': [
                {
                    'id': f'perf_test_{i}',
                    'business_name': f'Performance Test {i}',
                    'business_type': 'Manufacturing',
                    'score': 50 + (i % 50),
                    'location': 'Mumbai'
                }
                for i in range(100)  # 100 MSMEs
            ]
        }
        
        start_time = datetime.now()
        analysis = await ai_analytics_engine.analyze_portfolio(portfolio_data)
        end_time = datetime.now()
        
        processing_time = (end_time - start_time).total_seconds()
        
        assert analysis.total_msmes == 100
        assert processing_time < 2.0  # Should complete within 2 seconds
    
    @pytest.mark.asyncio
    async def test_cached_performance(self):
        """Test cached requests meet performance requirements"""
        async def slow_function(data):
            await asyncio.sleep(0.5)  # Simulate slow operation
            return {"result": "slow_computation"}
        
        cache_key = "performance_test_cache"
        
        # First call (cache miss)
        start_time = datetime.now()
        result1, metadata1 = await ai_performance_optimizer.optimize_ai_request(
            cache_key, CacheType.INSIGHTS, slow_function, {"test": "data"}
        )
        first_call_time = (datetime.now() - start_time).total_seconds()
        
        # Second call (cache hit)
        start_time = datetime.now()
        result2, metadata2 = await ai_performance_optimizer.optimize_ai_request(
            cache_key, CacheType.INSIGHTS, slow_function, {"test": "data"}
        )
        second_call_time = (datetime.now() - start_time).total_seconds()
        
        assert metadata1['cache_hit'] == False
        assert metadata2['cache_hit'] == True
        assert second_call_time < 0.1  # Cached call should be very fast
        assert first_call_time > second_call_time  # Cache should improve performance

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
