/**
 * Shared Data Fetching Component
 * Eliminates duplicate data fetching patterns across components
 */
import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface LoadingSkeletonProps {
  type: 'card' | 'table' | 'chart' | 'list';
  count?: number;
  className?: string;
}

export function LoadingSkeleton({ type, count = 3, className = '' }: LoadingSkeletonProps) {
  const renderCardSkeleton = () => (
    <Card className={`border-0 shadow-lg ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <Skeleton className="h-10 w-10 rounded-lg" />
          <Skeleton className="h-6 w-32" />
        </div>
      </CardHeader>
      <CardContent>
        <Skeleton className="h-8 w-16 mb-2" />
        <Skeleton className="h-4 w-24 mb-3" />
        <Skeleton className="h-2 w-full" />
      </CardContent>
    </Card>
  );

  const renderTableSkeleton = () => (
    <Card className={`border-0 shadow-lg ${className}`}>
      <CardHeader>
        <Skeleton className="h-6 w-48" />
        <Skeleton className="h-4 w-64" />
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {Array.from({ length: count }).map((_, i) => (
            <div key={i} className="flex items-center gap-4">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-8 w-16 ml-auto" />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );

  const renderChartSkeleton = () => (
    <Card className={`border-0 shadow-lg ${className}`}>
      <CardHeader>
        <Skeleton className="h-6 w-40" />
        <Skeleton className="h-4 w-56" />
      </CardHeader>
      <CardContent>
        <Skeleton className="h-64 w-full" />
      </CardContent>
    </Card>
  );

  const renderListSkeleton = () => (
    <div className={`space-y-3 ${className}`}>
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="flex items-center gap-3 p-3 border rounded-lg">
          <Skeleton className="h-8 w-8 rounded-full" />
          <div className="flex-1">
            <Skeleton className="h-4 w-32 mb-1" />
            <Skeleton className="h-3 w-48" />
          </div>
          <Skeleton className="h-6 w-16" />
        </div>
      ))}
    </div>
  );

  switch (type) {
    case 'card':
      return renderCardSkeleton();
    case 'table':
      return renderTableSkeleton();
    case 'chart':
      return renderChartSkeleton();
    case 'list':
      return renderListSkeleton();
    default:
      return renderCardSkeleton();
  }
}

interface ErrorStateProps {
  error: string;
  onRetry?: () => void;
  className?: string;
  showRetry?: boolean;
}

export function ErrorState({ 
  error, 
  onRetry, 
  className = '', 
  showRetry = true 
}: ErrorStateProps) {
  return (
    <Card className={`border-0 shadow-lg ${className}`}>
      <CardContent className="flex flex-col items-center justify-center py-12">
        <div className="flex items-center gap-3 mb-4">
          <AlertTriangle className="h-8 w-8 text-amber-500" />
          <h3 className="text-lg font-semibold text-foreground">Something went wrong</h3>
        </div>
        
        <p className="text-muted-foreground mb-6 text-center max-w-md">
          {error || 'An unexpected error occurred while loading the data.'}
        </p>
        
        {showRetry && onRetry && (
          <Button onClick={onRetry} variant="outline" className="gap-2">
            <RefreshCw className="h-4 w-4" />
            Try Again
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

interface EmptyStateProps {
  title: string;
  description: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}

export function EmptyState({ 
  title, 
  description, 
  action, 
  className = '' 
}: EmptyStateProps) {
  return (
    <Card className={`border-0 shadow-lg ${className}`}>
      <CardContent className="flex flex-col items-center justify-center py-12">
        <h3 className="text-lg font-semibold text-foreground mb-2">{title}</h3>
        <p className="text-muted-foreground mb-6 text-center max-w-md">{description}</p>
        
        {action && (
          <Button onClick={action.onClick} className="gap-2">
            {action.label}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}

interface DataFetcherProps<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  onRetry?: () => void;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  emptyState?: {
    title: string;
    description: string;
    action?: {
      label: string;
      onClick: () => void;
    };
  };
  children: (data: T) => React.ReactNode;
  className?: string;
}

export function DataFetcher<T>({
  data,
  loading,
  error,
  onRetry,
  loadingComponent,
  errorComponent,
  emptyState,
  children,
  className = ''
}: DataFetcherProps<T>) {
  if (loading) {
    return (
      <div className={className}>
        {loadingComponent || <LoadingSkeleton type="card" />}
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        {errorComponent || <ErrorState error={error} onRetry={onRetry} />}
      </div>
    );
  }

  if (!data) {
    if (emptyState) {
      return (
        <div className={className}>
          <EmptyState {...emptyState} />
        </div>
      );
    }
    return null;
  }

  return <div className={className}>{children(data)}</div>;
}

// Specialized data fetchers for common patterns
interface PortfolioDataFetcherProps {
  children: (data: { msmes: any[]; analytics: any }) => React.ReactNode;
  className?: string;
}

export function PortfolioDataFetcher({ children, className }: PortfolioDataFetcherProps) {
  // This would use the optimized data hook
  // For now, it's a placeholder that shows the pattern
  const data = null;
  const loading = false;
  const error = null;

  return (
    <DataFetcher
      data={data}
      loading={loading}
      error={error}
      loadingComponent={
        <div className="grid gap-6 md:grid-cols-3">
          <LoadingSkeleton type="card" />
          <LoadingSkeleton type="card" />
          <LoadingSkeleton type="card" />
        </div>
      }
      emptyState={{
        title: "No MSMEs Found",
        description: "Start by adding your first MSME to begin monitoring their credit health.",
        action: {
          label: "Add MSME",
          onClick: () => console.log("Add MSME clicked")
        }
      }}
      className={className}
    >
      {children}
    </DataFetcher>
  );
}

interface MSMEDetailDataFetcherProps {
  msmeId: string;
  children: (data: any) => React.ReactNode;
  className?: string;
}

export function MSMEDetailDataFetcher({ msmeId, children, className }: MSMEDetailDataFetcherProps) {
  // This would use the optimized MSME hook
  const data = null;
  const loading = false;
  const error = null;

  return (
    <DataFetcher
      data={data}
      loading={loading}
      error={error}
      loadingComponent={<LoadingSkeleton type="table" count={5} />}
      emptyState={{
        title: "MSME Not Found",
        description: `No MSME found with ID: ${msmeId}`,
        action: {
          label: "Back to Portfolio",
          onClick: () => window.history.back()
        }
      }}
      className={className}
    >
      {children}
    </DataFetcher>
  );
}
