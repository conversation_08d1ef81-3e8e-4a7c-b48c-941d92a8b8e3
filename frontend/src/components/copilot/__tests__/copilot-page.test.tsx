/**
 * Comprehensive tests for AI Copilot Page component
 * Tests functionality, performance, and compatibility
 */

import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CopilotPage } from '../copilot-page';
import { api } from '@/lib/api';
import { ChatMessage } from '@/types/copilot';

// Mock the API
jest.mock('@/lib/api', () => ({
  api: {
    askCopilot: jest.fn(),
    getCopilotInsights: jest.fn(),
  },
}));

// Mock performance APIs
Object.defineProperty(window, 'performance', {
  value: {
    now: jest.fn(() => Date.now()),
    memory: {
      usedJSHeapSize: 1024 * 1024 * 50, // 50MB
      totalJSHeapSize: 1024 * 1024 * 100, // 100MB
    },
  },
});

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock scrollTo for testing scroll behavior
Element.prototype.scrollTo = jest.fn();
HTMLElement.prototype.scrollTo = jest.fn();

const mockApiResponse = {
  response: 'Test AI response',
  data: {
    response_type: 'general',
    confidence: 0.9,
    ai_analytics: {
      portfolio_health_score: 85,
      total_msmes: 150,
      risk_distribution: {
        high: 0.1,
        medium: 0.3,
        low: 0.6,
      },
    },
  },
  suggestions: ['Follow-up question 1', 'Follow-up question 2'],
  query_id: 'test-query-id',
  timestamp: new Date().toISOString(),
};

describe('CopilotPage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (api.askCopilot as jest.Mock).mockResolvedValue(mockApiResponse);
  });

  describe('Basic Functionality', () => {
    test('renders without crashing', () => {
      render(<CopilotPage />);
      expect(screen.getByText('AI Copilot')).toBeInTheDocument();
    });

    test('displays welcome message when no messages', () => {
      render(<CopilotPage />);
      expect(screen.getByText('How can I help you today?')).toBeInTheDocument();
    });

    test('shows initial messages when provided', () => {
      const initialMessages: ChatMessage[] = [
        {
          id: '1',
          type: 'user',
          content: 'Test message',
          timestamp: new Date(),
        },
      ];

      render(<CopilotPage initialMessages={initialMessages} />);
      expect(screen.getByText('Test message')).toBeInTheDocument();
    });

    test('toggles quick actions panel', async () => {
      const user = userEvent.setup();
      render(<CopilotPage />);

      const quickActionsButton = screen.getByText('Quick Actions');
      await user.click(quickActionsButton);

      expect(screen.getByText('Intelligent queries with machine learning insights')).toBeInTheDocument();
    });

    test('toggles live insights panel', async () => {
      const user = userEvent.setup();
      render(<CopilotPage />);

      const insightsButton = screen.getByText('Live Insights');
      await user.click(insightsButton);

      // Check if insights panel is visible (assuming it has some identifiable content)
      expect(screen.getByText('Live Insights')).toBeInTheDocument();
    });
  });

  describe('Message Handling', () => {
    test('sends message when form is submitted', async () => {
      const user = userEvent.setup();
      render(<CopilotPage />);

      const input = screen.getByPlaceholderText(/Ask me about portfolio risks/);
      const sendButton = screen.getByLabelText('Send message');

      await user.type(input, 'Test query');
      await user.click(sendButton);

      await waitFor(() => {
        expect(api.askCopilot).toHaveBeenCalledWith({
          query: 'Test query',
          context: expect.any(Object),
        });
      });
    });

    test('sends message when Enter key is pressed', async () => {
      const user = userEvent.setup();
      render(<CopilotPage />);

      const input = screen.getByPlaceholderText(/Ask me about portfolio risks/);

      await user.type(input, 'Test query{enter}');

      await waitFor(() => {
        expect(api.askCopilot).toHaveBeenCalled();
      });
    });

    test('does not send empty messages', async () => {
      const user = userEvent.setup();
      render(<CopilotPage />);

      const sendButton = screen.getByLabelText('Send message');
      await user.click(sendButton);

      expect(api.askCopilot).not.toHaveBeenCalled();
    });

    test('displays loading state while sending message', async () => {
      const user = userEvent.setup();
      
      // Mock a delayed response
      (api.askCopilot as jest.Mock).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(mockApiResponse), 100))
      );

      render(<CopilotPage />);

      const input = screen.getByPlaceholderText(/Ask me about portfolio risks/);
      const sendButton = screen.getByLabelText('Send message');

      await user.type(input, 'Test query');
      await user.click(sendButton);

      // Check for loading indicator
      expect(screen.getByText('AI is thinking...')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.queryByText('AI is thinking...')).not.toBeInTheDocument();
      });
    });

    test('handles API errors gracefully', async () => {
      const user = userEvent.setup();
      
      (api.askCopilot as jest.Mock).mockRejectedValue(new Error('API Error'));

      render(<CopilotPage />);

      const input = screen.getByPlaceholderText(/Ask me about portfolio risks/);
      const sendButton = screen.getByLabelText('Send message');

      await user.type(input, 'Test query');
      await user.click(sendButton);

      await waitFor(() => {
        expect(screen.getByText(/I apologize, but I encountered an error/)).toBeInTheDocument();
      });
    });
  });

  describe('Performance Requirements', () => {
    test('handles large number of messages efficiently', async () => {
      const startTime = performance.now();
      
      // Create 1000 messages
      const largeMessageList: ChatMessage[] = Array.from({ length: 1000 }, (_, i) => ({
        id: `msg-${i}`,
        type: i % 2 === 0 ? 'user' : 'assistant',
        content: `Message ${i}`,
        timestamp: new Date(Date.now() - (1000 - i) * 1000),
      }));

      await act(async () => {
        render(<CopilotPage initialMessages={largeMessageList} />);
      });

      const renderTime = performance.now() - startTime;
      
      // Should render within 2 seconds (2000ms)
      expect(renderTime).toBeLessThan(2000);
    });

    test('debounces input changes for performance', async () => {
      const user = userEvent.setup();
      render(<CopilotPage />);

      const input = screen.getByPlaceholderText(/Ask me about portfolio risks/);

      // Type rapidly
      await user.type(input, 'rapid typing test');

      // Input should be updated immediately
      expect(input).toHaveValue('rapid typing test');
    });

    test('throttles API calls to prevent spam', async () => {
      const user = userEvent.setup();
      render(<CopilotPage />);

      const input = screen.getByPlaceholderText(/Ask me about portfolio risks/);
      const sendButton = screen.getByLabelText('Send message');

      // Send multiple messages rapidly
      await user.type(input, 'Message 1');
      await user.click(sendButton);
      
      await user.type(input, 'Message 2');
      await user.click(sendButton);
      
      await user.type(input, 'Message 3');
      await user.click(sendButton);

      // Should throttle API calls
      await waitFor(() => {
        expect(api.askCopilot).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels', () => {
      render(<CopilotPage />);

      expect(screen.getByLabelText('Chat input')).toBeInTheDocument();
      expect(screen.getByLabelText('Send message')).toBeInTheDocument();
    });

    test('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<CopilotPage />);

      // Tab to input field
      await user.tab();
      expect(screen.getByLabelText('Chat input')).toHaveFocus();

      // Tab to send button
      await user.tab();
      expect(screen.getByLabelText('Send message')).toHaveFocus();
    });

    test('handles Escape key to clear input', async () => {
      const user = userEvent.setup();
      render(<CopilotPage />);

      const input = screen.getByPlaceholderText(/Ask me about portfolio risks/);
      
      await user.type(input, 'Test message');
      expect(input).toHaveValue('Test message');

      await user.type(input, '{escape}');
      expect(input).toHaveValue('');
    });
  });

  describe('Component Integration', () => {
    test('integrates with QuickActions component', async () => {
      const user = userEvent.setup();
      render(<CopilotPage />);

      // Open quick actions
      const quickActionsButton = screen.getByText('Quick Actions');
      await user.click(quickActionsButton);

      // Should show quick actions
      expect(screen.getByText('AI Risk Analysis')).toBeInTheDocument();
    });

    test('integrates with InsightsFeed component', async () => {
      const user = userEvent.setup();
      render(<CopilotPage />);

      // Open insights
      const insightsButton = screen.getByText('Live Insights');
      await user.click(insightsButton);

      // Should show insights panel
      expect(screen.getByText('Live Insights')).toBeInTheDocument();
    });

    test('uses ShadCN UI components correctly', () => {
      render(<CopilotPage />);

      // Check for ShadCN Button components
      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(0);

      // Check for ShadCN Input component
      const input = screen.getByRole('textbox');
      expect(input).toBeInTheDocument();
    });
  });

  describe('Error Boundary', () => {
    test('catches and displays errors gracefully', () => {
      // Mock console.error to avoid noise in test output
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

      // Component that throws an error
      const ThrowError = () => {
        throw new Error('Test error');
      };

      render(<CopilotPage />);

      // Should not crash the entire application
      expect(screen.getByText('AI Copilot')).toBeInTheDocument();

      consoleSpy.mockRestore();
    });
  });

  describe('Memory Management', () => {
    test('cleans up resources on unmount', () => {
      const { unmount } = render(<CopilotPage />);

      // Should not throw errors when unmounting
      expect(() => unmount()).not.toThrow();
    });

    test('handles memory pressure gracefully', () => {
      // Mock high memory usage
      Object.defineProperty(window.performance, 'memory', {
        value: {
          usedJSHeapSize: 1024 * 1024 * 900, // 900MB
          totalJSHeapSize: 1024 * 1024 * 1000, // 1GB
        },
      });

      render(<CopilotPage />);

      // Should still render without issues
      expect(screen.getByText('AI Copilot')).toBeInTheDocument();
    });
  });

  describe('Chat Messages Scrolling', () => {
    test('maintains fixed viewport height without page scrolling', () => {
      render(<CopilotPage />);

      // Check that the main container has proper height constraints
      const mainContainer = document.querySelector('.h-full.bg-background.overflow-hidden.flex.flex-col');
      expect(mainContainer).toBeInTheDocument();
      expect(mainContainer).toHaveClass('overflow-hidden');
    });

    test('chat messages area has proper scrolling structure when messages exist', async () => {
      const user = userEvent.setup();
      (api.askCopilot as jest.Mock).mockResolvedValue(mockApiResponse);

      render(<CopilotPage />);

      // Send a message to trigger the scrollable area
      const input = screen.getByPlaceholderText(/Ask me about portfolio/);
      await user.type(input, 'Test message');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        // Check that the messages container has proper scrolling classes
        const messagesContainer = document.querySelector('.overflow-y-auto');
        expect(messagesContainer).toBeInTheDocument();
        expect(messagesContainer).toHaveClass('overflow-y-auto');
        expect(messagesContainer).toHaveClass('min-h-0'); // Critical for flex scrolling
      });
    });

    test('chat input remains visible at bottom', async () => {
      const user = userEvent.setup();
      render(<CopilotPage />);

      const input = screen.getByPlaceholderText(/Ask me about portfolio/);
      expect(input).toBeInTheDocument();

      // Input should be visible and accessible
      await user.click(input);
      expect(input).toHaveFocus();
    });

    test('scroll area has proper emerald styling classes when messages exist', async () => {
      const user = userEvent.setup();
      (api.askCopilot as jest.Mock).mockResolvedValue(mockApiResponse);

      render(<CopilotPage />);

      // Send a message to trigger the scrollable area
      const input = screen.getByPlaceholderText(/Ask me about portfolio/);
      await user.type(input, 'Test message');
      await user.keyboard('{Enter}');

      await waitFor(() => {
        // Check for emerald-themed scrollbar classes in the DOM
        const scrollableArea = document.querySelector('.scrollbar-thumb-emerald-200');
        const scrollTrack = document.querySelector('.scrollbar-track-emerald-50');

        // At least one should exist (they're applied to the same element)
        expect(scrollableArea || scrollTrack).toBeTruthy();
      });
    });

    test('flex layout structure supports proper scrolling', () => {
      render(<CopilotPage />);

      // Check the flex container structure
      const flexContainer = document.querySelector('.flex-1.flex.flex-col.min-h-0');
      expect(flexContainer).toBeInTheDocument();

      // Check that toggle buttons are flex-shrink-0
      const toggleContainer = document.querySelector('.flex-shrink-0');
      expect(toggleContainer).toBeInTheDocument();
    });
  });
});
