'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { NudgeRequest } from '@/types';
import { api } from '@/lib/api';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Send, MessageSquare, AlertTriangle, TrendingDown } from 'lucide-react';
import { validateNudgeMessage } from '@/lib/validation';

interface SendNudgeModalProps {
  isOpen: boolean;
  onClose: () => void;
  msmeId: string;
  msmeName: string;
  onNudgeSent?: () => void;
}

interface FormData {
  trigger_type: 'score_drop' | 'manual' | 'risk_alert';
  message: string;
  medium: 'whatsapp' | 'email' | 'sms';
}

const triggerTypeOptions = [
  {
    value: 'manual' as const,
    label: 'Manual Nudge',
    description: 'Send a custom message',
    icon: MessageSquare,
  },
  {
    value: 'score_drop' as const,
    label: 'Score Drop Alert',
    description: 'Alert about credit score decline',
    icon: TrendingDown,
  },
  {
    value: 'risk_alert' as const,
    label: 'Risk Alert',
    description: 'Warning about risk factors',
    icon: AlertTriangle,
  },
];

const mediumOptions = [
  { value: 'whatsapp' as const, label: 'WhatsApp' },
  { value: 'email' as const, label: 'Email' },
  { value: 'sms' as const, label: 'SMS' },
];

const defaultMessages = {
  manual: 'Hello! We wanted to reach out regarding your business profile.',
  score_drop: 'We noticed a decline in your credit score. Please review your recent business activities and contact us if you need assistance.',
  risk_alert: 'Important: We have identified some risk factors that may affect your credit profile. Please contact us to discuss.',
};

export function SendNudgeModal({ 
  isOpen, 
  onClose, 
  msmeId, 
  msmeName, 
  onNudgeSent 
}: SendNudgeModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      trigger_type: 'manual',
      message: defaultMessages.manual,
      medium: 'whatsapp',
    },
  });

  const watchedTriggerType = watch('trigger_type');

  // Update message when trigger type changes
  React.useEffect(() => {
    if (watchedTriggerType && defaultMessages[watchedTriggerType]) {
      setValue('message', defaultMessages[watchedTriggerType]);
    }
  }, [watchedTriggerType, setValue]);

  const onSubmit = async (data: FormData) => {
    try {
      setIsSubmitting(true);
      setError(null);

      // Validate and sanitize message for security
      const messageValidation = validateNudgeMessage(data.message);
      if (!messageValidation.isValid) {
        setError(`Invalid message: ${messageValidation.errors.join(', ')}`);
        setIsSubmitting(false);
        return;
      }

      const nudgeRequest: NudgeRequest = {
        trigger_type: data.trigger_type,
        message: messageValidation.sanitizedValue,
        medium: data.medium,
        metadata: {
          sent_from: 'dashboard',
          msme_name: msmeName,
        },
      };

      await api.sendNudgeToMSME(msmeId, nudgeRequest);
      
      setSuccess(true);
      setTimeout(() => {
        setSuccess(false);
        onClose();
        reset();
        onNudgeSent?.();
      }, 1500);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send nudge');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
      reset();
      setError(null);
      setSuccess(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            Send Nudge
          </DialogTitle>
          <DialogDescription>
            Send a notification to <strong>{msmeName}</strong>
          </DialogDescription>
        </DialogHeader>

        {success ? (
          <div className="py-8 text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Send className="h-8 w-8 text-green-600" />
            </div>
            <h3 className="text-lg font-semibold text-green-900 mb-2">Nudge Sent!</h3>
            <p className="text-green-700">Your message has been sent successfully.</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Trigger Type Selection */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Nudge Type</Label>
              <RadioGroup
                value={watchedTriggerType}
                onValueChange={(value) => setValue('trigger_type', value as any)}
                className="space-y-2"
              >
                {triggerTypeOptions.map((option) => {
                  const Icon = option.icon;
                  return (
                    <div key={option.value} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                      <RadioGroupItem value={option.value} id={option.value} />
                      <div className="flex items-center gap-3 flex-1">
                        <Icon className="h-4 w-4 text-gray-500" />
                        <div>
                          <Label htmlFor={option.value} className="font-medium cursor-pointer">
                            {option.label}
                          </Label>
                          <p className="text-xs text-gray-500">{option.description}</p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </RadioGroup>
            </div>

            {/* Medium Selection */}
            <div className="space-y-2">
              <Label htmlFor="medium" className="text-sm font-medium">
                Delivery Method
              </Label>
              <Select
                value={watch('medium')}
                onValueChange={(value) => setValue('medium', value as any)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select delivery method" />
                </SelectTrigger>
                <SelectContent>
                  {mediumOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Message */}
            <div className="space-y-2">
              <Label htmlFor="message" className="text-sm font-medium">
                Message
              </Label>
              <Textarea
                id="message"
                placeholder="Enter your message..."
                className="min-h-[100px]"
                {...register('message', {
                  required: 'Message is required',
                  minLength: { value: 10, message: 'Message must be at least 10 characters' },
                  maxLength: { value: 1000, message: 'Message must be less than 1000 characters' },
                })}
              />
              {errors.message && (
                <p className="text-sm text-red-600">{errors.message.message}</p>
              )}
              <p className="text-xs text-gray-500">
                {watch('message')?.length || 0}/1000 characters
              </p>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" />
                    Send Nudge
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
