#!/usr/bin/env python3
"""
Script to load seed data into Firestore
"""
import json
import sys
import os
from datetime import datetime

# Add parent directory to path to import Firebase client
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))

from firebase.init import get_firestore_client

def load_sample_msmes():
    """Load sample MSME data into Firestore"""
    try:
        # Load sample data
        with open('sample_msmes.json', 'r') as f:
            msmes_data = json.load(f)
        
        db = get_firestore_client()
        
        for msme_data in msmes_data:
            msme_id = msme_data['msme_id']
            
            # Extract signals and nudges
            signals = msme_data.pop('signals', [])
            nudges = msme_data.pop('nudges', [])
            
            # Create MSME document
            msme_ref = db.collection('msmes').document(msme_id)
            msme_ref.set(msme_data)
            print(f"Created MSME: {msme_data['name']} ({msme_id})")
            
            # Add signals
            for signal in signals:
                signal_id = signal['signal_id']
                signal_ref = msme_ref.collection('signals').document(signal_id)
                signal_ref.set(signal)
                print(f"  Added signal: {signal['source']} ({signal_id})")
            
            # Add nudges
            for nudge in nudges:
                nudge_id = nudge['nudge_id']
                nudge_ref = msme_ref.collection('nudges').document(nudge_id)
                nudge_ref.set(nudge)
                print(f"  Added nudge: {nudge['trigger_type']} ({nudge_id})")
        
        print(f"\nSuccessfully loaded {len(msmes_data)} MSME profiles with signals and nudges.")
        
    except Exception as e:
        print(f"Error loading seed data: {e}")
        return False
    
    return True

def load_rule_definitions():
    """Load rule definitions into Firestore"""
    try:
        with open('../rule_definitions.json', 'r') as f:
            rules_data = json.load(f)
        
        db = get_firestore_client()
        
        # Store rule definitions in a special collection
        rules_ref = db.collection('system').document('rules')
        rules_ref.set(rules_data)
        
        print("Successfully loaded rule definitions.")
        
    except Exception as e:
        print(f"Error loading rule definitions: {e}")
        return False
    
    return True

def main():
    """Main function to load all seed data"""
    print("=== Credit Chakra Seed Data Loader ===\n")
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Load sample MSMEs
    print("Loading sample MSME data...")
    if not load_sample_msmes():
        print("Failed to load MSME data.")
        return
    
    print()
    
    # Load rule definitions
    print("Loading rule definitions...")
    if not load_rule_definitions():
        print("Failed to load rule definitions.")
        return
    
    print("\n=== Seed data loading completed successfully! ===")

if __name__ == "__main__":
    main()
