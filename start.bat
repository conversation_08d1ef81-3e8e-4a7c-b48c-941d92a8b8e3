@echo off
REM Credit Chakra - Full Stack Startup Script for Windows
REM This script starts both the FastAPI backend and Next.js frontend

setlocal enabledelayedexpansion

echo [CREDIT-CHAKRA] Starting Credit Chakra Full Stack Application
echo =============================================================

REM Check prerequisites
echo [CREDIT-CHAKRA] Checking prerequisites...

python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed. Please install Python 3.8 or higher.
    pause
    exit /b 1
)

node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18 or higher.
    pause
    exit /b 1
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed. Please install npm.
    pause
    exit /b 1
)

echo [SUCCESS] All prerequisites are installed

REM Setup Python environment
echo [CREDIT-CHAKRA] Setting up Python environment...
cd backend

if not exist "venv" (
    echo [CREDIT-CHAKRA] Creating Python virtual environment...
    python -m venv venv
)

echo [CREDIT-CHAKRA] Activating Python virtual environment...
call venv\Scripts\activate.bat

echo [CREDIT-CHAKRA] Installing Python dependencies...
pip install --upgrade pip
pip install -r requirements.txt

cd ..

REM Setup Node.js environment
echo [CREDIT-CHAKRA] Setting up Node.js environment...
cd frontend

if not exist "node_modules" (
    echo [CREDIT-CHAKRA] Installing Node.js dependencies...
    npm install
) else (
    echo [CREDIT-CHAKRA] Node.js dependencies already installed
)

cd ..

REM Start backend
echo [CREDIT-CHAKRA] Starting FastAPI backend server...
cd backend
call venv\Scripts\activate.bat
start "Credit Chakra Backend" cmd /k "python main.py"
cd ..

REM Wait a bit for backend to start
timeout /t 5 /nobreak >nul

REM Start frontend
echo [CREDIT-CHAKRA] Starting Next.js frontend server...
cd frontend
start "Credit Chakra Frontend" cmd /k "npm run dev"
cd ..

REM Wait a bit for frontend to start
timeout /t 10 /nobreak >nul

echo.
echo [SUCCESS] Credit Chakra is now starting!
echo =====================================
echo [SUCCESS] Frontend (Analytics): http://localhost:3000
echo [SUCCESS] Portfolio: http://localhost:3000/msmes
echo [SUCCESS] Backend API: http://localhost:8000
echo [SUCCESS] API Docs: http://localhost:8000/docs
echo.
echo [CREDIT-CHAKRA] Both servers are starting in separate windows
echo [CREDIT-CHAKRA] Close those windows to stop the servers
echo.
pause
