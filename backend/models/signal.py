from pydantic import BaseModel, Field
from typing import Optional, Any
from datetime import datetime
from enum import Enum

class SignalSource(str, Enum):
    GST = "gst"
    UPI = "upi"
    REVIEWS = "reviews"
    JUSTDIAL = "justdial"
    INSTAGRAM = "instagram"
    MAPS = "maps"

class Signal(BaseModel):
    signal_id: Optional[str] = None
    msme_id: str = Field(..., min_length=1)
    source: SignalSource
    value: Any  # Raw signal value (could be number, string, dict)
    normalized: Optional[float] = Field(None, ge=0, le=1)  # Normalized score 0-1
    timestamp: Optional[datetime] = None
    metadata: Optional[dict] = Field(default_factory=dict)
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class SignalCreate(BaseModel):
    msme_id: str = Field(..., min_length=1)
    source: SignalSource
    value: Any
    metadata: Optional[dict] = Field(default_factory=dict)

class SignalUpdate(BaseModel):
    value: Optional[Any] = None
    normalized: Optional[float] = Field(None, ge=0, le=1)
    metadata: Optional[dict] = None
