"""
Performance tests for Credit Chakra Risk Monitor.

This module validates that the Risk Monitor meets the 2-second load requirement
and performs efficiently under various load conditions.

Author: Credit Chakra Team
Version: 1.0.0
"""

import pytest
import asyncio
import time
from datetime import datetime, date, timedelta
from concurrent.futures import Thread<PERSON>oolExecutor
from fastapi.testclient import TestClient

from main import app
from services.realtime_risk_monitor import realtime_monitor
from models.risk_monitoring import SMAClassification, SMAProgressionData, calculate_provision_amount


class TestPerformanceRequirements:
    """Test performance requirements for Risk Monitor."""
    
    def setup_method(self):
        """Set up test client."""
        self.client = TestClient(app)
    
    def test_sma_heatmap_load_time(self):
        """Test SMA heatmap loads within 2 seconds."""
        start_time = time.time()
        
        response = self.client.get("/dashboard/risk-monitor/sma-heatmap")
        
        load_time = time.time() - start_time
        
        assert response.status_code == 200
        assert load_time <= 2.0, f"SMA heatmap took {load_time:.2f}s, exceeds 2s requirement"
    
    def test_portfolio_summary_load_time(self):
        """Test portfolio summary loads within 2 seconds."""
        start_time = time.time()
        
        response = self.client.get("/dashboard/risk-monitor/portfolio-summary")
        
        load_time = time.time() - start_time
        
        assert response.status_code == 200
        assert load_time <= 2.0, f"Portfolio summary took {load_time:.2f}s, exceeds 2s requirement"
    
    def test_real_time_events_load_time(self):
        """Test real-time events load within 2 seconds."""
        start_time = time.time()
        
        response = self.client.get("/dashboard/risk-monitor/real-time-events")
        
        load_time = time.time() - start_time
        
        assert response.status_code == 200
        assert load_time <= 2.0, f"Real-time events took {load_time:.2f}s, exceeds 2s requirement"
    
    def test_sma_progression_alerts_load_time(self):
        """Test SMA progression alerts load within 2 seconds."""
        start_time = time.time()
        
        response = self.client.get("/dashboard/risk-monitor/sma-progression")
        
        load_time = time.time() - start_time
        
        assert response.status_code == 200
        assert load_time <= 2.0, f"SMA progression alerts took {load_time:.2f}s, exceeds 2s requirement"
    
    def test_portfolio_metrics_load_time(self):
        """Test portfolio metrics load within 2 seconds."""
        start_time = time.time()
        
        response = self.client.get("/dashboard/risk-monitor/portfolio-metrics")
        
        load_time = time.time() - start_time
        
        assert response.status_code == 200
        assert load_time <= 2.0, f"Portfolio metrics took {load_time:.2f}s, exceeds 2s requirement"
    
    def test_sma_classification_load_time(self):
        """Test SMA classification loads within 2 seconds."""
        due_date = (date.today() - timedelta(days=45)).strftime("%Y-%m-%d")
        outstanding_amount = 1000000
        
        start_time = time.time()
        
        response = self.client.post(
            f"/dashboard/risk-monitor/classify-sma/test_001?due_date={due_date}&outstanding_amount={outstanding_amount}"
        )
        
        load_time = time.time() - start_time
        
        assert response.status_code == 200
        assert load_time <= 2.0, f"SMA classification took {load_time:.2f}s, exceeds 2s requirement"
    
    def test_concurrent_requests_performance(self):
        """Test performance under concurrent load."""
        def make_request():
            start_time = time.time()
            response = self.client.get("/dashboard/risk-monitor/sma-heatmap")
            load_time = time.time() - start_time
            return response.status_code == 200, load_time
        
        # Test with 10 concurrent requests
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(10)]
            results = [future.result() for future in futures]
        
        # All requests should succeed
        success_rates = [result[0] for result in results]
        load_times = [result[1] for result in results]
        
        assert all(success_rates), "Some concurrent requests failed"
        assert max(load_times) <= 3.0, f"Max concurrent load time {max(load_times):.2f}s exceeds 3s threshold"
        assert sum(load_times) / len(load_times) <= 2.5, f"Average concurrent load time exceeds 2.5s threshold"
    
    @pytest.mark.asyncio
    async def test_async_service_performance(self):
        """Test async service methods performance."""
        start_time = time.time()
        
        # Test multiple async operations
        tasks = [
            realtime_monitor.get_sma_heatmap_data(),
            realtime_monitor.get_portfolio_sma_summary(),
            realtime_monitor.monitor_sma_progression(),
            realtime_monitor.generate_risk_events()
        ]
        
        results = await asyncio.gather(*tasks)
        
        load_time = time.time() - start_time
        
        assert len(results) == 4
        assert all(result is not None for result in results)
        assert load_time <= 2.0, f"Async operations took {load_time:.2f}s, exceeds 2s requirement"
    
    def test_large_dataset_performance(self):
        """Test performance with larger datasets."""
        # This would test with 1000+ MSME records in a real scenario
        # For now, we test the current implementation
        start_time = time.time()
        
        response = self.client.get("/dashboard/risk-monitor/portfolio-summary")
        
        load_time = time.time() - start_time
        
        assert response.status_code == 200
        assert load_time <= 2.0, f"Large dataset processing took {load_time:.2f}s, exceeds 2s requirement"
    
    def test_memory_usage_efficiency(self):
        """Test memory usage remains reasonable."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Make multiple requests to test memory usage
        for _ in range(50):
            response = self.client.get("/dashboard/risk-monitor/sma-heatmap")
            assert response.status_code == 200
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # Memory increase should be reasonable (less than 100MB for 50 requests)
        assert memory_increase < 100, f"Memory increased by {memory_increase:.2f}MB, potential memory leak"
    
    def test_response_size_efficiency(self):
        """Test response sizes are reasonable."""
        response = self.client.get("/dashboard/risk-monitor/sma-heatmap")
        assert response.status_code == 200

        content_length = len(response.content)
        # Response should be less than 1MB
        assert content_length < 1024 * 1024, f"Response size {content_length} bytes exceeds 1MB"

        response = self.client.get("/dashboard/risk-monitor/portfolio-summary")
        assert response.status_code == 200
        
        content_length = len(response.content)
        assert content_length < 1024 * 1024, f"Response size {content_length} bytes exceeds 1MB"


class TestScalabilityRequirements:
    """Test scalability requirements for Risk Monitor."""
    
    def setup_method(self):
        """Set up test client."""
        self.client = TestClient(app)
    
    def test_multiple_msme_classification(self):
        """Test classifying multiple MSMEs efficiently."""
        msme_ids = [f"test_{i:03d}" for i in range(20)]
        due_date = (date.today() - timedelta(days=45)).strftime("%Y-%m-%d")
        outstanding_amount = 1000000
        
        start_time = time.time()
        
        for msme_id in msme_ids:
            response = self.client.post(
                f"/dashboard/risk-monitor/classify-sma/{msme_id}?due_date={due_date}&outstanding_amount={outstanding_amount}"
            )
            assert response.status_code == 200
        
        total_time = time.time() - start_time
        avg_time_per_msme = total_time / len(msme_ids)
        
        assert avg_time_per_msme <= 0.5, f"Average classification time {avg_time_per_msme:.2f}s exceeds 0.5s per MSME"
    
    @pytest.mark.asyncio
    async def test_batch_processing_efficiency(self):
        """Test batch processing of SMA data."""
        start_time = time.time()
        
        # Simulate batch processing of multiple MSMEs
        batch_tasks = []
        for i in range(20):
            due_date = date.today() - timedelta(days=30 + i)
            task = realtime_monitor.classify_msme_sma_status(
                msme_id=f"batch_test_{i:03d}",
                due_date=due_date,
                outstanding_amount=1000000 + (i * 100000)
            )
            batch_tasks.append(task)
        
        results = await asyncio.gather(*batch_tasks)
        
        batch_time = time.time() - start_time
        
        assert len(results) == 20
        assert all(result.msme_id.startswith("batch_test_") for result in results)
        assert batch_time <= 3.0, f"Batch processing took {batch_time:.2f}s, exceeds 3s threshold"
    
    def test_api_rate_limiting_behavior(self):
        """Test API behavior under rapid requests."""
        start_time = time.time()
        
        # Make 100 rapid requests
        success_count = 0
        for _ in range(100):
            response = self.client.get("/dashboard/risk-monitor/sma-heatmap")
            if response.status_code == 200:
                success_count += 1
        
        total_time = time.time() - start_time
        
        # Should handle at least 80% of requests successfully
        success_rate = success_count / 100
        assert success_rate >= 0.8, f"Success rate {success_rate:.2f} below 80% threshold"
        
        # Should complete within reasonable time
        assert total_time <= 10.0, f"100 requests took {total_time:.2f}s, exceeds 10s threshold"


class TestRBICompliancePerformance:
    """Test RBI compliance features performance."""
    
    def setup_method(self):
        """Set up test client."""
        self.client = TestClient(app)
    
    @pytest.mark.asyncio
    async def test_compliance_calculation_performance(self):
        """Test RBI compliance calculations are fast."""
        start_time = time.time()
        
        # Test provision calculations for different SMA classifications
        
        test_amounts = [1000000, 5000000, 10000000, 50000000, 100000000]
        sma_classifications = [
            SMAClassification.STANDARD,
            SMAClassification.SMA_0,
            SMAClassification.SMA_1,
            SMAClassification.SMA_2,
            SMAClassification.NPA
        ]
        
        for amount in test_amounts:
            for classification in sma_classifications:
                provision = calculate_provision_amount(amount, classification)
                assert provision >= 0
        
        calculation_time = time.time() - start_time
        
        assert calculation_time <= 0.1, f"Provision calculations took {calculation_time:.3f}s, exceeds 0.1s"
    
    @pytest.mark.asyncio
    async def test_audit_trail_performance(self):
        """Test audit trail operations are efficient."""
        from models.risk_monitoring import SMAProgressionData, AuditAction
        
        start_time = time.time()
        
        # Create SMA data with multiple audit entries
        sma_data = SMAProgressionData(
            msme_id="perf_test_001",
            msme_name="Performance Test MSME",
            business_type="retail",
            location="Mumbai",
            current_classification=SMAClassification.SMA_1,
            days_past_due=45,
            outstanding_amount=1000000,
            credit_limit=1200000,
            next_review_date=date.today() + timedelta(days=7)
        )
        
        # Add 100 audit entries
        for i in range(100):
            sma_data.add_audit_entry(
                action=AuditAction.UPDATED,
                description=f"Test audit entry {i}",
                performed_by="performance_test"
            )
        
        audit_time = time.time() - start_time
        
        assert len(sma_data.audit_trail) == 100
        assert audit_time <= 0.5, f"Audit trail operations took {audit_time:.3f}s, exceeds 0.5s"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
