'use client';

import { useEffect, useState, useMemo, useC<PERSON>back, memo } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Activity,
  AlertTriangle,
  BarChart3,
  Building2,
  ChevronDown,
  ChevronUp,
  DollarSign,
  Filter,
  Lightbulb,
  MapPin,
  MessageSquare,
  Minus,
  Search,
  Shield,
  Target,
  TrendingDown,
  TrendingUp,
  Zap,
} from 'lucide-react';
import {
  Cell,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip as RechartsTooltip } from 'recharts';

import { BulkNudgeModal } from '@/components/msme/bulk-nudge-modal';
import { ReportsDashboard } from '@/components/reports/reports-dashboard';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { api } from '@/lib/api';
import { Analytics, MSME, TopAction } from '@/types';

/**
 * Portfolio Dashboard Component
 *
 * Main dashboard for viewing and managing MSME portfolio with comprehensive
 * analytics and risk monitoring capabilities. Features include:
 *
 * - Portfolio overview with 4-column table (Business Name, Risk Band, Credit Score, etc.)
 * - Analytics & insights tab with business type distribution and KPIs
 * - Risk filtering by band (green, yellow, red) with proper distribution
 * - Search functionality across MSME names and locations
 * - Top Opportunities & Actions section with actionable insights
 * - Bulk operations support for nudges and portfolio management
 * - Real-time data synchronization with emerald color palette
 * - Responsive design with minimalist UI principles
 *
 * @component
 * @example
 * ```tsx
 * // Basic usage
 * <PortfolioDashboard />
 *
 * // With URL filtering (handled automatically via searchParams)
 * // URL: /portfolio?filter=red
 * <PortfolioDashboard />
 * ```
 *
 * @returns The portfolio dashboard component
 */
export function PortfolioDashboard() {
  const [msmes, setMsmes] = useState<MSME[]>([]);
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [aiInsights, setAiInsights] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showBulkNudgeModal, setShowBulkNudgeModal] = useState(false);
  const [activeTab, setActiveTab] = useState('portfolio');
  const [isTopActionsCollapsed, setIsTopActionsCollapsed] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const filterRisk = searchParams?.get('filter');

  // Memoized navigation handlers for better performance
  const handleMSMEClick = useCallback((msmeId: string) => {
    router.push(`/msme/${msmeId}`);
  }, [router]);

  const handleFilterClick = useCallback((filter: string) => {
    router.push(`/?filter=${filter}`);
  }, [router]);

  const handleClearFilter = useCallback(() => {
    router.push('/');
    setSearchTerm('');
  }, [router]);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        const [portfolioData, analyticsData, insightsData] = await Promise.all([
          api.getPortfolio(),
          api.getAnalytics(),
          api.getCopilotInsights().catch(() => ({ insights: [] })) // Graceful fallback
        ]);
        setMsmes(portfolioData);
        setAnalytics(analyticsData);
        setAiInsights(insightsData.insights?.slice(0, 3) || []); // Top 3 insights
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  // Generate enhanced top actions with specific metrics and insights
  const generateTopActions = (): TopAction[] => {
    if (!analytics || msmes.length === 0) return [];

    const actions: TopAction[] = [];

    // Calculate detailed portfolio metrics
    const highRiskMsmes = msmes.filter(m => m.risk_band === 'red');
    const mediumRiskMsmes = msmes.filter(m => m.risk_band === 'yellow');
    const lowGstCompliance = msmes.filter(m => (m.gst_compliance || 0) < 70);

    // Calculate potential exposure and revenue
    const avgExposurePerMsme = 2.5; // ₹2.5L average exposure
    const totalHighRiskExposure = highRiskMsmes.length * avgExposurePerMsme;
    const potentialRevenue = mediumRiskMsmes.length * 1.8; // ₹1.8L potential per MSME

    // 1. High-Risk Portfolio Review (Priority Action)
    if (highRiskMsmes.length > 0) {
      const urgentCount = highRiskMsmes.filter(m => m.current_score < 40).length;
      actions.push({
        id: 'high_risk_review',
        title: 'Critical Risk Portfolio Review',
        description: `${highRiskMsmes.length} high-risk MSMEs (${urgentCount} critical) require immediate intervention. Total exposure: ₹${totalHighRiskExposure.toFixed(1)}L`,
        impact: 'high',
        type: 'risk_reduction',
        msme_count: highRiskMsmes.length,
        potential_value: `₹${totalHighRiskExposure.toFixed(1)}L exposure at risk`,
        action_items: [
          `Review ${urgentCount} critical cases (score <40)`,
          'Assess collateral and guarantees',
          'Implement enhanced monitoring protocols',
          'Schedule immediate risk assessment calls'
        ],
        priority_score: 95,
        estimated_completion_days: 3
      });
    }

    // 2. GST Compliance Enhancement
    if (lowGstCompliance.length > 0) {
      const complianceGap = lowGstCompliance.reduce((sum, m) => sum + (70 - (m.gst_compliance || 0)), 0);
      actions.push({
        id: 'gst_compliance',
        title: 'GST Compliance Enhancement',
        description: `${lowGstCompliance.length} MSMEs below 70% GST compliance. Average compliance gap: ${(complianceGap / lowGstCompliance.length).toFixed(0)}%`,
        impact: 'high',
        type: 'efficiency',
        msme_count: lowGstCompliance.length,
        potential_value: `${(complianceGap * 0.02).toFixed(1)} score points recoverable`,
        action_items: [
          'Review GST filing patterns and delays',
          'Provide compliance support and guidance',
          'Set up automated GST monitoring alerts',
          'Schedule compliance improvement meetings'
        ],
        priority_score: 85,
        estimated_completion_days: 7
      });
    }

    // 3. Growth & Revenue Opportunities
    if (mediumRiskMsmes.length > 0) {
      const growthCandidates = mediumRiskMsmes.filter(m => m.current_score > 55).length;
      actions.push({
        id: 'growth_opportunities',
        title: 'Revenue Growth Opportunities',
        description: `${growthCandidates} MSMEs ready for credit enhancement. ${mediumRiskMsmes.length - growthCandidates} need improvement first`,
        impact: 'medium',
        type: 'ltv_optimization',
        msme_count: mediumRiskMsmes.length,
        potential_value: `₹${potentialRevenue.toFixed(1)}L revenue potential`,
        action_items: [
          `Fast-track ${growthCandidates} ready candidates`,
          'Analyze business growth patterns',
          'Review and propose credit limit increases',
          'Develop relationship expansion strategies'
        ],
        priority_score: 70,
        estimated_completion_days: 5
      });
    }

    return actions.slice(0, 3); // Top 3 actions with enhanced insights
  };

  const topActions = useMemo(() => generateTopActions(), [analytics, msmes]);

  const filteredMsmes = useMemo(() => {
    return msmes.filter(msme => {
      const matchesSearch = searchTerm === '' ||
        msme.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        msme.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        msme.business_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        msme.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

      const matchesFilter = !filterRisk || msme.risk_band === filterRisk;

      return matchesSearch && matchesFilter;
    });
  }, [msmes, searchTerm, filterRisk]);

  const getRiskLabel = (risk: string) => {
    switch (risk) {
      case 'green': return 'Low';
      case 'yellow': return 'Medium';
      case 'red': return 'High';
      default: return 'Unknown';
    }
  };

  const getRiskBadgeVariant = (risk: string) => {
    switch (risk) {
      case 'green': return 'default';
      case 'yellow': return 'secondary';
      case 'red': return 'destructive';
      default: return 'outline';
    }
  };

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <Minus className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getBusinessTypeIcon = (type: string) => {
    switch (type) {
      case 'retail': return '🏪';
      case 'manufacturing': return '🏭';
      case 'services': return '💼';
      case 'b2b': return '🤝';
      default: return '🏢';
    }
  };

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'risk_reduction': return <Shield className="h-5 w-5" />;
      case 'ltv_optimization': return <DollarSign className="h-5 w-5" />;
      case 'portfolio_growth': return <TrendingUp className="h-5 w-5" />;
      case 'efficiency': return <Zap className="h-5 w-5" />;
      default: return <Lightbulb className="h-5 w-5" />;
    }
  };

  const getActionColor = (type: string) => {
    switch (type) {
      case 'risk_reduction': return 'text-red-600 bg-red-50 border-red-200';
      case 'ltv_optimization': return 'text-green-600 bg-green-50 border-green-200';
      case 'portfolio_growth': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'efficiency': return 'text-purple-600 bg-purple-50 border-purple-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="space-y-2">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-24 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-destructive">
          <CardHeader>
            <CardTitle className="text-destructive">Error Loading Portfolio</CardTitle>
            <CardDescription>{error}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => window.location.reload()}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <TooltipProvider>
      <div className="p-4 space-y-3">
        {/* Compact Header */}
        <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
          <div className="space-y-1">
            <h1 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
              Portfolio Dashboard
              {filterRisk && (
                <span className="ml-2 text-lg font-normal text-gray-600 dark:text-gray-400">
                  {getRiskLabel(filterRisk)} Risk
                </span>
              )}
            </h1>
            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
              <span className="flex items-center gap-1">
                <Building2 className="h-4 w-4" />
                {filteredMsmes.length} of {msmes.length} MSMEs
              </span>
            </div>
          </div>
        </div>

        {/* Enhanced Top Opportunities & Actions Section - 30% Smaller */}
        {topActions.length > 0 && (
          <Card className="border-0 shadow-sm bg-gradient-to-r from-emerald-50 to-emerald-100 dark:from-emerald-950 dark:to-emerald-900 mb-1">
            <CardHeader className="pb-1 pt-2">
              <CardTitle className="flex items-center gap-1.5 text-sm">
                <div className="p-0.5 bg-emerald-500 rounded-lg">
                  <Target className="h-3.5 w-3.5 text-white" />
                </div>
                Top Opportunities & Actions
                <Badge variant="secondary" className="ml-auto bg-emerald-100 text-emerald-700 text-xs px-1.5 py-0.5">
                  {topActions.length} Items
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 ml-2 hover:bg-emerald-200/50"
                  onClick={() => setIsTopActionsCollapsed(!isTopActionsCollapsed)}
                >
                  {isTopActionsCollapsed ? (
                    <ChevronDown className="h-3.5 w-3.5 text-emerald-700" />
                  ) : (
                    <ChevronUp className="h-3.5 w-3.5 text-emerald-700" />
                  )}
                </Button>
              </CardTitle>
            </CardHeader>
            {!isTopActionsCollapsed && (
              <CardContent className="pt-0 pb-2">
                <div className="grid gap-2 grid-cols-1 md:grid-cols-3">
                  {topActions.map((action) => (
                  <Card key={action.id} className={`border border-emerald-200 shadow-sm ${getActionColor(action.type)} hover:shadow-md transition-all duration-200`}>
                    <CardContent className="p-1.5">
                      <div className="flex items-start gap-1.5 mb-1">
                        <div className="p-0.5 bg-white/90 rounded-lg shadow-sm">
                          {getActionIcon(action.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className="text-sm font-semibold leading-tight mb-0.5">{action.title}</h4>
                          <p className="text-xs text-gray-600 leading-tight line-clamp-2">{action.description}</p>
                        </div>
                      </div>

                      {/* Enhanced Key Metrics with reduced spacing */}
                      <div className="grid grid-cols-2 gap-1 mb-1">
                        <div className="bg-white/70 rounded-lg p-0.5">
                          <div className="text-xs text-gray-500 mb-0.5">MSMEs</div>
                          <div className="text-sm font-bold text-gray-800">{action.msme_count}</div>
                        </div>
                        <div className="bg-white/70 rounded-lg p-0.5">
                          <div className="text-xs text-gray-500 mb-0.5">Value</div>
                          <div className="text-sm font-semibold text-emerald-600">{action.potential_value}</div>
                        </div>
                      </div>

                      {/* Additional Details */}
                      <div className="bg-white/50 rounded-lg p-0.5 mb-1">
                        <div className="flex justify-between items-center text-xs">
                          <span className="text-gray-500">Priority Score:</span>
                          <span className="font-semibold text-gray-700">{action.priority_score}/100</span>
                        </div>
                        <div className="flex justify-between items-center text-xs mt-0.5">
                          <span className="text-gray-500">Est. Completion:</span>
                          <span className="font-semibold text-gray-700">{action.estimated_completion_days} days</span>
                        </div>
                      </div>

                      {/* Action Button */}
                      <div className="flex justify-end">
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-5 px-2.5 text-xs hover:bg-emerald-50 hover:border-emerald-200"
                          onClick={() => {
                            router.push(`/portfolio?filter=${action.type === 'risk_reduction' ? 'red' : 'yellow'}`);
                          }}
                        >
                          View
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                  ))}
                </div>
              </CardContent>
            )}
          </Card>
        )}

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-1">
          <TabsList className="grid w-full grid-cols-3 bg-gray-100 dark:bg-gray-800">
            <TabsTrigger value="portfolio" className="data-[state=active]:bg-emerald-500 data-[state=active]:text-white">Portfolio Overview</TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-emerald-500 data-[state=active]:text-white">Analytics & Insights</TabsTrigger>
            <TabsTrigger value="reports" className="data-[state=active]:bg-emerald-500 data-[state=active]:text-white">Reports</TabsTrigger>
          </TabsList>

          <TabsContent value="portfolio" className="space-y-1 mt-1">
            {/* Portfolio Overview */}
            <Card className="border-0 shadow-lg bg-white dark:bg-gray-900">
              <CardHeader className="pb-1 pt-3 border-b border-gray-100 dark:border-gray-800">
                <CardTitle className="flex items-center gap-2 text-lg font-semibold">
                  <div className="p-1.5 bg-emerald-500/10 rounded-lg">
                    <Building2 className="h-4 w-4 text-emerald-600" />
                  </div>
                  Portfolio Overview
                  <Badge variant="secondary" className="ml-auto bg-emerald-50 text-emerald-700 border-emerald-200 px-2 py-0.5 text-xs">
                    {filteredMsmes.length} MSMEs
                  </Badge>
                </CardTitle>
                <CardDescription className="text-xs text-gray-600 dark:text-gray-400 mt-0.5">
                  Click on any MSME row to view detailed information and score breakdown
                </CardDescription>

                {/* Search and Filters - moved inside Portfolio Overview */}
                <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-800">
                  <div className="flex flex-col gap-3 md:flex-row md:items-center md:justify-between bg-gray-50 dark:bg-gray-900 rounded-lg p-3">
                    <div className="relative flex-1 max-w-sm">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Search MSMEs by name, location..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 shadow-sm border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
                      />
                    </div>

                    <div className="flex gap-2">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant={filterRisk === 'red' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => handleFilterClick('red')}
                            className="shadow-sm"
                          >
                            <AlertTriangle className="mr-2 h-4 w-4" />
                            High Risk ({analytics?.risk_distribution.red || 0})
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Show only high-risk MSMEs</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant={filterRisk === 'yellow' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => handleFilterClick('yellow')}
                            className="shadow-sm"
                          >
                            <Filter className="mr-2 h-4 w-4" />
                            Medium Risk ({analytics?.risk_distribution.yellow || 0})
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Show only medium-risk MSMEs</p>
                        </TooltipContent>
                      </Tooltip>

                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant={filterRisk === 'green' ? 'default' : 'outline'}
                            size="sm"
                            onClick={() => handleFilterClick('green')}
                            className="shadow-sm"
                          >
                            <Target className="mr-2 h-4 w-4" />
                            Low Risk ({analytics?.risk_distribution.green || 0})
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Show only low-risk MSMEs</p>
                        </TooltipContent>
                      </Tooltip>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowBulkNudgeModal(true)}
                        className="shadow-sm"
                      >
                        <MessageSquare className="mr-2 h-4 w-4" />
                        Bulk Nudge
                      </Button>
                    </div>
                  </div>

                  {filterRisk && (
                    <div className="flex items-center gap-2 mt-3">
                      <Badge variant="secondary">
                        Filtered by {getRiskLabel(filterRisk)} Risk
                      </Badge>
                      <Button
                        variant="link"
                        className="p-0 h-auto text-primary hover:text-primary/80"
                        onClick={handleClearFilter}
                      >
                        Clear filter
                      </Button>
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="p-0">
                <div className="overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow className="bg-emerald-50/50 dark:bg-emerald-950/20 border-b border-emerald-100 dark:border-emerald-800">
                        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 py-4 px-4">Business Name</TableHead>
                        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 text-center py-4 px-3">Risk Band</TableHead>
                        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 text-center py-4 px-3">Credit Score</TableHead>
                        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 text-center py-4 px-3">GST Compliance</TableHead>
                        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 text-center py-4 px-3">Banking Health</TableHead>
                        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 text-center py-4 px-3">Business Type</TableHead>
                        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 text-center py-4 px-3">Location</TableHead>
                        <TableHead className="font-semibold text-gray-700 dark:text-gray-300 text-center py-4 px-4">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredMsmes.map((msme) => (
                        <TableRow
                          key={msme.msme_id}
                          className="group hover:bg-emerald-50/50 dark:hover:bg-emerald-950/20 transition-all duration-200 border-b border-gray-100 dark:border-gray-800 cursor-pointer"
                          onClick={() => handleMSMEClick(msme.msme_id)}
                        >
                          <TableCell className="font-medium py-4 px-4">
                            <div className="flex items-center gap-3">
                              <div className="flex-shrink-0">
                                <div className="w-10 h-10 rounded-lg bg-emerald-500/10 flex items-center justify-center text-emerald-600 shadow-sm">
                                  {getBusinessTypeIcon(msme.business_type)}
                                </div>
                              </div>
                              <div className="min-w-0 flex-1">
                                <div className="font-semibold text-sm text-gray-900 dark:text-gray-100">{msme.name}</div>
                                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                  {msme.gst_number || 'GST-' + msme.msme_id.slice(-6).toUpperCase()}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-center py-4 px-3">
                            <Badge
                              variant={getRiskBadgeVariant(msme.risk_band)}
                              className="font-semibold px-2 py-1 text-xs shadow-sm"
                            >
                              {getRiskLabel(msme.risk_band)}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-center py-4 px-3">
                            <div className="flex flex-col items-center gap-1">
                              <div className="text-lg font-bold text-gray-900 dark:text-gray-100">{msme.current_score}</div>
                              <div className="flex items-center gap-1">
                                {getTrendIcon(msme.score_trend)}
                                <span className="text-xs text-gray-500 capitalize">
                                  {msme.score_trend || 'stable'}
                                </span>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-center py-4 px-3">
                            <div className="flex flex-col items-center gap-1">
                              <div className={`text-sm font-semibold ${
                                (msme.gst_compliance || 0) >= 85 ? 'text-emerald-600' :
                                (msme.gst_compliance || 0) >= 70 ? 'text-yellow-600' : 'text-red-600'
                              }`}>
                                {msme.gst_compliance ? `${msme.gst_compliance}%` : 'N/A'}
                              </div>
                              <div className="text-xs text-gray-500">
                                {(msme.gst_compliance || 0) >= 85 ? 'Excellent' :
                                 (msme.gst_compliance || 0) >= 70 ? 'Good' : 'Needs Attention'}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-center py-4 px-3">
                            <div className="flex flex-col items-center gap-1">
                              <div className={`text-sm font-semibold ${
                                (msme.banking_health || 0) >= 80 ? 'text-emerald-600' :
                                (msme.banking_health || 0) >= 60 ? 'text-yellow-600' : 'text-red-600'
                              }`}>
                                {msme.banking_health ? `${msme.banking_health}%` : 'N/A'}
                              </div>
                              <div className="text-xs text-gray-500">
                                {(msme.banking_health || 0) >= 80 ? 'Strong' :
                                 (msme.banking_health || 0) >= 60 ? 'Moderate' : 'Weak'}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-center py-4 px-3">
                            <div className="flex flex-col items-center gap-1">
                              <div className="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
                                {msme.business_type}
                              </div>
                              <div className="text-xs text-gray-500">
                                {msme.business_type === 'manufacturing' ? 'Mfg' :
                                 msme.business_type === 'retail' ? 'Retail' :
                                 msme.business_type === 'services' ? 'Service' : 'B2B'}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-center py-4 px-3">
                            <div className="flex flex-col items-center gap-1">
                              <div className="text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center gap-1">
                                <MapPin className="h-3 w-3 text-gray-400" />
                                {msme.location}
                              </div>
                              <div className="text-xs text-gray-500">
                                {(() => {
                                  const lastUpdated = new Date(msme.last_updated || Date.now());
                                  const now = new Date();
                                  const diffDays = Math.floor((now.getTime() - lastUpdated.getTime()) / (1000 * 60 * 60 * 24));

                                  if (diffDays === 0) return 'Today';
                                  if (diffDays === 1) return '1d ago';
                                  if (diffDays < 7) return `${diffDays}d ago`;
                                  return `${Math.floor(diffDays / 7)}w ago`;
                                })()}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="text-center py-4 px-4">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={(e: React.MouseEvent) => {
                                e.stopPropagation();
                                handleMSMEClick(msme.msme_id);
                              }}
                              className="opacity-0 group-hover:opacity-100 transition-all duration-200 border-emerald-200 text-emerald-700 hover:bg-emerald-50 hover:border-emerald-300 font-medium"
                            >
                              View
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                {filteredMsmes.length === 0 && (
                  <div className="text-center py-12">
                    <div className="flex flex-col items-center gap-4">
                      <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center">
                        <Building2 className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <div className="space-y-2">
                        <h3 className="text-lg font-semibold">No MSMEs found</h3>
                        <p className="text-muted-foreground">
                          {searchTerm ? (
                            <>No MSMEs match your search criteria &ldquo;{searchTerm}&rdquo;</>
                          ) : filterRisk ? (
                            <>No MSMEs found with {getRiskLabel(filterRisk).toLowerCase()} risk level</>
                          ) : (
                            <>Your portfolio is empty. Add some MSMEs to get started.</>
                          )}
                        </p>
                      </div>
                      {(searchTerm || filterRisk) && (
                        <Button
                          variant="outline"
                          onClick={handleClearFilter}
                        >
                          Clear filters
                        </Button>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            {/* Portfolio Overview KPIs */}
            {analytics && (
              <div className="grid gap-6 md:grid-cols-4">
                <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950 dark:to-emerald-900 hover:shadow-lg transition-all duration-200">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                    <CardTitle className="text-sm font-medium text-emerald-700 dark:text-emerald-300">Total MSMEs</CardTitle>
                    <div className="p-2 bg-emerald-500 rounded-lg">
                      <Building2 className="h-4 w-4 text-white" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-emerald-900 dark:text-emerald-100 mb-2">
                      {analytics.total_msmes}
                    </div>
                    <p className="text-sm text-emerald-600 dark:text-emerald-400">
                      Active portfolio businesses
                    </p>
                  </CardContent>
                  <div className="absolute top-0 right-0 w-20 h-20 bg-emerald-500/10 rounded-full -translate-y-10 translate-x-10" />
                </Card>

                <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900 hover:shadow-lg transition-all duration-200">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                    <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">Triggered Escalations</CardTitle>
                    <div className="p-2 bg-blue-500 rounded-lg">
                      <AlertTriangle className="h-4 w-4 text-white" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-blue-900 dark:text-blue-100 mb-2">
                      {analytics?.triggered_escalations || 7}
                    </div>
                    <p className="text-sm text-blue-600 dark:text-blue-400">
                      Cases auto-flagged for RM or collections review
                    </p>
                  </CardContent>
                  <div className="absolute top-0 right-0 w-20 h-20 bg-blue-500/10 rounded-full -translate-y-10 translate-x-10" />
                </Card>

                <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900 hover:shadow-lg transition-all duration-200">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                    <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">Portfolio Health</CardTitle>
                    <div className="p-2 bg-purple-500 rounded-lg">
                      <Activity className="h-4 w-4 text-white" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-purple-900 dark:text-purple-100 mb-2">
                      {(() => {
                        const total = analytics.total_msmes;
                        const healthScore = Math.round(
                          ((analytics.risk_distribution.green * 100) +
                           (analytics.risk_distribution.yellow * 70) +
                           (analytics.risk_distribution.red * 30)) / total
                        );
                        return healthScore;
                      })()}%
                    </div>
                    <p className="text-sm text-purple-600 dark:text-purple-400">
                      Overall portfolio health
                    </p>
                  </CardContent>
                  <div className="absolute top-0 right-0 w-20 h-20 bg-purple-500/10 rounded-full -translate-y-10 translate-x-10" />
                </Card>

                <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900 hover:shadow-lg transition-all duration-200">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                    <CardTitle className="text-sm font-medium text-orange-700 dark:text-orange-300">Growth Potential</CardTitle>
                    <div className="p-2 bg-orange-500 rounded-lg">
                      <TrendingUp className="h-4 w-4 text-white" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold text-orange-900 dark:text-orange-100 mb-2">
                      {analytics.risk_distribution.yellow + Math.floor(analytics.risk_distribution.green * 0.3)}
                    </div>
                    <p className="text-sm text-orange-600 dark:text-orange-400">
                      MSMEs ready for expansion
                    </p>
                  </CardContent>
                  <div className="absolute top-0 right-0 w-20 h-20 bg-orange-500/10 rounded-full -translate-y-10 translate-x-10" />
                </Card>
              </div>
            )}



            {/* Business Type Distribution & Portfolio Metrics */}
            {analytics && (
              <div className="grid gap-6 md:grid-cols-2">
                {/* Business Type Distribution - Pie Chart */}
                <Card className="border-0 shadow-lg">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-2">
                      <div className="p-2 bg-emerald-500/10 rounded-lg">
                        <Building2 className="h-5 w-5 text-emerald-500" />
                      </div>
                      Business Type Distribution
                    </CardTitle>
                    <CardDescription>
                      Portfolio composition by business category
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {(() => {
                      // Ensure we have business type distribution data
                      const businessTypes = analytics.business_type_distribution || {};
                      const chartData = Object.entries(businessTypes).map(([type, count]) => ({
                        name: type.charAt(0).toUpperCase() + type.slice(1),
                        value: count as number,
                        percentage: analytics.total_msmes > 0 ? ((count as number / analytics.total_msmes) * 100).toFixed(1) : '0'
                      }));

                      // Colors for pie chart segments
                      const COLORS = ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6'];

                      // If no data, show placeholder
                      if (chartData.length === 0 || chartData.every(item => item.value === 0)) {
                        return (
                          <div className="flex items-center justify-center h-64 text-muted-foreground">
                            <div className="text-center">
                              <Building2 className="h-8 w-8 mx-auto mb-2 opacity-50" />
                              <p className="text-sm">No business type data available</p>
                            </div>
                          </div>
                        );
                      }

                      return (
                        <div className="space-y-4">
                          <ResponsiveContainer width="100%" height={240}>
                            <PieChart>
                              <Pie
                                data={chartData}
                                cx="50%"
                                cy="50%"
                                labelLine={false}
                                label={({ name, percentage }) => `${name}: ${percentage}%`}
                                outerRadius={80}
                                fill="#8884d8"
                                dataKey="value"
                              >
                                {chartData.map((_, index) => (
                                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                ))}
                              </Pie>
                              <RechartsTooltip
                                formatter={(value) => [`${value} MSMEs`, 'Count']}
                                labelFormatter={(label) => `Business Type: ${label}`}
                              />
                            </PieChart>
                          </ResponsiveContainer>

                          {/* Legend */}
                          <div className="grid grid-cols-2 gap-2">
                            {chartData.map((item, index) => (
                              <div key={item.name} className="flex items-center gap-2">
                                <div
                                  className="w-3 h-3 rounded-full"
                                  style={{ backgroundColor: COLORS[index % COLORS.length] }}
                                />
                                <span className="text-sm font-medium">{item.name}</span>
                                <Badge variant="secondary" className="ml-auto text-xs">
                                  {item.value}
                                </Badge>
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    })()}
                  </CardContent>
                </Card>

                {/* Portfolio Performance Metrics */}
                <Card className="border-0 shadow-lg">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-2">
                      <div className="p-2 bg-blue-500/10 rounded-lg">
                        <BarChart3 className="h-5 w-5 text-blue-500" />
                      </div>
                      Portfolio Performance
                    </CardTitle>
                    <CardDescription>
                      Key performance indicators and trends
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {/* Performance Metrics */}
                      <div className="grid gap-4">
                        <div className="flex items-center justify-between p-3 bg-emerald-50 rounded-lg border border-emerald-200">
                          <div className="flex items-center gap-2">
                            <div className="p-1.5 bg-emerald-500 rounded-lg">
                              <TrendingUp className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <div className="text-sm font-medium text-emerald-700">Low Risk MSMEs</div>
                              <div className="text-xs text-emerald-600">Stable portfolio base</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-emerald-700">{analytics.risk_distribution.green}</div>
                            <div className="text-xs text-emerald-600">
                              {Math.round((analytics.risk_distribution.green / analytics.total_msmes) * 100)}%
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                          <div className="flex items-center gap-2">
                            <div className="p-1.5 bg-blue-500 rounded-lg">
                              <Target className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <div className="text-sm font-medium text-blue-700">Medium Risk MSMEs</div>
                              <div className="text-xs text-blue-600">Growth opportunities</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-blue-700">{analytics.risk_distribution.yellow}</div>
                            <div className="text-xs text-blue-600">
                              {Math.round((analytics.risk_distribution.yellow / analytics.total_msmes) * 100)}%
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                          <div className="flex items-center gap-2">
                            <div className="p-1.5 bg-orange-500 rounded-lg">
                              <AlertTriangle className="h-4 w-4 text-white" />
                            </div>
                            <div>
                              <div className="text-sm font-medium text-orange-700">High Risk MSMEs</div>
                              <div className="text-xs text-orange-600">Require attention</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-2xl font-bold text-orange-700">{analytics.risk_distribution.red}</div>
                            <div className="text-xs text-orange-600">
                              {Math.round((analytics.risk_distribution.red / analytics.total_msmes) * 100)}%
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Portfolio Summary */}
            {analytics && (
              <Card className="border-0 shadow-lg">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2">
                    <div className="p-2 bg-emerald-500/10 rounded-lg">
                      <Lightbulb className="h-5 w-5 text-emerald-500" />
                    </div>
                    Portfolio Summary
                  </CardTitle>
                  <CardDescription>
                    Key insights and performance indicators
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-3">
                      <h4 className="font-semibold text-sm">Portfolio Health</h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Healthy MSMEs (Low Risk)</span>
                          <span className="font-medium text-emerald-600">{analytics.risk_distribution.green}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Watch List (Medium Risk)</span>
                          <span className="font-medium text-yellow-600">{analytics.risk_distribution.yellow}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Attention Required (High Risk)</span>
                          <span className="font-medium text-red-600">{analytics.risk_distribution.red}</span>
                        </div>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <h4 className="font-semibold text-sm">Data Coverage</h4>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Total Data Signals</span>
                          <span className="font-medium">{analytics.total_signals}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Average per MSME</span>
                          <span className="font-medium">{analytics.average_signals_per_msme.toFixed(1)}</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-muted-foreground">Data Completeness</span>
                          <span className="font-medium">
                            {Math.min(100, Math.round((analytics.average_signals_per_msme / 8) * 100))}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* AI Insights Summary */}
            {aiInsights.length > 0 && (
              <Card className="border-0 shadow-lg">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2">
                    <div className="p-2 bg-emerald-500/10 rounded-lg">
                      <Lightbulb className="h-5 w-5 text-emerald-500" />
                    </div>
                    AI Insights & Recommendations
                  </CardTitle>
                  <CardDescription>
                    Top actionable insights from portfolio analysis
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid gap-4 md:grid-cols-3">
                    {aiInsights.map((insight, index) => (
                      <div
                        key={insight.id || index}
                        className="p-5 bg-emerald-50 dark:bg-emerald-950/20 rounded-lg border border-emerald-200 dark:border-emerald-800 space-y-4"
                      >
                        <div className="flex items-start gap-3.5">
                          <div className={`p-2 rounded-lg ${
                            insight.severity === 'high' ? 'bg-red-100 text-red-600' :
                            insight.severity === 'medium' ? 'bg-yellow-100 text-yellow-600' :
                            'bg-emerald-100 text-emerald-600'
                          }`}>
                            <AlertTriangle className="h-3.5 w-3.5" />
                          </div>
                          <div className="flex-1 min-w-0 space-y-2">
                            <h4 className="font-medium text-sm text-emerald-900 dark:text-emerald-100 line-clamp-2 leading-relaxed">
                              {insight.title}
                            </h4>
                            <p className="text-xs text-emerald-600 dark:text-emerald-400 line-clamp-3 leading-relaxed">
                              {insight.description}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 pt-1">
                          <Button
                            size="sm"
                            variant="outline"
                            className="h-7 px-3 text-xs hover:bg-emerald-50 hover:border-emerald-200"
                            onClick={() => {
                              // Navigate to detailed view for this insight
                              router.push('/analytics');
                            }}
                          >
                            View Details
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                  {aiInsights.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <Lightbulb className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">AI insights are being processed...</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Footer */}
            {analytics && (
              <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground bg-muted/30 rounded-lg p-4">
                <Activity className="h-4 w-4" />
                <span>Last updated: {new Date(analytics.last_updated).toLocaleString()}</span>
              </div>
            )}
          </TabsContent>

          <TabsContent value="reports" className="space-y-6">
            {/* Reports Dashboard */}
            <ReportsDashboard />
          </TabsContent>
        </Tabs>

        {/* Bulk Nudge Modal */}
        <BulkNudgeModal
          isOpen={showBulkNudgeModal}
          onClose={() => setShowBulkNudgeModal(false)}
          onNudgesSent={(count) => {
            console.log(`${count} nudges sent successfully`);
            setShowBulkNudgeModal(false);
          }}
        />
      </div>
    </TooltipProvider>
  );
}
