# Credit Chakra Deployment

This directory contains deployment configurations and scripts for the Credit Chakra platform.

## Prerequisites

1. **Google Cloud Project**: Create a GCP project with billing enabled
2. **gcloud CLI**: Install and authenticate with Google Cloud
3. **Firebase Project**: Set up Firebase with Firestore enabled
4. **Service Account**: Create a service account with necessary permissions

## Setup

### 1. Install gcloud CLI

```bash
# macOS
brew install google-cloud-sdk

# Linux
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Windows
# Download from https://cloud.google.com/sdk/docs/install
```

### 2. Authenticate and Set Project

```bash
# Login to Google Cloud
gcloud auth login

# Set your project ID
export GOOGLE_CLOUD_PROJECT="your-project-id"
gcloud config set project $GOOGLE_CLOUD_PROJECT

# Enable required APIs
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com
gcloud services enable firestore.googleapis.com
```

### 3. Firebase Setup

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Create a new project or select existing GCP project
3. Enable Firestore Database
4. Create a service account:
   ```bash
   gcloud iam service-accounts create credit-chakra-backend \
       --display-name="Credit Chakra Backend Service Account"
   
   gcloud projects add-iam-policy-binding $GOOGLE_CLOUD_PROJECT \
       --member="serviceAccount:credit-chakra-backend@$GOOGLE_CLOUD_PROJECT.iam.gserviceaccount.com" \
       --role="roles/datastore.user"
   
   gcloud iam service-accounts keys create firebase-key.json \
       --iam-account=credit-chakra-backend@$GOOGLE_CLOUD_PROJECT.iam.gserviceaccount.com
   ```

## Deployment

### Backend Deployment

1. **Quick Deployment**:
   ```bash
   cd deploy
   ./deploy.sh
   ```

2. **Manual Deployment**:
   ```bash
   # Build and push image
   cd backend
   gcloud builds submit --tag gcr.io/$GOOGLE_CLOUD_PROJECT/credit-chakra-backend
   
   # Deploy to Cloud Run
   cd ../deploy
   gcloud run services replace cloudrun.yaml --region=us-central1
   ```

### Environment Variables

The following environment variables are configured in Cloud Run:

- `PORT`: Application port (8000)
- `GOOGLE_CLOUD_PROJECT`: GCP project ID
- `DEBUG`: Debug mode (false for production)
- `ALLOWED_ORIGINS`: CORS allowed origins

Firebase credentials are automatically available in Cloud Run environment.

## Configuration Files

### cloudrun.yaml

Cloud Run service configuration with:
- **Resource Limits**: 1 CPU, 1GB RAM
- **Scaling**: 0-10 instances
- **Health Checks**: Liveness and readiness probes
- **Environment**: Production-ready settings

### Dockerfile

Multi-stage Docker build with:
- Python 3.11 slim base image
- Non-root user for security
- Health check endpoint
- Optimized for Cloud Run

## Monitoring and Logging

### Health Checks

- **Endpoint**: `/health`
- **Liveness Probe**: Every 10 seconds
- **Readiness Probe**: Every 5 seconds

### Logging

View logs using gcloud:

```bash
# Real-time logs
gcloud run services logs tail credit-chakra-backend --region=us-central1

# Recent logs
gcloud run services logs read credit-chakra-backend --region=us-central1 --limit=50
```

### Monitoring

Access Cloud Run metrics in the GCP Console:
- Request count and latency
- Error rates
- CPU and memory usage
- Instance count

## Security

### IAM Permissions

The service account needs:
- `roles/datastore.user` - Firestore access
- `roles/logging.logWriter` - Cloud Logging
- `roles/monitoring.metricWriter` - Cloud Monitoring

### Network Security

- **Ingress**: All traffic allowed (configure as needed)
- **CORS**: Configured for frontend domains
- **Authentication**: Firebase Auth integration

## Scaling

### Auto Scaling

- **Min Instances**: 0 (cost optimization)
- **Max Instances**: 10 (adjust based on load)
- **Concurrency**: 80 requests per instance

### Performance Tuning

- **CPU**: 1000m (1 vCPU)
- **Memory**: 1Gi
- **Timeout**: 300 seconds

## Troubleshooting

### Common Issues

1. **Build Failures**:
   ```bash
   # Check build logs
   gcloud builds log [BUILD_ID]
   ```

2. **Service Not Starting**:
   ```bash
   # Check service logs
   gcloud run services logs read credit-chakra-backend --region=us-central1
   ```

3. **Permission Errors**:
   ```bash
   # Verify service account permissions
   gcloud projects get-iam-policy $GOOGLE_CLOUD_PROJECT
   ```

### Debug Commands

```bash
# Get service details
gcloud run services describe credit-chakra-backend --region=us-central1

# List all revisions
gcloud run revisions list --service=credit-chakra-backend --region=us-central1

# Test health endpoint
curl https://your-service-url/health
```

## Cost Optimization

- **Cold Starts**: Min instances set to 0
- **Resource Limits**: Right-sized for workload
- **Request Timeout**: 5 minutes maximum
- **Automatic Scaling**: Based on request volume

## Next Steps

1. Set up CI/CD pipeline with GitHub Actions
2. Configure custom domain and SSL
3. Set up monitoring alerts
4. Implement backup strategies
5. Add load testing
