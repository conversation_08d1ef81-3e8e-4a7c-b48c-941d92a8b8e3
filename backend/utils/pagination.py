"""
Advanced pagination and filtering utilities for Credit Chakra API
Designed to handle large datasets efficiently (1000+ MSME records)
"""
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from datetime import datetime
import math

@dataclass
class PaginationParams:
    """Pagination parameters with validation"""
    page: int = 1
    limit: int = 10
    offset: int = 0
    
    def __post_init__(self):
        # Validate and normalize parameters
        self.page = max(1, self.page)
        self.limit = max(1, min(100, self.limit))  # Max 100 items per page
        self.offset = (self.page - 1) * self.limit

@dataclass
class FilterParams:
    """Advanced filtering parameters"""
    risk_band: Optional[str] = None
    business_type: Optional[str] = None
    location: Optional[str] = None
    score_min: Optional[float] = None
    score_max: Optional[float] = None
    gst_compliance_min: Optional[int] = None
    banking_health_min: Optional[int] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    search_term: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary, excluding None values"""
        return {k: v for k, v in self.__dict__.items() if v is not None}

@dataclass
class SortParams:
    """Sorting parameters"""
    field: str = 'created_at'
    direction: str = 'desc'  # 'asc' or 'desc'
    
    def __post_init__(self):
        # Validate sort direction
        if self.direction not in ['asc', 'desc']:
            self.direction = 'desc'

@dataclass
class PaginatedResponse:
    """Standardized paginated response"""
    data: List[Dict[str, Any]]
    pagination: Dict[str, Any]
    filters: Dict[str, Any]
    total_count: int
    
    @classmethod
    def create(
        cls,
        data: List[Dict[str, Any]],
        total_count: int,
        pagination_params: PaginationParams,
        filter_params: FilterParams,
        sort_params: SortParams
    ) -> 'PaginatedResponse':
        """Create a paginated response with metadata"""
        total_pages = math.ceil(total_count / pagination_params.limit)
        
        pagination_info = {
            'current_page': pagination_params.page,
            'per_page': pagination_params.limit,
            'total_pages': total_pages,
            'total_count': total_count,
            'has_next': pagination_params.page < total_pages,
            'has_prev': pagination_params.page > 1,
            'next_page': pagination_params.page + 1 if pagination_params.page < total_pages else None,
            'prev_page': pagination_params.page - 1 if pagination_params.page > 1 else None
        }
        
        return cls(
            data=data,
            pagination=pagination_info,
            filters=filter_params.to_dict(),
            total_count=total_count
        )

class AdvancedFilter:
    """Advanced filtering engine for MSME data"""
    
    @staticmethod
    def apply_filters(
        data: List[Dict[str, Any]], 
        filters: FilterParams
    ) -> List[Dict[str, Any]]:
        """Apply multiple filters to dataset"""
        filtered_data = data.copy()
        
        # Risk band filter
        if filters.risk_band:
            filtered_data = [
                item for item in filtered_data 
                if item.get('risk_band') == filters.risk_band
            ]
        
        # Business type filter
        if filters.business_type:
            filtered_data = [
                item for item in filtered_data 
                if item.get('business_type') == filters.business_type
            ]
        
        # Location filter (partial match)
        if filters.location:
            location_lower = filters.location.lower()
            filtered_data = [
                item for item in filtered_data 
                if location_lower in item.get('location', '').lower()
            ]
        
        # Score range filters
        if filters.score_min is not None:
            filtered_data = [
                item for item in filtered_data 
                if item.get('current_score', 0) >= filters.score_min
            ]
        
        if filters.score_max is not None:
            filtered_data = [
                item for item in filtered_data 
                if item.get('current_score', 0) <= filters.score_max
            ]
        
        # GST compliance filter
        if filters.gst_compliance_min is not None:
            filtered_data = [
                item for item in filtered_data 
                if item.get('gst_compliance', 0) >= filters.gst_compliance_min
            ]
        
        # Banking health filter
        if filters.banking_health_min is not None:
            filtered_data = [
                item for item in filtered_data 
                if item.get('banking_health', 0) >= filters.banking_health_min
            ]
        
        # Date range filters
        if filters.created_after:
            filtered_data = [
                item for item in filtered_data 
                if datetime.fromisoformat(item.get('created_at', '').replace('Z', '+00:00')) >= filters.created_after
            ]
        
        if filters.created_before:
            filtered_data = [
                item for item in filtered_data 
                if datetime.fromisoformat(item.get('created_at', '').replace('Z', '+00:00')) <= filters.created_before
            ]
        
        # Search term filter (searches name, location, business_type)
        if filters.search_term:
            search_lower = filters.search_term.lower()
            filtered_data = [
                item for item in filtered_data 
                if (
                    search_lower in item.get('name', '').lower() or
                    search_lower in item.get('location', '').lower() or
                    search_lower in item.get('business_type', '').lower()
                )
            ]
        
        return filtered_data
    
    @staticmethod
    def apply_sorting(
        data: List[Dict[str, Any]], 
        sort_params: SortParams
    ) -> List[Dict[str, Any]]:
        """Apply sorting to dataset"""
        reverse = sort_params.direction == 'desc'
        
        # Define sort key function based on field
        def get_sort_key(item: Dict[str, Any]) -> Any:
            value = item.get(sort_params.field)
            
            # Handle different data types
            if value is None:
                return 0 if sort_params.field in ['current_score', 'gst_compliance', 'banking_health'] else ''
            
            # For numeric fields
            if sort_params.field in ['current_score', 'gst_compliance', 'banking_health', 'monthly_turnover', 'digital_score']:
                return float(value) if value is not None else 0
            
            # For date fields
            if sort_params.field in ['created_at', 'last_signal_date']:
                try:
                    return datetime.fromisoformat(str(value).replace('Z', '+00:00'))
                except:
                    return datetime.min
            
            # For string fields
            return str(value).lower()
        
        try:
            return sorted(data, key=get_sort_key, reverse=reverse)
        except Exception:
            # Fallback to original order if sorting fails
            return data

class PaginationService:
    """Service for handling pagination, filtering, and sorting"""
    
    @staticmethod
    def paginate_data(
        data: List[Dict[str, Any]],
        pagination_params: PaginationParams,
        filter_params: FilterParams,
        sort_params: SortParams
    ) -> PaginatedResponse:
        """Apply filtering, sorting, and pagination to data"""
        
        # Step 1: Apply filters
        filtered_data = AdvancedFilter.apply_filters(data, filter_params)
        total_filtered = len(filtered_data)
        
        # Step 2: Apply sorting
        sorted_data = AdvancedFilter.apply_sorting(filtered_data, sort_params)
        
        # Step 3: Apply pagination
        start_idx = pagination_params.offset
        end_idx = start_idx + pagination_params.limit
        paginated_data = sorted_data[start_idx:end_idx]
        
        # Step 4: Create response
        return PaginatedResponse.create(
            data=paginated_data,
            total_count=total_filtered,
            pagination_params=pagination_params,
            filter_params=filter_params,
            sort_params=sort_params
        )
    
    @staticmethod
    def create_pagination_params(
        page: Optional[int] = None,
        limit: Optional[int] = None
    ) -> PaginationParams:
        """Create pagination parameters with defaults"""
        return PaginationParams(
            page=page or 1,
            limit=limit or 10
        )
    
    @staticmethod
    def create_filter_params(**kwargs) -> FilterParams:
        """Create filter parameters from keyword arguments"""
        return FilterParams(**kwargs)
    
    @staticmethod
    def create_sort_params(
        field: Optional[str] = None,
        direction: Optional[str] = None
    ) -> SortParams:
        """Create sort parameters with defaults"""
        return SortParams(
            field=field or 'created_at',
            direction=direction or 'desc'
        )

# Utility functions for common pagination patterns
def paginate_msme_portfolio(
    data: List[Dict[str, Any]],
    page: int = 1,
    limit: int = 10,
    risk_band: Optional[str] = None,
    business_type: Optional[str] = None,
    search: Optional[str] = None,
    sort_by: str = 'created_at',
    sort_order: str = 'desc'
) -> PaginatedResponse:
    """Convenience function for paginating MSME portfolio data"""
    
    pagination_params = PaginationService.create_pagination_params(page, limit)
    filter_params = FilterParams(
        risk_band=risk_band,
        business_type=business_type,
        search_term=search
    )
    sort_params = SortParams(field=sort_by, direction=sort_order)
    
    return PaginationService.paginate_data(
        data, pagination_params, filter_params, sort_params
    )

def get_portfolio_summary(data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Get summary statistics for portfolio data"""
    if not data:
        return {
            'total_count': 0,
            'risk_distribution': {'green': 0, 'yellow': 0, 'red': 0},
            'business_type_distribution': {},
            'average_score': 0,
            'score_range': {'min': 0, 'max': 0}
        }
    
    # Risk distribution
    risk_dist = {'green': 0, 'yellow': 0, 'red': 0}
    for item in data:
        risk_band = item.get('risk_band', 'red')
        risk_dist[risk_band] = risk_dist.get(risk_band, 0) + 1
    
    # Business type distribution
    business_dist = {}
    for item in data:
        btype = item.get('business_type', 'unknown')
        business_dist[btype] = business_dist.get(btype, 0) + 1
    
    # Score statistics
    scores = [item.get('current_score', 0) for item in data]
    avg_score = sum(scores) / len(scores) if scores else 0
    
    return {
        'total_count': len(data),
        'risk_distribution': risk_dist,
        'business_type_distribution': business_dist,
        'average_score': round(avg_score, 1),
        'score_range': {
            'min': min(scores) if scores else 0,
            'max': max(scores) if scores else 0
        }
    }
