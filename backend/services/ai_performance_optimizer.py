"""
AI Performance Optimizer for Credit Chakra
Optimizes AI copilot performance for 1000+ MSME records with caching and intelligent processing
"""

import asyncio
import logging
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass
from enum import Enum
import json
import hashlib
from concurrent.futures import ThreadPoolExecutor
import threading

logger = logging.getLogger(__name__)

class CacheType(Enum):
    PORTFOLIO_ANALYSIS = "portfolio_analysis"
    BUSINESS_PROFILE = "business_profile"
    RISK_ASSESSMENT = "risk_assessment"
    INSIGHTS = "insights"
    NUDGES = "nudges"
    PREDICTIONS = "predictions"

class ProcessingPriority(Enum):
    CRITICAL = 1
    HIGH = 2
    MEDIUM = 3
    LOW = 4
    BACKGROUND = 5

@dataclass
class CacheEntry:
    key: str
    data: Any
    created_at: datetime
    expires_at: datetime
    access_count: int
    last_accessed: datetime
    cache_type: CacheType
    size_bytes: int

@dataclass
class PerformanceMetrics:
    request_count: int
    avg_response_time: float
    cache_hit_rate: float
    error_rate: float
    concurrent_requests: int
    memory_usage_mb: float
    cpu_usage_percent: float

class AIPerformanceOptimizer:
    """
    Advanced performance optimizer for AI copilot with intelligent caching and processing
    """
    
    def __init__(self):
        self.cache = {}
        self.cache_stats = {cache_type: {'hits': 0, 'misses': 0} for cache_type in CacheType}
        self.performance_metrics = PerformanceMetrics(0, 0.0, 0.0, 0.0, 0, 0.0, 0.0)
        self.request_queue = asyncio.Queue()
        self.processing_pool = ThreadPoolExecutor(max_workers=8)
        self.cache_lock = threading.RLock()
        self.max_cache_size_mb = 500  # 500MB cache limit
        self.current_cache_size = 0
        self.request_times = []
        self.error_count = 0
        self.total_requests = 0
        
        # Cache TTL settings (in seconds)
        self.cache_ttl = {
            CacheType.PORTFOLIO_ANALYSIS: 300,  # 5 minutes
            CacheType.BUSINESS_PROFILE: 1800,   # 30 minutes
            CacheType.RISK_ASSESSMENT: 600,     # 10 minutes
            CacheType.INSIGHTS: 180,            # 3 minutes
            CacheType.NUDGES: 900,              # 15 minutes
            CacheType.PREDICTIONS: 3600         # 1 hour
        }
        
        # Background tasks will be started when needed
        self._tasks_started = False

    async def _ensure_tasks_started(self):
        """Ensure background tasks are started when needed."""
        if not self._tasks_started:
            try:
                asyncio.create_task(self._cache_cleanup_task())
                asyncio.create_task(self._performance_monitoring_task())
                self._tasks_started = True
            except RuntimeError:
                # No event loop running, tasks will be started later
                pass

    async def optimize_ai_request(
        self, 
        request_key: str, 
        cache_type: CacheType, 
        ai_function: Callable,
        *args, 
        **kwargs
    ) -> Tuple[Any, Dict[str, Any]]:
        """
        Optimize AI request with caching, batching, and performance monitoring
        """
        await self._ensure_tasks_started()
        start_time = time.time()
        self.total_requests += 1
        
        try:
            # Check cache first
            cached_result = await self._get_from_cache(request_key, cache_type)
            if cached_result is not None:
                response_time = time.time() - start_time
                self._update_performance_metrics(response_time, cache_hit=True)
                return cached_result, {
                    'cache_hit': True,
                    'response_time_ms': round(response_time * 1000, 2),
                    'source': 'cache'
                }
            
            # Execute AI function with optimization
            result = await self._execute_optimized_ai_function(ai_function, *args, **kwargs)
            
            # Cache the result
            await self._store_in_cache(request_key, cache_type, result)
            
            response_time = time.time() - start_time
            self._update_performance_metrics(response_time, cache_hit=False)
            
            return result, {
                'cache_hit': False,
                'response_time_ms': round(response_time * 1000, 2),
                'source': 'ai_computation'
            }
            
        except Exception as e:
            self.error_count += 1
            response_time = time.time() - start_time
            self._update_performance_metrics(response_time, error=True)
            logger.error(f"AI request optimization failed: {str(e)}")
            raise
    
    async def batch_optimize_portfolio_analysis(self, msme_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Batch optimize portfolio analysis for multiple MSMEs
        """
        if len(msme_list) <= 100:
            # Small batch - process normally
            return await self._process_small_batch(msme_list)
        else:
            # Large batch - use chunking and parallel processing
            return await self._process_large_batch(msme_list)
    
    async def _process_small_batch(self, msme_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process small batches efficiently"""
        start_time = time.time()
        
        # Group by business type for optimized processing
        grouped_msmes = {}
        for msme in msme_list:
            business_type = msme.get('business_type', 'Unknown')
            if business_type not in grouped_msmes:
                grouped_msmes[business_type] = []
            grouped_msmes[business_type].append(msme)
        
        # Process each group in parallel
        tasks = []
        for business_type, msmes in grouped_msmes.items():
            task = self._process_business_type_group(business_type, msmes)
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Aggregate results
        aggregated_results = {
            'total_msmes': len(msme_list),
            'processing_time_ms': round((time.time() - start_time) * 1000, 2),
            'business_type_analysis': {},
            'overall_metrics': {}
        }
        
        for i, (business_type, result) in enumerate(zip(grouped_msmes.keys(), results)):
            if not isinstance(result, Exception):
                aggregated_results['business_type_analysis'][business_type] = result
        
        return aggregated_results
    
    async def _process_large_batch(self, msme_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process large batches with chunking and streaming"""
        chunk_size = 100
        chunks = [msme_list[i:i + chunk_size] for i in range(0, len(msme_list), chunk_size)]
        
        start_time = time.time()
        processed_chunks = []
        
        # Process chunks with controlled concurrency
        semaphore = asyncio.Semaphore(4)  # Limit to 4 concurrent chunks
        
        async def process_chunk_with_semaphore(chunk):
            async with semaphore:
                return await self._process_small_batch(chunk)
        
        tasks = [process_chunk_with_semaphore(chunk) for chunk in chunks]
        chunk_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Aggregate chunk results
        total_processing_time = time.time() - start_time
        
        aggregated_results = {
            'total_msmes': len(msme_list),
            'chunks_processed': len(chunks),
            'processing_time_ms': round(total_processing_time * 1000, 2),
            'avg_chunk_time_ms': round((total_processing_time / len(chunks)) * 1000, 2),
            'business_type_analysis': {},
            'performance_optimizations': {
                'chunking_used': True,
                'parallel_processing': True,
                'cache_utilization': self._calculate_cache_utilization()
            }
        }
        
        return aggregated_results
    
    async def _process_business_type_group(self, business_type: str, msmes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Process MSMEs grouped by business type for optimization"""
        # Use business type specific optimizations
        optimization_strategy = self._get_business_type_optimization(business_type)
        
        # Calculate group metrics efficiently
        scores = [msme.get('score', 50) for msme in msmes]
        avg_score = sum(scores) / len(scores) if scores else 50
        
        risk_distribution = {
            'high': len([s for s in scores if s < 40]) / len(scores),
            'medium': len([s for s in scores if 40 <= s < 70]) / len(scores),
            'low': len([s for s in scores if s >= 70]) / len(scores)
        }
        
        return {
            'business_type': business_type,
            'msme_count': len(msmes),
            'avg_score': round(avg_score, 1),
            'risk_distribution': risk_distribution,
            'optimization_strategy': optimization_strategy,
            'processing_efficiency': 'optimized'
        }
    
    def _get_business_type_optimization(self, business_type: str) -> Dict[str, Any]:
        """Get optimization strategy for specific business type"""
        strategies = {
            'Manufacturing': {
                'focus_areas': ['operational_efficiency', 'supply_chain'],
                'cache_priority': 'high',
                'processing_weight': 1.2
            },
            'Retail': {
                'focus_areas': ['digital_adoption', 'customer_metrics'],
                'cache_priority': 'medium',
                'processing_weight': 1.0
            },
            'Services': {
                'focus_areas': ['service_quality', 'client_retention'],
                'cache_priority': 'medium',
                'processing_weight': 0.9
            },
            'Technology': {
                'focus_areas': ['innovation', 'scalability'],
                'cache_priority': 'high',
                'processing_weight': 1.1
            }
        }
        
        return strategies.get(business_type, strategies['Manufacturing'])
    
    async def _get_from_cache(self, key: str, cache_type: CacheType) -> Optional[Any]:
        """Get data from cache with performance tracking"""
        with self.cache_lock:
            cache_key = f"{cache_type.value}:{key}"
            
            if cache_key in self.cache:
                entry = self.cache[cache_key]
                
                # Check if expired
                if datetime.now(timezone.utc) > entry.expires_at:
                    del self.cache[cache_key]
                    self.current_cache_size -= entry.size_bytes
                    self.cache_stats[cache_type]['misses'] += 1
                    return None
                
                # Update access stats
                entry.access_count += 1
                entry.last_accessed = datetime.now(timezone.utc)
                self.cache_stats[cache_type]['hits'] += 1
                
                return entry.data
            
            self.cache_stats[cache_type]['misses'] += 1
            return None
    
    async def _store_in_cache(self, key: str, cache_type: CacheType, data: Any):
        """Store data in cache with size management"""
        with self.cache_lock:
            cache_key = f"{cache_type.value}:{key}"
            
            # Estimate data size
            data_size = len(json.dumps(data, default=str).encode('utf-8'))
            
            # Check cache size limits
            if self.current_cache_size + data_size > self.max_cache_size_mb * 1024 * 1024:
                await self._evict_cache_entries(data_size)
            
            # Create cache entry
            ttl_seconds = self.cache_ttl.get(cache_type, 300)
            expires_at = datetime.now(timezone.utc) + timedelta(seconds=ttl_seconds)
            
            entry = CacheEntry(
                key=cache_key,
                data=data,
                created_at=datetime.now(timezone.utc),
                expires_at=expires_at,
                access_count=1,
                last_accessed=datetime.now(timezone.utc),
                cache_type=cache_type,
                size_bytes=data_size
            )
            
            self.cache[cache_key] = entry
            self.current_cache_size += data_size
    
    async def _evict_cache_entries(self, required_space: int):
        """Evict cache entries using LRU strategy"""
        with self.cache_lock:
            # Sort by last accessed time (LRU)
            sorted_entries = sorted(
                self.cache.items(),
                key=lambda x: x[1].last_accessed
            )
            
            freed_space = 0
            entries_to_remove = []
            
            for cache_key, entry in sorted_entries:
                entries_to_remove.append(cache_key)
                freed_space += entry.size_bytes
                
                if freed_space >= required_space:
                    break
            
            # Remove entries
            for cache_key in entries_to_remove:
                if cache_key in self.cache:
                    entry = self.cache[cache_key]
                    self.current_cache_size -= entry.size_bytes
                    del self.cache[cache_key]
    
    async def _execute_optimized_ai_function(self, ai_function: Callable, *args, **kwargs) -> Any:
        """Execute AI function with optimization"""
        # Use thread pool for CPU-intensive operations
        loop = asyncio.get_event_loop()
        
        if asyncio.iscoroutinefunction(ai_function):
            return await ai_function(*args, **kwargs)
        else:
            return await loop.run_in_executor(self.processing_pool, ai_function, *args, **kwargs)
    
    def _update_performance_metrics(self, response_time: float, cache_hit: bool = False, error: bool = False):
        """Update performance metrics"""
        self.request_times.append(response_time)
        
        # Keep only last 1000 request times for rolling average
        if len(self.request_times) > 1000:
            self.request_times = self.request_times[-1000:]
        
        # Update metrics
        self.performance_metrics.avg_response_time = sum(self.request_times) / len(self.request_times)
        
        total_cache_requests = sum(
            stats['hits'] + stats['misses'] 
            for stats in self.cache_stats.values()
        )
        total_cache_hits = sum(stats['hits'] for stats in self.cache_stats.values())
        
        if total_cache_requests > 0:
            self.performance_metrics.cache_hit_rate = total_cache_hits / total_cache_requests
        
        if self.total_requests > 0:
            self.performance_metrics.error_rate = self.error_count / self.total_requests
    
    def _calculate_cache_utilization(self) -> Dict[str, Any]:
        """Calculate cache utilization metrics"""
        return {
            'cache_size_mb': round(self.current_cache_size / (1024 * 1024), 2),
            'cache_utilization_percent': round((self.current_cache_size / (self.max_cache_size_mb * 1024 * 1024)) * 100, 2),
            'total_entries': len(self.cache),
            'hit_rate_percent': round(self.performance_metrics.cache_hit_rate * 100, 2)
        }
    
    async def _cache_cleanup_task(self):
        """Background task for cache cleanup"""
        while True:
            try:
                await asyncio.sleep(60)  # Run every minute
                
                with self.cache_lock:
                    current_time = datetime.now(timezone.utc)
                    expired_keys = []
                    
                    for cache_key, entry in self.cache.items():
                        if current_time > entry.expires_at:
                            expired_keys.append(cache_key)
                    
                    for cache_key in expired_keys:
                        entry = self.cache[cache_key]
                        self.current_cache_size -= entry.size_bytes
                        del self.cache[cache_key]
                    
                    if expired_keys:
                        logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
                        
            except Exception as e:
                logger.error(f"Cache cleanup task error: {str(e)}")
    
    async def _performance_monitoring_task(self):
        """Background task for performance monitoring"""
        while True:
            try:
                await asyncio.sleep(30)  # Monitor every 30 seconds
                
                # Log performance metrics
                metrics = self.get_performance_summary()
                logger.info(f"AI Performance Metrics: {metrics}")
                
                # Alert if performance degrades
                if metrics['avg_response_time_ms'] > 2000:  # 2 second threshold
                    logger.warning(f"AI response time exceeding threshold: {metrics['avg_response_time_ms']}ms")
                
                if metrics['error_rate_percent'] > 5:  # 5% error rate threshold
                    logger.warning(f"AI error rate exceeding threshold: {metrics['error_rate_percent']}%")
                    
            except Exception as e:
                logger.error(f"Performance monitoring task error: {str(e)}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        return {
            'avg_response_time_ms': round(self.performance_metrics.avg_response_time * 1000, 2),
            'cache_hit_rate_percent': round(self.performance_metrics.cache_hit_rate * 100, 2),
            'error_rate_percent': round(self.performance_metrics.error_rate * 100, 2),
            'total_requests': self.total_requests,
            'cache_utilization': self._calculate_cache_utilization(),
            'cache_stats_by_type': {
                cache_type.value: {
                    'hits': stats['hits'],
                    'misses': stats['misses'],
                    'hit_rate': round(stats['hits'] / (stats['hits'] + stats['misses']) * 100, 2) if (stats['hits'] + stats['misses']) > 0 else 0
                }
                for cache_type, stats in self.cache_stats.items()
            }
        }

# Global instance
ai_performance_optimizer = AIPerformanceOptimizer()
