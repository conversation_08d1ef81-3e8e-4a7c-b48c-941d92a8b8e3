#!/usr/bin/env python3
"""
Test script to verify that the empty query issue is fixed
"""

import requests
import json

def test_empty_query():
    """Test that empty queries are properly rejected"""
    url = "http://localhost:8000/api/copilot/ask"
    
    # Test cases
    test_cases = [
        {
            "name": "Empty query string",
            "payload": {"query": "", "context": {}},
            "should_fail": True
        },
        {
            "name": "Whitespace only query",
            "payload": {"query": "   ", "context": {}},
            "should_fail": True
        },
        {
            "name": "Valid query",
            "payload": {"query": "Show high-risk MSMEs", "context": {}},
            "should_fail": False
        },
        {
            "name": "Single character query",
            "payload": {"query": "a", "context": {}},
            "should_fail": False
        }
    ]
    
    print("🧪 Testing empty query handling...")
    print("=" * 50)
    
    for test_case in test_cases:
        print(f"\n📋 Test: {test_case['name']}")
        print(f"   Payload: {test_case['payload']}")
        
        try:
            response = requests.post(
                url,
                json=test_case['payload'],
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if test_case['should_fail']:
                if response.status_code == 422:
                    print(f"   ✅ PASS: Correctly rejected with status {response.status_code}")
                    try:
                        error_detail = response.json()
                        print(f"   📝 Error details: {error_detail}")
                    except:
                        print(f"   📝 Error text: {response.text}")
                else:
                    print(f"   ❌ FAIL: Expected 422, got {response.status_code}")
                    print(f"   📝 Response: {response.text}")
            else:
                if response.status_code == 200:
                    print(f"   ✅ PASS: Successfully processed with status {response.status_code}")
                    try:
                        result = response.json()
                        print(f"   📝 Response type: {type(result)}")
                        if isinstance(result, dict) and 'response' in result:
                            print(f"   📝 AI Response length: {len(result['response'])} chars")
                    except:
                        print(f"   📝 Response text length: {len(response.text)} chars")
                else:
                    print(f"   ❌ FAIL: Expected 200, got {response.status_code}")
                    print(f"   📝 Response: {response.text}")
                    
        except requests.exceptions.RequestException as e:
            print(f"   ❌ ERROR: Request failed - {e}")
        except Exception as e:
            print(f"   ❌ ERROR: Unexpected error - {e}")
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")

if __name__ == "__main__":
    test_empty_query()
