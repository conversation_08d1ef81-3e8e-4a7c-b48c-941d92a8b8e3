'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ComposedChart
} from 'recharts';

interface FinancialChartsProps {
  data: {
    liquidity: Record<string, unknown>;
    profitability: Record<string, unknown>;
    leverage: Record<string, unknown>;
    efficiency: Record<string, unknown>;
    trends: Record<string, unknown>[];
    benchmarks: Record<string, unknown>;
  };
}

export default function FinancialCharts({ data }: FinancialChartsProps) {
  // Prepare data for different chart types
  const liquidityData = [
    { name: 'Current Ratio', value: data.liquidity.current_ratio, benchmark: 2.0 },
    { name: 'Quick Ratio', value: data.liquidity.quick_ratio, benchmark: 1.0 },
    { name: 'Cash Ratio', value: data.liquidity.cash_ratio, benchmark: 0.2 }
  ];

  const profitabilityData = [
    { name: 'Gross Margin', value: data.profitability.gross_profit_margin, benchmark: 25 },
    { name: 'Net Margin', value: data.profitability.net_profit_margin, benchmark: 10 },
    { name: 'Operating Margin', value: data.profitability.operating_profit_margin, benchmark: 15 },
    { name: 'ROA', value: data.profitability.return_on_assets, benchmark: 8 },
    { name: 'ROE', value: data.profitability.return_on_equity, benchmark: 15 }
  ];

  const leverageData = [
    { name: 'Debt/Equity', value: data.leverage.debt_to_equity_ratio, benchmark: 0.5 },
    { name: 'Debt/Assets', value: data.leverage.debt_to_assets_ratio, benchmark: 0.4 },
    { name: 'DSCR', value: data.leverage.debt_service_coverage_ratio, benchmark: 1.25 },
    { name: 'Interest Coverage', value: data.leverage.interest_coverage_ratio, benchmark: 3.0 }
  ];

  const efficiencyData = [
    { name: 'Asset Turnover', value: data.efficiency.asset_turnover, benchmark: 1.5 },
    { name: 'Inventory Turnover', value: data.efficiency.inventory_turnover, benchmark: 6 },
    { name: 'Receivables Turnover', value: data.efficiency.receivables_turnover, benchmark: 8 }
  ];

  const radarData = [
    { metric: 'Liquidity', score: data.liquidity.overall_liquidity_score, fullMark: 100 },
    { metric: 'Profitability', score: data.profitability.overall_profitability_score, fullMark: 100 },
    { metric: 'Leverage', score: data.leverage.overall_leverage_score, fullMark: 100 },
    { metric: 'Efficiency', score: data.efficiency.overall_efficiency_score, fullMark: 100 }
  ];

  const pieData = [
    { name: 'Liquidity', value: data.liquidity.overall_liquidity_score, color: '#3B82F6' },
    { name: 'Profitability', value: data.profitability.overall_profitability_score, color: '#10B981' },
    { name: 'Leverage', value: data.leverage.overall_leverage_score, color: '#8B5CF6' },
    { name: 'Efficiency', value: data.efficiency.overall_efficiency_score, color: '#F59E0B' }
  ];

  const trendData = data.trends || [
    { month: 'Jan', liquidity: 78, profitability: 72, leverage: 68, efficiency: 75 },
    { month: 'Feb', liquidity: 80, profitability: 74, leverage: 70, efficiency: 76 },
    { month: 'Mar', liquidity: 82, profitability: 76, leverage: 72, efficiency: 78 },
    { month: 'Apr', liquidity: 81, profitability: 78, leverage: 74, efficiency: 79 },
    { month: 'May', liquidity: 83, profitability: 79, leverage: 75, efficiency: 80 },
    { month: 'Jun', liquidity: 82, profitability: 79, leverage: 75, efficiency: 72 }
  ];

  const COLORS = ['#3B82F6', '#10B981', '#8B5CF6', '#F59E0B'];

  return (
    <div className="space-y-6">
      {/* Financial Health Overview */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Financial Health Radar</CardTitle>
            <CardDescription>Overall performance across key metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <RadarChart data={radarData}>
                <PolarGrid />
                <PolarAngleAxis dataKey="metric" />
                <PolarRadiusAxis angle={90} domain={[0, 100]} />
                <Radar
                  name="Score"
                  dataKey="score"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.3}
                  strokeWidth={2}
                />
              </RadarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Score Distribution</CardTitle>
            <CardDescription>Relative performance by category</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${(value || 0).toFixed(0)}`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Liquidity Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Liquidity Ratios vs Benchmarks</CardTitle>
          <CardDescription>Short-term financial health indicators</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <ComposedChart data={liquidityData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill="#3B82F6" name="Actual" />
              <Line type="monotone" dataKey="benchmark" stroke="#EF4444" strokeWidth={2} name="Benchmark" />
            </ComposedChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Profitability Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Profitability Metrics</CardTitle>
          <CardDescription>Revenue generation and profit efficiency</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={profitabilityData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill="#10B981" name="Actual" />
              <Bar dataKey="benchmark" fill="#10B981" fillOpacity={0.3} name="Benchmark" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Leverage Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Leverage & Coverage Ratios</CardTitle>
          <CardDescription>Debt management and financial leverage</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={leverageData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill="#8B5CF6" name="Actual" />
              <Bar dataKey="benchmark" fill="#8B5CF6" fillOpacity={0.3} name="Benchmark" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Efficiency Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Efficiency Metrics</CardTitle>
          <CardDescription>Asset utilization and operational efficiency</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={efficiencyData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill="#F59E0B" name="Actual" />
              <Bar dataKey="benchmark" fill="#F59E0B" fillOpacity={0.3} name="Benchmark" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Trend Analysis */}
      <Card>
        <CardHeader>
          <CardTitle>Financial Metrics Trends</CardTitle>
          <CardDescription>6-month performance evolution</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="liquidity" stroke="#3B82F6" strokeWidth={2} name="Liquidity" />
              <Line type="monotone" dataKey="profitability" stroke="#10B981" strokeWidth={2} name="Profitability" />
              <Line type="monotone" dataKey="leverage" stroke="#8B5CF6" strokeWidth={2} name="Leverage" />
              <Line type="monotone" dataKey="efficiency" stroke="#F59E0B" strokeWidth={2} name="Efficiency" />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Performance Comparison */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Industry Comparison</CardTitle>
            <CardDescription>Performance vs industry benchmarks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { metric: 'Overall Score', actual: 78.5, industry: 65.2, color: 'bg-blue-500' },
                { metric: 'Liquidity', actual: 82.4, industry: 70.1, color: 'bg-green-500' },
                { metric: 'Profitability', actual: 79.1, industry: 62.8, color: 'bg-purple-500' },
                { metric: 'Leverage', actual: 74.6, industry: 68.5, color: 'bg-orange-500' }
              ].map((item, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{item.metric}</span>
                    <span className="font-medium">{item.actual.toFixed(1)} vs {item.industry.toFixed(1)}</span>
                  </div>
                  <div className="relative h-2 bg-gray-200 rounded">
                    <div 
                      className={`absolute h-2 ${item.color} rounded`}
                      style={{ width: `${(item.actual / 100) * 100}%` }}
                    />
                    <div 
                      className="absolute h-2 border-r-2 border-gray-600"
                      style={{ left: `${(item.industry / 100) * 100}%` }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Your Score</span>
                    <span>Industry Avg</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Risk Assessment</CardTitle>
            <CardDescription>Risk indicators across categories</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { category: 'Liquidity Risk', level: 'Low', score: 15, color: 'text-green-600' },
                { category: 'Credit Risk', level: 'Medium', score: 35, color: 'text-yellow-600' },
                { category: 'Operational Risk', level: 'Medium', score: 42, color: 'text-yellow-600' },
                { category: 'Market Risk', level: 'Low', score: 28, color: 'text-green-600' }
              ].map((risk, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div>
                    <p className="font-medium text-sm">{risk.category}</p>
                    <p className={`text-xs ${risk.color}`}>{risk.level} Risk</p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">{risk.score}</p>
                    <Badge variant={risk.level === 'Low' ? 'default' : 'secondary'} className="text-xs">
                      {risk.level}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
