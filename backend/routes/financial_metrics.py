from fastapi import APIRouter, HTTPException, status, BackgroundTasks
from typing import List, Optional
from datetime import datetime, date

from models.financial_metrics import (
    FinancialMetricsRequest, FinancialMetricsResponse, ComprehensiveFinancialMetrics,
    LiquidityMetrics, ProfitabilityMetrics, LeverageMetrics, EfficiencyMetrics,
    FinancialMetric, MetricCategory, RiskLevel
)
from services.financial_metrics_service import FinancialMetricsService

router = APIRouter()
financial_metrics_service = FinancialMetricsService()

@router.post("/financial-metrics/calculate", response_model=FinancialMetricsResponse)
async def calculate_financial_metrics(request: FinancialMetricsRequest):
    """Calculate comprehensive financial metrics for an MSME"""
    try:
        comprehensive_metrics = await financial_metrics_service.calculate_comprehensive_metrics(
            request.msme_id, request.calculation_date
        )
        
        if not comprehensive_metrics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Insufficient financial data to calculate metrics"
            )
        
        return FinancialMetricsResponse(
            msme_id=request.msme_id,
            comprehensive_metrics=comprehensive_metrics,
            status="success",
            message="Successfully calculated financial metrics"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate financial metrics: {str(e)}"
        )

@router.get("/financial-metrics/{msme_id}/comprehensive", response_model=ComprehensiveFinancialMetrics)
async def get_comprehensive_metrics(msme_id: str, calculation_date: Optional[date] = None):
    """Get comprehensive financial metrics"""
    try:
        if not calculation_date:
            calculation_date = date.today()
        
        metrics = await financial_metrics_service.calculate_comprehensive_metrics(msme_id, calculation_date)
        
        if not metrics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No financial metrics found for this MSME"
            )
        
        return metrics
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get comprehensive metrics: {str(e)}"
        )

@router.get("/financial-metrics/{msme_id}/liquidity", response_model=LiquidityMetrics)
async def get_liquidity_metrics(msme_id: str, calculation_date: Optional[date] = None):
    """Get liquidity metrics"""
    try:
        comprehensive_metrics = await financial_metrics_service.calculate_comprehensive_metrics(
            msme_id, calculation_date or date.today()
        )
        
        if not comprehensive_metrics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No financial data found"
            )
        
        return comprehensive_metrics.liquidity_metrics
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get liquidity metrics: {str(e)}"
        )

@router.get("/financial-metrics/{msme_id}/profitability", response_model=ProfitabilityMetrics)
async def get_profitability_metrics(msme_id: str, calculation_date: Optional[date] = None):
    """Get profitability metrics"""
    try:
        comprehensive_metrics = await financial_metrics_service.calculate_comprehensive_metrics(
            msme_id, calculation_date or date.today()
        )
        
        if not comprehensive_metrics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No financial data found"
            )
        
        return comprehensive_metrics.profitability_metrics
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get profitability metrics: {str(e)}"
        )

@router.get("/financial-metrics/{msme_id}/leverage", response_model=LeverageMetrics)
async def get_leverage_metrics(msme_id: str, calculation_date: Optional[date] = None):
    """Get leverage/debt metrics"""
    try:
        comprehensive_metrics = await financial_metrics_service.calculate_comprehensive_metrics(
            msme_id, calculation_date or date.today()
        )
        
        if not comprehensive_metrics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No financial data found"
            )
        
        return comprehensive_metrics.leverage_metrics
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get leverage metrics: {str(e)}"
        )

@router.get("/financial-metrics/{msme_id}/efficiency", response_model=EfficiencyMetrics)
async def get_efficiency_metrics(msme_id: str, calculation_date: Optional[date] = None):
    """Get efficiency/activity metrics"""
    try:
        comprehensive_metrics = await financial_metrics_service.calculate_comprehensive_metrics(
            msme_id, calculation_date or date.today()
        )
        
        if not comprehensive_metrics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No financial data found"
            )
        
        return comprehensive_metrics.efficiency_metrics
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get efficiency metrics: {str(e)}"
        )

@router.get("/financial-metrics/{msme_id}/summary", response_model=dict)
async def get_financial_metrics_summary(msme_id: str):
    """Get financial metrics summary with key insights"""
    try:
        metrics = await financial_metrics_service.calculate_comprehensive_metrics(msme_id, date.today())
        
        if not metrics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No financial metrics found"
            )
        
        summary = {
            "msme_id": msme_id,
            "overall_assessment": {
                "financial_score": metrics.overall_financial_score,
                "financial_grade": metrics.financial_grade,
                "risk_level": metrics.overall_risk_level.value,
                "industry_percentile": metrics.industry_percentile
            },
            "category_scores": {
                "liquidity": metrics.liquidity_metrics.overall_liquidity_score,
                "profitability": metrics.profitability_metrics.overall_profitability_score,
                "leverage": metrics.leverage_metrics.overall_leverage_score,
                "efficiency": metrics.efficiency_metrics.overall_efficiency_score
            },
            "key_ratios": {
                "current_ratio": metrics.liquidity_metrics.current_ratio,
                "debt_to_equity": metrics.leverage_metrics.debt_to_equity_ratio,
                "net_profit_margin": metrics.profitability_metrics.net_profit_margin,
                "return_on_assets": metrics.profitability_metrics.return_on_assets,
                "cash_conversion_cycle": metrics.efficiency_metrics.cash_conversion_cycle,
                "debt_service_coverage": metrics.leverage_metrics.debt_service_coverage_ratio
            },
            "risk_assessment": {
                "overall_risk": metrics.overall_risk_level.value,
                "liquidity_risk": metrics.liquidity_metrics.liquidity_risk_level.value,
                "leverage_risk": metrics.leverage_metrics.leverage_risk_level.value,
                "risk_factors": metrics.risk_factors
            },
            "trends": {
                "financial_trend": metrics.financial_trend.value,
                "profitability_trend": metrics.profitability_metrics.profitability_trend.value,
                "efficiency_trend": metrics.efficiency_metrics.efficiency_trend.value
            },
            "recommendations": {
                "improvement_areas": metrics.improvement_areas,
                "action_items": metrics.action_items
            },
            "data_quality": {
                "completeness": metrics.data_completeness,
                "confidence": metrics.calculation_confidence
            },
            "last_updated": metrics.last_updated
        }
        
        return summary
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get financial metrics summary: {str(e)}"
        )

@router.get("/financial-metrics/{msme_id}/benchmarks", response_model=dict)
async def get_financial_benchmarks(msme_id: str):
    """Get financial metrics with industry benchmarks"""
    try:
        metrics = await financial_metrics_service.calculate_comprehensive_metrics(msme_id, date.today())
        
        if not metrics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No financial metrics found"
            )
        
        benchmarks = {
            "msme_id": msme_id,
            "liquidity_benchmarks": {
                "current_ratio": {
                    "actual": metrics.liquidity_metrics.current_ratio,
                    "industry_benchmark": 2.0,
                    "performance": "Above" if metrics.liquidity_metrics.current_ratio >= 2.0 else "Below",
                    "score": metrics.liquidity_metrics.current_ratio_score
                },
                "quick_ratio": {
                    "actual": metrics.liquidity_metrics.quick_ratio,
                    "industry_benchmark": 1.0,
                    "performance": "Above" if metrics.liquidity_metrics.quick_ratio >= 1.0 else "Below",
                    "score": metrics.liquidity_metrics.quick_ratio_score
                }
            },
            "profitability_benchmarks": {
                "net_profit_margin": {
                    "actual": metrics.profitability_metrics.net_profit_margin,
                    "industry_benchmark": 10.0,
                    "performance": "Above" if metrics.profitability_metrics.net_profit_margin >= 10.0 else "Below",
                    "score": metrics.profitability_metrics.net_profit_margin_score
                },
                "return_on_assets": {
                    "actual": metrics.profitability_metrics.return_on_assets,
                    "industry_benchmark": 8.0,
                    "performance": "Above" if metrics.profitability_metrics.return_on_assets >= 8.0 else "Below",
                    "score": metrics.profitability_metrics.roa_score
                }
            },
            "leverage_benchmarks": {
                "debt_to_equity": {
                    "actual": metrics.leverage_metrics.debt_to_equity_ratio,
                    "industry_benchmark": 0.5,
                    "performance": "Better" if metrics.leverage_metrics.debt_to_equity_ratio <= 0.5 else "Worse",
                    "score": metrics.leverage_metrics.debt_to_equity_score
                },
                "debt_service_coverage": {
                    "actual": metrics.leverage_metrics.debt_service_coverage_ratio,
                    "industry_benchmark": 1.25,
                    "performance": "Above" if metrics.leverage_metrics.debt_service_coverage_ratio >= 1.25 else "Below",
                    "score": metrics.leverage_metrics.dscr_score
                }
            },
            "efficiency_benchmarks": {
                "asset_turnover": {
                    "actual": metrics.efficiency_metrics.asset_turnover,
                    "industry_benchmark": 1.5,
                    "performance": "Above" if metrics.efficiency_metrics.asset_turnover >= 1.5 else "Below",
                    "score": metrics.efficiency_metrics.asset_turnover_score
                },
                "cash_conversion_cycle": {
                    "actual": metrics.efficiency_metrics.cash_conversion_cycle,
                    "industry_benchmark": 60.0,
                    "performance": "Better" if metrics.efficiency_metrics.cash_conversion_cycle <= 60.0 else "Worse",
                    "score": metrics.efficiency_metrics.cash_conversion_cycle_score
                }
            },
            "overall_performance": {
                "industry_percentile": metrics.industry_percentile,
                "peer_comparison": "Top 25%" if metrics.industry_percentile >= 75 else 
                                 "Above Average" if metrics.industry_percentile >= 60 else
                                 "Average" if metrics.industry_percentile >= 40 else
                                 "Below Average"
            }
        }
        
        return benchmarks
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get financial benchmarks: {str(e)}"
        )

@router.get("/financial-metrics/{msme_id}/credit-impact", response_model=dict)
async def get_credit_impact_analysis(msme_id: str):
    """Get financial metrics impact on credit scoring"""
    try:
        metrics = await financial_metrics_service.calculate_comprehensive_metrics(msme_id, date.today())
        
        if not metrics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No financial metrics found"
            )
        
        # Calculate credit impact weights
        credit_impact = {
            "msme_id": msme_id,
            "overall_credit_contribution": {
                "financial_health_score": metrics.overall_financial_score,
                "credit_score_impact": _calculate_credit_score_impact(metrics.overall_financial_score),
                "grade": metrics.financial_grade,
                "risk_level": metrics.overall_risk_level.value
            },
            "component_contributions": {
                "liquidity_impact": {
                    "score": metrics.liquidity_metrics.overall_liquidity_score,
                    "weight": 25.0,
                    "contribution": metrics.liquidity_metrics.overall_liquidity_score * 0.25,
                    "key_driver": "Current Ratio" if metrics.liquidity_metrics.current_ratio_score == max(
                        metrics.liquidity_metrics.current_ratio_score,
                        metrics.liquidity_metrics.quick_ratio_score,
                        metrics.liquidity_metrics.cash_ratio_score
                    ) else "Quick Ratio"
                },
                "profitability_impact": {
                    "score": metrics.profitability_metrics.overall_profitability_score,
                    "weight": 30.0,
                    "contribution": metrics.profitability_metrics.overall_profitability_score * 0.30,
                    "key_driver": "Net Profit Margin" if metrics.profitability_metrics.net_profit_margin_score >= metrics.profitability_metrics.roa_score else "Return on Assets"
                },
                "leverage_impact": {
                    "score": metrics.leverage_metrics.overall_leverage_score,
                    "weight": 25.0,
                    "contribution": metrics.leverage_metrics.overall_leverage_score * 0.25,
                    "key_driver": "DSCR" if metrics.leverage_metrics.dscr_score >= metrics.leverage_metrics.debt_to_equity_score else "Debt-to-Equity"
                },
                "efficiency_impact": {
                    "score": metrics.efficiency_metrics.overall_efficiency_score,
                    "weight": 20.0,
                    "contribution": metrics.efficiency_metrics.overall_efficiency_score * 0.20,
                    "key_driver": "Cash Conversion Cycle" if metrics.efficiency_metrics.cash_conversion_cycle_score >= metrics.efficiency_metrics.asset_turnover_score else "Asset Turnover"
                }
            },
            "risk_adjustments": {
                "positive_factors": _identify_positive_credit_factors(metrics),
                "negative_factors": _identify_negative_credit_factors(metrics),
                "net_risk_adjustment": _calculate_risk_adjustment(metrics)
            },
            "improvement_potential": {
                "quick_wins": _identify_quick_wins(metrics),
                "long_term_improvements": _identify_long_term_improvements(metrics),
                "potential_score_increase": _calculate_potential_improvement(metrics)
            }
        }
        
        return credit_impact
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get credit impact analysis: {str(e)}"
        )

@router.post("/financial-metrics/{msme_id}/refresh", status_code=status.HTTP_202_ACCEPTED)
async def refresh_financial_metrics(msme_id: str, background_tasks: BackgroundTasks):
    """Trigger refresh of financial metrics calculation"""
    try:
        background_tasks.add_task(_refresh_financial_metrics_background, msme_id)
        
        return {
            "message": "Financial metrics refresh initiated",
            "msme_id": msme_id,
            "estimated_completion": "1-2 minutes"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initiate financial metrics refresh: {str(e)}"
        )

# Helper functions
def _calculate_credit_score_impact(financial_score: float) -> float:
    """Calculate credit score impact from financial metrics"""
    # Financial metrics contribute 40% to overall credit score
    return (financial_score / 100) * 40

def _identify_positive_credit_factors(metrics: ComprehensiveFinancialMetrics) -> List[str]:
    """Identify positive factors for credit scoring"""
    factors = []
    
    if metrics.liquidity_metrics.current_ratio >= 2.0:
        factors.append("Strong liquidity position")
    
    if metrics.profitability_metrics.net_profit_margin >= 10.0:
        factors.append("Healthy profit margins")
    
    if metrics.leverage_metrics.debt_service_coverage_ratio >= 1.5:
        factors.append("Strong debt servicing capability")
    
    if metrics.efficiency_metrics.cash_conversion_cycle <= 60:
        factors.append("Efficient working capital management")
    
    if metrics.overall_financial_score >= 80:
        factors.append("Excellent overall financial health")
    
    return factors

def _identify_negative_credit_factors(metrics: ComprehensiveFinancialMetrics) -> List[str]:
    """Identify negative factors for credit scoring"""
    factors = []
    
    if metrics.liquidity_metrics.current_ratio < 1.0:
        factors.append("Liquidity concerns")
    
    if metrics.profitability_metrics.net_profit_margin < 5.0:
        factors.append("Low profitability")
    
    if metrics.leverage_metrics.debt_to_equity_ratio > 1.0:
        factors.append("High leverage")
    
    if metrics.leverage_metrics.debt_service_coverage_ratio < 1.25:
        factors.append("Weak debt servicing capability")
    
    if metrics.efficiency_metrics.cash_conversion_cycle > 90:
        factors.append("Inefficient working capital management")
    
    return factors

def _calculate_risk_adjustment(metrics: ComprehensiveFinancialMetrics) -> float:
    """Calculate risk adjustment factor"""
    if metrics.overall_risk_level == RiskLevel.LOW:
        return 5.0
    elif metrics.overall_risk_level == RiskLevel.MEDIUM:
        return 0.0
    elif metrics.overall_risk_level == RiskLevel.HIGH:
        return -5.0
    else:  # CRITICAL
        return -10.0

def _identify_quick_wins(metrics: ComprehensiveFinancialMetrics) -> List[str]:
    """Identify quick improvement opportunities"""
    quick_wins = []
    
    if metrics.efficiency_metrics.days_sales_outstanding > 60:
        quick_wins.append("Improve collection processes to reduce DSO")
    
    if metrics.liquidity_metrics.cash_ratio < 0.1:
        quick_wins.append("Build cash reserves for better liquidity")
    
    if metrics.leverage_metrics.interest_coverage_ratio < 3:
        quick_wins.append("Focus on reducing interest expenses")
    
    return quick_wins

def _identify_long_term_improvements(metrics: ComprehensiveFinancialMetrics) -> List[str]:
    """Identify long-term improvement opportunities"""
    improvements = []
    
    if metrics.profitability_metrics.return_on_assets < 8:
        improvements.append("Improve asset utilization and operational efficiency")
    
    if metrics.leverage_metrics.debt_to_equity_ratio > 0.8:
        improvements.append("Reduce debt levels or increase equity")
    
    if metrics.efficiency_metrics.inventory_turnover < 4:
        improvements.append("Optimize inventory management")
    
    return improvements

def _calculate_potential_improvement(metrics: ComprehensiveFinancialMetrics) -> float:
    """Calculate potential score improvement"""
    current_score = metrics.overall_financial_score
    max_possible = 100.0
    
    # Estimate improvement potential based on current performance
    if current_score >= 85:
        return min(5.0, max_possible - current_score)
    elif current_score >= 70:
        return min(15.0, max_possible - current_score)
    elif current_score >= 50:
        return min(25.0, max_possible - current_score)
    else:
        return min(35.0, max_possible - current_score)

async def _refresh_financial_metrics_background(msme_id: str):
    """Background task to refresh financial metrics"""
    try:
        # Recalculate comprehensive metrics
        await financial_metrics_service.calculate_comprehensive_metrics(msme_id, date.today())
        
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Financial metrics refresh failed for {msme_id}: {str(e)}")
