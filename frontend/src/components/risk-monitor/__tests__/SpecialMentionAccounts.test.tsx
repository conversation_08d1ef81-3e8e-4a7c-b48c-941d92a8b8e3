/**
 * Test suite for Special Mention Accounts component.
 * 
 * Comprehensive testing for SMA heatmap visualization, priority actions,
 * real-time data integration, and RBI compliance features.
 * 
 * Author: Credit Chakra Team
 * Version: 1.0.0
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { jest } from '@jest/globals';
import { SpecialMentionAccounts } from '../SpecialMentionAccounts';

// Mock fetch globally
global.fetch = jest.fn() as jest.MockedFunction<typeof fetch>;

// Mock data for testing
const mockSMAHeatmapData = {
  data: {
    sma_distribution: {
      standard: 12,
      sma_0: 4,
      sma_1: 2,
      sma_2: 1,
      npa: 1
    },
    business_type_sma: {
      retail: { standard: 8, sma_0: 2, sma_1: 1, sma_2: 0, npa: 0 },
      manufacturing: { standard: 3, sma_0: 1, sma_1: 1, sma_2: 1, npa: 0 },
      services: { standard: 1, sma_0: 1, sma_1: 0, sma_2: 0, npa: 1 }
    },
    geographic_sma: {
      'Mumbai': { standard: 4, sma_0: 1, sma_1: 1, sma_2: 0, npa: 0 },
      'Delhi': { standard: 3, sma_0: 1, sma_1: 0, sma_2: 0, npa: 1 },
      'Bangalore': { standard: 2, sma_0: 1, sma_1: 1, sma_2: 1, npa: 0 }
    },
    exposure_by_sma: {
      standard: ********,
      sma_0: ********,
      sma_1: 8000000,
      sma_2: 5000000,
      npa: 3000000
    },
    total_exposure: *********,
    npa_ratio: 0.027,
    sma_ratio: 0.221,
    generated_at: new Date().toISOString()
  }
};

const mockProgressionAlerts = [
  {
    alert_id: 'alert_001',
    msme_id: 'msme_001',
    msme_name: 'श्री गणेश इलेक्ट्रॉनिक्स',
    alert_type: 'sma_progression',
    severity: 'high' as const,
    message: 'SMA progression from standard to sma_1',
    current_score: 65.2,
    risk_band: 'yellow',
    dpd_classification: 'sma_1',
    days_past_due: 45,
    acknowledged: false,
    timestamp: new Date().toISOString()
  },
  {
    alert_id: 'alert_002',
    msme_id: 'msme_002',
    msme_name: 'Annapurna Catering Services',
    alert_type: 'sma_progression',
    severity: 'medium' as const,
    message: 'SMA progression from standard to sma_0',
    current_score: 58.7,
    risk_band: 'yellow',
    dpd_classification: 'sma_0',
    days_past_due: 25,
    acknowledged: false,
    timestamp: new Date().toISOString()
  }
];

const mockPortfolioSummary = {
  data: {
    total_msmes: 20,
    sma_accounts: 7,
    npa_accounts: 1,
    sma_ratio: 0.35,
    npa_ratio: 0.05,
    sma_trend_30d: 0.012,
    npa_trend_30d: 0.003,
    total_exposure: *********,
    sma_exposure: ********,
    npa_exposure: 3000000,
    provision_required: 1875000,
    regulatory_alerts: [
      {
        type: 'sma_reporting',
        message: 'SMA reporting due in 2 days',
        severity: 'medium',
        due_date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString()
      }
    ],
    last_updated: new Date().toISOString()
  }
};

describe('SpecialMentionAccounts Component', () => {
  beforeEach(() => {
    // Reset fetch mock before each test
    (fetch as jest.MockedFunction<typeof fetch>).mockClear();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state initially', () => {
    // Mock fetch to never resolve to test loading state
    (fetch as jest.MockedFunction<typeof fetch>).mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    render(<SpecialMentionAccounts />);

    // Check for skeleton loading elements by their data-slot attribute
    const skeletonElements = document.querySelectorAll('[data-slot="skeleton"]');
    expect(skeletonElements.length).toBeGreaterThan(0);
  });

  it('renders error state when API calls fail', async () => {
    // Mock fetch to reject with a specific error
    (fetch as jest.MockedFunction<typeof fetch>).mockRejectedValue(
      new Error('API Error')
    );

    render(<SpecialMentionAccounts />);

    await waitFor(() => {
      expect(screen.getByText('API Error')).toBeInTheDocument();
      expect(screen.getByText('Retry')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('renders SMA data successfully', async () => {
    // Mock successful API responses
    (fetch as jest.MockedFunction<typeof fetch>)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockSMAHeatmapData,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockProgressionAlerts,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPortfolioSummary,
      } as Response);

    render(<SpecialMentionAccounts />);

    await waitFor(() => {
      expect(screen.getByText('SMA Monitor')).toBeInTheDocument();
      expect(screen.getByText('RBI-compliant SMA classification and early warning system')).toBeInTheDocument();
    });

    // Check for portfolio summary cards
    await waitFor(() => {
      expect(screen.getByText('Total MSMEs')).toBeInTheDocument();
      expect(screen.getByText('20')).toBeInTheDocument();
      expect(screen.getByText('SMA Accounts')).toBeInTheDocument();
      expect(screen.getByText('7')).toBeInTheDocument();
      expect(screen.getByText('NPA Accounts')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument();
    });
  });

  it('displays SMA distribution correctly', async () => {
    (fetch as jest.MockedFunction<typeof fetch>)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockSMAHeatmapData,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockProgressionAlerts,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPortfolioSummary,
      } as Response);

    render(<SpecialMentionAccounts />);

    await waitFor(() => {
      expect(screen.getByText('SMA Classification Distribution')).toBeInTheDocument();
    });

    // Check for SMA classification badges
    await waitFor(() => {
      expect(screen.getByText('STANDARD')).toBeInTheDocument();
      expect(screen.getByText('SMA-0')).toBeInTheDocument();
      expect(screen.getByText('SMA-1')).toBeInTheDocument();
      expect(screen.getByText('SMA-2')).toBeInTheDocument();
      expect(screen.getByText('NPA')).toBeInTheDocument();
    });
  });

  it('handles tab navigation correctly', async () => {
    (fetch as jest.MockedFunction<typeof fetch>)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockSMAHeatmapData,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockProgressionAlerts,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPortfolioSummary,
      } as Response);

    render(<SpecialMentionAccounts />);

    await waitFor(() => {
      expect(screen.getByText('SMA Overview')).toBeInTheDocument();
    });

    // Test tab navigation
    const heatmapTab = screen.getByText('Heatmap Analysis');
    fireEvent.click(heatmapTab);

    await waitFor(() => {
      expect(screen.getByText('SMA Distribution by Business Type')).toBeInTheDocument();
      expect(screen.getByText('SMA Distribution by Location')).toBeInTheDocument();
    });

    const actionsTab = screen.getByText('Priority Actions');
    fireEvent.click(actionsTab);

    await waitFor(() => {
      expect(screen.getByText('Priority Actions Required')).toBeInTheDocument();
    });
  });

  it('displays priority actions correctly', async () => {
    (fetch as jest.MockedFunction<typeof fetch>)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockSMAHeatmapData,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockProgressionAlerts,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPortfolioSummary,
      } as Response);

    render(<SpecialMentionAccounts />);

    // Navigate to actions tab
    await waitFor(() => {
      const actionsTab = screen.getByText('Priority Actions');
      fireEvent.click(actionsTab);
    });

    // Check for progression alerts
    await waitFor(() => {
      expect(screen.getByText('श्री गणेश इलेक्ट्रॉनिक्स')).toBeInTheDocument();
      expect(screen.getByText('Annapurna Catering Services')).toBeInTheDocument();
      expect(screen.getByText('HIGH')).toBeInTheDocument();
      expect(screen.getByText('MEDIUM')).toBeInTheDocument();
    });

    // Check for action buttons
    expect(screen.getAllByText('View')).toHaveLength(expect.any(Number));
    expect(screen.getAllByText('Acknowledge')).toHaveLength(expect.any(Number));
  });

  it('displays regulatory alerts', async () => {
    (fetch as jest.MockedFunction<typeof fetch>)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockSMAHeatmapData,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockProgressionAlerts,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPortfolioSummary,
      } as Response);

    render(<SpecialMentionAccounts />);

    // Navigate to actions tab
    await waitFor(() => {
      const actionsTab = screen.getByText('Priority Actions');
      fireEvent.click(actionsTab);
    });

    // Check for regulatory alerts
    await waitFor(() => {
      expect(screen.getByText('Regulatory Compliance Alerts')).toBeInTheDocument();
      expect(screen.getByText('SMA reporting due in 2 days')).toBeInTheDocument();
    });
  });

  it('handles refresh functionality', async () => {
    (fetch as jest.MockedFunction<typeof fetch>)
      .mockResolvedValue({
        ok: true,
        json: async () => mockSMAHeatmapData,
      } as Response)
      .mockResolvedValue({
        ok: true,
        json: async () => mockProgressionAlerts,
      } as Response)
      .mockResolvedValue({
        ok: true,
        json: async () => mockPortfolioSummary,
      } as Response);

    render(<SpecialMentionAccounts />);

    await waitFor(() => {
      expect(screen.getByText('Refresh')).toBeInTheDocument();
    });

    const refreshButton = screen.getByText('Refresh');
    fireEvent.click(refreshButton);

    // Verify that fetch was called again
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledTimes(6); // 3 initial + 3 refresh calls
    });
  });

  it('formats currency correctly', async () => {
    (fetch as jest.MockedFunction<typeof fetch>)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockSMAHeatmapData,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockProgressionAlerts,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPortfolioSummary,
      } as Response);

    render(<SpecialMentionAccounts />);

    await waitFor(() => {
      expect(screen.getByText('Provision Required')).toBeInTheDocument();
      expect(screen.getByText('₹18.8L')).toBeInTheDocument(); // 1875000 formatted
    });
  });

  it('applies correct severity colors', async () => {
    (fetch as jest.MockedFunction<typeof fetch>)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockSMAHeatmapData,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockProgressionAlerts,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPortfolioSummary,
      } as Response);

    render(<SpecialMentionAccounts />);

    // Navigate to actions tab
    await waitFor(() => {
      const actionsTab = screen.getByText('Priority Actions');
      fireEvent.click(actionsTab);
    });

    // Check for severity badges with correct styling
    await waitFor(() => {
      const highSeverityBadge = screen.getByText('HIGH');
      expect(highSeverityBadge).toHaveClass('bg-orange-500');
      
      const mediumSeverityBadge = screen.getByText('MEDIUM');
      expect(mediumSeverityBadge).toHaveClass('bg-yellow-500');
    });
  });

  it('meets 2-second load requirement', async () => {
    const startTime = Date.now();
    
    (fetch as jest.MockedFunction<typeof fetch>)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => {
          // Simulate fast API response
          await new Promise(resolve => setTimeout(resolve, 100));
          return mockSMAHeatmapData;
        },
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockProgressionAlerts,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => mockPortfolioSummary,
      } as Response);

    render(<SpecialMentionAccounts />);

    await waitFor(() => {
      expect(screen.getByText('SMA Monitor')).toBeInTheDocument();
    }, { timeout: 3000 });

    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeGreaterThanOrEqual(2000); // Should enforce minimum 2-second load
  });

  it('handles empty data gracefully', async () => {
    const emptyData = {
      data: {
        sma_distribution: {},
        business_type_sma: {},
        geographic_sma: {},
        exposure_by_sma: {},
        total_exposure: 0,
        npa_ratio: 0,
        sma_ratio: 0,
        generated_at: new Date().toISOString()
      }
    };

    (fetch as jest.MockedFunction<typeof fetch>)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => emptyData,
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => [],
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: { ...mockPortfolioSummary.data, total_msmes: 0 } }),
      } as Response);

    render(<SpecialMentionAccounts />);

    await waitFor(() => {
      expect(screen.getByText('SMA Monitor')).toBeInTheDocument();
    });

    // Navigate to actions tab to check empty state
    await waitFor(() => {
      const actionsTab = screen.getByText('Priority Actions');
      fireEvent.click(actionsTab);
    });

    await waitFor(() => {
      expect(screen.getByText('No priority actions at this time')).toBeInTheDocument();
      expect(screen.getByText('All SMA accounts are being monitored')).toBeInTheDocument();
    });
  });
});
