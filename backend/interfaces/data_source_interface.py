"""
Extensible interfaces for additional data sources in Credit Chakra
Designed to support GST API, Account Aggregator, Udyam registration, and future integrations
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime, timezone, date
from enum import Enum

class DataSourceType(Enum):
    """Types of data sources"""
    GST_API = "gst_api"
    ACCOUNT_AGGREGATOR = "account_aggregator"
    UDYAM_REGISTRATION = "udyam_registration"
    BANK_STATEMENT = "bank_statement"
    CREDIT_BUREAU = "credit_bureau"
    SOCIAL_MEDIA = "social_media"
    MARKETPLACE = "marketplace"
    GOVERNMENT_DB = "government_db"

class DataSourceStatus(Enum):
    """Status of data source integration"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    MAINTENANCE = "maintenance"
    RATE_LIMITED = "rate_limited"

@dataclass
class DataSourceConfig:
    """Configuration for data source"""
    source_type: DataSourceType
    api_endpoint: str
    api_key: Optional[str] = None
    rate_limit: Optional[int] = None  # requests per minute
    timeout: int = 30  # seconds
    retry_attempts: int = 3
    cache_ttl: int = 3600  # seconds
    enabled: bool = True
    metadata: Dict[str, Any] = None

@dataclass
class DataSourceResponse:
    """Standardized response from data source"""
    source_type: DataSourceType
    msme_id: str
    data: Dict[str, Any]
    status: DataSourceStatus
    timestamp: datetime
    confidence_score: float  # 0.0 to 1.0
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = None

class DataSourceInterface(ABC):
    """Abstract interface for all data sources"""
    
    def __init__(self, config: DataSourceConfig):
        self.config = config
        self.status = DataSourceStatus.INACTIVE
        self.last_error: Optional[str] = None
        self.request_count = 0
        self.last_request_time: Optional[datetime] = None
    
    @abstractmethod
    async def fetch_data(self, msme_id: str, **kwargs) -> DataSourceResponse:
        """Fetch data for a specific MSME"""
        pass
    
    @abstractmethod
    async def validate_credentials(self) -> bool:
        """Validate API credentials and connectivity"""
        pass
    
    @abstractmethod
    def get_required_parameters(self) -> List[str]:
        """Get list of required parameters for this data source"""
        pass
    
    @abstractmethod
    def get_data_schema(self) -> Dict[str, Any]:
        """Get the expected data schema for this source"""
        pass
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on data source"""
        try:
            is_valid = await self.validate_credentials()
            if is_valid:
                self.status = DataSourceStatus.ACTIVE
                return {
                    "status": "healthy",
                    "source_type": self.config.source_type.value,
                    "last_check": datetime.now(timezone.utc).isoformat(),
                    "request_count": self.request_count
                }
            else:
                self.status = DataSourceStatus.ERROR
                return {
                    "status": "unhealthy",
                    "source_type": self.config.source_type.value,
                    "error": "Credential validation failed"
                }
        except Exception as e:
            self.status = DataSourceStatus.ERROR
            self.last_error = str(e)
            return {
                "status": "error",
                "source_type": self.config.source_type.value,
                "error": str(e)
            }
    
    def is_rate_limited(self) -> bool:
        """Check if requests are rate limited"""
        if not self.config.rate_limit:
            return False
        
        if not self.last_request_time:
            return False
        
        time_diff = (datetime.now(timezone.utc) - self.last_request_time).total_seconds()
        return time_diff < (60 / self.config.rate_limit)

class GSTDataSource(DataSourceInterface):
    """GST API data source implementation"""
    
    async def fetch_data(self, msme_id: str, gstin: str, **kwargs) -> DataSourceResponse:
        """Fetch GST data for MSME"""
        try:
            # Mock implementation - replace with actual GST API call
            mock_data = {
                "gstin": gstin,
                "business_name": "Sample Business",
                "registration_date": "2020-01-15",
                "status": "Active",
                "returns": [
                    {
                        "period": "2024-01",
                        "filing_date": "2024-02-10",
                        "turnover": 2500000,
                        "tax_paid": 450000,
                        "status": "Filed"
                    }
                ],
                "compliance_score": 85.5
            }
            
            return DataSourceResponse(
                source_type=DataSourceType.GST_API,
                msme_id=msme_id,
                data=mock_data,
                status=DataSourceStatus.ACTIVE,
                timestamp=datetime.now(timezone.utc),
                confidence_score=0.95
            )
        except Exception as e:
            return DataSourceResponse(
                source_type=DataSourceType.GST_API,
                msme_id=msme_id,
                data={},
                status=DataSourceStatus.ERROR,
                timestamp=datetime.now(timezone.utc),
                confidence_score=0.0,
                error_message=str(e)
            )
    
    async def validate_credentials(self) -> bool:
        """Validate GST API credentials"""
        # Mock validation - replace with actual API call
        return self.config.api_key is not None
    
    def get_required_parameters(self) -> List[str]:
        """Required parameters for GST data fetch"""
        return ["gstin"]
    
    def get_data_schema(self) -> Dict[str, Any]:
        """GST data schema"""
        return {
            "gstin": "string",
            "business_name": "string",
            "registration_date": "date",
            "status": "string",
            "returns": "array",
            "compliance_score": "number"
        }

class AccountAggregatorDataSource(DataSourceInterface):
    """Account Aggregator data source implementation"""
    
    async def fetch_data(self, msme_id: str, consent_id: str, **kwargs) -> DataSourceResponse:
        """Fetch banking data through Account Aggregator"""
        try:
            # Mock implementation
            mock_data = {
                "consent_id": consent_id,
                "accounts": [
                    {
                        "account_id": "ACC001",
                        "bank_name": "State Bank of India",
                        "account_type": "Current",
                        "balance": 1250000,
                        "transactions": [
                            {
                                "date": "2024-01-15",
                                "amount": 500000,
                                "type": "Credit",
                                "description": "Customer Payment"
                            }
                        ]
                    }
                ],
                "financial_summary": {
                    "avg_monthly_balance": 1100000,
                    "transaction_volume": ********,
                    "banking_health_score": 78.5
                }
            }
            
            return DataSourceResponse(
                source_type=DataSourceType.ACCOUNT_AGGREGATOR,
                msme_id=msme_id,
                data=mock_data,
                status=DataSourceStatus.ACTIVE,
                timestamp=datetime.now(timezone.utc),
                confidence_score=0.92
            )
        except Exception as e:
            return DataSourceResponse(
                source_type=DataSourceType.ACCOUNT_AGGREGATOR,
                msme_id=msme_id,
                data={},
                status=DataSourceStatus.ERROR,
                timestamp=datetime.now(timezone.utc),
                confidence_score=0.0,
                error_message=str(e)
            )
    
    async def validate_credentials(self) -> bool:
        """Validate Account Aggregator credentials"""
        return self.config.api_key is not None
    
    def get_required_parameters(self) -> List[str]:
        """Required parameters for AA data fetch"""
        return ["consent_id"]
    
    def get_data_schema(self) -> Dict[str, Any]:
        """Account Aggregator data schema"""
        return {
            "consent_id": "string",
            "accounts": "array",
            "financial_summary": "object"
        }

class UdyamDataSource(DataSourceInterface):
    """Udyam Registration data source implementation"""
    
    async def fetch_data(self, msme_id: str, udyam_number: str, **kwargs) -> DataSourceResponse:
        """Fetch Udyam registration data"""
        try:
            # Mock implementation
            mock_data = {
                "udyam_number": udyam_number,
                "enterprise_name": "Sample Enterprise",
                "enterprise_type": "Micro",
                "major_activity": "Manufacturing",
                "registration_date": "2021-03-15",
                "investment": 2500000,
                "turnover": 8500000,
                "employment": 15,
                "status": "Active"
            }
            
            return DataSourceResponse(
                source_type=DataSourceType.UDYAM_REGISTRATION,
                msme_id=msme_id,
                data=mock_data,
                status=DataSourceStatus.ACTIVE,
                timestamp=datetime.now(timezone.utc),
                confidence_score=0.98
            )
        except Exception as e:
            return DataSourceResponse(
                source_type=DataSourceType.UDYAM_REGISTRATION,
                msme_id=msme_id,
                data={},
                status=DataSourceStatus.ERROR,
                timestamp=datetime.now(timezone.utc),
                confidence_score=0.0,
                error_message=str(e)
            )
    
    async def validate_credentials(self) -> bool:
        """Validate Udyam API credentials"""
        return True  # Udyam is typically public data
    
    def get_required_parameters(self) -> List[str]:
        """Required parameters for Udyam data fetch"""
        return ["udyam_number"]
    
    def get_data_schema(self) -> Dict[str, Any]:
        """Udyam data schema"""
        return {
            "udyam_number": "string",
            "enterprise_name": "string",
            "enterprise_type": "string",
            "major_activity": "string",
            "registration_date": "date",
            "investment": "number",
            "turnover": "number",
            "employment": "number",
            "status": "string"
        }

class DataSourceManager:
    """Manages multiple data sources"""
    
    def __init__(self):
        self.data_sources: Dict[DataSourceType, DataSourceInterface] = {}
        self.cache: Dict[str, DataSourceResponse] = {}
    
    def register_data_source(self, data_source: DataSourceInterface) -> None:
        """Register a new data source"""
        self.data_sources[data_source.config.source_type] = data_source
    
    async def fetch_from_source(
        self,
        source_type: DataSourceType,
        msme_id: str,
        **kwargs
    ) -> Optional[DataSourceResponse]:
        """Fetch data from specific source"""
        if source_type not in self.data_sources:
            return None
        
        data_source = self.data_sources[source_type]
        
        # Check cache first
        cache_key = f"{source_type.value}_{msme_id}_{hash(str(kwargs))}"
        if cache_key in self.cache:
            cached_response = self.cache[cache_key]
            cache_age = (datetime.now(timezone.utc) - cached_response.timestamp).total_seconds()
            if cache_age < data_source.config.cache_ttl:
                return cached_response
        
        # Check rate limiting
        if data_source.is_rate_limited():
            return None
        
        # Fetch fresh data
        response = await data_source.fetch_data(msme_id, **kwargs)
        
        # Cache successful responses
        if response.status == DataSourceStatus.ACTIVE:
            self.cache[cache_key] = response
        
        return response
    
    async def fetch_all_sources(self, msme_id: str, **kwargs) -> Dict[DataSourceType, DataSourceResponse]:
        """Fetch data from all available sources"""
        results = {}
        
        for source_type, data_source in self.data_sources.items():
            if data_source.config.enabled:
                try:
                    response = await self.fetch_from_source(source_type, msme_id, **kwargs)
                    if response:
                        results[source_type] = response
                except Exception as e:
                    # Log error but continue with other sources
                    print(f"Error fetching from {source_type.value}: {e}")
        
        return results
    
    async def health_check_all(self) -> Dict[DataSourceType, Dict[str, Any]]:
        """Perform health check on all data sources"""
        results = {}
        
        for source_type, data_source in self.data_sources.items():
            results[source_type] = await data_source.health_check()
        
        return results

# Global data source manager instance
data_source_manager = DataSourceManager()
