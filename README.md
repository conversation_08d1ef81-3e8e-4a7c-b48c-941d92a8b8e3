# Credit Chakra

**MSME Credit Scoring and Monitoring Platform**

Credit Chakra is a comprehensive platform for assessing and monitoring the creditworthiness of Micro, Small, and Medium Enterprises (MSMEs) using multiple data sources and real-time scoring algorithms.

## 🚀 Quick Start

### Prerequisites
- **Python 3.13+** - [Download Python](https://python.org/downloads/)
- **Node.js 18+** - [Download Node.js](https://nodejs.org/)
- **npm** (comes with Node.js)

### ✨ Recent Improvements (Latest Cleanup)
- **Frontend**: Fixed 200+ TypeScript/ESLint warnings and errors
- **Code Structure**: Optimized file organization and naming conventions
- **Security**: Added comprehensive security headers and CORS configuration
- **Performance**: Implemented Next.js optimizations and font loading improvements
- **Dependencies**: Cleaned up unused imports and redundant files
- **Testing**: Organized test files and improved test structure

### One-Command Startup

#### For macOS/Linux:
```bash
./start.sh
```

#### For Windows:
```batch
start.bat
```

#### Using npm:
```bash
npm start          # macOS/Linux
npm run start:windows  # Windows
```

### Application URLs
Once started, access the application at:
- **📈 Analytics Dashboard**: http://localhost:3000
- **👥 Portfolio Management**: http://localhost:3000/msmes
- **🔧 Backend API**: http://localhost:8000
- **📚 API Documentation**: http://localhost:8000/docs

## 🚀 Features

- **Multi-Source Data Integration**: GST, UPI, Reviews, Social Media, Maps
- **Real-Time Credit Scoring**: Dynamic scoring based on weighted signals
- **Risk Band Classification**: Green, Yellow, Red risk categories
- **Automated Nudges**: WhatsApp, Email, SMS notifications
- **Dashboard Analytics**: Borrower profiles and timeline visualization
- **Firebase Integration**: Scalable cloud infrastructure

## 📁 Project Structure

```
credit-chakra/
│
├── backend/                 # FastAPI Backend (Credit Manager Dashboard)
│   ├── main.py             # Application entry point
│   ├── models/             # Pydantic data models
│   ├── routes/             # API endpoints
│   ├── services/           # Business logic
│   ├── firebase/           # Firebase integration
│   └── requirements.txt    # Python dependencies
│
├── frontend/               # Next.js Frontend (Credit Manager Dashboard)
│   ├── src/                # Source code
│   ├── components/         # React components
│   ├── pages/              # Next.js pages
│   └── package.json        # Node dependencies
│
├── msme-app/               # MSME Mobile Application
│   ├── backend/            # FastAPI Backend for Mobile
│   ├── frontend/           # Flutter Mobile App
│   ├── firestore/          # Mobile App Database Config
│   └── deploy/             # Mobile App Deployment
│
├── firestore/             # Database Configuration (Shared)
│   ├── firestore.rules    # Security rules
│   ├── seed_data/         # Sample data
│   └── rule_definitions.json  # Scoring rules
│
└── deploy/                # Deployment Configuration
    ├── cloudrun.yaml      # Cloud Run config
    ├── deploy.sh          # Deployment script
    └── README.md          # Deployment guide
```

## 🛠️ Technology Stack

### Backend
- **FastAPI**: Modern Python web framework
- **Pydantic**: Data validation and serialization
- **Firebase Admin SDK**: Database and authentication
- **Uvicorn**: ASGI server

### Database
- **Firestore**: NoSQL document database
- **Firebase Auth**: User authentication

### Deployment
- **Google Cloud Run**: Serverless container platform
- **Docker**: Containerization
- **Cloud Build**: CI/CD pipeline

## 🚦 Getting Started

### Prerequisites

- Python 3.11+
- Google Cloud Project
- Firebase Project
- Docker (for deployment)

### Local Development

1. **Clone the repository**:
   ```bash
   git clone https://github.com/GouniManikumar12/credit-chakra.git
   cd credit-chakra
   ```

2. **Set up the backend**:
   ```bash
   cd backend
   pip install -r requirements.txt
   cp .env.example .env
   # Edit .env with your Firebase credentials
   ```

3. **Run the backend**:
   ```bash
   uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

4. **Access the API**:
   - API: http://localhost:8000
   - Docs: http://localhost:8000/docs
   - Health: http://localhost:8000/health

### Testing

Run the API test suite:
```bash
cd backend
python test_api.py
```

## 📊 Data Model

### MSME Profile
```json
{
  "msme_id": "unique_identifier",
  "name": "Business Name",
  "business_type": "retail|b2b|services|manufacturing",
  "location": "City, State",
  "score": 750,
  "risk_band": "green|yellow|red",
  "tags": ["retail", "electronics"]
}
```

### Signal Sources
- **GST**: Monthly turnover data
- **UPI**: Transaction volume and frequency
- **Reviews**: Ratings from Google Maps, Zomato
- **Social Media**: Instagram, JustDial presence
- **Maps**: Business listing information

### Scoring Algorithm
1. Collect signals from multiple sources
2. Normalize each signal (0-1 scale)
3. Apply weighted scoring:
   - GST: 30%
   - UPI: 25%
   - Reviews: 20%
   - JustDial: 10%
   - Instagram: 10%
   - Maps: 5%
4. Calculate final score (0-1000)
5. Determine risk band

## 🔧 API Endpoints

### MSME Management
- `POST /api/msme/create` - Create MSME profile
- `GET /api/msme/{id}` - Get MSME profile
- `PUT /api/msme/{id}` - Update MSME profile
- `GET /api/msme/` - List all MSMEs

### Signal Management
- `POST /api/signals/add` - Add new signal
- `GET /api/signals/{msme_id}` - Get MSME signals
- `GET /api/signals/{msme_id}/{signal_id}` - Get specific signal

### Nudge Management
- `POST /api/nudges/send` - Send notification
- `GET /api/nudges/{msme_id}` - Get MSME nudges
- `PUT /api/nudges/{msme_id}/{nudge_id}` - Update nudge status

## 🚀 Deployment

### Quick Deploy to Cloud Run

1. **Set up Google Cloud**:
   ```bash
   gcloud auth login
   gcloud config set project YOUR_PROJECT_ID
   ```

2. **Deploy**:
   ```bash
   cd deploy
   ./deploy.sh
   ```

See [deploy/README.md](deploy/README.md) for detailed deployment instructions.

## 📈 Monitoring

### Health Checks
- **Endpoint**: `/health`
- **Status**: Service health and database connectivity

### Logging
- **Cloud Logging**: Structured application logs
- **Error Tracking**: Automatic error reporting
- **Performance Monitoring**: Request latency and throughput

## 🔒 Security

### Authentication
- Firebase Auth integration
- JWT token validation
- Role-based access control

### Data Protection
- Firestore security rules
- HTTPS encryption
- Input validation and sanitization

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>

## 🗺️ Roadmap

### Phase 1 (Current)
- [x] Backend API development
- [x] Firebase integration
- [x] Basic scoring algorithm
- [x] Cloud Run deployment

### Phase 2 (Next)
- [ ] Frontend dashboard
- [ ] Advanced analytics
- [ ] Machine learning scoring
- [ ] Mobile app

### Phase 3 (Future)
- [ ] Real-time data streams
- [ ] Advanced nudge automation
- [ ] Third-party integrations
- [ ] Multi-tenant support

---

**Built with ❤️ for the MSME community**
