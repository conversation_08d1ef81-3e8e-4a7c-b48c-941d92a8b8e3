#!/usr/bin/env python3
"""
Pytest test suite for Credit Chakra Reports API endpoints.

This script tests the basic functionality of all report generation endpoints
to ensure they are working correctly.

Author: Credit Chakra Team
Version: 1.0.0
"""

import pytest
import asyncio
import sys
import os
from datetime import date, timedelta

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.reports_service import reports_service


@pytest.mark.asyncio
async def test_portfolio_summary_report():
    """Test Portfolio Summary Report generation"""
    print("Testing Portfolio Summary Report...")
    try:
        start_date = date.today() - timedelta(days=365)
        end_date = date.today()
        
        report = await reports_service.generate_portfolio_summary_report(
            start_date=start_date,
            end_date=end_date,
            generated_by="test_user"
        )
        
        print(f"✅ Portfolio Summary Report generated successfully")
        print(f"   Report ID: {report.metadata.report_id}")
        print(f"   Total MSMEs: {report.total_msmes}")
        print(f"   Portfolio Health Score: {report.overall_portfolio_health_score:.1f}%")
        print(f"   High Risk MSMEs: {report.risk_distribution.high_risk_count}")
        return True
        
    except Exception as e:
        print(f"❌ Portfolio Summary Report failed: {str(e)}")
        return False


@pytest.mark.asyncio
async def test_risk_exposure_report():
    """Test Risk Exposure Report generation"""
    print("\nTesting Risk Exposure Report...")
    try:
        start_date = date.today() - timedelta(days=365)
        end_date = date.today()
        
        report = await reports_service.generate_risk_exposure_report(
            start_date=start_date,
            end_date=end_date,
            generated_by="test_user"
        )
        
        print(f"✅ Risk Exposure Report generated successfully")
        print(f"   Report ID: {report.metadata.report_id}")
        print(f"   Total Portfolio Exposure: ₹{report.total_portfolio_exposure:,.0f}")
        print(f"   Geographic Distributions: {len(report.geographic_distribution)}")
        print(f"   Top 10 High Risk MSMEs: {len(report.top_10_high_risk_msmes)}")
        return True
        
    except Exception as e:
        print(f"❌ Risk Exposure Report failed: {str(e)}")
        return False


@pytest.mark.asyncio
async def test_compliance_regulatory_report():
    """Test Compliance & Regulatory Report generation"""
    print("\nTesting Compliance & Regulatory Report...")
    try:
        start_date = date.today() - timedelta(days=365)
        end_date = date.today()
        
        report = await reports_service.generate_compliance_regulatory_report(
            start_date=start_date,
            end_date=end_date,
            generated_by="test_user"
        )
        
        print(f"✅ Compliance & Regulatory Report generated successfully")
        print(f"   Report ID: {report.metadata.report_id}")
        print(f"   Overall Compliance Score: {report.overall_compliance_score:.1f}%")
        print(f"   GST Compliance Rate: {report.gst_compliance_status.compliance_rate:.1f}%")
        print(f"   Udyam Verification Rate: {report.udyam_registration_status.verification_rate:.1f}%")
        return True
        
    except Exception as e:
        print(f"❌ Compliance & Regulatory Report failed: {str(e)}")
        return False


@pytest.mark.asyncio
async def test_performance_trends_report():
    """Test Performance Trends Report generation"""
    print("\nTesting Performance Trends Report...")
    try:
        start_date = date.today() - timedelta(days=365)
        end_date = date.today()
        
        report = await reports_service.generate_performance_trends_report(
            start_date=start_date,
            end_date=end_date,
            generated_by="test_user"
        )
        
        print(f"✅ Performance Trends Report generated successfully")
        print(f"   Report ID: {report.metadata.report_id}")
        print(f"   Monthly Performance Records: {len(report.monthly_performance)}")
        print(f"   YoY Portfolio Growth: {report.yoy_portfolio_growth:.1f}%")
        print(f"   Nudge Delivery Rate: {report.nudge_effectiveness_metrics.delivery_rate:.1f}%")
        return True
        
    except Exception as e:
        print(f"❌ Performance Trends Report failed: {str(e)}")
        return False


@pytest.mark.asyncio
async def test_detailed_msme_profile_report():
    """Test Detailed MSME Profile Report generation"""
    print("\nTesting Detailed MSME Profile Report...")
    try:
        start_date = date.today() - timedelta(days=365)
        end_date = date.today()
        
        # Use a mock MSME ID from the data service
        from services.data_service import data_service
        portfolio_data = data_service.get_all_msmes()
        if not portfolio_data:
            print("❌ No MSME data available for testing")
            return False

        msme_id = portfolio_data[0].msme_id
        
        report = await reports_service.generate_detailed_msme_profile_report(
            msme_id=msme_id,
            start_date=start_date,
            end_date=end_date,
            generated_by="test_user"
        )
        
        print(f"✅ Detailed MSME Profile Report generated successfully")
        print(f"   Report ID: {report.metadata.report_id}")
        print(f"   MSME: {report.msme_name} ({report.msme_id})")
        print(f"   Overall Credit Score: {report.overall_credit_score:.0f}")
        print(f"   Parameter Scores: {len(report.parameter_scores)}")
        print(f"   Signal History: {len(report.signal_history)} entries")
        return True
        
    except Exception as e:
        print(f"❌ Detailed MSME Profile Report failed: {str(e)}")
        return False


async def main():
    """Run all report tests"""
    print("🚀 Starting Credit Chakra Reports API Tests")
    print("=" * 50)
    
    tests = [
        test_portfolio_summary_report,
        test_risk_exposure_report,
        test_compliance_regulatory_report,
        test_performance_trends_report,
        test_detailed_msme_profile_report
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if await test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Reports API is working correctly.")
        return 0
    else:
        print(f"⚠️  {total - passed} tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
