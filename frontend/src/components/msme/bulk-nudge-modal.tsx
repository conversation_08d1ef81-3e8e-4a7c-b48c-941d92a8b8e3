'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { MSME } from '@/types';
import { api } from '@/lib/api';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Scroll<PERSON>rea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  MessageSquare, 
  Filter, 
  Users, 
  Send, 
  AlertTriangle,
  TrendingDown,
  Building2,
  MapPin,
  BarChart3,
  X,
  CheckCircle2
} from 'lucide-react';

interface BulkNudgeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onNudgesSent?: (count: number) => void;
}

interface FilterCriteria {
  riskBands: string[];
  businessTypes: string[];
  scoreRange: [number, number];
  locations: string[];
  searchTerm: string;
}

interface FormData {
  trigger_type: 'score_drop' | 'manual' | 'risk_alert';
  message: string;
  medium: 'whatsapp' | 'email' | 'sms';
}

const triggerTypeOptions = [
  {
    value: 'manual' as const,
    label: 'Manual Nudge',
    description: 'Send a custom message',
    icon: MessageSquare,
  },
  {
    value: 'score_drop' as const,
    label: 'Score Drop Alert',
    description: 'Alert about credit score decline',
    icon: TrendingDown,
  },
  {
    value: 'risk_alert' as const,
    label: 'Risk Alert',
    description: 'High risk warning notification',
    icon: AlertTriangle,
  },
];

const mediumOptions = [
  { value: 'whatsapp' as const, label: 'WhatsApp', icon: '📱' },
  { value: 'email' as const, label: 'Email', icon: '📧' },
  { value: 'sms' as const, label: 'SMS', icon: '💬' },
];

const riskBandOptions = [
  { value: 'green', label: 'Low Risk', color: 'bg-green-500' },
  { value: 'yellow', label: 'Medium Risk', color: 'bg-yellow-500' },
  { value: 'red', label: 'High Risk', color: 'bg-red-500' },
];

const businessTypeOptions = [
  { value: 'retail', label: 'Retail' },
  { value: 'b2b', label: 'B2B' },
  { value: 'manufacturing', label: 'Manufacturing' },
  { value: 'services', label: 'Services' },
];

export function BulkNudgeModal({ isOpen, onClose, onNudgesSent }: BulkNudgeModalProps) {
  const [msmes, setMsmes] = useState<MSME[]>([]);
  const [filteredMsmes, setFilteredMsmes] = useState<MSME[]>([]);
  const [selectedMsmes, setSelectedMsmes] = useState<Set<string>>(new Set());
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [currentTab, setCurrentTab] = useState('filter');
  
  const [filters, setFilters] = useState<FilterCriteria>({
    riskBands: [],
    businessTypes: [],
    scoreRange: [0, 1000],
    locations: [],
    searchTerm: '',
  });

  const [formData, setFormData] = useState<FormData>({
    trigger_type: 'manual',
    message: '',
    medium: 'whatsapp',
  });

  // Fetch MSMEs when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchMsmes();
    }
  }, [isOpen]);

  const fetchMsmes = async () => {
    try {
      setLoading(true);
      const data = await api.getPortfolio();
      setMsmes(data);
    } catch (error) {
      console.error('Failed to fetch MSMEs:', error);
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = useCallback(() => {
    let filtered = msmes;

    // Filter by risk bands
    if (filters.riskBands.length > 0) {
      filtered = filtered.filter(msme => filters.riskBands.includes(msme.risk_band));
    }

    // Filter by business types
    if (filters.businessTypes.length > 0) {
      filtered = filtered.filter(msme => filters.businessTypes.includes(msme.business_type));
    }

    // Filter by score range
    filtered = filtered.filter(msme => 
      msme.current_score >= filters.scoreRange[0] && 
      msme.current_score <= filters.scoreRange[1]
    );

    // Filter by search term
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(msme => 
        msme.name.toLowerCase().includes(searchLower) ||
        msme.location.toLowerCase().includes(searchLower) ||
        msme.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    setFilteredMsmes(filtered);
  }, [msmes, filters]);

  // Apply filters when filters or MSMEs change
  useEffect(() => {
    applyFilters();
  }, [applyFilters]);

  const handleFilterChange = (key: keyof FilterCriteria, value: unknown) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const toggleMsmeSelection = (msmeId: string) => {
    const newSelected = new Set(selectedMsmes);
    if (newSelected.has(msmeId)) {
      newSelected.delete(msmeId);
    } else {
      newSelected.add(msmeId);
    }
    setSelectedMsmes(newSelected);
  };

  const selectAll = () => {
    setSelectedMsmes(new Set(filteredMsmes.map(msme => msme.msme_id)));
  };

  const clearSelection = () => {
    setSelectedMsmes(new Set());
  };

  const handleSendBulkNudges = async () => {
    if (selectedMsmes.size === 0 || !formData.message.trim()) {
      return;
    }

    try {
      setSending(true);
      const selectedMsmeIds = Array.from(selectedMsmes);

      // Send bulk nudges using the new API
      const result = await api.sendBulkNudges(selectedMsmeIds, formData);

      onNudgesSent?.(result.successful);
      onClose();
      
      // Reset form
      setSelectedMsmes(new Set());
      setFormData({
        trigger_type: 'manual',
        message: '',
        medium: 'whatsapp',
      });
      setCurrentTab('filter');
      
    } catch (error) {
      console.error('Failed to send bulk nudges:', error);
    } finally {
      setSending(false);
    }
  };

  const getRiskBadgeVariant = (risk: string) => {
    switch (risk) {
      case 'green': return 'default';
      case 'yellow': return 'secondary';
      case 'red': return 'destructive';
      default: return 'outline';
    }
  };

  const getRiskLabel = (risk: string) => {
    switch (risk) {
      case 'green': return 'Low';
      case 'yellow': return 'Medium';
      case 'red': return 'High';
      default: return 'Unknown';
    }
  };



  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Bulk Nudge Campaign
          </DialogTitle>
          <DialogDescription>
            Send notifications to multiple MSMEs based on your selection criteria
          </DialogDescription>
        </DialogHeader>

        <Tabs value={currentTab} onValueChange={setCurrentTab} className="flex-1">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="filter" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filter & Select
            </TabsTrigger>
            <TabsTrigger value="compose" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Compose Message
            </TabsTrigger>
            <TabsTrigger value="review" className="flex items-center gap-2">
              <CheckCircle2 className="h-4 w-4" />
              Review & Send
            </TabsTrigger>
          </TabsList>

          <div className="mt-4 flex-1 overflow-hidden">
            <TabsContent value="filter" className="h-full space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Filters Panel */}
                <div className="space-y-4">
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Filters</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {/* Search */}
                      <div>
                        <Label htmlFor="search">Search</Label>
                        <Input
                          id="search"
                          placeholder="Search by name, location, tags..."
                          value={filters.searchTerm}
                          onChange={(e) => handleFilterChange('searchTerm', e.target.value)}
                        />
                      </div>

                      {/* Risk Bands */}
                      <div>
                        <Label>Risk Bands</Label>
                        <div className="space-y-2 mt-2">
                          {riskBandOptions.map((option) => (
                            <div key={option.value} className="flex items-center space-x-2">
                              <Checkbox
                                id={`risk-${option.value}`}
                                checked={filters.riskBands.includes(option.value)}
                                onCheckedChange={(checked) => {
                                  const newRiskBands = checked
                                    ? [...filters.riskBands, option.value]
                                    : filters.riskBands.filter(r => r !== option.value);
                                  handleFilterChange('riskBands', newRiskBands);
                                }}
                              />
                              <label htmlFor={`risk-${option.value}`} className="flex items-center gap-2 text-sm">
                                <div className={`w-3 h-3 rounded-full ${option.color}`} />
                                {option.label}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Business Types */}
                      <div>
                        <Label>Business Types</Label>
                        <div className="space-y-2 mt-2">
                          {businessTypeOptions.map((option) => (
                            <div key={option.value} className="flex items-center space-x-2">
                              <Checkbox
                                id={`business-${option.value}`}
                                checked={filters.businessTypes.includes(option.value)}
                                onCheckedChange={(checked) => {
                                  const newBusinessTypes = checked
                                    ? [...filters.businessTypes, option.value]
                                    : filters.businessTypes.filter(b => b !== option.value);
                                  handleFilterChange('businessTypes', newBusinessTypes);
                                }}
                              />
                              <label htmlFor={`business-${option.value}`} className="text-sm">
                                {option.label}
                              </label>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Score Range */}
                      <div>
                        <Label>Score Range: {filters.scoreRange[0]} - {filters.scoreRange[1]}</Label>
                        <div className="mt-2">
                          <Slider
                            value={filters.scoreRange}
                            onValueChange={(value) => handleFilterChange('scoreRange', value as [number, number])}
                            max={1000}
                            min={0}
                            step={10}
                            className="w-full"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* MSME List */}
                <div className="md:col-span-2">
                  <Card>
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-sm">
                          MSMEs ({filteredMsmes.length} found)
                        </CardTitle>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline" onClick={selectAll}>
                            Select All
                          </Button>
                          <Button size="sm" variant="outline" onClick={clearSelection}>
                            Clear
                          </Button>
                        </div>
                      </div>
                      {selectedMsmes.size > 0 && (
                        <Badge variant="secondary">
                          {selectedMsmes.size} selected
                        </Badge>
                      )}
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-96">
                        <div className="space-y-2">
                          {loading ? (
                            <div className="text-center py-8 text-muted-foreground">
                              Loading MSMEs...
                            </div>
                          ) : filteredMsmes.length === 0 ? (
                            <div className="text-center py-8 text-muted-foreground">
                              No MSMEs match your filters
                            </div>
                          ) : (
                            filteredMsmes.map((msme) => (
                              <div
                                key={msme.msme_id}
                                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                                  selectedMsmes.has(msme.msme_id)
                                    ? 'border-primary bg-primary/5'
                                    : 'border-border hover:bg-muted/50'
                                }`}
                                onClick={() => toggleMsmeSelection(msme.msme_id)}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-1">
                                      <Checkbox
                                        checked={selectedMsmes.has(msme.msme_id)}
                                        onChange={() => {}}
                                      />
                                      <h4 className="font-medium text-sm">{msme.name}</h4>
                                      <Badge variant={getRiskBadgeVariant(msme.risk_band)} className="text-xs">
                                        {getRiskLabel(msme.risk_band)} Risk
                                      </Badge>
                                    </div>
                                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                      <span className="flex items-center gap-1">
                                        <Building2 className="h-3 w-3" />
                                        {msme.business_type}
                                      </span>
                                      <span className="flex items-center gap-1">
                                        <MapPin className="h-3 w-3" />
                                        {msme.location}
                                      </span>
                                      <span className="flex items-center gap-1">
                                        <BarChart3 className="h-3 w-3" />
                                        {msme.current_score}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))
                          )}
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="compose" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Compose Nudge Message</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Trigger Type */}
                  <div>
                    <Label>Nudge Type</Label>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mt-2">
                      {triggerTypeOptions.map((option) => (
                        <div
                          key={option.value}
                          className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                            formData.trigger_type === option.value
                              ? 'border-primary bg-primary/5'
                              : 'border-border hover:bg-muted/50'
                          }`}
                          onClick={() => setFormData(prev => ({ ...prev, trigger_type: option.value }))}
                        >
                          <div className="flex items-center gap-2 mb-1">
                            <option.icon className="h-4 w-4" />
                            <span className="font-medium text-sm">{option.label}</span>
                          </div>
                          <p className="text-xs text-muted-foreground">{option.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Medium */}
                  <div>
                    <Label>Delivery Method</Label>
                    <Select
                      value={formData.medium}
                      onValueChange={(value: 'whatsapp' | 'email' | 'sms') =>
                        setFormData(prev => ({ ...prev, medium: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {mediumOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            <div className="flex items-center gap-2">
                              <span>{option.icon}</span>
                              {option.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Message */}
                  <div>
                    <Label htmlFor="message">Message</Label>
                    <Textarea
                      id="message"
                      placeholder="Enter your nudge message..."
                      value={formData.message}
                      onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                      rows={6}
                      className="mt-2"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      {formData.message.length}/500 characters
                    </p>
                  </div>

                  {/* Message Templates */}
                  <div>
                    <Label>Quick Templates</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setFormData(prev => ({
                          ...prev,
                          message: "Your credit score has improved! Keep up the good work with your business operations."
                        }))}
                      >
                        Score Improvement
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setFormData(prev => ({
                          ...prev,
                          message: "We noticed a decline in your credit score. Please review your recent business activities."
                        }))}
                      >
                        Score Alert
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setFormData(prev => ({
                          ...prev,
                          message: "Your business is performing well! Consider exploring new growth opportunities."
                        }))}
                      >
                        Growth Opportunity
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setFormData(prev => ({
                          ...prev,
                          message: "Please update your business information to maintain accurate credit assessment."
                        }))}
                      >
                        Update Request
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="review" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Campaign Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle>Campaign Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Recipients:</span>
                      <Badge variant="secondary">{selectedMsmes.size} MSMEs</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Nudge Type:</span>
                      <span className="text-sm font-medium">
                        {triggerTypeOptions.find(t => t.value === formData.trigger_type)?.label}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Delivery Method:</span>
                      <span className="text-sm font-medium">
                        {mediumOptions.find(m => m.value === formData.medium)?.label}
                      </span>
                    </div>
                    <Separator />
                    <div>
                      <span className="text-sm text-muted-foreground">Message Preview:</span>
                      <div className="mt-2 p-3 bg-muted rounded-lg">
                        <p className="text-sm">{formData.message || 'No message entered'}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Selected MSMEs */}
                <Card>
                  <CardHeader>
                    <CardTitle>Selected MSMEs</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-64">
                      <div className="space-y-2">
                        {Array.from(selectedMsmes).map((msmeId) => {
                          const msme = msmes.find(m => m.msme_id === msmeId);
                          if (!msme) return null;

                          return (
                            <div key={msmeId} className="flex items-center justify-between p-2 border rounded">
                              <div>
                                <p className="text-sm font-medium">{msme.name}</p>
                                <p className="text-xs text-muted-foreground">{msme.location}</p>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge variant={getRiskBadgeVariant(msme.risk_band)} className="text-xs">
                                  {getRiskLabel(msme.risk_band)}
                                </Badge>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => toggleMsmeSelection(msmeId)}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </ScrollArea>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </div>
        </Tabs>

        <DialogFooter className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {selectedMsmes.size > 0 && (
              <>
                <Users className="h-4 w-4" />
                {selectedMsmes.size} MSMEs selected
              </>
            )}
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            {currentTab === 'filter' && (
              <Button
                onClick={() => setCurrentTab('compose')}
                disabled={selectedMsmes.size === 0}
              >
                Next: Compose Message
              </Button>
            )}
            {currentTab === 'compose' && (
              <Button
                onClick={() => setCurrentTab('review')}
                disabled={!formData.message.trim()}
              >
                Next: Review
              </Button>
            )}
            {currentTab === 'review' && (
              <Button
                onClick={handleSendBulkNudges}
                disabled={selectedMsmes.size === 0 || !formData.message.trim() || sending}
                className="flex items-center gap-2"
              >
                {sending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Sending...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4" />
                    Send {selectedMsmes.size} Nudges
                  </>
                )}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
