#!/bin/bash

# Credit Chakra - Full Stack Startup Script
# This script starts both the FastAPI backend and Next.js frontend

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[CREDIT-CHAKRA]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Function to kill processes on specific ports
cleanup_ports() {
    print_status "Cleaning up existing processes..."
    
    if port_in_use 8000; then
        print_warning "Port 8000 is in use. Killing existing process..."
        lsof -ti :8000 | xargs kill -9 2>/dev/null || true
    fi
    
    if port_in_use 3000; then
        print_warning "Port 3000 is in use. Killing existing process..."
        lsof -ti :3000 | xargs kill -9 2>/dev/null || true
    fi
    
    sleep 2
}

# Function to setup Python environment
setup_python_env() {
    print_status "Setting up Python environment..."

    cd backend

    # Always ensure we have the latest environment
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python3 -m venv venv
        print_success "✅ Virtual environment created successfully!"
    else
        print_status "Virtual environment already exists"
    fi

    # Activate virtual environment
    print_status "Activating Python virtual environment..."
    source venv/bin/activate

    # Install/upgrade pip
    print_status "Upgrading pip..."
    pip install --upgrade pip --quiet

    # Install/update requirements (always get latest compatible versions)
    print_status "Installing/updating Python dependencies..."
    pip install -r requirements.txt --upgrade --quiet

    print_success "✅ Python environment ready!"
    cd ..
}

# Function to setup Node.js environment
setup_node_env() {
    print_status "Setting up Node.js environment..."
    
    cd frontend
    
    # Check if node_modules exists
    if [ ! -d "node_modules" ]; then
        print_status "Installing Node.js dependencies..."
        npm install
    else
        print_status "Node.js dependencies already installed"
    fi
    
    cd ..
}

# Function to start backend
start_backend() {
    print_status "Starting FastAPI backend server..."
    
    cd backend
    source venv/bin/activate
    
    # Start backend in background
    python main.py &
    BACKEND_PID=$!
    
    cd ..
    
    # Wait for backend to start
    print_status "Waiting for backend to start..."
    for i in {1..30}; do
        if curl -s http://localhost:8000/health >/dev/null 2>&1; then
            print_success "Backend server started successfully on http://localhost:8000"
            return 0
        fi
        sleep 1
    done
    
    print_error "Backend failed to start within 30 seconds"
    return 1
}

# Function to start frontend
start_frontend() {
    print_status "Starting Next.js frontend server..."
    
    cd frontend
    
    # Start frontend in background
    npm run dev &
    FRONTEND_PID=$!
    
    cd ..
    
    # Wait for frontend to start
    print_status "Waiting for frontend to start..."
    for i in {1..60}; do
        if curl -s http://localhost:3000 >/dev/null 2>&1; then
            print_success "Frontend server started successfully on http://localhost:3000"
            return 0
        fi
        sleep 1
    done
    
    print_error "Frontend failed to start within 60 seconds"
    return 1
}

# Function to handle cleanup on exit
cleanup() {
    print_status "Shutting down servers..."
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    # Kill any remaining processes on our ports
    lsof -ti :8000 | xargs kill -9 2>/dev/null || true
    lsof -ti :3000 | xargs kill -9 2>/dev/null || true
    
    print_success "Cleanup completed"
    exit 0
}

# Main execution
main() {
    print_status "Starting Credit Chakra Full Stack Application"
    print_status "=============================================="
    
    # Check prerequisites
    print_status "Checking prerequisites..."
    
    if ! command_exists python3; then
        print_error "Python 3 is not installed. Please install Python 3.8 or higher."
        exit 1
    fi
    
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 18 or higher."
        exit 1
    fi
    
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    print_success "All prerequisites are installed"
    
    # Setup trap for cleanup
    trap cleanup SIGINT SIGTERM EXIT
    
    # Cleanup existing processes
    cleanup_ports
    
    # Setup environments
    setup_python_env
    setup_node_env
    
    # Start services
    start_backend
    start_frontend
    
    # Display success message
    echo ""
    print_success "🚀 Credit Chakra is now running!"
    print_success "================================"
    print_success "📊 Frontend (Analytics): http://localhost:3000"
    print_success "👥 Portfolio: http://localhost:3000/msmes"
    print_success "🔧 Backend API: http://localhost:8000"
    print_success "📚 API Docs: http://localhost:8000/docs"
    echo ""
    print_status "Press Ctrl+C to stop all servers"
    
    # Wait for user to stop
    wait
}

# Run main function
main "$@"
