from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from enum import Enum

class ConsentStatus(str, Enum):
    PENDING = "PENDING"
    ACTIVE = "ACTIVE"
    PAUSED = "PAUSED"
    REVOKED = "REVOKED"
    EXPIRED = "EXPIRED"

class AccountType(str, Enum):
    SAVINGS = "SAVINGS"
    CURRENT = "CURRENT"
    OVERDRAFT = "OVERDRAFT"
    CREDIT_CARD = "CREDIT_CARD"
    TERM_DEPOSIT = "TERM_DEPOSIT"

class TransactionType(str, Enum):
    DEBIT = "DEBIT"
    CREDIT = "CREDIT"

class TransactionMode(str, Enum):
    UPI = "UPI"
    NEFT = "NEFT"
    RTGS = "RTGS"
    IMPS = "IMPS"
    CHEQUE = "CHEQUE"
    CASH = "CASH"
    CARD = "CARD"
    NET_BANKING = "NET_BANKING"

class ConsentRequest(BaseModel):
    """Account Aggregator consent request model"""
    consent_id: str = Field(..., description="Unique consent identifier")
    customer_id: str = Field(..., description="Customer identifier")
    msme_id: str = Field(..., description="MSME identifier")
    
    # Consent details
    purpose: str = Field(..., description="Purpose of data access")
    data_life: int = Field(..., description="Data life in months")
    frequency_unit: str = Field("MONTH", description="Frequency unit")
    frequency_value: int = Field(1, description="Frequency value")
    
    # Account information
    account_types: List[AccountType] = Field(..., description="Types of accounts to access")
    fi_types: List[str] = Field(..., description="Financial institution types")
    
    # Date range
    from_date: date = Field(..., description="Data from date")
    to_date: date = Field(..., description="Data to date")
    
    # Consent metadata
    consent_start: datetime = Field(..., description="Consent start time")
    consent_expiry: datetime = Field(..., description="Consent expiry time")
    status: ConsentStatus = Field(ConsentStatus.PENDING, description="Consent status")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class BankAccount(BaseModel):
    """Bank account information from AA"""
    account_id: str = Field(..., description="Account identifier")
    masked_account_number: str = Field(..., description="Masked account number")
    account_type: AccountType = Field(..., description="Account type")
    
    # Bank details
    bank_name: str = Field(..., description="Bank name")
    bank_code: str = Field(..., description="Bank code")
    branch_name: str = Field(..., description="Branch name")
    ifsc_code: str = Field(..., description="IFSC code")
    
    # Account details
    account_holder_name: str = Field(..., description="Account holder name")
    currency: str = Field("INR", description="Account currency")
    
    # Balance information
    current_balance: float = Field(0.0, description="Current account balance")
    available_balance: float = Field(0.0, description="Available balance")
    
    # Account status
    is_active: bool = Field(True, description="Account active status")
    last_updated: datetime = Field(default_factory=datetime.utcnow)

class Transaction(BaseModel):
    """Bank transaction from AA"""
    transaction_id: str = Field(..., description="Transaction identifier")
    account_id: str = Field(..., description="Account identifier")
    
    # Transaction details
    transaction_date: datetime = Field(..., description="Transaction date")
    value_date: datetime = Field(..., description="Value date")
    transaction_type: TransactionType = Field(..., description="Transaction type")
    amount: float = Field(..., description="Transaction amount")
    
    # Transaction metadata
    description: str = Field(..., description="Transaction description")
    reference_number: str = Field(..., description="Transaction reference")
    mode: TransactionMode = Field(..., description="Transaction mode")
    
    # Balance information
    balance_after_transaction: float = Field(0.0, description="Balance after transaction")
    
    # Counterparty information
    counterparty_name: Optional[str] = Field(None, description="Counterparty name")
    counterparty_account: Optional[str] = Field(None, description="Counterparty account")
    
    # Categories
    category: Optional[str] = Field(None, description="Transaction category")
    subcategory: Optional[str] = Field(None, description="Transaction subcategory")
    
    # Flags
    is_digital: bool = Field(False, description="Is digital transaction")
    is_business_related: bool = Field(False, description="Is business related")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)

class CashFlowStatement(BaseModel):
    """Cash flow statement from bank data"""
    account_id: str = Field(..., description="Account identifier")
    period_start: date = Field(..., description="Period start date")
    period_end: date = Field(..., description="Period end date")
    
    # Opening and closing balances
    opening_balance: float = Field(0.0, description="Opening balance")
    closing_balance: float = Field(0.0, description="Closing balance")
    
    # Cash flow components
    total_inflows: float = Field(0.0, description="Total cash inflows")
    total_outflows: float = Field(0.0, description="Total cash outflows")
    net_cash_flow: float = Field(0.0, description="Net cash flow")
    
    # Operating cash flows
    operating_inflows: float = Field(0.0, description="Operating cash inflows")
    operating_outflows: float = Field(0.0, description="Operating cash outflows")
    operating_cash_flow: float = Field(0.0, description="Operating cash flow")
    
    # Investment cash flows
    investment_inflows: float = Field(0.0, description="Investment cash inflows")
    investment_outflows: float = Field(0.0, description="Investment cash outflows")
    investment_cash_flow: float = Field(0.0, description="Investment cash flow")
    
    # Financing cash flows
    financing_inflows: float = Field(0.0, description="Financing cash inflows")
    financing_outflows: float = Field(0.0, description="Financing cash outflows")
    financing_cash_flow: float = Field(0.0, description="Financing cash flow")
    
    # Digital payment metrics
    digital_transaction_count: int = Field(0, description="Digital transaction count")
    digital_transaction_value: float = Field(0.0, description="Digital transaction value")
    digital_adoption_rate: float = Field(0.0, description="Digital adoption rate")
    
    # UPI specific metrics
    upi_transaction_count: int = Field(0, description="UPI transaction count")
    upi_transaction_value: float = Field(0.0, description="UPI transaction value")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)

class BankingAnalytics(BaseModel):
    """Comprehensive banking analytics from AA data"""
    msme_id: str = Field(..., description="MSME identifier")
    consent_id: str = Field(..., description="Consent identifier")
    
    # Account summary
    total_accounts: int = Field(0, description="Total number of accounts")
    active_accounts: int = Field(0, description="Number of active accounts")
    account_types: List[AccountType] = Field(default_factory=list)
    
    # Balance analytics
    total_balance: float = Field(0.0, description="Total balance across accounts")
    avg_monthly_balance: float = Field(0.0, description="Average monthly balance")
    min_balance: float = Field(0.0, description="Minimum balance in period")
    max_balance: float = Field(0.0, description="Maximum balance in period")
    balance_volatility: float = Field(0.0, description="Balance volatility coefficient")
    
    # Transaction analytics
    total_transactions: int = Field(0, description="Total transaction count")
    avg_monthly_transactions: float = Field(0.0, description="Average monthly transactions")
    avg_transaction_amount: float = Field(0.0, description="Average transaction amount")
    
    # Cash flow analytics
    avg_monthly_inflow: float = Field(0.0, description="Average monthly inflow")
    avg_monthly_outflow: float = Field(0.0, description="Average monthly outflow")
    cash_flow_volatility: float = Field(0.0, description="Cash flow volatility")
    working_capital_cycle: float = Field(0.0, description="Working capital cycle in days")
    
    # Digital adoption metrics
    digital_transaction_ratio: float = Field(0.0, description="Digital transaction ratio")
    upi_adoption_rate: float = Field(0.0, description="UPI adoption rate")
    digital_payment_growth: float = Field(0.0, description="Digital payment growth rate")
    
    # Banking behavior
    account_utilization_rate: float = Field(0.0, description="Account utilization rate")
    overdraft_usage: float = Field(0.0, description="Overdraft usage percentage")
    bounce_rate: float = Field(0.0, description="Cheque bounce rate")
    
    # Risk indicators
    irregular_transaction_flag: bool = Field(False, description="Irregular transaction pattern")
    cash_intensive_flag: bool = Field(False, description="High cash transaction flag")
    dormant_account_flag: bool = Field(False, description="Dormant account flag")
    
    # Seasonal patterns
    seasonal_variation: float = Field(0.0, description="Seasonal variation index")
    peak_months: List[int] = Field(default_factory=list, description="Peak business months")
    low_months: List[int] = Field(default_factory=list, description="Low business months")
    
    # Credit worthiness indicators
    banking_score: float = Field(0.0, description="Banking behavior score (0-100)")
    stability_score: float = Field(0.0, description="Financial stability score (0-100)")
    digital_maturity_score: float = Field(0.0, description="Digital maturity score (0-100)")
    
    # Trends
    balance_trend: str = Field("stable", description="Balance trend")
    transaction_trend: str = Field("stable", description="Transaction trend")
    digital_trend: str = Field("stable", description="Digital adoption trend")
    
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    data_quality_score: float = Field(0.0, description="Data quality score")

class AADataRequest(BaseModel):
    """Request model for AA data fetching"""
    consent_id: str = Field(..., description="Consent identifier")
    customer_id: str = Field(..., description="Customer identifier")
    from_date: date = Field(..., description="Data from date")
    to_date: date = Field(..., description="Data to date")
    account_types: List[AccountType] = Field(default_factory=list)
    include_analytics: bool = Field(True, description="Include analytics calculation")

class AADataResponse(BaseModel):
    """Response model for AA data"""
    consent_id: str
    customer_id: str
    msme_id: Optional[str] = None
    
    accounts: List[BankAccount] = Field(default_factory=list)
    transactions: List[Transaction] = Field(default_factory=list)
    cash_flow_statements: List[CashFlowStatement] = Field(default_factory=list)
    analytics: Optional[BankingAnalytics] = None
    
    data_freshness: datetime = Field(default_factory=datetime.utcnow)
    source: str = Field("ACCOUNT_AGGREGATOR", description="Data source")
    status: str = Field("success", description="Response status")
    message: Optional[str] = None

class AAHealthCheck(BaseModel):
    """AA data source health check"""
    consent_id: str
    customer_id: str
    last_sync: datetime
    sync_status: str = Field(..., description="success/failed/partial")
    data_completeness: float = Field(0.0, description="Data completeness percentage")
    api_response_time: float = Field(0.0, description="API response time in seconds")
    consent_status: ConsentStatus
    accounts_synced: int = Field(0, description="Number of accounts synced")
    transactions_synced: int = Field(0, description="Number of transactions synced")
    error_count: int = Field(0, description="Number of errors in last sync")
    next_sync_scheduled: datetime
