#!/usr/bin/env node

/**
 * Codebase cleanup script for Credit Chakra
 * Removes unused imports, standardizes naming conventions, and optimizes code
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  // Directories to scan
  scanDirs: [
    'frontend/src/components',
    'frontend/src/hooks',
    'frontend/src/lib',
    'frontend/src/app',
    'frontend/src/types',
    'frontend/src/tests',
    'backend/routes',
    'backend/services',
    'backend/models',
    'backend/utils',
    'backend/tests'
  ],
  
  // File extensions to process
  extensions: ['.ts', '.tsx', '.js', '.jsx', '.py'],
  
  // Patterns to identify unused imports
  unusedImportPatterns: [
    /^import\s+.*\s+from\s+['"][^'"]*['"];\s*$/gm,
    /^from\s+.*\s+import\s+.*$/gm
  ],
  
  // Naming convention patterns
  namingConventions: {
    // Component files should be kebab-case
    componentFiles: /^[a-z][a-z0-9]*(-[a-z0-9]+)*\.(tsx|jsx)$/,
    // Hook files should start with 'use'
    hookFiles: /^use[A-Z][a-zA-Z0-9]*\.(ts|js)$/,
    // Utility files should be kebab-case
    utilFiles: /^[a-z][a-z0-9]*(-[a-z0-9]+)*\.(ts|js)$/,
    // Python files should be snake_case
    pythonFiles: /^[a-z][a-z0-9]*(_[a-z0-9]+)*\.py$/
  }
};

class CodebaseCleanup {
  constructor() {
    this.issues = [];
    this.fixes = [];
    this.stats = {
      filesScanned: 0,
      issuesFound: 0,
      fixesApplied: 0
    };
  }

  async run() {
    console.log('🧹 Starting Credit Chakra codebase cleanup...\n');
    
    for (const dir of config.scanDirs) {
      if (fs.existsSync(dir)) {
        await this.scanDirectory(dir);
      }
    }
    
    this.generateReport();
  }

  async scanDirectory(dirPath) {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        await this.scanDirectory(fullPath);
      } else if (this.shouldProcessFile(fullPath)) {
        await this.processFile(fullPath);
      }
    }
  }

  shouldProcessFile(filePath) {
    const ext = path.extname(filePath);
    return config.extensions.includes(ext);
  }

  async processFile(filePath) {
    this.stats.filesScanned++;
    
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const ext = path.extname(filePath);
      
      // Check naming conventions
      this.checkNamingConventions(filePath);
      
      // Process based on file type
      if (['.ts', '.tsx', '.js', '.jsx'].includes(ext)) {
        await this.processJavaScriptFile(filePath, content);
      } else if (ext === '.py') {
        await this.processPythonFile(filePath, content);
      }
      
    } catch (error) {
      this.addIssue(filePath, 'error', `Failed to process file: ${error.message}`);
    }
  }

  checkNamingConventions(filePath) {
    const fileName = path.basename(filePath);
    const dir = path.dirname(filePath);
    
    // Check component naming
    if (dir.includes('components') && fileName.endsWith('.tsx')) {
      if (!config.namingConventions.componentFiles.test(fileName)) {
        this.addIssue(filePath, 'naming', 'Component file should use kebab-case naming');
      }
    }
    
    // Check hook naming
    if (dir.includes('hooks') && (fileName.endsWith('.ts') || fileName.endsWith('.js'))) {
      if (!config.namingConventions.hookFiles.test(fileName)) {
        this.addIssue(filePath, 'naming', 'Hook file should start with "use" and use camelCase');
      }
    }
    
    // Check Python naming
    if (fileName.endsWith('.py')) {
      if (!config.namingConventions.pythonFiles.test(fileName)) {
        this.addIssue(filePath, 'naming', 'Python file should use snake_case naming');
      }
    }
  }

  async processJavaScriptFile(filePath, content) {
    let modifiedContent = content;
    let hasChanges = false;
    
    // Find unused imports
    const imports = this.extractImports(content);
    const usedIdentifiers = this.extractUsedIdentifiers(content);
    
    for (const importInfo of imports) {
      const unusedImports = importInfo.identifiers.filter(id => 
        !usedIdentifiers.includes(id) && !this.isTypeOnlyImport(id, content)
      );
      
      if (unusedImports.length > 0) {
        this.addIssue(filePath, 'unused-import', 
          `Unused imports: ${unusedImports.join(', ')} from ${importInfo.source}`);
        
        // Auto-fix: remove unused imports
        const newImportLine = this.removeUnusedFromImport(importInfo.line, unusedImports);
        if (newImportLine !== importInfo.line) {
          modifiedContent = modifiedContent.replace(importInfo.line, newImportLine);
          hasChanges = true;
          this.addFix(filePath, `Removed unused imports: ${unusedImports.join(', ')}`);
        }
      }
    }
    
    // Check for console.log statements (should be removed in production)
    const consoleMatches = content.match(/console\.(log|warn|error|debug)\(/g);
    if (consoleMatches) {
      this.addIssue(filePath, 'console', `Found ${consoleMatches.length} console statements`);
    }
    
    // Check for TODO/FIXME comments
    const todoMatches = content.match(/\/\/\s*(TODO|FIXME|HACK):/gi);
    if (todoMatches) {
      this.addIssue(filePath, 'todo', `Found ${todoMatches.length} TODO/FIXME comments`);
    }
    
    // Apply changes if any
    if (hasChanges) {
      fs.writeFileSync(filePath, modifiedContent);
      this.stats.fixesApplied++;
    }
  }

  async processPythonFile(filePath, content) {
    // Check for unused imports in Python
    const imports = this.extractPythonImports(content);
    const usedIdentifiers = this.extractPythonUsedIdentifiers(content);
    
    for (const importInfo of imports) {
      const unusedImports = importInfo.identifiers.filter(id => 
        !usedIdentifiers.includes(id)
      );
      
      if (unusedImports.length > 0) {
        this.addIssue(filePath, 'unused-import', 
          `Unused Python imports: ${unusedImports.join(', ')}`);
      }
    }
    
    // Check for print statements (should use logging)
    const printMatches = content.match(/print\(/g);
    if (printMatches) {
      this.addIssue(filePath, 'print', `Found ${printMatches.length} print statements - consider using logging`);
    }
  }

  extractImports(content) {
    const imports = [];
    const importRegex = /import\s+(?:{([^}]+)}|\*\s+as\s+(\w+)|(\w+))\s+from\s+['"]([^'"]+)['"];?/g;
    
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      const [fullMatch, namedImports, namespaceImport, defaultImport, source] = match;
      
      let identifiers = [];
      if (namedImports) {
        identifiers = namedImports.split(',').map(id => id.trim());
      } else if (namespaceImport) {
        identifiers = [namespaceImport];
      } else if (defaultImport) {
        identifiers = [defaultImport];
      }
      
      imports.push({
        line: fullMatch,
        identifiers,
        source,
        type: namedImports ? 'named' : namespaceImport ? 'namespace' : 'default'
      });
    }
    
    return imports;
  }

  extractUsedIdentifiers(content) {
    // Simple heuristic: find all word characters that could be identifiers
    const identifierRegex = /\b[a-zA-Z_$][a-zA-Z0-9_$]*\b/g;
    const matches = content.match(identifierRegex) || [];
    return [...new Set(matches)];
  }

  extractPythonImports(content) {
    const imports = [];
    const importRegex = /(?:from\s+([^\s]+)\s+)?import\s+([^\n]+)/g;
    
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      const [fullMatch, fromModule, importedItems] = match;
      
      const identifiers = importedItems.split(',').map(item => {
        const parts = item.trim().split(' as ');
        return parts[parts.length - 1].trim();
      });
      
      imports.push({
        line: fullMatch,
        identifiers,
        source: fromModule || 'builtin',
        type: fromModule ? 'from' : 'direct'
      });
    }
    
    return imports;
  }

  extractPythonUsedIdentifiers(content) {
    const identifierRegex = /\b[a-zA-Z_][a-zA-Z0-9_]*\b/g;
    const matches = content.match(identifierRegex) || [];
    return [...new Set(matches)];
  }

  isTypeOnlyImport(identifier, content) {
    // Check if identifier is only used in type annotations
    const typeUsageRegex = new RegExp(`:\\s*${identifier}\\b|<${identifier}>|${identifier}\\[`, 'g');
    return typeUsageRegex.test(content);
  }

  removeUnusedFromImport(importLine, unusedImports) {
    // Simple implementation - would need more sophisticated parsing for complex cases
    let newLine = importLine;
    
    for (const unused of unusedImports) {
      // Remove the unused import, handling commas properly
      newLine = newLine.replace(new RegExp(`\\b${unused}\\b,?\\s*`, 'g'), '');
      newLine = newLine.replace(/,\s*}/, ' }'); // Clean up trailing commas
    }
    
    // If all imports removed, remove the entire line
    if (newLine.match(/import\s*{\s*}\s*from/)) {
      return '';
    }
    
    return newLine;
  }

  addIssue(filePath, type, message) {
    this.issues.push({ filePath, type, message });
    this.stats.issuesFound++;
  }

  addFix(filePath, description) {
    this.fixes.push({ filePath, description });
  }

  generateReport() {
    console.log('\n📊 Cleanup Report');
    console.log('==================');
    console.log(`Files scanned: ${this.stats.filesScanned}`);
    console.log(`Issues found: ${this.stats.issuesFound}`);
    console.log(`Fixes applied: ${this.stats.fixesApplied}\n`);
    
    // Group issues by type
    const issuesByType = {};
    for (const issue of this.issues) {
      if (!issuesByType[issue.type]) {
        issuesByType[issue.type] = [];
      }
      issuesByType[issue.type].push(issue);
    }
    
    // Display issues by type
    for (const [type, issues] of Object.entries(issuesByType)) {
      console.log(`\n${type.toUpperCase()} (${issues.length} issues):`);
      console.log('─'.repeat(50));
      
      for (const issue of issues.slice(0, 10)) { // Show first 10
        console.log(`  ${issue.filePath}: ${issue.message}`);
      }
      
      if (issues.length > 10) {
        console.log(`  ... and ${issues.length - 10} more`);
      }
    }
    
    // Display fixes applied
    if (this.fixes.length > 0) {
      console.log('\n✅ FIXES APPLIED:');
      console.log('─'.repeat(50));
      
      for (const fix of this.fixes.slice(0, 10)) {
        console.log(`  ${fix.filePath}: ${fix.description}`);
      }
      
      if (this.fixes.length > 10) {
        console.log(`  ... and ${this.fixes.length - 10} more`);
      }
    }
    
    console.log('\n🎉 Cleanup completed!');
    
    // Save detailed report to file
    const reportData = {
      timestamp: new Date().toISOString(),
      stats: this.stats,
      issues: this.issues,
      fixes: this.fixes
    };
    
    fs.writeFileSync('cleanup-report.json', JSON.stringify(reportData, null, 2));
    console.log('📄 Detailed report saved to cleanup-report.json');
  }
}

// Run the cleanup if this script is executed directly
if (require.main === module) {
  const cleanup = new CodebaseCleanup();
  cleanup.run().catch(console.error);
}

module.exports = CodebaseCleanup;
