/**
 * WebSocket hook for real-time data management in Credit Chakra.
 * 
 * This hook provides WebSocket connectivity for real-time risk monitoring,
 * portfolio updates, and alert notifications with automatic reconnection
 * and subscription management.
 * 
 * @fileoverview WebSocket hook for real-time features
 * <AUTHOR> Chakra Team
 * @version 1.0.0
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { WebSocketMessage, RiskAlert, PortfolioRiskMetrics, SMAData, BusinessType } from '@/types';

interface UseWebSocketOptions {
  url: string;
  autoConnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Event) => void;
  onMessage?: (message: WebSocketMessage) => void;
}

interface WebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  reconnectAttempts: number;
}

interface UseWebSocketReturn {
  // Connection state
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  reconnectAttempts: number;
  
  // Connection methods
  connect: () => void;
  disconnect: () => void;
  reconnect: () => void;
  
  // Subscription methods
  subscribeToRiskEvents: () => void;
  subscribeToPortfolioMetrics: () => void;
  subscribeToAlerts: () => void;
  subscribeToSMAProgression: () => void;
  
  // Message sending
  sendMessage: (message: any) => void;
  
  // Data streams
  riskEvents: RiskAlert[];
  portfolioMetrics: PortfolioRiskMetrics | null;
  alerts: RiskAlert[];
  smaProgressions: SMAData[];
}

/**
 * Custom hook for WebSocket connection and real-time data management.
 * 
 * @param options - WebSocket configuration options
 * @returns WebSocket state and methods
 */
export function useWebSocket(options: UseWebSocketOptions): UseWebSocketReturn {
  const {
    url,
    autoConnect = true,
    reconnectInterval = 5000,
    maxReconnectAttempts = 10,
    onConnect,
    onDisconnect,
    onError,
    onMessage
  } = options;

  // WebSocket instance ref
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const clientIdRef = useRef<string>(`client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);

  // Connection state
  const [state, setState] = useState<WebSocketState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    reconnectAttempts: 0
  });

  // Data streams
  const [riskEvents, setRiskEvents] = useState<RiskAlert[]>([]);
  const [portfolioMetrics, setPortfolioMetrics] = useState<PortfolioRiskMetrics | null>(null);
  const [alerts, setAlerts] = useState<RiskAlert[]>([]);
  const [smaProgressions, setSmaProgressions] = useState<SMAData[]>([]);

  // Clear reconnect timeout
  const clearReconnectTimeout = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }
  }, []);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setState(prev => ({ ...prev, isConnecting: true, error: null }));

    try {
      const wsUrl = `${url}?client_id=${clientIdRef.current}`;
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      ws.onopen = () => {
        setState(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          error: null,
          reconnectAttempts: 0
        }));
        clearReconnectTimeout();
        onConnect?.();
      };

      ws.onclose = () => {
        setState(prev => ({ ...prev, isConnected: false, isConnecting: false }));
        onDisconnect?.();
        
        // Attempt reconnection if not manually disconnected
        if (state.reconnectAttempts < maxReconnectAttempts) {
          reconnectTimeoutRef.current = setTimeout(() => {
            setState(prev => ({ ...prev, reconnectAttempts: prev.reconnectAttempts + 1 }));
            connect();
          }, reconnectInterval);
        }
      };

      ws.onerror = (error) => {
        setState(prev => ({ ...prev, error: 'WebSocket connection error', isConnecting: false }));
        onError?.(error);
      };

      ws.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          onMessage?.(message);
          
          // Handle different message types
          switch (message.message_type) {
            case 'risk_event':
              setRiskEvents(prev => [message.data as unknown as RiskAlert, ...prev.slice(0, 99)]); // Keep last 100
              break;
              
            case 'portfolio_metrics':
              setPortfolioMetrics(message.data as unknown as PortfolioRiskMetrics);
              break;
              
            case 'alert':
              setAlerts(prev => {
                const newAlert = message.data as unknown as RiskAlert;
                const existingIndex = prev.findIndex(alert => alert.id === newAlert.id);
                if (existingIndex >= 0) {
                  // Update existing alert
                  const updated = [...prev];
                  updated[existingIndex] = newAlert;
                  return updated;
                } else {
                  // Add new alert
                  return [newAlert, ...prev.slice(0, 49)]; // Keep last 50
                }
              });
              break;
              
            case 'sma_progression':
              setSmaProgressions(prev => {
                // Safely validate and convert the data
                const data = message.data;
                if (!data || typeof data !== 'object') {
                  console.warn('Invalid SMA progression data received');
                  return prev;
                }

                // Type-safe conversion with validation
                const newSMA: SMAData = {
                  msme_id: String(data.msme_id || ''),
                  msme_name: String(data.msme_name || ''),
                  business_type: (data.business_type as BusinessType) || 'manufacturing',
                  current_classification: (data.current_classification as SMAData['current_classification']) || 'standard',
                  days_past_due: Number(data.days_past_due) || 0,
                  outstanding_amount: Number(data.outstanding_amount) || 0,
                  last_payment_date: String(data.last_payment_date || ''),
                  classification_history: Array.isArray(data.classification_history) ? data.classification_history : [],
                  stress_indicators: Array.isArray(data.stress_indicators) ? data.stress_indicators : [],
                  early_warning_signals: Array.isArray(data.early_warning_signals) ? data.early_warning_signals : [],
                  rbi_reporting_required: Boolean(data.rbi_reporting_required),
                  provision_required: Number(data.provision_required) || 0,
                  last_updated: String(data.last_updated || new Date().toISOString()),
                  next_review_date: String(data.next_review_date || new Date().toISOString())
                };

                const existingIndex = prev.findIndex(sma => sma.msme_id === newSMA.msme_id);
                if (existingIndex >= 0) {
                  // Update existing SMA data
                  const updated = [...prev];
                  updated[existingIndex] = newSMA;
                  return updated;
                } else {
                  // Add new SMA data
                  return [newSMA, ...prev];
                }
              });
              break;
              
            default:
              console.log('Unknown message type:', message.message_type);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        error: 'Failed to create WebSocket connection', 
        isConnecting: false 
      }));
    }
  }, [url, onConnect, onDisconnect, onError, onMessage, maxReconnectAttempts, reconnectInterval, clearReconnectTimeout, state.reconnectAttempts]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    clearReconnectTimeout();
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    setState(prev => ({ ...prev, isConnected: false, isConnecting: false, reconnectAttempts: 0 }));
  }, [clearReconnectTimeout]);

  // Reconnect to WebSocket
  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(connect, 1000);
  }, [disconnect, connect]);

  // Send message through WebSocket
  const sendMessage = useCallback((message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify({
        ...message,
        client_id: clientIdRef.current,
        timestamp: new Date().toISOString()
      }));
    } else {
      console.warn('WebSocket is not connected. Cannot send message.');
    }
  }, []);

  // Subscription methods
  const subscribeToRiskEvents = useCallback(() => {
    sendMessage({ type: 'subscribe', subscription: 'risk_events' });
  }, [sendMessage]);

  const subscribeToPortfolioMetrics = useCallback(() => {
    sendMessage({ type: 'subscribe', subscription: 'portfolio_metrics' });
  }, [sendMessage]);

  const subscribeToAlerts = useCallback(() => {
    sendMessage({ type: 'subscribe', subscription: 'alerts' });
  }, [sendMessage]);

  const subscribeToSMAProgression = useCallback(() => {
    sendMessage({ type: 'subscribe', subscription: 'sma_progression' });
  }, [sendMessage]);

  // Auto-connect on mount
  useEffect(() => {
    if (autoConnect) {
      connect();
    }

    // Cleanup on unmount
    return () => {
      clearReconnectTimeout();
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [autoConnect, connect, clearReconnectTimeout]);

  // Heartbeat to keep connection alive
  useEffect(() => {
    if (!state.isConnected) return;

    const heartbeatInterval = setInterval(() => {
      sendMessage({ type: 'ping' });
    }, 30000); // Send ping every 30 seconds

    return () => clearInterval(heartbeatInterval);
  }, [state.isConnected, sendMessage]);

  return {
    // Connection state
    isConnected: state.isConnected,
    isConnecting: state.isConnecting,
    error: state.error,
    reconnectAttempts: state.reconnectAttempts,
    
    // Connection methods
    connect,
    disconnect,
    reconnect,
    
    // Subscription methods
    subscribeToRiskEvents,
    subscribeToPortfolioMetrics,
    subscribeToAlerts,
    subscribeToSMAProgression,
    
    // Message sending
    sendMessage,
    
    // Data streams
    riskEvents,
    portfolioMetrics,
    alerts,
    smaProgressions
  };
}
