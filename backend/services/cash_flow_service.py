import asyncio
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta, date
from dateutil.relativedelta import relativedelta
import statistics
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_absolute_error

from models.cash_flow import (
    CashFlowItem, CashFlowStatement, SeasonalAnalysis, CashFlowForecast,
    WorkingCapitalAnalysis, CashFlowAnalytics, CashFlowRequest, CashFlowResponse,
    CashFlowCategory, CashFlowType, SeasonalPattern
)
from models.account_aggregator import Transaction, TransactionType, TransactionMode
from firebase.init import get_firestore_client

logger = logging.getLogger(__name__)

class CashFlowService:
    """Service for cash flow analysis and forecasting"""
    
    def __init__(self):
        self.db = get_firestore_client()
        
    async def generate_cash_flow_statement(self, msme_id: str, period_start: date, 
                                         period_end: date) -> Optional[CashFlowStatement]:
        """Generate cash flow statement from transaction data"""
        try:
            # Get transaction data from Account Aggregator
            transactions = await self._get_transactions_for_period(msme_id, period_start, period_end)
            
            if not transactions:
                return None
            
            # Classify transactions into cash flow categories
            cash_flow_items = await self._classify_transactions(transactions)
            
            # Generate cash flow statement
            statement = self._build_cash_flow_statement(
                msme_id, period_start, period_end, cash_flow_items
            )
            
            # Store statement
            await self._store_cash_flow_statement(statement)
            
            return statement
            
        except Exception as e:
            logger.error(f"Error generating cash flow statement: {str(e)}")
            return None
    
    async def analyze_seasonal_patterns(self, msme_id: str, analysis_year: int) -> Optional[SeasonalAnalysis]:
        """Analyze seasonal cash flow patterns"""
        try:
            # Get historical cash flow statements
            statements = await self._get_historical_statements(msme_id, analysis_year)
            
            if len(statements) < 6:  # Need at least 6 months of data
                return None
            
            # Analyze patterns
            analysis = self._perform_seasonal_analysis(msme_id, statements, analysis_year)
            
            # Store analysis
            await self._store_seasonal_analysis(analysis)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing seasonal patterns: {str(e)}")
            return None
    
    async def generate_cash_flow_forecast(self, msme_id: str, forecast_horizon: int = 12) -> Optional[CashFlowForecast]:
        """Generate cash flow forecast"""
        try:
            # Get historical data
            historical_statements = await self._get_historical_statements(msme_id)
            seasonal_analysis = await self._get_seasonal_analysis(msme_id)
            
            if len(historical_statements) < 3:  # Need at least 3 months of data
                return None
            
            # Generate forecast
            forecast = self._build_forecast_model(
                msme_id, historical_statements, seasonal_analysis, forecast_horizon
            )
            
            # Store forecast
            await self._store_cash_flow_forecast(forecast)
            
            return forecast
            
        except Exception as e:
            logger.error(f"Error generating cash flow forecast: {str(e)}")
            return None
    
    async def analyze_working_capital(self, msme_id: str, analysis_date: date) -> Optional[WorkingCapitalAnalysis]:
        """Analyze working capital cycle"""
        try:
            # Get recent financial data
            recent_statements = await self._get_recent_statements(msme_id, analysis_date, 3)
            
            if not recent_statements:
                return None
            
            # Calculate working capital metrics
            analysis = self._calculate_working_capital_metrics(msme_id, recent_statements, analysis_date)
            
            # Store analysis
            await self._store_working_capital_analysis(analysis)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing working capital: {str(e)}")
            return None
    
    async def calculate_cash_flow_analytics(self, msme_id: str) -> Optional[CashFlowAnalytics]:
        """Calculate comprehensive cash flow analytics"""
        try:
            # Get all required data
            statements = await self._get_historical_statements(msme_id)
            seasonal_analysis = await self._get_seasonal_analysis(msme_id)
            working_capital = await self._get_working_capital_analysis(msme_id)
            
            if not statements:
                return None
            
            # Calculate analytics
            analytics = self._calculate_comprehensive_analytics(
                msme_id, statements, seasonal_analysis, working_capital
            )
            
            # Store analytics
            await self._store_cash_flow_analytics(analytics)
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error calculating cash flow analytics: {str(e)}")
            return None
    
    # Transaction classification methods
    async def _classify_transactions(self, transactions: List[Transaction]) -> List[CashFlowItem]:
        """Classify transactions into cash flow categories"""
        cash_flow_items = []
        
        for transaction in transactions:
            item = self._classify_single_transaction(transaction)
            if item:
                cash_flow_items.append(item)
        
        return cash_flow_items
    
    def _classify_single_transaction(self, transaction: Transaction) -> Optional[CashFlowItem]:
        """Classify a single transaction"""
        try:
            # Determine category and subcategory based on description and amount
            category, subcategory, confidence = self._determine_cash_flow_category(transaction)
            
            flow_type = CashFlowType.INFLOW if transaction.transaction_type == TransactionType.CREDIT else CashFlowType.OUTFLOW
            
            return CashFlowItem(
                item_id=transaction.transaction_id,
                category=category,
                flow_type=flow_type,
                description=transaction.description,
                amount=abs(transaction.amount),
                date=transaction.transaction_date.date(),
                subcategory=subcategory,
                account_source=transaction.account_id,
                counterparty=transaction.counterparty_name,
                is_recurring=self._detect_recurring_pattern(transaction),
                confidence_score=confidence
            )
            
        except Exception as e:
            logger.error(f"Error classifying transaction: {str(e)}")
            return None
    
    def _determine_cash_flow_category(self, transaction: Transaction) -> tuple[CashFlowCategory, str, float]:
        """Determine cash flow category from transaction details"""
        description = transaction.description.lower()
        amount = abs(transaction.amount)
        
        # Operating activities keywords
        operating_keywords = {
            'revenue': ['payment', 'sale', 'invoice', 'customer', 'revenue'],
            'supplier': ['supplier', 'vendor', 'purchase', 'inventory'],
            'salary': ['salary', 'wage', 'payroll', 'employee'],
            'tax': ['tax', 'gst', 'tds', 'income tax'],
            'interest': ['interest', 'loan interest', 'bank charges'],
            'utilities': ['electricity', 'water', 'telephone', 'internet', 'rent']
        }
        
        # Investing activities keywords
        investing_keywords = {
            'equipment': ['equipment', 'machinery', 'computer', 'vehicle'],
            'investment': ['investment', 'mutual fund', 'shares', 'bonds'],
            'asset_sale': ['asset sale', 'equipment sale']
        }
        
        # Financing activities keywords
        financing_keywords = {
            'loan': ['loan', 'borrowing', 'credit line'],
            'equity': ['equity', 'capital contribution', 'investment'],
            'dividend': ['dividend', 'distribution']
        }
        
        # Check operating activities
        for subcategory, keywords in operating_keywords.items():
            if any(keyword in description for keyword in keywords):
                return CashFlowCategory.OPERATING, subcategory, 85.0
        
        # Check investing activities
        for subcategory, keywords in investing_keywords.items():
            if any(keyword in description for keyword in keywords):
                return CashFlowCategory.INVESTING, subcategory, 80.0
        
        # Check financing activities
        for subcategory, keywords in financing_keywords.items():
            if any(keyword in description for keyword in keywords):
                return CashFlowCategory.FINANCING, subcategory, 80.0
        
        # Default to operating if uncertain
        if transaction.is_business_related:
            return CashFlowCategory.OPERATING, 'other', 60.0
        else:
            return CashFlowCategory.OPERATING, 'other', 40.0
    
    def _detect_recurring_pattern(self, transaction: Transaction) -> bool:
        """Detect if transaction is part of a recurring pattern"""
        # Simplified detection - in practice, would analyze historical patterns
        recurring_keywords = ['salary', 'rent', 'emi', 'subscription', 'insurance']
        description = transaction.description.lower()
        
        return any(keyword in description for keyword in recurring_keywords)
    
    # Cash flow statement building
    def _build_cash_flow_statement(self, msme_id: str, period_start: date, 
                                 period_end: date, cash_flow_items: List[CashFlowItem]) -> CashFlowStatement:
        """Build cash flow statement from classified items"""
        statement_id = f"{msme_id}_{period_start}_{period_end}"
        
        # Initialize statement
        statement = CashFlowStatement(
            statement_id=statement_id,
            msme_id=msme_id,
            period_start=period_start,
            period_end=period_end,
            period_type="monthly"
        )
        
        # Calculate operating cash flows
        operating_items = [item for item in cash_flow_items if item.category == CashFlowCategory.OPERATING]
        statement.operating_cash_inflows = sum(item.amount for item in operating_items if item.flow_type == CashFlowType.INFLOW)
        statement.operating_cash_outflows = sum(item.amount for item in operating_items if item.flow_type == CashFlowType.OUTFLOW)
        statement.net_operating_cash_flow = statement.operating_cash_inflows - statement.operating_cash_outflows
        
        # Calculate operating subcategories
        statement.revenue_collections = sum(
            item.amount for item in operating_items 
            if item.flow_type == CashFlowType.INFLOW and item.subcategory == 'revenue'
        )
        statement.supplier_payments = sum(
            item.amount for item in operating_items 
            if item.flow_type == CashFlowType.OUTFLOW and item.subcategory == 'supplier'
        )
        statement.employee_payments = sum(
            item.amount for item in operating_items 
            if item.flow_type == CashFlowType.OUTFLOW and item.subcategory == 'salary'
        )
        statement.tax_payments = sum(
            item.amount for item in operating_items 
            if item.flow_type == CashFlowType.OUTFLOW and item.subcategory == 'tax'
        )
        
        # Calculate investing cash flows
        investing_items = [item for item in cash_flow_items if item.category == CashFlowCategory.INVESTING]
        statement.investing_cash_inflows = sum(item.amount for item in investing_items if item.flow_type == CashFlowType.INFLOW)
        statement.investing_cash_outflows = sum(item.amount for item in investing_items if item.flow_type == CashFlowType.OUTFLOW)
        statement.net_investing_cash_flow = statement.investing_cash_inflows - statement.investing_cash_outflows
        
        # Calculate financing cash flows
        financing_items = [item for item in cash_flow_items if item.category == CashFlowCategory.FINANCING]
        statement.financing_cash_inflows = sum(item.amount for item in financing_items if item.flow_type == CashFlowType.INFLOW)
        statement.financing_cash_outflows = sum(item.amount for item in financing_items if item.flow_type == CashFlowType.OUTFLOW)
        statement.net_financing_cash_flow = statement.financing_cash_inflows - statement.financing_cash_outflows
        
        # Calculate net change in cash
        statement.net_change_in_cash = (
            statement.net_operating_cash_flow + 
            statement.net_investing_cash_flow + 
            statement.net_financing_cash_flow
        )
        
        # Calculate quality metrics
        total_items = len(cash_flow_items)
        high_confidence_items = len([item for item in cash_flow_items if item.confidence_score >= 80])
        statement.classification_accuracy = (high_confidence_items / total_items * 100) if total_items > 0 else 0
        statement.data_completeness = min(100, (total_items / 50) * 100)  # Assume 50 transactions is 100% complete
        
        return statement
    
    # Seasonal analysis methods
    def _perform_seasonal_analysis(self, msme_id: str, statements: List[CashFlowStatement], 
                                 analysis_year: int) -> SeasonalAnalysis:
        """Perform seasonal pattern analysis"""
        analysis = SeasonalAnalysis(
            msme_id=msme_id,
            analysis_period=str(analysis_year)
        )
        
        # Group statements by month
        monthly_data = {}
        for statement in statements:
            month = statement.period_start.month
            if month not in monthly_data:
                monthly_data[month] = []
            monthly_data[month].append(statement.net_operating_cash_flow)
        
        # Calculate monthly patterns
        for month in range(1, 13):
            if month in monthly_data:
                monthly_flows = monthly_data[month]
                avg_flow = sum(monthly_flows) / len(monthly_flows)
                volatility = statistics.stdev(monthly_flows) if len(monthly_flows) > 1 else 0
                
                analysis.monthly_averages[month] = avg_flow
                analysis.monthly_volatility[month] = volatility
                
                # Determine pattern
                if avg_flow > statistics.mean([sum(flows)/len(flows) for flows in monthly_data.values()]) * 1.2:
                    analysis.monthly_patterns[month] = SeasonalPattern.HIGH
                elif avg_flow < statistics.mean([sum(flows)/len(flows) for flows in monthly_data.values()]) * 0.8:
                    analysis.monthly_patterns[month] = SeasonalPattern.LOW
                else:
                    analysis.monthly_patterns[month] = SeasonalPattern.MEDIUM
        
        # Calculate seasonal metrics
        if analysis.monthly_averages:
            monthly_values = list(analysis.monthly_averages.values())
            mean_value = statistics.mean(monthly_values)
            std_value = statistics.stdev(monthly_values) if len(monthly_values) > 1 else 0
            analysis.seasonal_index = (std_value / mean_value * 100) if mean_value != 0 else 0
            
            # Identify peak and low months
            sorted_months = sorted(analysis.monthly_averages.items(), key=lambda x: x[1], reverse=True)
            analysis.peak_months = [month for month, _ in sorted_months[:3]]
            analysis.low_months = [month for month, _ in sorted_months[-3:]]
            
            # Calculate predictability
            analysis.predictability_score = max(0, 100 - analysis.seasonal_index)
            analysis.volatility_score = min(100, analysis.seasonal_index)
        
        return analysis

    # Comprehensive analytics methods
    def _calculate_comprehensive_analytics(self, msme_id: str, statements: List[CashFlowStatement],
                                         seasonal_analysis: Optional[SeasonalAnalysis],
                                         working_capital: Optional[WorkingCapitalAnalysis]) -> CashFlowAnalytics:
        """Calculate comprehensive cash flow analytics"""
        analytics = CashFlowAnalytics(msme_id=msme_id)

        if not statements:
            return analytics

        # Calculate basic ratios
        latest_statement = statements[-1]

        # Operating cash flow metrics
        if latest_statement.revenue_collections > 0:
            analytics.operating_cash_flow_ratio = latest_statement.net_operating_cash_flow / latest_statement.revenue_collections

        # Cash flow health metrics
        total_debt = latest_statement.financing_cash_outflows  # Simplified
        if total_debt > 0:
            analytics.cash_flow_to_debt_ratio = latest_statement.net_operating_cash_flow / total_debt

        analytics.free_cash_flow = latest_statement.net_operating_cash_flow - latest_statement.investing_cash_outflows

        # Calculate volatility and predictability
        if len(statements) > 1:
            net_flows = [s.net_operating_cash_flow for s in statements]
            analytics.cash_flow_volatility = (statistics.stdev(net_flows) / statistics.mean(net_flows) * 100) if statistics.mean(net_flows) != 0 else 0
            analytics.cash_flow_predictability = max(0, 100 - analytics.cash_flow_volatility)

        # Growth metrics
        if len(statements) >= 2:
            old_flow = statements[0].net_operating_cash_flow
            new_flow = statements[-1].net_operating_cash_flow
            if old_flow != 0:
                analytics.cash_flow_growth_rate = ((new_flow / old_flow) - 1) * 100

        # Working capital metrics
        if working_capital:
            analytics.current_ratio = working_capital.current_assets / working_capital.current_liabilities if working_capital.current_liabilities > 0 else 0
            analytics.cash_ratio = latest_statement.closing_cash / working_capital.current_liabilities if working_capital.current_liabilities > 0 else 0

        # Quality scores
        analytics.cash_flow_quality = self._calculate_cash_flow_quality(statements)
        analytics.liquidity_score = self._calculate_liquidity_score(analytics)
        analytics.efficiency_score = self._calculate_efficiency_score(analytics, working_capital)
        analytics.cash_flow_health_score = self._calculate_overall_health_score(analytics)

        return analytics

    def _calculate_cash_flow_quality(self, statements: List[CashFlowStatement]) -> float:
        """Calculate cash flow quality score"""
        if not statements:
            return 0.0

        quality_factors = []

        # Consistency of positive operating cash flow
        positive_periods = sum(1 for s in statements if s.net_operating_cash_flow > 0)
        consistency_score = (positive_periods / len(statements)) * 100
        quality_factors.append(consistency_score)

        # Data completeness
        avg_completeness = statistics.mean([s.data_completeness for s in statements])
        quality_factors.append(avg_completeness)

        # Classification accuracy
        avg_accuracy = statistics.mean([s.classification_accuracy for s in statements])
        quality_factors.append(avg_accuracy)

        return sum(quality_factors) / len(quality_factors)

    def _calculate_liquidity_score(self, analytics: CashFlowAnalytics) -> float:
        """Calculate liquidity score"""
        liquidity_factors = []

        # Current ratio
        if analytics.current_ratio >= 2.0:
            liquidity_factors.append(100)
        elif analytics.current_ratio >= 1.5:
            liquidity_factors.append(80)
        elif analytics.current_ratio >= 1.0:
            liquidity_factors.append(60)
        else:
            liquidity_factors.append(30)

        # Cash ratio
        if analytics.cash_ratio >= 0.5:
            liquidity_factors.append(90)
        elif analytics.cash_ratio >= 0.2:
            liquidity_factors.append(70)
        else:
            liquidity_factors.append(40)

        # Operating cash flow ratio
        if analytics.operating_cash_flow_ratio >= 0.2:
            liquidity_factors.append(90)
        elif analytics.operating_cash_flow_ratio >= 0.1:
            liquidity_factors.append(70)
        else:
            liquidity_factors.append(40)

        return sum(liquidity_factors) / len(liquidity_factors)

    def _calculate_efficiency_score(self, analytics: CashFlowAnalytics,
                                  working_capital: Optional[WorkingCapitalAnalysis]) -> float:
        """Calculate efficiency score"""
        efficiency_factors = []

        # Cash flow predictability
        efficiency_factors.append(analytics.cash_flow_predictability)

        # Working capital efficiency
        if working_capital:
            if working_capital.cash_conversion_cycle <= 30:
                efficiency_factors.append(90)
            elif working_capital.cash_conversion_cycle <= 60:
                efficiency_factors.append(70)
            else:
                efficiency_factors.append(40)
        else:
            efficiency_factors.append(50)

        # Cash flow growth
        if analytics.cash_flow_growth_rate > 10:
            efficiency_factors.append(90)
        elif analytics.cash_flow_growth_rate > 0:
            efficiency_factors.append(70)
        else:
            efficiency_factors.append(40)

        return sum(efficiency_factors) / len(efficiency_factors)

    def _calculate_overall_health_score(self, analytics: CashFlowAnalytics) -> float:
        """Calculate overall cash flow health score"""
        weights = {
            'quality': 0.3,
            'liquidity': 0.3,
            'efficiency': 0.2,
            'growth': 0.2
        }

        quality_score = analytics.cash_flow_quality
        liquidity_score = analytics.liquidity_score
        efficiency_score = analytics.efficiency_score

        # Growth score
        growth_score = min(100, max(0, 50 + analytics.cash_flow_growth_rate))

        health_score = (
            quality_score * weights['quality'] +
            liquidity_score * weights['liquidity'] +
            efficiency_score * weights['efficiency'] +
            growth_score * weights['growth']
        )

        return round(health_score, 2)

    # Storage and retrieval methods
    async def _store_cash_flow_statement(self, statement: CashFlowStatement):
        """Store cash flow statement in Firestore"""
        try:
            doc_ref = self.db.collection('cash_flow_statements').document(statement.statement_id)
            doc_ref.set(statement.dict())
        except Exception as e:
            logger.error(f"Error storing cash flow statement: {str(e)}")

    async def _store_seasonal_analysis(self, analysis: SeasonalAnalysis):
        """Store seasonal analysis in Firestore"""
        try:
            doc_id = f"{analysis.msme_id}_{analysis.analysis_period}"
            doc_ref = self.db.collection('seasonal_analysis').document(doc_id)
            doc_ref.set(analysis.dict())
        except Exception as e:
            logger.error(f"Error storing seasonal analysis: {str(e)}")

    async def _store_cash_flow_forecast(self, forecast: CashFlowForecast):
        """Store cash flow forecast in Firestore"""
        try:
            doc_ref = self.db.collection('cash_flow_forecasts').document(forecast.forecast_id)
            doc_ref.set(forecast.dict())
        except Exception as e:
            logger.error(f"Error storing cash flow forecast: {str(e)}")

    async def _store_working_capital_analysis(self, analysis: WorkingCapitalAnalysis):
        """Store working capital analysis in Firestore"""
        try:
            doc_id = f"{analysis.msme_id}_{analysis.analysis_date}"
            doc_ref = self.db.collection('working_capital_analysis').document(doc_id)
            doc_ref.set(analysis.dict())
        except Exception as e:
            logger.error(f"Error storing working capital analysis: {str(e)}")

    async def _store_cash_flow_analytics(self, analytics: CashFlowAnalytics):
        """Store cash flow analytics in Firestore"""
        try:
            doc_ref = self.db.collection('cash_flow_analytics').document(analytics.msme_id)
            doc_ref.set(analytics.dict())
        except Exception as e:
            logger.error(f"Error storing cash flow analytics: {str(e)}")

    # Retrieval methods
    async def _get_transactions_for_period(self, msme_id: str, period_start: date, period_end: date) -> List[Transaction]:
        """Get transactions for a specific period"""
        try:
            # This would integrate with Account Aggregator service
            # For now, return mock data
            return []
        except Exception as e:
            logger.error(f"Error retrieving transactions: {str(e)}")
            return []

    async def _get_historical_statements(self, msme_id: str, year: Optional[int] = None) -> List[CashFlowStatement]:
        """Get historical cash flow statements"""
        try:
            query = self.db.collection('cash_flow_statements').where('msme_id', '==', msme_id)
            if year:
                start_date = date(year, 1, 1)
                end_date = date(year, 12, 31)
                query = query.where('period_start', '>=', start_date).where('period_start', '<=', end_date)

            docs = query.stream()
            return [CashFlowStatement(**doc.to_dict()) for doc in docs]
        except Exception as e:
            logger.error(f"Error retrieving historical statements: {str(e)}")
            return []

    async def _get_recent_statements(self, msme_id: str, analysis_date: date, months: int) -> List[CashFlowStatement]:
        """Get recent cash flow statements"""
        try:
            start_date = analysis_date - relativedelta(months=months)
            docs = self.db.collection('cash_flow_statements').where('msme_id', '==', msme_id).where('period_start', '>=', start_date).stream()
            return [CashFlowStatement(**doc.to_dict()) for doc in docs]
        except Exception as e:
            logger.error(f"Error retrieving recent statements: {str(e)}")
            return []

    async def _get_seasonal_analysis(self, msme_id: str) -> Optional[SeasonalAnalysis]:
        """Get seasonal analysis"""
        try:
            # Get the most recent analysis
            docs = self.db.collection('seasonal_analysis').where('msme_id', '==', msme_id).order_by('analysis_period', direction='DESCENDING').limit(1).stream()
            for doc in docs:
                return SeasonalAnalysis(**doc.to_dict())
            return None
        except Exception as e:
            logger.error(f"Error retrieving seasonal analysis: {str(e)}")
            return None

    async def _get_working_capital_analysis(self, msme_id: str) -> Optional[WorkingCapitalAnalysis]:
        """Get working capital analysis"""
        try:
            # Get the most recent analysis
            docs = self.db.collection('working_capital_analysis').where('msme_id', '==', msme_id).order_by('analysis_date', direction='DESCENDING').limit(1).stream()
            for doc in docs:
                return WorkingCapitalAnalysis(**doc.to_dict())
            return None
        except Exception as e:
            logger.error(f"Error retrieving working capital analysis: {str(e)}")
            return None
