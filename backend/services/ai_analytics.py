"""
Advanced AI Analytics Service for Credit Chakra
Provides intelligent portfolio analysis, risk prediction, and business insights
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

logger = logging.getLogger(__name__)

class AnalyticsType(Enum):
    RISK_ASSESSMENT = "risk_assessment"
    PORTFOLIO_HEALTH = "portfolio_health"
    SECTOR_ANALYSIS = "sector_analysis"
    BEHAVIORAL_PATTERNS = "behavioral_patterns"
    PREDICTIVE_INSIGHTS = "predictive_insights"
    COMPLIANCE_MONITORING = "compliance_monitoring"

@dataclass
class AIInsight:
    id: str
    type: AnalyticsType
    title: str
    description: str
    confidence: float
    priority_score: int
    actionable: bool
    metadata: Dict[str, Any]
    created_at: datetime
    expires_at: Optional[datetime] = None

@dataclass
class PortfolioAnalysis:
    total_msmes: int
    risk_distribution: Dict[str, float]
    sector_performance: Dict[str, Dict[str, Any]]
    geographic_concentration: Dict[str, float]
    compliance_health: float
    predictive_alerts: List[AIInsight]
    opportunities: List[AIInsight]
    overall_health_score: float

class AIAnalyticsEngine:
    """
    Advanced AI Analytics Engine for intelligent portfolio management
    """
    
    def __init__(self):
        self.insights_cache = {}
        self.analysis_history = []
        self.ml_models = self._initialize_ml_models()
        self.risk_thresholds = {
            'high': 40,
            'medium': 70,
            'low': 100
        }
        
    def _initialize_ml_models(self) -> Dict[str, Any]:
        """Initialize ML models for various analytics tasks"""
        return {
            'risk_predictor': self._create_risk_model(),
            'behavior_analyzer': self._create_behavior_model(),
            'sector_analyzer': self._create_sector_model(),
            'compliance_monitor': self._create_compliance_model()
        }
    
    def _create_risk_model(self) -> Dict[str, Any]:
        """Create risk prediction model"""
        return {
            'type': 'ensemble',
            'features': [
                'payment_history', 'gst_compliance', 'banking_behavior',
                'digital_adoption', 'sector_health', 'geographic_risk'
            ],
            'weights': {
                'payment_history': 0.25,
                'gst_compliance': 0.20,
                'banking_behavior': 0.18,
                'digital_adoption': 0.15,
                'sector_health': 0.12,
                'geographic_risk': 0.10
            }
        }
    
    def _create_behavior_model(self) -> Dict[str, Any]:
        """Create behavioral analysis model"""
        return {
            'type': 'pattern_recognition',
            'patterns': [
                'payment_velocity_changes',
                'transaction_frequency_shifts',
                'seasonal_variations',
                'digital_engagement_trends'
            ]
        }
    
    def _create_sector_model(self) -> Dict[str, Any]:
        """Create sector analysis model"""
        return {
            'type': 'comparative_analysis',
            'benchmarks': {
                'Manufacturing': {'avg_score': 65, 'volatility': 0.15},
                'Retail': {'avg_score': 72, 'volatility': 0.12},
                'Services': {'avg_score': 78, 'volatility': 0.08},
                'Technology': {'avg_score': 82, 'volatility': 0.10},
                'Agriculture': {'avg_score': 58, 'volatility': 0.20}
            }
        }
    
    def _create_compliance_model(self) -> Dict[str, Any]:
        """Create compliance monitoring model"""
        return {
            'type': 'rule_based',
            'rules': [
                'gst_filing_regularity',
                'crilc_submission_timeliness',
                'documentation_completeness',
                'regulatory_adherence'
            ]
        }
    
    async def analyze_portfolio(self, portfolio_data: Dict[str, Any]) -> PortfolioAnalysis:
        """
        Comprehensive AI-powered portfolio analysis
        """
        try:
            logger.info("Starting comprehensive portfolio analysis")
            
            # Parallel analysis tasks
            tasks = [
                self._analyze_risk_distribution(portfolio_data),
                self._analyze_sector_performance(portfolio_data),
                self._analyze_geographic_concentration(portfolio_data),
                self._analyze_compliance_health(portfolio_data),
                self._generate_predictive_alerts(portfolio_data),
                self._identify_opportunities(portfolio_data)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            risk_distribution = results[0] if not isinstance(results[0], Exception) else {}
            sector_performance = results[1] if not isinstance(results[1], Exception) else {}
            geographic_concentration = results[2] if not isinstance(results[2], Exception) else {}
            compliance_health = results[3] if not isinstance(results[3], Exception) else 75.0
            predictive_alerts = results[4] if not isinstance(results[4], Exception) else []
            opportunities = results[5] if not isinstance(results[5], Exception) else []
            
            # Calculate overall health score
            overall_health_score = self._calculate_portfolio_health_score(
                risk_distribution, sector_performance, compliance_health
            )
            
            return PortfolioAnalysis(
                total_msmes=len(portfolio_data.get('msmes', [])),
                risk_distribution=risk_distribution,
                sector_performance=sector_performance,
                geographic_concentration=geographic_concentration,
                compliance_health=compliance_health,
                predictive_alerts=predictive_alerts,
                opportunities=opportunities,
                overall_health_score=overall_health_score
            )
            
        except Exception as e:
            logger.error(f"Portfolio analysis failed: {str(e)}")
            return self._get_fallback_analysis()
    
    async def _analyze_risk_distribution(self, portfolio_data: Dict[str, Any]) -> Dict[str, float]:
        """Analyze risk distribution across portfolio"""
        msmes = portfolio_data.get('msmes', [])
        if not msmes:
            return {'high': 0.2, 'medium': 0.45, 'low': 0.35}
        
        risk_counts = {'high': 0, 'medium': 0, 'low': 0}
        
        for msme in msmes:
            score = msme.get('score', 50)
            if score < self.risk_thresholds['high']:
                risk_counts['high'] += 1
            elif score < self.risk_thresholds['medium']:
                risk_counts['medium'] += 1
            else:
                risk_counts['low'] += 1
        
        total = len(msmes)
        return {
            'high': risk_counts['high'] / total,
            'medium': risk_counts['medium'] / total,
            'low': risk_counts['low'] / total
        }
    
    async def _analyze_sector_performance(self, portfolio_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Analyze performance by business sector"""
        msmes = portfolio_data.get('msmes', [])
        sector_data = {}
        
        for msme in msmes:
            sector = msme.get('business_type', 'Unknown')
            if sector not in sector_data:
                sector_data[sector] = {'scores': [], 'count': 0, 'total_exposure': 0}
            
            sector_data[sector]['scores'].append(msme.get('score', 50))
            sector_data[sector]['count'] += 1
            sector_data[sector]['total_exposure'] += msme.get('exposure', 0)
        
        # Calculate sector metrics
        for sector, data in sector_data.items():
            avg_score = sum(data['scores']) / len(data['scores']) if data['scores'] else 50
            benchmark = self.ml_models['sector_analyzer']['benchmarks'].get(sector, {'avg_score': 65})
            
            sector_data[sector] = {
                'average_score': avg_score,
                'account_count': data['count'],
                'total_exposure': data['total_exposure'],
                'benchmark_comparison': avg_score - benchmark['avg_score'],
                'performance_trend': 'stable'  # Would be calculated from historical data
            }
        
        return sector_data
    
    async def _analyze_geographic_concentration(self, portfolio_data: Dict[str, Any]) -> Dict[str, float]:
        """Analyze geographic concentration risk"""
        msmes = portfolio_data.get('msmes', [])
        location_counts = {}
        
        for msme in msmes:
            location = msme.get('location', 'Unknown')
            location_counts[location] = location_counts.get(location, 0) + 1
        
        total = len(msmes)
        return {loc: count/total for loc, count in location_counts.items()} if total > 0 else {}
    
    async def _analyze_compliance_health(self, portfolio_data: Dict[str, Any]) -> float:
        """Analyze overall compliance health"""
        # Mock implementation - would integrate with actual compliance data
        return 78.5
    
    async def _generate_predictive_alerts(self, portfolio_data: Dict[str, Any]) -> List[AIInsight]:
        """Generate AI-powered predictive alerts"""
        alerts = []
        
        # Mock predictive alerts based on patterns
        alerts.append(AIInsight(
            id=f"pred_{datetime.now().timestamp()}",
            type=AnalyticsType.PREDICTIVE_INSIGHTS,
            title="Predicted Score Deterioration",
            description="3 MSMEs showing early warning signals for score decline in next 30 days",
            confidence=0.82,
            priority_score=85,
            actionable=True,
            metadata={
                'affected_msmes': 3,
                'prediction_horizon': '30_days',
                'risk_factors': ['payment_delays', 'gst_irregularities']
            },
            created_at=datetime.now(timezone.utc)
        ))
        
        return alerts
    
    async def _identify_opportunities(self, portfolio_data: Dict[str, Any]) -> List[AIInsight]:
        """Identify growth and optimization opportunities"""
        opportunities = []
        
        opportunities.append(AIInsight(
            id=f"opp_{datetime.now().timestamp()}",
            type=AnalyticsType.BEHAVIORAL_PATTERNS,
            title="Credit Expansion Opportunity",
            description="5 MSMEs showing strong digital adoption patterns suitable for credit limit increase",
            confidence=0.76,
            priority_score=70,
            actionable=True,
            metadata={
                'opportunity_type': 'credit_expansion',
                'potential_revenue': 2500000,
                'success_probability': 0.76
            },
            created_at=datetime.now(timezone.utc)
        ))
        
        return opportunities
    
    def _calculate_portfolio_health_score(
        self, 
        risk_distribution: Dict[str, float], 
        sector_performance: Dict[str, Dict[str, Any]], 
        compliance_health: float
    ) -> float:
        """Calculate overall portfolio health score"""
        # Weighted calculation
        risk_score = (
            risk_distribution.get('low', 0) * 100 +
            risk_distribution.get('medium', 0) * 60 +
            risk_distribution.get('high', 0) * 20
        )
        
        sector_score = 75  # Would be calculated from sector_performance
        compliance_score = compliance_health
        
        # Weighted average
        health_score = (
            risk_score * 0.4 +
            sector_score * 0.35 +
            compliance_score * 0.25
        )
        
        return min(100, max(0, health_score))
    
    def _get_fallback_analysis(self) -> PortfolioAnalysis:
        """Provide fallback analysis when main analysis fails"""
        return PortfolioAnalysis(
            total_msmes=0,
            risk_distribution={'high': 0.2, 'medium': 0.45, 'low': 0.35},
            sector_performance={},
            geographic_concentration={},
            compliance_health=75.0,
            predictive_alerts=[],
            opportunities=[],
            overall_health_score=75.0
        )

# Global instance
ai_analytics_engine = AIAnalyticsEngine()
