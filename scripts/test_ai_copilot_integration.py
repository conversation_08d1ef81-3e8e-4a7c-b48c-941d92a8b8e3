#!/usr/bin/env python3
"""
AI Copilot Integration Test Script
Comprehensive testing of all AI copilot enhancements and integrations
"""

import asyncio
import sys
import os
import time
import json
from datetime import datetime
from typing import Dict, List, Any

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

# Import all the AI services
from services.ai_analytics import ai_analytics_engine
from services.ai_nudge_engine import ai_nudge_engine
from services.ai_business_analyzer import ai_business_analyzer
from services.ai_performance_optimizer import ai_performance_optimizer, CacheType
from services.llm_integration import LLMIntegrationService

class AICopilotIntegrationTester:
    """Comprehensive integration tester for AI Copilot enhancements"""
    
    def __init__(self):
        self.test_results = {}
        self.performance_metrics = {}
        self.llm_service = LLMIntegrationService()
        
    async def run_all_tests(self):
        """Run all integration tests"""
        print("🚀 Starting AI Copilot Integration Tests")
        print("=" * 60)
        
        # Test data setup
        await self.setup_test_data()
        
        # Core functionality tests
        await self.test_ai_analytics_engine()
        await self.test_ai_nudge_engine()
        await self.test_ai_business_analyzer()
        await self.test_performance_optimizer()
        await self.test_llm_integration()
        
        # Integration tests
        await self.test_end_to_end_workflow()
        await self.test_performance_benchmarks()
        await self.test_error_handling()
        
        # Generate report
        self.generate_test_report()
        
    async def setup_test_data(self):
        """Setup test data for integration tests"""
        print("\n📊 Setting up test data...")
        
        self.test_portfolio = {
            'msmes': [
                {
                    'id': 'test_manufacturing_1',
                    'business_name': 'Advanced Manufacturing Co',
                    'business_type': 'Manufacturing',
                    'score': 78,
                    'location': 'Mumbai',
                    'exposure': 5000000
                },
                {
                    'id': 'test_retail_1',
                    'business_name': 'Digital Retail Store',
                    'business_type': 'Retail',
                    'score': 45,
                    'location': 'Delhi',
                    'exposure': 2000000
                },
                {
                    'id': 'test_tech_1',
                    'business_name': 'Tech Solutions Pvt Ltd',
                    'business_type': 'Technology',
                    'score': 85,
                    'location': 'Bangalore',
                    'exposure': 8000000
                },
                {
                    'id': 'test_services_1',
                    'business_name': 'Professional Services Inc',
                    'business_type': 'Services',
                    'score': 62,
                    'location': 'Pune',
                    'exposure': 3000000
                }
            ]
        }
        
        print(f"✅ Test portfolio created with {len(self.test_portfolio['msmes'])} MSMEs")
        
    async def test_ai_analytics_engine(self):
        """Test AI Analytics Engine functionality"""
        print("\n🧠 Testing AI Analytics Engine...")
        
        start_time = time.time()
        
        try:
            # Test portfolio analysis
            analysis = await ai_analytics_engine.analyze_portfolio(self.test_portfolio)
            
            # Validate results
            assert analysis.total_msmes == 4, f"Expected 4 MSMEs, got {analysis.total_msmes}"
            assert 0 <= analysis.overall_health_score <= 100, f"Invalid health score: {analysis.overall_health_score}"
            assert len(analysis.risk_distribution) == 3, "Risk distribution should have 3 categories"
            assert len(analysis.sector_performance) > 0, "Sector performance should not be empty"
            
            processing_time = time.time() - start_time
            
            self.test_results['ai_analytics'] = {
                'status': 'PASSED',
                'processing_time': processing_time,
                'total_msmes': analysis.total_msmes,
                'health_score': analysis.overall_health_score,
                'sectors_analyzed': len(analysis.sector_performance)
            }
            
            print(f"✅ AI Analytics Engine test passed ({processing_time:.2f}s)")
            print(f"   - Portfolio health score: {analysis.overall_health_score:.1f}")
            print(f"   - Sectors analyzed: {len(analysis.sector_performance)}")
            
        except Exception as e:
            self.test_results['ai_analytics'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ AI Analytics Engine test failed: {str(e)}")
    
    async def test_ai_nudge_engine(self):
        """Test AI Nudge Engine functionality"""
        print("\n💬 Testing AI Nudge Engine...")
        
        start_time = time.time()
        
        try:
            # Test nudge generation for low-score MSME
            low_score_msme = self.test_portfolio['msmes'][1]  # Retail with score 45
            nudges = await ai_nudge_engine.generate_intelligent_nudges(low_score_msme)
            
            # Validate results
            assert isinstance(nudges, list), "Nudges should be a list"
            assert len(nudges) <= 5, f"Should return max 5 nudges, got {len(nudges)}"
            
            if nudges:
                nudge = nudges[0]
                assert hasattr(nudge, 'confidence_score'), "Nudge should have confidence score"
                assert 0 <= nudge.confidence_score <= 1, f"Invalid confidence: {nudge.confidence_score}"
                assert hasattr(nudge, 'message'), "Nudge should have message"
                assert len(nudge.message) > 0, "Nudge message should not be empty"
            
            processing_time = time.time() - start_time
            
            self.test_results['ai_nudge_engine'] = {
                'status': 'PASSED',
                'processing_time': processing_time,
                'nudges_generated': len(nudges),
                'avg_confidence': sum(n.confidence_score for n in nudges) / len(nudges) if nudges else 0
            }
            
            print(f"✅ AI Nudge Engine test passed ({processing_time:.2f}s)")
            print(f"   - Nudges generated: {len(nudges)}")
            if nudges:
                print(f"   - Average confidence: {sum(n.confidence_score for n in nudges) / len(nudges):.2f}")
            
        except Exception as e:
            self.test_results['ai_nudge_engine'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ AI Nudge Engine test failed: {str(e)}")
    
    async def test_ai_business_analyzer(self):
        """Test AI Business Analyzer functionality"""
        print("\n📈 Testing AI Business Analyzer...")
        
        start_time = time.time()
        
        try:
            # Test business analysis for high-score MSME
            high_score_msme = self.test_portfolio['msmes'][2]  # Tech with score 85
            profile = await ai_business_analyzer.analyze_business_profile(high_score_msme)
            
            # Validate results
            assert profile.msme_id == high_score_msme['id'], "MSME ID mismatch"
            assert 0 <= profile.overall_health_score <= 100, f"Invalid health score: {profile.overall_health_score}"
            assert len(profile.financial_strength) > 0, "Financial strength should not be empty"
            assert len(profile.digital_maturity) > 0, "Digital maturity should not be empty"
            assert len(profile.competitive_advantages) >= 0, "Competitive advantages should be a list"
            
            processing_time = time.time() - start_time
            
            self.test_results['ai_business_analyzer'] = {
                'status': 'PASSED',
                'processing_time': processing_time,
                'health_score': profile.overall_health_score,
                'health_category': profile.health_category.value,
                'insights_generated': len(profile.ai_insights)
            }
            
            print(f"✅ AI Business Analyzer test passed ({processing_time:.2f}s)")
            print(f"   - Health score: {profile.overall_health_score:.1f}")
            print(f"   - Health category: {profile.health_category.value}")
            print(f"   - AI insights: {len(profile.ai_insights)}")
            
        except Exception as e:
            self.test_results['ai_business_analyzer'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ AI Business Analyzer test failed: {str(e)}")
    
    async def test_performance_optimizer(self):
        """Test AI Performance Optimizer functionality"""
        print("\n⚡ Testing AI Performance Optimizer...")
        
        start_time = time.time()
        
        try:
            # Test caching functionality
            async def mock_ai_function(data):
                await asyncio.sleep(0.1)  # Simulate processing
                return {"result": "cached_test", "data": data}
            
            cache_key = "integration_test_cache"
            
            # First call (cache miss)
            result1, metadata1 = await ai_performance_optimizer.optimize_ai_request(
                cache_key, CacheType.INSIGHTS, mock_ai_function, {"test": "data"}
            )
            
            # Second call (cache hit)
            result2, metadata2 = await ai_performance_optimizer.optimize_ai_request(
                cache_key, CacheType.INSIGHTS, mock_ai_function, {"test": "data"}
            )
            
            # Validate caching
            assert metadata1['cache_hit'] == False, "First call should be cache miss"
            assert metadata2['cache_hit'] == True, "Second call should be cache hit"
            assert metadata2['response_time_ms'] < metadata1['response_time_ms'], "Cache should improve performance"
            
            # Test batch processing
            large_msme_list = [
                {'id': f'batch_test_{i}', 'business_type': 'Manufacturing', 'score': 70}
                for i in range(150)
            ]
            
            batch_result = await ai_performance_optimizer.batch_optimize_portfolio_analysis(large_msme_list)
            assert batch_result['total_msmes'] == 150, "Batch processing count mismatch"
            
            processing_time = time.time() - start_time
            
            # Get performance summary
            perf_summary = ai_performance_optimizer.get_performance_summary()
            
            self.test_results['performance_optimizer'] = {
                'status': 'PASSED',
                'processing_time': processing_time,
                'cache_hit_rate': perf_summary['cache_hit_rate_percent'],
                'avg_response_time': perf_summary['avg_response_time_ms'],
                'batch_processing': True
            }
            
            print(f"✅ Performance Optimizer test passed ({processing_time:.2f}s)")
            print(f"   - Cache hit rate: {perf_summary['cache_hit_rate_percent']:.1f}%")
            print(f"   - Avg response time: {perf_summary['avg_response_time_ms']:.1f}ms")
            
        except Exception as e:
            self.test_results['performance_optimizer'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ Performance Optimizer test failed: {str(e)}")
    
    async def test_llm_integration(self):
        """Test enhanced LLM integration"""
        print("\n🤖 Testing Enhanced LLM Integration...")
        
        start_time = time.time()
        
        try:
            # Test domain knowledge enhancement
            query = "What is NPA and how does it affect MSME credit scoring?"
            context = {"user_id": "test_user", "user_intent": "analysis"}
            
            enhanced_context = self.llm_service._enhance_context_with_domain_knowledge(query, context)
            
            # Validate domain knowledge enhancement
            assert 'relevant_credit_terms' in enhanced_context, "Domain knowledge should be added"
            assert 'npa' in enhanced_context['relevant_credit_terms'], "NPA term should be included"
            
            # Test conversation memory
            self.llm_service._update_conversation_memory("test_user", query, context)
            assert "test_user" in self.llm_service.conversation_memory, "Conversation memory should be updated"
            
            # Test enhanced mock response
            response = await self.llm_service._generate_enhanced_mock_response(query, enhanced_context)
            
            assert hasattr(response, 'content'), "Response should have content"
            assert hasattr(response, 'confidence'), "Response should have confidence"
            assert response.confidence > 0, "Confidence should be positive"
            
            processing_time = time.time() - start_time
            
            self.test_results['llm_integration'] = {
                'status': 'PASSED',
                'processing_time': processing_time,
                'domain_knowledge_enhanced': True,
                'conversation_memory_active': True,
                'response_confidence': response.confidence
            }
            
            print(f"✅ LLM Integration test passed ({processing_time:.2f}s)")
            print(f"   - Domain knowledge enhanced: ✓")
            print(f"   - Conversation memory active: ✓")
            print(f"   - Response confidence: {response.confidence:.2f}")
            
        except Exception as e:
            self.test_results['llm_integration'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ LLM Integration test failed: {str(e)}")
    
    async def test_end_to_end_workflow(self):
        """Test complete end-to-end AI workflow"""
        print("\n🔄 Testing End-to-End AI Workflow...")
        
        start_time = time.time()
        
        try:
            # Step 1: Portfolio analysis
            portfolio_analysis = await ai_analytics_engine.analyze_portfolio(self.test_portfolio)
            
            # Step 2: Business profile analysis for each MSME
            business_profiles = []
            for msme in self.test_portfolio['msmes']:
                profile = await ai_business_analyzer.analyze_business_profile(msme)
                business_profiles.append(profile)
            
            # Step 3: Generate nudges for MSMEs that need attention
            nudges_generated = []
            for msme in self.test_portfolio['msmes']:
                if msme['score'] < 60:  # Focus on lower-score MSMEs
                    nudges = await ai_nudge_engine.generate_intelligent_nudges(msme)
                    nudges_generated.extend(nudges)
            
            # Step 4: Performance optimization validation
            perf_summary = ai_performance_optimizer.get_performance_summary()
            
            processing_time = time.time() - start_time
            
            # Validate end-to-end workflow
            assert portfolio_analysis.total_msmes == 4, "Portfolio analysis should cover all MSMEs"
            assert len(business_profiles) == 4, "Should have business profiles for all MSMEs"
            assert processing_time < 10.0, f"End-to-end workflow too slow: {processing_time:.2f}s"
            
            self.test_results['end_to_end_workflow'] = {
                'status': 'PASSED',
                'processing_time': processing_time,
                'portfolio_analyzed': True,
                'business_profiles_generated': len(business_profiles),
                'nudges_generated': len(nudges_generated),
                'performance_optimized': True
            }
            
            print(f"✅ End-to-End Workflow test passed ({processing_time:.2f}s)")
            print(f"   - Portfolio analyzed: ✓")
            print(f"   - Business profiles: {len(business_profiles)}")
            print(f"   - Nudges generated: {len(nudges_generated)}")
            
        except Exception as e:
            self.test_results['end_to_end_workflow'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ End-to-End Workflow test failed: {str(e)}")
    
    async def test_performance_benchmarks(self):
        """Test performance benchmarks against SLA requirements"""
        print("\n📊 Testing Performance Benchmarks...")
        
        try:
            # Test 2-second SLA for portfolio analysis
            large_portfolio = {
                'msmes': [
                    {
                        'id': f'perf_test_{i}',
                        'business_name': f'Performance Test {i}',
                        'business_type': 'Manufacturing',
                        'score': 50 + (i % 50),
                        'location': 'Mumbai'
                    }
                    for i in range(200)  # 200 MSMEs
                ]
            }
            
            start_time = time.time()
            analysis = await ai_analytics_engine.analyze_portfolio(large_portfolio)
            processing_time = time.time() - start_time
            
            sla_met = processing_time < 2.0
            
            self.performance_metrics = {
                'portfolio_analysis_time': processing_time,
                'sla_requirement': 2.0,
                'sla_met': sla_met,
                'msmes_processed': analysis.total_msmes,
                'throughput_msmes_per_second': analysis.total_msmes / processing_time
            }
            
            self.test_results['performance_benchmarks'] = {
                'status': 'PASSED' if sla_met else 'WARNING',
                'processing_time': processing_time,
                'sla_met': sla_met,
                'throughput': analysis.total_msmes / processing_time
            }
            
            status_icon = "✅" if sla_met else "⚠️"
            print(f"{status_icon} Performance Benchmark test completed")
            print(f"   - Processing time: {processing_time:.2f}s (SLA: 2.0s)")
            print(f"   - MSMEs processed: {analysis.total_msmes}")
            print(f"   - Throughput: {analysis.total_msmes / processing_time:.1f} MSMEs/sec")
            
        except Exception as e:
            self.test_results['performance_benchmarks'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ Performance Benchmark test failed: {str(e)}")
    
    async def test_error_handling(self):
        """Test error handling and fallback mechanisms"""
        print("\n🛡️ Testing Error Handling...")
        
        try:
            # Test fallback mechanisms
            fallback_analysis = ai_analytics_engine._get_fallback_analysis()
            fallback_profile = ai_business_analyzer._get_fallback_profile({'id': 'test_fallback'})
            
            # Validate fallbacks
            assert fallback_analysis.total_msmes == 0, "Fallback analysis should have 0 MSMEs"
            assert fallback_profile.msme_id == 'test_fallback', "Fallback profile ID mismatch"
            
            self.test_results['error_handling'] = {
                'status': 'PASSED',
                'fallback_mechanisms': True,
                'graceful_degradation': True
            }
            
            print("✅ Error Handling test passed")
            print("   - Fallback mechanisms: ✓")
            print("   - Graceful degradation: ✓")
            
        except Exception as e:
            self.test_results['error_handling'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ Error Handling test failed: {str(e)}")
    
    def generate_test_report(self):
        """Generate comprehensive test report"""
        print("\n" + "=" * 60)
        print("📋 AI COPILOT INTEGRATION TEST REPORT")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASSED')
        failed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'FAILED')
        warning_tests = sum(1 for result in self.test_results.values() if result['status'] == 'WARNING')
        
        print(f"\n📊 Test Summary:")
        print(f"   Total Tests: {total_tests}")
        print(f"   Passed: {passed_tests} ✅")
        print(f"   Failed: {failed_tests} ❌")
        print(f"   Warnings: {warning_tests} ⚠️")
        print(f"   Success Rate: {(passed_tests / total_tests) * 100:.1f}%")
        
        print(f"\n📈 Performance Metrics:")
        if self.performance_metrics:
            print(f"   Portfolio Analysis Time: {self.performance_metrics['processing_time']:.2f}s")
            print(f"   SLA Met: {'✅' if self.performance_metrics['sla_met'] else '❌'}")
            print(f"   Throughput: {self.performance_metrics['throughput_msmes_per_second']:.1f} MSMEs/sec")
        
        print(f"\n🔍 Detailed Results:")
        for test_name, result in self.test_results.items():
            status_icon = {"PASSED": "✅", "FAILED": "❌", "WARNING": "⚠️"}.get(result['status'], "❓")
            print(f"   {test_name}: {status_icon} {result['status']}")
            if 'processing_time' in result:
                print(f"      Processing Time: {result['processing_time']:.2f}s")
            if 'error' in result:
                print(f"      Error: {result['error']}")
        
        # Save detailed report to file
        report_data = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_tests': total_tests,
                'passed': passed_tests,
                'failed': failed_tests,
                'warnings': warning_tests,
                'success_rate': (passed_tests / total_tests) * 100
            },
            'performance_metrics': self.performance_metrics,
            'detailed_results': self.test_results
        }
        
        with open('ai_copilot_test_report.json', 'w') as f:
            json.dump(report_data, f, indent=2, default=str)
        
        print(f"\n💾 Detailed report saved to: ai_copilot_test_report.json")
        
        if failed_tests == 0:
            print(f"\n🎉 All tests passed! AI Copilot is ready for production.")
        else:
            print(f"\n⚠️ {failed_tests} test(s) failed. Please review and fix issues before deployment.")

async def main():
    """Main test execution function"""
    tester = AICopilotIntegrationTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
