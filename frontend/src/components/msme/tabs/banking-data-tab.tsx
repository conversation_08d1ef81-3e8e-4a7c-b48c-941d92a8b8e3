'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Building2,
  CreditCard,
  TrendingUp,
  TrendingDown,
  AlertCircle,
  CheckCircle,
  Smartphone,
  RefreshCw,
  Calendar,
  BarChart3,
  Zap,
  Shield,
  Activity,
  Wallet
} from 'lucide-react';

interface BankingData {
  consent_id: string;
  accounts: any[];
  analytics: {
    total_accounts: number;
    active_accounts: number;
    total_balance: number;
    avg_monthly_balance: number;
    balance_volatility: number;
    total_transactions: number;
    avg_monthly_transactions: number;
    digital_transaction_ratio: number;
    upi_adoption_rate: number;
    digital_payment_growth: number;
    banking_score: number;
    stability_score: number;
    digital_maturity_score: number;
    cash_intensive_flag: boolean;
    irregular_transaction_flag: boolean;
    dormant_account_flag: boolean;
    balance_trend: string;
    transaction_trend: string;
    digital_trend: string;
  };
  last_updated: string;
}

interface BankingDataTabProps {
  msmeId: string;
}

export default function BankingDataTab({ msmeId }: BankingDataTabProps) {
  const [bankingData, setBankingData] = useState<BankingData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchBankingData();
  }, [msmeId]);

  const fetchBankingData = async () => {
    try {
      setLoading(true);
      // Mock data - in production, this would call the actual API
      const mockData: BankingData = {
        consent_id: "consent_123456",
        accounts: [],
        analytics: {
          total_accounts: 3,
          active_accounts: 3,
          total_balance: 1250000,
          avg_monthly_balance: 980000,
          balance_volatility: 18.5,
          total_transactions: 1247,
          avg_monthly_transactions: 104,
          digital_transaction_ratio: 78.5,
          upi_adoption_rate: 65.2,
          digital_payment_growth: 23.4,
          banking_score: 82.1,
          stability_score: 76.8,
          digital_maturity_score: 71.3,
          cash_intensive_flag: false,
          irregular_transaction_flag: false,
          dormant_account_flag: false,
          balance_trend: "stable",
          transaction_trend: "increasing",
          digital_trend: "improving"
        },
        last_updated: new Date().toISOString()
      };
      
      setBankingData(mockData);
    } catch (error) {
      console.error('Error fetching banking data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchBankingData();
    setRefreshing(false);
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing':
      case 'improving':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'decreasing':
      case 'declining':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <BarChart3 className="h-4 w-4 text-blue-500" />;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadge = (score: number) => {
    if (score >= 80) return <Badge variant="default" className="bg-green-100 text-green-800">Excellent</Badge>;
    if (score >= 60) return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Good</Badge>;
    return <Badge variant="destructive">Needs Improvement</Badge>;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!bankingData) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Banking Data Available</h3>
        <p className="text-gray-500 mb-4">Account Aggregator consent is not set up for this MSME.</p>
        <Button onClick={fetchBankingData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Refresh */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Banking Data Analysis</h2>
          <p className="text-muted-foreground">Account Aggregator Integration</p>
        </div>
        <Button 
          variant="outline" 
          onClick={handleRefresh} 
          disabled={refreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <Wallet className="h-5 w-5" />
              Total Balance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 mb-2">
              ₹{(bankingData.analytics.total_balance / 100000).toFixed(1)}L
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getTrendIcon(bankingData.analytics.balance_trend)}
              <span className="text-blue-600">{bankingData.analytics.total_accounts} accounts</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700">
              <Smartphone className="h-5 w-5" />
              Digital Adoption
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 mb-2">
              {bankingData.analytics.digital_transaction_ratio.toFixed(1)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getTrendIcon(bankingData.analytics.digital_trend)}
              <span className="text-green-600">UPI: {bankingData.analytics.upi_adoption_rate.toFixed(1)}%</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <Activity className="h-5 w-5" />
              Transaction Volume
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 mb-2">
              {bankingData.analytics.avg_monthly_transactions}
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getTrendIcon(bankingData.analytics.transaction_trend)}
              <span className="text-purple-600">per month</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <Shield className="h-5 w-5" />
              Banking Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 mb-2">
              {bankingData.analytics.banking_score.toFixed(0)}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Progress value={bankingData.analytics.banking_score} className="h-2 flex-1" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="digital">Digital Adoption</TabsTrigger>
          <TabsTrigger value="stability">Stability</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Account Summary
                </CardTitle>
                <CardDescription>Banking relationship overview</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600 mb-1">
                      {bankingData.analytics.total_accounts}
                    </div>
                    <div className="text-sm text-blue-600">Total Accounts</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600 mb-1">
                      {bankingData.analytics.active_accounts}
                    </div>
                    <div className="text-sm text-green-600">Active Accounts</div>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Average Monthly Balance</span>
                    <span className="font-medium">₹{(bankingData.analytics.avg_monthly_balance / 100000).toFixed(1)}L</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Balance Volatility</span>
                    <span className="font-medium">{bankingData.analytics.balance_volatility.toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Balance Trend</span>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(bankingData.analytics.balance_trend)}
                      <span className="font-medium capitalize">{bankingData.analytics.balance_trend}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Transaction Overview
                </CardTitle>
                <CardDescription>Transaction behavior analysis</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600 mb-1">
                      {bankingData.analytics.total_transactions}
                    </div>
                    <div className="text-sm text-purple-600">Total Transactions</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600 mb-1">
                      {bankingData.analytics.avg_monthly_transactions}
                    </div>
                    <div className="text-sm text-orange-600">Monthly Average</div>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Digital Transaction Ratio</span>
                    <span className="font-medium">{bankingData.analytics.digital_transaction_ratio.toFixed(1)}%</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Transaction Trend</span>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(bankingData.analytics.transaction_trend)}
                      <span className="font-medium capitalize">{bankingData.analytics.transaction_trend}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="digital" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Smartphone className="h-5 w-5" />
                Digital Payment Adoption
              </CardTitle>
              <CardDescription>Digital transformation metrics</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-3">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    {bankingData.analytics.digital_transaction_ratio.toFixed(1)}%
                  </div>
                  <div className="text-sm text-green-600">Digital Transactions</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600 mb-1">
                    {bankingData.analytics.upi_adoption_rate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-blue-600">UPI Adoption</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600 mb-1">
                    +{bankingData.analytics.digital_payment_growth.toFixed(1)}%
                  </div>
                  <div className="text-sm text-purple-600">Growth Rate</div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h4 className="font-medium">Digital Maturity Assessment</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Digital Maturity Score</span>
                    <span className={`text-sm font-semibold ${getScoreColor(bankingData.analytics.digital_maturity_score)}`}>
                      {bankingData.analytics.digital_maturity_score.toFixed(1)}/100
                    </span>
                  </div>
                  <Progress value={bankingData.analytics.digital_maturity_score} className="h-2" />
                  
                  <div className="flex items-center justify-between text-sm">
                    <span>Digital Trend</span>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(bankingData.analytics.digital_trend)}
                      <span className="font-medium capitalize">{bankingData.analytics.digital_trend}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stability" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Financial Stability
              </CardTitle>
              <CardDescription>Banking behavior stability metrics</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Stability Scores</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Banking Score</span>
                      <span className="font-semibold">{bankingData.analytics.banking_score.toFixed(1)}/100</span>
                    </div>
                    <Progress value={bankingData.analytics.banking_score} className="h-2" />
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Stability Score</span>
                      <span className="font-semibold">{bankingData.analytics.stability_score.toFixed(1)}/100</span>
                    </div>
                    <Progress value={bankingData.analytics.stability_score} className="h-2" />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-medium">Risk Indicators</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Cash Intensive</span>
                      <Badge variant={bankingData.analytics.cash_intensive_flag ? "destructive" : "default"}>
                        {bankingData.analytics.cash_intensive_flag ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Irregular Transactions</span>
                      <Badge variant={bankingData.analytics.irregular_transaction_flag ? "destructive" : "default"}>
                        {bankingData.analytics.irregular_transaction_flag ? "Yes" : "No"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Dormant Accounts</span>
                      <Badge variant={bankingData.analytics.dormant_account_flag ? "destructive" : "default"}>
                        {bankingData.analytics.dormant_account_flag ? "Yes" : "No"}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h4 className="font-medium">Volatility Analysis</h4>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                    <BarChart3 className="h-4 w-4 text-blue-500" />
                    <div>
                      <p className="font-medium text-sm">Balance Volatility</p>
                      <p className="text-xs text-muted-foreground">{bankingData.analytics.balance_volatility.toFixed(1)}%</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                    <Activity className="h-4 w-4 text-green-500" />
                    <div>
                      <p className="font-medium text-sm">Transaction Consistency</p>
                      <p className="text-xs text-muted-foreground">
                        {bankingData.analytics.balance_volatility < 20 ? 'Stable' : 
                         bankingData.analytics.balance_volatility < 35 ? 'Moderate' : 'Volatile'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Banking Insights & Recommendations
              </CardTitle>
              <CardDescription>AI-powered insights and improvement suggestions</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium text-green-700">Strengths</h4>
                  <div className="space-y-2">
                    {bankingData.analytics.digital_transaction_ratio >= 70 && (
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>High digital adoption</span>
                      </div>
                    )}
                    {bankingData.analytics.banking_score >= 80 && (
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Excellent banking behavior</span>
                      </div>
                    )}
                    {!bankingData.analytics.cash_intensive_flag && (
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Low cash dependency</span>
                      </div>
                    )}
                    {bankingData.analytics.balance_volatility < 20 && (
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Stable cash flows</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-medium text-orange-700">Improvement Areas</h4>
                  <div className="space-y-2">
                    {bankingData.analytics.upi_adoption_rate < 60 && (
                      <div className="flex items-center gap-2 text-sm">
                        <AlertCircle className="h-4 w-4 text-orange-500" />
                        <span>Increase UPI usage</span>
                      </div>
                    )}
                    {bankingData.analytics.balance_volatility > 25 && (
                      <div className="flex items-center gap-2 text-sm">
                        <AlertCircle className="h-4 w-4 text-orange-500" />
                        <span>Reduce balance volatility</span>
                      </div>
                    )}
                    {bankingData.analytics.digital_maturity_score < 70 && (
                      <div className="flex items-center gap-2 text-sm">
                        <AlertCircle className="h-4 w-4 text-orange-500" />
                        <span>Improve digital maturity</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h4 className="font-medium">Overall Assessment</h4>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-lg font-bold text-blue-600 mb-1">
                      {bankingData.analytics.banking_score.toFixed(0)}
                    </div>
                    <div className="text-sm text-blue-600">Banking Score</div>
                    {getScoreBadge(bankingData.analytics.banking_score)}
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-lg font-bold text-green-600 mb-1">
                      {bankingData.analytics.stability_score.toFixed(0)}
                    </div>
                    <div className="text-sm text-green-600">Stability Score</div>
                    {getScoreBadge(bankingData.analytics.stability_score)}
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-lg font-bold text-purple-600 mb-1">
                      {bankingData.analytics.digital_maturity_score.toFixed(0)}
                    </div>
                    <div className="text-sm text-purple-600">Digital Maturity</div>
                    {getScoreBadge(bankingData.analytics.digital_maturity_score)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Data Quality Footer */}
      <Card className="bg-muted/30">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                Last updated: {new Date(bankingData.last_updated).toLocaleDateString()}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">Consent ID:</span>
              <Badge variant="outline">{bankingData.consent_id}</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
