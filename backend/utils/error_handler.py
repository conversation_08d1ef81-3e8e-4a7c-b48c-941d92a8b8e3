"""
Centralized error handling utilities for Credit Chakra API
Provides consistent error responses and logging across all endpoints
"""
from fastapi import HTTPException, status
from typing import Any, Dict, Optional
import logging
from datetime import datetime, timezone
import functools

logger = logging.getLogger(__name__)

class CreditChakraError(Exception):
    """Base exception class for Credit Chakra application"""
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        self.message = message
        self.error_code = error_code or "GENERAL_ERROR"
        self.details = details or {}
        super().__init__(self.message)

class MSMENotFoundError(CreditChakraError):
    """Raised when MSME profile is not found"""
    def __init__(self, msme_id: str):
        super().__init__(
            message=f"MSME profile not found: {msme_id}",
            error_code="MSME_NOT_FOUND",
            details={"msme_id": msme_id}
        )

class ValidationError(CreditChakraError):
    """Raised when input validation fails"""
    def __init__(self, field: str, message: str):
        super().__init__(
            message=f"Validation error for {field}: {message}",
            error_code="VALIDATION_ERROR",
            details={"field": field, "validation_message": message}
        )

class DatabaseError(CreditChakraError):
    """Raised when database operations fail"""
    def __init__(self, operation: str, details: str = None):
        super().__init__(
            message=f"Database operation failed: {operation}",
            error_code="DATABASE_ERROR",
            details={"operation": operation, "details": details}
        )

class ExternalServiceError(CreditChakraError):
    """Raised when external service calls fail"""
    def __init__(self, service: str, details: str = None):
        super().__init__(
            message=f"External service error: {service}",
            error_code="EXTERNAL_SERVICE_ERROR",
            details={"service": service, "details": details}
        )

def handle_common_exceptions(func):
    """Decorator to handle common exceptions and convert to HTTP responses"""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except MSMENotFoundError as e:
            logger.warning(f"MSME not found: {e.details}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail={
                    "error": e.error_code,
                    "message": e.message,
                    "details": e.details,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            )
        except ValidationError as e:
            logger.warning(f"Validation error: {e.details}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={
                    "error": e.error_code,
                    "message": e.message,
                    "details": e.details,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            )
        except DatabaseError as e:
            logger.error(f"Database error: {e.details}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "error": e.error_code,
                    "message": "Internal database error occurred",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            )
        except ExternalServiceError as e:
            logger.error(f"External service error: {e.details}")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail={
                    "error": e.error_code,
                    "message": f"External service temporarily unavailable: {e.details.get('service', 'unknown')}",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            )
        except HTTPException:
            # Re-raise FastAPI HTTP exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={
                    "error": "INTERNAL_SERVER_ERROR",
                    "message": "An unexpected error occurred",
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
            )
    return wrapper

def create_success_response(data: Any, message: str = "Success") -> Dict[str, Any]:
    """Create standardized success response"""
    return {
        "success": True,
        "message": message,
        "data": data,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

def create_error_response(error_code: str, message: str, details: Dict[str, Any] = None) -> Dict[str, Any]:
    """Create standardized error response"""
    return {
        "success": False,
        "error": error_code,
        "message": message,
        "details": details or {},
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

def validate_msme_id(msme_id: str) -> None:
    """Validate MSME ID format"""
    if not msme_id or not msme_id.strip():
        raise ValidationError("msme_id", "MSME ID cannot be empty")
    
    if len(msme_id) < 3:
        raise ValidationError("msme_id", "MSME ID must be at least 3 characters long")

def validate_pagination_params(limit: int, offset: int) -> None:
    """Validate pagination parameters"""
    if limit < 1 or limit > 1000:
        raise ValidationError("limit", "Limit must be between 1 and 1000")
    
    if offset < 0:
        raise ValidationError("offset", "Offset must be non-negative")
