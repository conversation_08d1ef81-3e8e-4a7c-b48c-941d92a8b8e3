/**
 * Advanced Risk Monitor Component
 * Prepared for sophisticated risk management features including SMA heatmaps,
 * stress testing, regulatory reporting, and real-time monitoring
 */
'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { useOptimizedPortfolio } from '@/hooks/useOptimizedData';
import { RiskDistribution, RiskBadge } from '@/components/shared/risk-card';
import { DataFetcher, LoadingSkeleton } from '@/components/shared/data-fetcher';
import { MSME, Analytics } from '@/types';
import {
  Shield, AlertTriangle, TrendingUp, TrendingDown, Activity, Users, BarChart3,
  Clock, Filter, RefreshCw, Bell, Eye, Building2, MapPin, Calendar,
  Target, Zap, Database, FileText, Download, Settings
} from 'lucide-react';

interface RiskAlert {
  id: string;
  msme_id: string;
  msme_name: string;
  type: 'score_drop' | 'high_risk' | 'no_activity' | 'signal_anomaly' | 'sma_progression';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  acknowledged: boolean;
  dpd_classification?: 'SMA-0' | 'SMA-1' | 'SMA-2' | 'NPA';
  days_past_due?: number;
}

interface SMAData {
  msme_id: string;
  msme_name: string;
  business_type: string;
  current_classification: 'Standard' | 'SMA-0' | 'SMA-1' | 'SMA-2' | 'NPA';
  days_past_due: number;
  outstanding_amount: number;
  last_payment_date: string;
  risk_progression: string[];
  stress_indicators: string[];
}

interface StressTestScenario {
  id: string;
  name: string;
  description: string;
  parameters: {
    economic_downturn: number;
    sector_impact: number;
    interest_rate_change: number;
  };
  results?: {
    projected_npa_ratio: number;
    affected_msmes: number;
    potential_loss: number;
  };
}

export function AdvancedRiskMonitor() {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedTimeframe, setSelectedTimeframe] = useState('7d');
  const [riskAlerts, setRiskAlerts] = useState<RiskAlert[]>([]);
  const [smaData, setSmaData] = useState<SMAData[]>([]);
  const [stressTestScenarios, setStressTestScenarios] = useState<StressTestScenario[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  const {
    msmes,
    analytics,
    loading,
    error,
    refetch
  } = useOptimizedPortfolio({
    enableCache: true,
    enablePagination: false
  });

  // Generate mock risk alerts
  const generateRiskAlerts = useCallback((msmeData: MSME[]) => {
    const alerts: RiskAlert[] = [];
    
    // High-risk MSMEs
    msmeData
      .filter(msme => msme.risk_band === 'red')
      .slice(0, 5)
      .forEach((msme, index) => {
        alerts.push({
          id: `alert-${msme.msme_id}-${index}`,
          msme_id: msme.msme_id,
          msme_name: msme.name,
          type: 'high_risk',
          severity: 'critical',
          message: `${msme.name} has entered high-risk category with score ${Math.round(msme.current_score)}`,
          timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
          acknowledged: false,
          dpd_classification: 'SMA-1',
          days_past_due: Math.floor(Math.random() * 60) + 30
        });
      });

    // Score drop alerts
    msmeData
      .filter(msme => msme.score_trend === 'declining')
      .slice(0, 3)
      .forEach((msme, index) => {
        alerts.push({
          id: `score-drop-${msme.msme_id}-${index}`,
          msme_id: msme.msme_id,
          msme_name: msme.name,
          type: 'score_drop',
          severity: 'high',
          message: `Significant score decline detected for ${msme.name}`,
          timestamp: new Date(Date.now() - Math.random() * 12 * 60 * 60 * 1000).toISOString(),
          acknowledged: false
        });
      });

    setRiskAlerts(alerts.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()));
  }, []);

  // Generate SMA (Special Mention Account) data
  const generateSMAData = useCallback((msmeData: MSME[]) => {
    const smaEntries: SMAData[] = [];
    
    msmeData
      .filter(msme => msme.risk_band !== 'green')
      .slice(0, 10)
      .forEach(msme => {
        const dpd = Math.floor(Math.random() * 120);
        let classification: SMAData['current_classification'] = 'Standard';
        
        if (dpd >= 90) classification = 'SMA-2';
        else if (dpd >= 60) classification = 'SMA-1';
        else if (dpd >= 30) classification = 'SMA-0';

        smaEntries.push({
          msme_id: msme.msme_id,
          msme_name: msme.name,
          business_type: msme.business_type,
          current_classification: classification,
          days_past_due: dpd,
          outstanding_amount: Math.floor(Math.random() * 5000000) + 1000000,
          last_payment_date: new Date(Date.now() - dpd * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          risk_progression: classification !== 'Standard' ? ['Standard', 'SMA-0', classification] : ['Standard'],
          stress_indicators: [
            'Declining revenue',
            'Irregular payments',
            'Market volatility'
          ].slice(0, Math.floor(Math.random() * 3) + 1)
        });
      });

    setSmaData(smaEntries);
  }, []);

  // Generate stress test scenarios
  const generateStressTestScenarios = useCallback(() => {
    const scenarios: StressTestScenario[] = [
      {
        id: 'economic_downturn',
        name: 'Economic Downturn',
        description: 'Moderate economic recession with 15% GDP decline',
        parameters: {
          economic_downturn: 15,
          sector_impact: 20,
          interest_rate_change: 2
        },
        results: {
          projected_npa_ratio: 8.5,
          affected_msmes: Math.floor(msmes.length * 0.25),
          potential_loss: 125000000
        }
      },
      {
        id: 'sector_crisis',
        name: 'Sector-Specific Crisis',
        description: 'Manufacturing sector disruption',
        parameters: {
          economic_downturn: 5,
          sector_impact: 35,
          interest_rate_change: 1
        },
        results: {
          projected_npa_ratio: 12.3,
          affected_msmes: Math.floor(msmes.length * 0.18),
          potential_loss: 89000000
        }
      },
      {
        id: 'interest_rate_shock',
        name: 'Interest Rate Shock',
        description: 'Sudden 5% increase in interest rates',
        parameters: {
          economic_downturn: 8,
          sector_impact: 15,
          interest_rate_change: 5
        },
        results: {
          projected_npa_ratio: 15.7,
          affected_msmes: Math.floor(msmes.length * 0.32),
          potential_loss: 156000000
        }
      }
    ];

    setStressTestScenarios(scenarios);
  }, [msmes.length]);

  useEffect(() => {
    if (msmes.length > 0) {
      generateRiskAlerts(msmes);
      generateSMAData(msmes);
      generateStressTestScenarios();
    }
  }, [msmes, generateRiskAlerts, generateSMAData, generateStressTestScenarios]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await refetch();
    setRefreshing(false);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getSMAColor = (classification: string) => {
    switch (classification) {
      case 'Standard': return 'text-green-600 bg-green-50';
      case 'SMA-0': return 'text-yellow-600 bg-yellow-50';
      case 'SMA-1': return 'text-orange-600 bg-orange-50';
      case 'SMA-2': return 'text-red-600 bg-red-50';
      case 'NPA': return 'text-red-800 bg-red-100';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-4">
          <LoadingSkeleton type="card" />
          <LoadingSkeleton type="card" />
          <LoadingSkeleton type="card" />
          <LoadingSkeleton type="card" />
        </div>
        <LoadingSkeleton type="table" count={8} />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-2">
          <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
            Risk Monitor
          </h1>
          <p className="text-lg text-muted-foreground">
            Advanced risk assessment and early warning system
          </p>
        </div>
        <div className="flex gap-3">
          <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">24 Hours</SelectItem>
              <SelectItem value="7d">7 Days</SelectItem>
              <SelectItem value="30d">30 Days</SelectItem>
              <SelectItem value="90d">90 Days</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={handleRefresh} disabled={refreshing} variant="outline" size="lg">
            <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Risk Overview Cards */}
      {analytics && (
        <RiskDistribution
          riskDistribution={analytics.risk_distribution}
          totalCount={analytics.total_msmes}
          showTrends={true}
          trends={{
            green: 2.3,
            yellow: -1.5,
            red: -0.8
          }}
        />
      )}

      {/* Advanced Risk Monitoring Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="alerts">Risk Alerts</TabsTrigger>
          <TabsTrigger value="sma">SMA Monitor</TabsTrigger>
          <TabsTrigger value="stress">Stress Testing</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Real-time Risk Metrics */}
          <div className="grid gap-6 md:grid-cols-3">
            <Card className="border-0 shadow-lg">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <AlertTriangle className="h-4 w-4 text-amber-500" />
                  Active Alerts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{riskAlerts.filter(a => !a.acknowledged).length}</div>
                <p className="text-sm text-muted-foreground">Requiring attention</p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-blue-500" />
                  SMA Accounts
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{smaData.filter(s => s.current_classification !== 'Standard').length}</div>
                <p className="text-sm text-muted-foreground">Special mention accounts</p>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-sm">
                  <Target className="h-4 w-4 text-emerald-500" />
                  Portfolio Health
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">87.5%</div>
                <p className="text-sm text-muted-foreground">Overall health score</p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Risk Events */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Recent Risk Events</CardTitle>
              <CardDescription>Latest risk-related activities and changes</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {riskAlerts.slice(0, 5).map((alert) => (
                  <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Badge className={getSeverityColor(alert.severity)}>
                        {alert.severity.toUpperCase()}
                      </Badge>
                      <div>
                        <div className="font-medium">{alert.msme_name}</div>
                        <div className="text-sm text-muted-foreground">{alert.message}</div>
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {new Date(alert.timestamp).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Risk Alerts Dashboard</CardTitle>
              <CardDescription>Monitor and manage risk alerts across your portfolio</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>MSME</TableHead>
                    <TableHead>Alert Type</TableHead>
                    <TableHead>Severity</TableHead>
                    <TableHead>DPD Classification</TableHead>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {riskAlerts.map((alert) => (
                    <TableRow key={alert.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{alert.msme_name}</div>
                          <div className="text-sm text-muted-foreground">{alert.msme_id}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="capitalize">
                          {alert.type.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getSeverityColor(alert.severity)}>
                          {alert.severity.toUpperCase()}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {alert.dpd_classification && (
                          <Badge className={getSMAColor(alert.dpd_classification)}>
                            {alert.dpd_classification}
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {new Date(alert.timestamp).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <Badge variant={alert.acknowledged ? "default" : "destructive"}>
                          {alert.acknowledged ? "Acknowledged" : "Pending"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sma" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>SMA (Special Mention Account) Monitor</CardTitle>
              <CardDescription>Track accounts with payment irregularities and risk progression</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>MSME</TableHead>
                    <TableHead>Classification</TableHead>
                    <TableHead>Days Past Due</TableHead>
                    <TableHead>Outstanding Amount</TableHead>
                    <TableHead>Last Payment</TableHead>
                    <TableHead>Risk Progression</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {smaData.map((sma) => (
                    <TableRow key={sma.msme_id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{sma.msme_name}</div>
                          <div className="text-sm text-muted-foreground capitalize">{sma.business_type}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getSMAColor(sma.current_classification)}>
                          {sma.current_classification}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{sma.days_past_due} days</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{formatCurrency(sma.outstanding_amount)}</div>
                      </TableCell>
                      <TableCell>
                        {new Date(sma.last_payment_date).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          {sma.risk_progression.map((stage, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {stage}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button size="sm" variant="outline">
                          <FileText className="h-4 w-4 mr-1" />
                          Details
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="stress" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Stress Testing Scenarios</CardTitle>
              <CardDescription>Evaluate portfolio resilience under adverse conditions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-3">
                {stressTestScenarios.map((scenario) => (
                  <Card key={scenario.id} className="border">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-lg">{scenario.name}</CardTitle>
                      <CardDescription>{scenario.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      {scenario.results && (
                        <div className="space-y-3">
                          <div>
                            <div className="text-sm text-muted-foreground">Projected NPA Ratio</div>
                            <div className="text-2xl font-bold text-red-600">{scenario.results.projected_npa_ratio}%</div>
                          </div>
                          <div>
                            <div className="text-sm text-muted-foreground">Affected MSMEs</div>
                            <div className="font-medium">{scenario.results.affected_msmes}</div>
                          </div>
                          <div>
                            <div className="text-sm text-muted-foreground">Potential Loss</div>
                            <div className="font-medium">{formatCurrency(scenario.results.potential_loss)}</div>
                          </div>
                          <Button size="sm" className="w-full">
                            <BarChart3 className="h-4 w-4 mr-1" />
                            View Details
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>RBI Compliance Dashboard</CardTitle>
              <CardDescription>Monitor regulatory compliance and reporting requirements</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-semibold">Regulatory Reports</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">Monthly NPA Report</div>
                        <div className="text-sm text-muted-foreground">Due: End of month</div>
                      </div>
                      <Badge variant="default">Current</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">SMA Classification Report</div>
                        <div className="text-sm text-muted-foreground">Due: 15th of month</div>
                      </div>
                      <Badge variant="secondary">Pending</Badge>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-semibold">Compliance Metrics</h4>
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Documentation Completeness</span>
                        <span>94%</span>
                      </div>
                      <Progress value={94} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Risk Classification Accuracy</span>
                        <span>98%</span>
                      </div>
                      <Progress value={98} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Reporting Timeliness</span>
                        <span>91%</span>
                      </div>
                      <Progress value={91} className="h-2" />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
