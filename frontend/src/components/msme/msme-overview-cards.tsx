'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { MSME, ScoreDetails } from '@/types';
import {
  BarChart3, Shield, TrendingUp, TrendingDown, Minus,
  DollarSign, FileText, Send, Calendar, Zap, RefreshCw
} from 'lucide-react';

interface MSMEOverviewCardsProps {
  msme: MSME;
  scoreDetails: ScoreDetails;
  onSendNudge: () => void;
  onGenerateReport: () => void;
  onUpdateData: () => void;
  onScheduleReview: () => void;
}

const formatDate = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

  if (diffInHours < 1) {
    return 'Just now';
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  } else if (diffInHours < 168) { // 7 days
    const days = Math.floor(diffInHours / 24);
    return `${days}d ago`;
  } else {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  }
};

export function MSMEOverviewCards({
  msme,
  scoreDetails,
  onSendNudge,
  onGenerateReport,
  onUpdateData,
  onScheduleReview
}: MSMEOverviewCardsProps) {
  const getRiskBadgeVariant = (risk: string) => {
    switch (risk) {
      case 'green': return 'default';
      case 'yellow': return 'secondary';
      case 'red': return 'destructive';
      default: return 'outline';
    }
  };

  const getRiskLabel = (risk: string) => {
    switch (risk) {
      case 'green': return 'Low';
      case 'yellow': return 'Medium';
      case 'red': return 'High';
      default: return 'Unknown';
    }
  };

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'improving':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTrendText = (trend?: string) => {
    switch (trend) {
      case 'improving':
        return 'Improving';
      case 'declining':
        return 'Declining';
      default:
        return 'Stable';
    }
  };

  // Generate risk factors based on MSME data
  const generateRiskFactors = () => {
    const factors = [];
    
    if ((msme.gst_compliance || 0) < 80) {
      factors.push('GST compliance below optimal threshold');
    }
    if ((msme.banking_health || 0) < 75) {
      factors.push('Banking health requires attention');
    }
    if (msme.risk_band === 'red') {
      factors.push('High credit risk classification');
    } else if (msme.risk_band === 'yellow') {
      factors.push('Medium risk requires monitoring');
    }
    
    // Add positive factors for low risk
    if (msme.risk_band === 'green') {
      factors.push('Strong financial performance');
      factors.push('Consistent business operations');
    }
    
    return factors.slice(0, 3); // Limit to 3 factors
  };

  // Generate business insights
  const generateBusinessInsights = () => {
    const insights = [];
    
    // Business type specific insights
    if (msme.business_type === 'manufacturing') {
      insights.push('Manufacturing sector with stable operations');
    } else if (msme.business_type === 'retail') {
      insights.push('Retail business with customer-facing operations');
    } else if (msme.business_type === 'services') {
      insights.push('Service-based business model');
    }
    
    // Location based insights
    if (msme.location.includes('Mumbai') || msme.location.includes('Delhi') || msme.location.includes('Bangalore')) {
      insights.push('Located in major business hub');
    }
    
    // Digital score insights
    if ((msme.digital_score || 0) > 80) {
      insights.push('High digital adoption rate');
    } else if ((msme.digital_score || 0) < 60) {
      insights.push('Opportunity for digital transformation');
    }
    
    return insights.slice(0, 3); // Limit to 3 insights
  };

  const riskFactors = generateRiskFactors();
  const businessInsights = generateBusinessInsights();

  return (
    <TooltipProvider>
      <div className="grid gap-4 md:grid-cols-4">
        {/* Enhanced Credit Score Card */}
        <Card className="border border-emerald-200 bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950 dark:to-emerald-900">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-3 text-emerald-700">
              <div className="p-2 bg-emerald-500 rounded-lg shadow-sm">
                <BarChart3 className="h-4 w-4 text-white" />
              </div>
              <span className="text-base font-semibold tracking-tight">Credit Score</span>
            </CardTitle>
          </CardHeader>

          <CardContent className="space-y-5">
            <div className="flex items-end gap-2">
              <span className="text-4xl font-bold text-emerald-700 leading-none">
                {scoreDetails.current_score.toFixed(1)}
              </span>
              <span className="text-sm text-emerald-600 font-medium">/100</span>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Badge variant={getRiskBadgeVariant(scoreDetails.risk_band)} className="font-medium">
                  {getRiskLabel(scoreDetails.risk_band)} Risk
                </Badge>

                <div className="flex items-center gap-1 text-xs text-emerald-600 font-medium">
                  {getTrendIcon(msme.score_trend)}
                  <span>{getTrendText(msme.score_trend)}</span>
                </div>
              </div>

              <Progress value={scoreDetails.current_score} className="h-2 rounded-sm" />

              <div className="grid grid-cols-1 gap-2 text-xs text-emerald-600 pt-1">
                <div className="flex justify-between">
                  <span className="font-medium">Last Updated:</span>
                  <span>{formatDate(scoreDetails.last_updated)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Risk Assessment Card */}
        <Card className="border border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <div className="p-2 bg-blue-500 rounded-lg">
                <Shield className="h-4 w-4 text-white" />
              </div>
              Risk Assessment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="text-xs font-medium text-blue-700 mb-2">Key Risk Factors:</div>
              {riskFactors.map((factor, index) => (
                <div key={index} className="flex items-start gap-2 text-xs text-blue-700">
                  <div className="w-1 h-1 rounded-full bg-blue-500 mt-1.5 flex-shrink-0"></div>
                  <span className="leading-tight">{factor}</span>
                </div>
              ))}
              {businessInsights.map((insight, index) => (
                <div key={index} className="flex items-start gap-2 text-xs text-blue-600">
                  <div className="w-1 h-1 rounded-full bg-blue-400 mt-1.5 flex-shrink-0"></div>
                  <span className="leading-tight">{insight}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Financial Health Card */}
        <Card className="border border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <div className="p-2 bg-purple-500 rounded-lg">
                <DollarSign className="h-4 w-4 text-white" />
              </div>
              Financial Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span className="text-purple-600">GST Compliance</span>
                <span className="font-semibold text-purple-700">{msme.gst_compliance || 0}%</span>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-purple-600">Banking Health</span>
                <span className="font-semibold text-purple-700">{msme.banking_health || 0}%</span>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-purple-600">Monthly Turnover</span>
                <span className="font-semibold text-purple-700">
                  ₹{((msme.monthly_turnover || 0) / 100000).toFixed(1)}L
                </span>
              </div>
              <div className="flex items-center justify-between text-xs">
                <span className="text-purple-600">Digital Score</span>
                <span className="font-semibold text-purple-700">{msme.digital_score || 0}%</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions Card */}
        <Card className="border border-gray-200">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-gray-700">
              <div className="p-2 bg-gray-500 rounded-lg">
                <Zap className="h-4 w-4 text-white" />
              </div>
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full text-xs h-8 bg-white/70 hover:bg-white"
                  onClick={onSendNudge}
                >
                  <Send className="h-3 w-3 mr-2" />
                  Send Nudge
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Send notification to MSME</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full text-xs h-8 bg-white/70 hover:bg-white"
                  onClick={onGenerateReport}
                >
                  <FileText className="h-3 w-3 mr-2" />
                  Generate Report
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Download detailed report</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full text-xs h-8 bg-white/70 hover:bg-white"
                  onClick={onUpdateData}
                >
                  <RefreshCw className="h-3 w-3 mr-2" />
                  Update Data
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Refresh MSME data</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  size="sm"
                  variant="outline"
                  className="w-full text-xs h-8 bg-white/70 hover:bg-white"
                  onClick={onScheduleReview}
                >
                  <Calendar className="h-3 w-3 mr-2" />
                  Schedule Review
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Schedule credit review</p>
              </TooltipContent>
            </Tooltip>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}
