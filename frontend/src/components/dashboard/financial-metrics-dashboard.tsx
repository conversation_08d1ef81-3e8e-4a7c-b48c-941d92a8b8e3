'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import FinancialCharts from '@/components/charts/financial-charts';
import CashFlowCharts from '@/components/charts/cash-flow-charts';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3,
  PieChart,
  LineChart,
  Activity,
  DollarSign,
  Target,
  RefreshCw,
  Download,
  Calendar
} from 'lucide-react';

interface FinancialMetricsDashboardProps {
  msmeId?: string;
  portfolioView?: boolean;
}

export default function FinancialMetricsDashboard({ 
  msmeId, 
  portfolioView = false 
}: FinancialMetricsDashboardProps) {
  const [selectedPeriod, setSelectedPeriod] = useState('12M');
  const [selectedMetric] = useState('all');
  const [comparisonMode, setComparisonMode] = useState('industry');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState<Record<string, unknown> | null>(null);

  useEffect(() => {
    fetchDashboardData();
  }, [msmeId, selectedPeriod, selectedMetric, comparisonMode]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Mock comprehensive dashboard data
      const mockData = {
        summary: {
          overall_financial_score: 78.5,
          financial_grade: "B+",
          risk_level: "MEDIUM",
          industry_percentile: 72.3,
          trend: "improving",
          last_updated: new Date().toISOString()
        },
        liquidity: {
          current_ratio: 2.1,
          quick_ratio: 1.4,
          cash_ratio: 0.3,
          working_capital: 850000,
          overall_liquidity_score: 82.4,
          liquidity_risk_level: "LOW"
        },
        profitability: {
          gross_profit_margin: 28.5,
          net_profit_margin: 12.3,
          operating_profit_margin: 18.7,
          return_on_assets: 9.2,
          return_on_equity: 16.8,
          overall_profitability_score: 79.1,
          profitability_trend: "improving"
        },
        leverage: {
          debt_to_equity_ratio: 0.65,
          debt_to_assets_ratio: 0.39,
          debt_service_coverage_ratio: 1.8,
          interest_coverage_ratio: 4.2,
          overall_leverage_score: 74.6,
          leverage_risk_level: "MEDIUM"
        },
        efficiency: {
          asset_turnover: 1.3,
          inventory_turnover: 5.2,
          receivables_turnover: 8.1,
          days_sales_outstanding: 45,
          cash_conversion_cycle: 68,
          overall_efficiency_score: 71.8,
          efficiency_trend: "stable"
        },
        trends: [
          { month: 'Jan', liquidity: 78, profitability: 72, leverage: 68, efficiency: 75 },
          { month: 'Feb', liquidity: 80, profitability: 74, leverage: 70, efficiency: 76 },
          { month: 'Mar', liquidity: 82, profitability: 76, leverage: 72, efficiency: 78 },
          { month: 'Apr', liquidity: 81, profitability: 78, leverage: 74, efficiency: 79 },
          { month: 'May', liquidity: 83, profitability: 79, leverage: 75, efficiency: 80 },
          { month: 'Jun', liquidity: 82, profitability: 79, leverage: 75, efficiency: 72 }
        ],
        benchmarks: {
          industry_avg: {
            liquidity: 70.2,
            profitability: 65.8,
            leverage: 72.1,
            efficiency: 68.9
          },
          peer_avg: {
            liquidity: 75.5,
            profitability: 71.2,
            leverage: 69.8,
            efficiency: 73.4
          }
        },
        cash_flow: {
          statements: [],
          forecast: {
            monthly_forecasts: [],
            projected_gaps: [],
            surplus_periods: [],
            net_cash_flow_forecast: 1250000,
            confidence_level: 82.5
          },
          seasonal: {
            monthly_averages: {},
            monthly_patterns: {},
            seasonal_score: 78.5
          },
          analytics: {
            cash_flow_health_score: 82.4,
            liquidity_score: 78.9,
            efficiency_score: 75.2,
            free_cash_flow: 1850000,
            operating_cash_flow_ratio: 0.35,
            cash_flow_volatility: 18.5,
            cash_flow_growth_rate: 12.3
          }
        }
      };
      
      setDashboardData(mockData);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDashboardData();
    setRefreshing(false);
  };

  const handleExport = () => {
    // Mock export functionality
    console.log('Exporting financial metrics dashboard...');
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <BarChart3 className="h-4 w-4 text-blue-500" />;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getRiskBadge = (risk: string) => {
    switch (risk) {
      case 'LOW': return <Badge variant="default" className="bg-green-100 text-green-800">Low Risk</Badge>;
      case 'MEDIUM': return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Medium Risk</Badge>;
      case 'HIGH': return <Badge variant="destructive">High Risk</Badge>;
      default: return <Badge variant="outline">Unknown</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
        <div className="h-96 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Financial Data Available</h3>
        <p className="text-gray-500 mb-4">Financial metrics data is not available.</p>
        <Button onClick={fetchDashboardData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Financial Metrics Dashboard</h2>
          <p className="text-muted-foreground">
            {portfolioView ? 'Portfolio-wide financial analysis' : 'Comprehensive financial performance analysis'}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3M">3 Months</SelectItem>
              <SelectItem value="6M">6 Months</SelectItem>
              <SelectItem value="12M">12 Months</SelectItem>
              <SelectItem value="24M">24 Months</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={comparisonMode} onValueChange={setComparisonMode}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="industry">vs Industry</SelectItem>
              <SelectItem value="peers">vs Peers</SelectItem>
              <SelectItem value="historical">vs Historical</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleRefresh} 
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <Target className="h-5 w-5" />
              Overall Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 mb-2">
              {(dashboardData.summary as any)?.overall_financial_score?.toFixed(1) || '0.0'}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Badge variant="outline" className="bg-blue-100 text-blue-800">
                Grade {(dashboardData.summary as any)?.financial_grade || 'N/A'}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700">
              <DollarSign className="h-5 w-5" />
              Liquidity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 mb-2">
              {(dashboardData.liquidity as any)?.overall_liquidity_score?.toFixed(0) || '0'}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getRiskBadge((dashboardData.liquidity as any)?.liquidity_risk_level || 'Low')}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <TrendingUp className="h-5 w-5" />
              Profitability
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 mb-2">
              {(dashboardData.profitability as any)?.overall_profitability_score?.toFixed(0) || '0'}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getTrendIcon((dashboardData.profitability as any)?.profitability_trend || 'stable')}
              <span className="capitalize">{(dashboardData.profitability as any)?.profitability_trend || 'stable'}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <Activity className="h-5 w-5" />
              Efficiency
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 mb-2">
              {(dashboardData.efficiency as any)?.overall_efficiency_score?.toFixed(0) || '0'}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getTrendIcon((dashboardData.efficiency as any)?.efficiency_trend || 'stable')}
              <span className="capitalize">{(dashboardData.efficiency as any)?.efficiency_trend || 'stable'}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="financial-charts">Financial Analysis</TabsTrigger>
          <TabsTrigger value="cash-flow">Cash Flow</TabsTrigger>
          <TabsTrigger value="benchmarks">Benchmarks</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Financial Health Breakdown
                </CardTitle>
                <CardDescription>Key performance indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Liquidity Score</span>
                    <span className={`font-medium ${getScoreColor((dashboardData.liquidity as any)?.overall_liquidity_score || 0)}`}>
                      {(dashboardData.liquidity as any)?.overall_liquidity_score?.toFixed(0) || '0'}/100
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Profitability Score</span>
                    <span className={`font-medium ${getScoreColor((dashboardData.profitability as any)?.overall_profitability_score || 0)}`}>
                      {(dashboardData.profitability as any)?.overall_profitability_score?.toFixed(0) || '0'}/100
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Leverage Score</span>
                    <span className={`font-medium ${getScoreColor((dashboardData.leverage as any)?.overall_leverage_score || 0)}`}>
                      {(dashboardData.leverage as any)?.overall_leverage_score?.toFixed(0) || '0'}/100
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Efficiency Score</span>
                    <span className={`font-medium ${getScoreColor((dashboardData.efficiency as any)?.overall_efficiency_score || 0)}`}>
                      {(dashboardData.efficiency as any)?.overall_efficiency_score?.toFixed(0) || '0'}/100
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  Key Ratios
                </CardTitle>
                <CardDescription>Critical financial ratios</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Current Ratio</span>
                    <span className="font-medium">{(dashboardData.liquidity as any)?.current_ratio?.toFixed(2) || '0.00'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">ROE</span>
                    <span className="font-medium">{(dashboardData.profitability as any)?.return_on_equity?.toFixed(1) || '0.0'}%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Debt/Equity</span>
                    <span className="font-medium">{(dashboardData.leverage as any)?.debt_to_equity_ratio?.toFixed(2) || '0.00'}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Asset Turnover</span>
                    <span className="font-medium">{(dashboardData.efficiency as any)?.asset_turnover?.toFixed(1) || '0.0'}x</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="financial-charts" className="space-y-6">
          <FinancialCharts data={dashboardData as any} />
        </TabsContent>

        <TabsContent value="cash-flow" className="space-y-6">
          <CashFlowCharts data={dashboardData.cash_flow as any} />
        </TabsContent>

        <TabsContent value="benchmarks" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Industry Comparison</CardTitle>
                <CardDescription>Performance vs industry averages</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries((dashboardData.benchmarks as any)?.industry_avg || {}).map(([metric, value]) => (
                    <div key={metric} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="capitalize">{metric}</span>
                        <span>Your: {(dashboardData as any)[metric]?.overall_liquidity_score?.toFixed(0) || 'N/A'} | Industry: {(value as number).toFixed(0)}</span>
                      </div>
                      <div className="relative h-2 bg-gray-200 rounded">
                        <div 
                          className="absolute h-2 bg-blue-500 rounded"
                          style={{ width: `${Math.min(100, ((dashboardData as any)[metric]?.overall_liquidity_score || 0))}%` }}
                        />
                        <div 
                          className="absolute h-2 border-r-2 border-gray-600"
                          style={{ left: `${Math.min(100, value as number)}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Peer Comparison</CardTitle>
                <CardDescription>Performance vs peer group</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries((dashboardData.benchmarks as any)?.peer_avg || {}).map(([metric, value]) => (
                    <div key={metric} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="capitalize">{metric}</span>
                        <span>Your: {(dashboardData as any)[metric]?.overall_liquidity_score?.toFixed(0) || 'N/A'} | Peers: {(value as number).toFixed(0)}</span>
                      </div>
                      <div className="relative h-2 bg-gray-200 rounded">
                        <div 
                          className="absolute h-2 bg-green-500 rounded"
                          style={{ width: `${Math.min(100, ((dashboardData as any)[metric]?.overall_liquidity_score || 0))}%` }}
                        />
                        <div 
                          className="absolute h-2 border-r-2 border-gray-600"
                          style={{ left: `${Math.min(100, value as number)}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Footer */}
      <Card className="bg-muted/30">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                Last updated: {new Date((dashboardData.summary as any)?.last_updated || Date.now()).toLocaleString()}
              </span>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Period:</span>
                <Badge variant="outline">{selectedPeriod}</Badge>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Comparison:</span>
                <Badge variant="outline">{comparisonMode}</Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
