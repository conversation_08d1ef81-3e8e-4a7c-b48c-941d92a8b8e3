'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import {
  RefreshCw,
  Search,
  Download,
  Upload,
  Activity,
  Users
} from 'lucide-react';

interface MSME {
  id: string;
  name: string;
  sector: string;
  risk_level: string;
  last_refresh: string;
  data_sources: string[];
  refresh_status: 'current' | 'stale' | 'outdated';
}

interface BulkRefreshJob {
  job_id: string;
  msme_count: number;
  data_sources: string[];
  priority: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  progress: number;
  started_at?: string;
  completed_at?: string;
  estimated_completion?: string;
}

interface BulkRefreshManagerProps {
  onRefreshScheduled?: (jobId: string) => void;
}

export default function BulkRefreshManager({ onRefreshScheduled }: BulkRefreshManagerProps) {
  const [msmes, setMsmes] = useState<MSME[]>([]);
  const [selectedMsmes, setSelectedMsmes] = useState<string[]>([]);
  const [selectedDataSources, setSelectedDataSources] = useState<string[]>([]);
  const [selectedPriority, setSelectedPriority] = useState('MEDIUM');
  const [filterSector, setFilterSector] = useState('all');
  const [filterRisk, setFilterRisk] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [bulkJobs, setBulkJobs] = useState<BulkRefreshJob[]>([]);
  const [loading, setLoading] = useState(true);

  const dataSources = [
    { id: 'GST', name: 'GST Data', description: 'GST turnover and compliance' },
    { id: 'ACCOUNT_AGGREGATOR', name: 'Banking Data', description: 'Bank statements and transactions' },
    { id: 'UDYAM', name: 'Udyam Registration', description: 'MSME registration data' },
    { id: 'CASH_FLOW', name: 'Cash Flow', description: 'Cash flow analysis' },
    { id: 'FINANCIAL_METRICS', name: 'Financial Metrics', description: 'Financial ratios and metrics' },
    { id: 'COMPLIANCE', name: 'Compliance', description: 'Compliance and audit data' }
  ];

  useEffect(() => {
    fetchMsmes();
    fetchBulkJobs();
  }, []);

  const fetchMsmes = async () => {
    try {
      setLoading(true);
      
      // Mock MSME data
      const mockMsmes: MSME[] = [
        {
          id: 'MSME_001',
          name: 'Tech Solutions Pvt Ltd',
          sector: 'Technology',
          risk_level: 'LOW',
          last_refresh: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          data_sources: ['GST', 'ACCOUNT_AGGREGATOR', 'FINANCIAL_METRICS'],
          refresh_status: 'current'
        },
        {
          id: 'MSME_002',
          name: 'Manufacturing Co',
          sector: 'Manufacturing',
          risk_level: 'MEDIUM',
          last_refresh: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
          data_sources: ['GST', 'UDYAM', 'CASH_FLOW'],
          refresh_status: 'stale'
        },
        {
          id: 'MSME_003',
          name: 'Trading Enterprise',
          sector: 'Trading',
          risk_level: 'HIGH',
          last_refresh: new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString(),
          data_sources: ['GST', 'COMPLIANCE'],
          refresh_status: 'outdated'
        },
        {
          id: 'MSME_004',
          name: 'Service Provider Ltd',
          sector: 'Services',
          risk_level: 'LOW',
          last_refresh: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          data_sources: ['ACCOUNT_AGGREGATOR', 'FINANCIAL_METRICS', 'COMPLIANCE'],
          refresh_status: 'current'
        },
        {
          id: 'MSME_005',
          name: 'Agriculture Corp',
          sector: 'Agriculture',
          risk_level: 'MEDIUM',
          last_refresh: new Date(Date.now() - 30 * 60 * 60 * 1000).toISOString(),
          data_sources: ['GST', 'UDYAM', 'CASH_FLOW', 'FINANCIAL_METRICS'],
          refresh_status: 'outdated'
        }
      ];
      
      setMsmes(mockMsmes);
    } catch (error) {
      console.error('Error fetching MSMEs:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchBulkJobs = async () => {
    try {
      // Mock bulk job data
      const mockJobs: BulkRefreshJob[] = [
        {
          job_id: 'BULK_001',
          msme_count: 25,
          data_sources: ['GST', 'FINANCIAL_METRICS'],
          priority: 'HIGH',
          status: 'IN_PROGRESS',
          progress: 68,
          started_at: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
          estimated_completion: new Date(Date.now() + 10 * 60 * 1000).toISOString()
        },
        {
          job_id: 'BULK_002',
          msme_count: 12,
          data_sources: ['ACCOUNT_AGGREGATOR'],
          priority: 'MEDIUM',
          status: 'COMPLETED',
          progress: 100,
          started_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          completed_at: new Date(Date.now() - 1.5 * 60 * 60 * 1000).toISOString()
        }
      ];
      
      setBulkJobs(mockJobs);
    } catch (error) {
      console.error('Error fetching bulk jobs:', error);
    }
  };

  const filteredMsmes = msmes.filter(msme => {
    const matchesSearch = searchTerm === '' || 
      msme.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      msme.id.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesSector = filterSector === 'all' || msme.sector === filterSector;
    const matchesRisk = filterRisk === 'all' || msme.risk_level === filterRisk;
    const matchesStatus = filterStatus === 'all' || msme.refresh_status === filterStatus;
    
    return matchesSearch && matchesSector && matchesRisk && matchesStatus;
  });

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedMsmes(filteredMsmes.map(msme => msme.id));
    } else {
      setSelectedMsmes([]);
    }
  };

  const handleSelectMsme = (msmeId: string, checked: boolean) => {
    if (checked) {
      setSelectedMsmes(prev => [...prev, msmeId]);
    } else {
      setSelectedMsmes(prev => prev.filter(id => id !== msmeId));
    }
  };

  const handleDataSourceToggle = (sourceId: string, checked: boolean) => {
    if (checked) {
      setSelectedDataSources(prev => [...prev, sourceId]);
    } else {
      setSelectedDataSources(prev => prev.filter(id => id !== sourceId));
    }
  };

  const scheduleBulkRefresh = async () => {
    if (selectedMsmes.length === 0 || selectedDataSources.length === 0) {
      return;
    }

    try {
      const jobId = `BULK_${Date.now()}`;
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newJob: BulkRefreshJob = {
        job_id: jobId,
        msme_count: selectedMsmes.length,
        data_sources: selectedDataSources,
        priority: selectedPriority,
        status: 'PENDING',
        progress: 0,
        estimated_completion: new Date(Date.now() + selectedMsmes.length * 2 * 60 * 1000).toISOString()
      };
      
      setBulkJobs(prev => [newJob, ...prev]);
      
      // Clear selections
      setSelectedMsmes([]);
      setSelectedDataSources([]);
      
      if (onRefreshScheduled) {
        onRefreshScheduled(jobId);
      }
      
      // Simulate job progression
      setTimeout(() => {
        setBulkJobs(prev => prev.map(job => 
          job.job_id === jobId 
            ? { ...job, status: 'IN_PROGRESS' as const, started_at: new Date().toISOString() }
            : job
        ));
      }, 2000);
      
    } catch (error) {
      console.error('Error scheduling bulk refresh:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'current':
        return <Badge variant="default" className="bg-green-100 text-green-800">Current</Badge>;
      case 'stale':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Stale</Badge>;
      case 'outdated':
        return <Badge variant="destructive">Outdated</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getRiskBadge = (risk: string) => {
    switch (risk) {
      case 'LOW':
        return <Badge variant="default" className="bg-green-100 text-green-800">Low</Badge>;
      case 'MEDIUM':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Medium</Badge>;
      case 'HIGH':
        return <Badge variant="destructive">High</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getJobStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'IN_PROGRESS':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">In Progress</Badge>;
      case 'COMPLETED':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
      case 'FAILED':
        return <Badge variant="destructive">Failed</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const uniqueSectors = [...new Set(msmes.map(msme => msme.sector))];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Bulk Refresh Manager</h3>
          <p className="text-muted-foreground">Schedule data refresh for multiple MSMEs</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => console.log('Import MSMEs')}>
            <Upload className="h-4 w-4 mr-2" />
            Import
          </Button>
          <Button variant="outline" onClick={() => console.log('Export results')}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Active Bulk Jobs */}
      {bulkJobs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Active Bulk Jobs
            </CardTitle>
            <CardDescription>Currently running bulk refresh operations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {bulkJobs.map((job) => (
                <div key={job.job_id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium">{job.job_id}</span>
                      {getJobStatusBadge(job.status)}
                      <Badge variant="outline">{job.priority}</Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {job.msme_count} MSMEs • {job.data_sources.join(', ')}
                    </div>
                    {job.status === 'IN_PROGRESS' && (
                      <div className="mt-2">
                        <div className="flex items-center justify-between text-xs mb-1">
                          <span>Progress</span>
                          <span>{job.progress}%</span>
                        </div>
                        <Progress value={job.progress} className="h-2" />
                      </div>
                    )}
                  </div>
                  <div className="text-right text-sm text-muted-foreground">
                    {job.status === 'IN_PROGRESS' && job.estimated_completion && (
                      <div>ETA: {formatTimeAgo(job.estimated_completion)}</div>
                    )}
                    {job.completed_at && (
                      <div>Completed: {formatTimeAgo(job.completed_at)}</div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bulk Refresh Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Schedule Bulk Refresh</CardTitle>
          <CardDescription>Select MSMEs and data sources for bulk refresh</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Data Source Selection */}
          <div className="space-y-4">
            <h4 className="font-medium">Select Data Sources</h4>
            <div className="grid gap-3 md:grid-cols-2">
              {dataSources.map((source) => (
                <div key={source.id} className="flex items-center space-x-2">
                  <Checkbox
                    id={source.id}
                    checked={selectedDataSources.includes(source.id)}
                    onCheckedChange={(checked) => handleDataSourceToggle(source.id, checked as boolean)}
                  />
                  <div className="flex-1">
                    <label htmlFor={source.id} className="text-sm font-medium cursor-pointer">
                      {source.name}
                    </label>
                    <p className="text-xs text-muted-foreground">{source.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Priority Selection */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Priority</label>
            <Select value={selectedPriority} onValueChange={setSelectedPriority}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="LOW">Low Priority</SelectItem>
                <SelectItem value="MEDIUM">Medium Priority</SelectItem>
                <SelectItem value="HIGH">High Priority</SelectItem>
                <SelectItem value="CRITICAL">Critical Priority</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Schedule Button */}
          <div className="flex items-center gap-4">
            <Button 
              onClick={scheduleBulkRefresh}
              disabled={selectedMsmes.length === 0 || selectedDataSources.length === 0}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Schedule Refresh ({selectedMsmes.length} MSMEs)
            </Button>
            
            {selectedMsmes.length > 0 && selectedDataSources.length > 0 && (
              <div className="text-sm text-muted-foreground">
                Estimated time: {(selectedMsmes.length * selectedDataSources.length * 2).toFixed(0)} minutes
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* MSME Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            MSME Selection
          </CardTitle>
          <CardDescription>
            Select MSMEs for bulk refresh ({selectedMsmes.length} of {filteredMsmes.length} selected)
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Filters */}
          <div className="grid gap-4 md:grid-cols-5">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search MSMEs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            
            <Select value={filterSector} onValueChange={setFilterSector}>
              <SelectTrigger>
                <SelectValue placeholder="All Sectors" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sectors</SelectItem>
                {uniqueSectors.map(sector => (
                  <SelectItem key={sector} value={sector}>{sector}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={filterRisk} onValueChange={setFilterRisk}>
              <SelectTrigger>
                <SelectValue placeholder="All Risk Levels" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Risk Levels</SelectItem>
                <SelectItem value="LOW">Low Risk</SelectItem>
                <SelectItem value="MEDIUM">Medium Risk</SelectItem>
                <SelectItem value="HIGH">High Risk</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger>
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="current">Current</SelectItem>
                <SelectItem value="stale">Stale</SelectItem>
                <SelectItem value="outdated">Outdated</SelectItem>
              </SelectContent>
            </Select>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="select-all"
                checked={selectedMsmes.length === filteredMsmes.length && filteredMsmes.length > 0}
                onCheckedChange={handleSelectAll}
              />
              <label htmlFor="select-all" className="text-sm font-medium cursor-pointer">
                Select All
              </label>
            </div>
          </div>

          {/* MSME List */}
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {filteredMsmes.map((msme) => (
              <div key={msme.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50">
                <div className="flex items-center space-x-3">
                  <Checkbox
                    checked={selectedMsmes.includes(msme.id)}
                    onCheckedChange={(checked) => handleSelectMsme(msme.id, checked as boolean)}
                  />
                  <div className="flex-1">
                    <div className="font-medium text-sm">{msme.name}</div>
                    <div className="text-xs text-muted-foreground">{msme.id} • {msme.sector}</div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  {getRiskBadge(msme.risk_level)}
                  {getStatusBadge(msme.refresh_status)}
                  <div className="text-xs text-muted-foreground">
                    {formatTimeAgo(msme.last_refresh)}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
