/**
 * Comprehensive TypeScript type definitions for Credit Chakra application.
 *
 * This module provides type safety and consistency across all components,
 * supporting portfolio management, risk assessment, and analytics features.
 *
 * @fileoverview Type definitions for MSME credit scoring and monitoring platform
 * <AUTHOR> Chakra Team
 * @version 1.0.0
 */

import React from 'react';

// Base types
export type RiskBand = 'green' | 'yellow' | 'red';
export type BusinessType = 'retail' | 'manufacturing' | 'services' | 'b2b';
export type ScoreTrend = 'improving' | 'stable' | 'declining';
export type SignalSource = 'gst' | 'upi' | 'bank' | 'social' | 'manual' | 'reviews' | 'justdial' | 'instagram' | 'maps' | 'website';

export interface MSME {
  msme_id: string;
  name: string;
  business_type: BusinessType;
  location: string;
  current_score: number;
  risk_band: RiskBand;
  score_trend?: ScoreTrend;
  signals_count: number;
  recent_nudges: number;
  last_signal_date: string;
  created_at: string;
  last_updated?: string; // Last update timestamp
  tags: string[];
  // Enhanced fields for banking/financial industry standards
  gst_compliance?: number; // GST compliance score (0-100)
  banking_health?: number; // Banking health score (0-100)
  monthly_turnover?: number; // Monthly GST turnover
  digital_score?: number; // Digital payment adoption score (0-100)
  gst_number?: string; // GST registration number
}

export interface Analytics {
  total_msmes: number;
  total_signals: number;
  triggered_escalations: number;
  risk_distribution: {
    green: number;
    yellow: number;
    red: number;
  };
  business_type_distribution: {
    [key: string]: number;
  };
  average_signals_per_msme: number;
  last_updated: string;
}

export interface ScoreDetails {
  msme_id: string;
  msme_name: string;
  current_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  score_breakdown: {
    base_score: number;
    gst_penalty: number;
    reviews_penalty: number;
    upi_penalty: number;
    details: {
      [key: string]: string;
    };
  };
  signals_count: number;
  last_updated: string;
}

// SignalSource type already defined above

export interface Signal {
  signal_id: string;
  msme_id: string;
  source: SignalSource;
  value: string | number | Record<string, unknown>; // Raw signal value (could be number, string, dict)
  normalized: number; // Normalized score 0-1
  timestamp: string;
  metadata: Record<string, unknown>;
}

export interface SignalInput {
  source: SignalSource;
  value: string | number | Record<string, unknown>;
  metadata?: Record<string, unknown>;
  timestamp?: string;
}

export interface Nudge {
  nudge_id: string;
  msme_id: string;
  trigger_type: 'score_drop' | 'manual' | 'risk_alert';
  message: string;
  medium: 'whatsapp' | 'email' | 'sms';
  sent_at: string;
  status: 'sent' | 'delivered' | 'failed';
  metadata: Record<string, unknown>;
}

export interface NudgeRequest {
  trigger_type: 'score_drop' | 'manual' | 'risk_alert';
  message: string;
  medium: 'whatsapp' | 'email' | 'sms';
  metadata?: Record<string, unknown>;
}

// Enhanced scoring system interfaces
export interface ScoreParameter {
  name: string;
  current_score: number;
  weight_percentage: number;
  penalty_impact: number;
  risk_level: 'low' | 'medium' | 'high';
  trend: 'improving' | 'declining' | 'stable';
  data_points: string[];
  methodology: string;
  last_updated: string;
}

export interface EnhancedScoreDetails {
  msme_id: string;
  msme_name: string;
  current_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  parameters: {
    gst_compliance: ScoreParameter;
    banking_health: ScoreParameter;
    digital_payment_adoption: ScoreParameter;
    business_legitimacy: ScoreParameter;
    market_reputation: ScoreParameter;
    financial_stability: ScoreParameter;
    industry_risk_factor: ScoreParameter;
    geographic_risk: ScoreParameter;
    operational_maturity: ScoreParameter;
    compliance_history: ScoreParameter;
  };
  total_penalty_impact: number;
  signals_count: number;
  last_updated: string;
}

// Top Actions and Opportunities interfaces
export interface TopAction {
  id: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  type: 'risk_reduction' | 'ltv_optimization' | 'portfolio_growth' | 'efficiency';
  msme_count: number;
  potential_value: string;
  action_items: string[];
  priority_score: number;
  estimated_completion_days: number;
}

export interface PortfolioInsights {
  total_msmes: number;
  risk_distribution: {
    green: number;
    yellow: number;
    red: number;
  };
  top_actions: TopAction[];
  portfolio_health_score: number;
  diversification_index: number;
  data_completeness_score: number;
  automation_opportunities: number;
}

// Enhanced AI Copilot Types
export interface CopilotQuery {
  query: string;
  context?: {
    conversation_history?: Message[];
    user_intent?: string;
    portfolio_summary?: string;
    timestamp?: string;
  };
}

export interface CopilotResponse {
  query_id: string;
  response: string;
  confidence: number;
  reasoning?: string;
  data?: Record<string, unknown>;
  timestamp: string;
  metadata?: {
    model?: string;
    tokens?: number;
    provider?: string;
    processing_time?: number;
  };
}

export interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  data?: Record<string, unknown>;
}

export interface CopilotInsight {
  id: string;
  type: 'alert' | 'trend' | 'compliance' | 'geographic' | 'behavioral';
  severity: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  timestamp: Date;
  actionable: boolean;
  dismissed?: boolean;
  priority_score?: number;
  exposure_amount?: number;
  metadata?: Record<string, unknown>;
}

export interface QuickAction {
  title: string;
  query: string;
  icon: React.ComponentType<{ className?: string }>;
  count?: string;
  priority: 'high' | 'medium' | 'low';
  category?: 'risk' | 'compliance' | 'analytics' | 'operations';
}

// Advanced Risk Modeling Types
export interface RiskPrediction {
  msme_id: string;
  probability_of_default: number;
  risk_score: number;
  risk_band: 'green' | 'yellow' | 'red';
  confidence_interval: [number, number];
  feature_importance: Record<string, number>;
  model_version: string;
  prediction_timestamp: string;
  explanation: Record<string, unknown>;
}

export interface BehaviorProfile {
  msme_id: string;
  behavior_score: number;
  risk_indicators: string[];
  patterns: BehaviorPattern[];
  recommendations: string[];
  last_updated: string;
}

export interface BehaviorPattern {
  pattern_type: string;
  confidence: number;
  description: string;
  impact_score: number;
  trend: 'improving' | 'declining' | 'stable';
}

// Compliance and Monitoring Types
export interface ComplianceHealth {
  overall_score: number;
  overdue_tasks: number;
  upcoming_deadlines: number;
  auto_completion_rate: number;
  critical_alerts: number;
  last_updated: string;
}

export interface RiskEvent {
  id: string;
  msme_id: string;
  event_type: string;
  severity: 'high' | 'medium' | 'low';
  description: string;
  impact_score: number;
  timestamp: string;
  metadata: Record<string, unknown>;
}

// Portfolio Analytics Types
export interface PortfolioMetrics {
  total_exposure: number;
  portfolio_health_score: number;
  npa_ratio: number;
  average_score: number;
  risk_adjusted_return: number;
  diversification_score: number;
  stress_test_results: StressTestResult[];
}

export interface StressTestResult {
  scenario: string;
  impact_score: number;
  affected_msmes: number;
  potential_loss: number;
  mitigation_strategies: string[];
}

// Additional comprehensive types for optimization
export interface TopAction {
  id: string;
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high';
  type: 'risk_reduction' | 'ltv_optimization' | 'portfolio_growth' | 'efficiency';
  msme_count: number;
  potential_value: string;
  action_items: string[];
  priority_score: number;
  estimated_completion_days: number;
}

// Pagination interfaces
export interface PaginationParams {
  page: number;
  limit: number;
  offset: number;
}

export interface PaginationInfo {
  current_page: number;
  per_page: number;
  total_pages: number;
  total_count: number;
  has_next: boolean;
  has_prev: boolean;
  next_page: number | null;
  prev_page: number | null;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationInfo;
  filters: Record<string, unknown>;
  total_count: number;
}

// Filter interfaces
export interface MSMEFilters {
  risk_band?: RiskBand;
  business_type?: BusinessType;
  location?: string;
  score_min?: number;
  score_max?: number;
  gst_compliance_min?: number;
  banking_health_min?: number;
  search_term?: string;
  created_after?: string;
  created_before?: string;
}

export interface SortParams {
  field: string;
  direction: 'asc' | 'desc';
}

// Risk monitoring interfaces
export interface RiskAlert {
  id: string;
  msme_id: string;
  msme_name: string;
  type: 'score_drop' | 'high_risk' | 'no_activity' | 'signal_anomaly' | 'sma_progression' | 'payment_delay' | 'compliance_breach' | 'exposure_limit' | 'sector_concentration' | 'geographic_concentration' | 'data_anomaly';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: string;
  acknowledged: boolean;
  acknowledged_by?: string;
  acknowledged_at?: string;
  resolved?: boolean;
  resolved_at?: string;
  dpd_classification?: 'standard' | 'sma_0' | 'sma_1' | 'sma_2' | 'npa';
  days_past_due?: number;
  impact_score?: number;
  exposure_amount?: number;
  metadata?: Record<string, unknown>;
}

export interface SMAData {
  msme_id: string;
  msme_name: string;
  business_type: BusinessType;
  current_classification: 'standard' | 'sma_0' | 'sma_1' | 'sma_2' | 'npa';
  days_past_due: number;
  outstanding_amount: number;
  last_payment_date: string;
  classification_history: Array<{
    classification: string;
    date: string;
    days_past_due: number;
  }>;
  stress_indicators: string[];
  early_warning_signals: string[];
  rbi_reporting_required: boolean;
  provision_required: number;
  last_updated: string;
  next_review_date: string;
}

// Real-time WebSocket interfaces
export interface WebSocketMessage {
  message_id: string;
  message_type: 'risk_event' | 'portfolio_metrics' | 'alert' | 'sma_progression' | 'connection_status';
  data: Record<string, unknown>;
  timestamp: string;
  client_id?: string;
}

export interface PortfolioRiskMetrics {
  total_msmes: number;
  total_exposure: number;
  avg_risk_score: number;
  risk_distribution: {
    green: number;
    yellow: number;
    red: number;
  };
  sma_distribution: {
    standard: number;
    sma_0: number;
    sma_1: number;
    sma_2: number;
    npa: number;
  };
  npa_ratio: number;
  sma_ratio: number;
  portfolio_health_score: number;
  concentration_metrics: Record<string, number>;
  sector_concentration: Record<string, number>;
  geographic_concentration: Record<string, number>;
  trend_indicators: Record<string, number>;
  score_trend_30d: number;
  npa_trend_30d: number;
  last_updated: string;
  calculation_date: string;
}

export interface RiskHeatmapData {
  geographic_risk: Record<string, number>;
  sector_risk: Record<string, number>;
  exposure_concentration: Record<string, number>;
  time_series_risk: Array<{
    date: string;
    risk_score: number;
    npa_ratio: number;
    sma_ratio: number;
  }>;
  risk_correlations: Record<string, Record<string, number>>;
  generated_at: string;
  data_as_of: string;
}

export interface ComplianceStatus {
  msme_id: string;
  sma_reporting_compliant: boolean;
  provision_adequate: boolean;
  exposure_limit_compliant: boolean;
  documentation_complete: boolean;
  overall_compliance_score: number;
  compliance_issues: string[];
  remediation_actions: string[];
  last_audit_date?: string;
  next_audit_date?: string;
  auditor?: string;
  assessed_at: string;
  valid_until: string;
}

// Component prop interfaces
export interface RiskCardProps {
  riskLevel: RiskBand;
  count: number;
  totalCount: number;
  title?: string;
  showProgress?: boolean;
  showTrend?: boolean;
  trendDirection?: 'up' | 'down' | 'stable';
  trendValue?: number;
  className?: string;
  onClick?: () => void;
}

export interface RiskDistributionProps {
  riskDistribution: {
    green: number;
    yellow: number;
    red: number;
  };
  totalCount: number;
  showTrends?: boolean;
  trends?: {
    green?: number;
    yellow?: number;
    red?: number;
  };
  onRiskClick?: (riskLevel: RiskBand) => void;
  className?: string;
}

// Hook return types
export interface UseOptimizedPortfolioReturn {
  msmes: MSME[];
  analytics: Analytics | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  currentPage: number;
  totalPages: number;
  paginatedMsmes: MSME[];
  setCurrentPage: (page: number) => void;
  filteredMsmes: MSME[];
  setFilter: (filter: Partial<MSME>) => void;
  clearFilter: () => void;
}

// API response interfaces
export interface APIResponse<T = unknown> {
  success: boolean;
  message: string;
  data: T;
  timestamp: string;
}

export interface APIError {
  success: false;
  error: string;
  message: string;
  details: Record<string, unknown>;
  timestamp: string;
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// All types are exported directly for easier imports
// Use: import { MSME, Analytics, Signal } from '@/types'
