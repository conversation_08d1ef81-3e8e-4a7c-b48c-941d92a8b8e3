# Credit Chakra Backend

FastAPI-based backend for the Credit Chakra MSME credit scoring platform.

## Features

- **MSME Profile Management**: Create and manage MSME business profiles
- **Signal Processing**: Collect and normalize signals from various sources (GST, UPI, Reviews, etc.)
- **Credit Scoring**: Real-time credit score calculation based on multiple data sources
- **Nudge System**: Automated notifications via WhatsApp, Email, SMS
- **Firebase Integration**: Firestore for data storage and Firebase Auth ready

## Project Structure

```
backend/
├── main.py                 # FastAPI application entry point
├── requirements.txt        # Python dependencies
├── .env.example           # Environment variables template
├── models/                # Pydantic data models
│   ├── msme.py           # MSME profile models
│   ├── signal.py         # Signal data models
│   └── nudge.py          # Nudge/notification models
├── routes/               # API route handlers
│   ├── msme.py          # MSME CRUD operations
│   ├── signals.py       # Signal management
│   └── nudges.py        # Nudge operations
├── services/            # Business logic
│   └── scoring.py       # Credit scoring algorithms
├── firebase/            # Firebase integration
│   └── init.py         # Firebase client initialization
└── test_api.py         # API testing script
```

## Setup

### 1. Install Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 2. Environment Configuration

Copy the example environment file and configure:

```bash
cp .env.example .env
```

Edit `.env` with your Firebase credentials:

```env
FIREBASE_CREDENTIALS_PATH=path/to/your/firebase-service-account.json
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001
```

### 3. Firebase Setup

1. Create a Firebase project at https://console.firebase.google.com
2. Enable Firestore Database
3. Generate a service account key
4. Download the JSON file and update `FIREBASE_CREDENTIALS_PATH` in `.env`

### 4. Run the Application

```bash
# Development mode
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Or using Python
python main.py
```

The API will be available at `http://localhost:8000`

## API Endpoints

### Health Check
- `GET /` - Root endpoint
- `GET /health` - Health check

### MSME Management
- `POST /api/msme/create` - Create new MSME profile
- `GET /api/msme/{msme_id}` - Get MSME profile
- `GET /api/msme/` - List all MSMEs (with pagination)
- `PUT /api/msme/{msme_id}` - Update MSME profile

### Signal Management
- `POST /api/signals/add` - Add new signal
- `GET /api/signals/{msme_id}` - Get signals for MSME
- `GET /api/signals/{msme_id}/{signal_id}` - Get specific signal

### Nudge Management
- `POST /api/nudges/send` - Send nudge/notification
- `GET /api/nudges/{msme_id}` - Get nudges for MSME
- `PUT /api/nudges/{msme_id}/{nudge_id}` - Update nudge status

## Testing

Run the test script to verify all endpoints:

```bash
python test_api.py
```

## API Documentation

Once the server is running, visit:
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Data Models

### MSME Profile
- Basic business information
- Credit score (0-1000)
- Risk band (green/yellow/red)
- Business tags and metadata

### Signals
- Source-specific data (GST, UPI, Reviews, etc.)
- Raw and normalized values
- Automatic score recalculation

### Nudges
- Trigger-based notifications
- Multi-channel delivery (WhatsApp, Email, SMS)
- Status tracking

## Scoring Algorithm

The credit scoring system:
1. Collects signals from multiple sources
2. Normalizes each signal to 0-1 scale
3. Applies weighted scoring based on signal importance
4. Calculates final score (0-1000)
5. Determines risk band based on score thresholds

### Signal Weights
- GST: 30%
- UPI: 25%
- Reviews: 20%
- JustDial: 10%
- Instagram: 10%
- Maps: 5%
