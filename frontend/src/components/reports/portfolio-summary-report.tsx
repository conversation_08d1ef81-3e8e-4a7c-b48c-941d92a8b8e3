'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Building2, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle, 
  Download,
  RefreshCw,
  BarChart3,
  PieChart
} from 'lucide-react';
import { Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, <PERSON><PERSON><PERSON> as RechartsPie<PERSON>hart } from 'recharts';
import { api } from '@/lib/api';

interface PortfolioSummaryData {
  metadata: {
    report_id: string;
    generated_at: string;
    generated_by: string;
    reporting_period_start: string;
    reporting_period_end: string;
    total_records: number;
  };
  total_msmes: number;
  active_msmes: number;
  inactive_msmes: number;
  risk_distribution: {
    high_risk_count: number;
    high_risk_percentage: number;
    medium_risk_count: number;
    medium_risk_percentage: number;
    low_risk_count: number;
    low_risk_percentage: number;
  };
  sma_accounts_summary: {
    standard_accounts: number;
    sma_0_count: number;
    sma_1_count: number;
    sma_2_count: number;
    total_overdue_amount: number;
    provision_required: number;
  };
  business_type_distribution: {
    retail_count: number;
    retail_percentage: number;
    manufacturing_count: number;
    manufacturing_percentage: number;
    services_count: number;
    services_percentage: number;
    b2b_count: number;
    b2b_percentage: number;
  };
  overall_portfolio_health_score: number;
  average_credit_score: number;
  portfolio_growth_rate: number;
  key_insights: string[];
  recommendations: string[];
}

interface PortfolioSummaryReportProps {
  startDate: string;
  endDate: string;
  onExport?: (format: 'pdf' | 'csv') => void;
}

export function PortfolioSummaryReport({ startDate, endDate, onExport }: PortfolioSummaryReportProps) {
  const [data, setData] = useState<PortfolioSummaryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchReportData();
  }, [startDate, endDate]);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Call the actual API
      const reportData = await api.generatePortfolioSummaryReport(
        startDate,
        endDate,
        'dashboard_user'
      );

      setData(reportData as unknown as PortfolioSummaryData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load report');
      console.error('Error fetching portfolio summary report:', err);
    } finally {
      setLoading(false);
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'high': return '#ef4444'; // red-500
      case 'medium': return '#f59e0b'; // amber-500
      case 'low': return '#10b981'; // emerald-500
      default: return '#6b7280'; // gray-500
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return 'text-emerald-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getHealthScoreBadge = (score: number) => {
    if (score >= 80) return { label: 'Excellent', variant: 'default' as const, className: 'bg-emerald-500' };
    if (score >= 60) return { label: 'Good', variant: 'secondary' as const, className: 'bg-yellow-500' };
    return { label: 'Needs Attention', variant: 'destructive' as const, className: 'bg-red-500' };
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid gap-6 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-20" />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          <Skeleton className="h-64" />
          <Skeleton className="h-64" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-6">
          <div className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            <span className="font-medium">Error loading report</span>
          </div>
          <p className="text-sm text-red-600 mt-2">{error}</p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={fetchReportData}
            className="mt-4"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return null;
  }

  // Prepare chart data
  const riskDistributionData = [
    { name: 'Low Risk', value: data.risk_distribution.low_risk_count, color: getRiskColor('low') },
    { name: 'Medium Risk', value: data.risk_distribution.medium_risk_count, color: getRiskColor('medium') },
    { name: 'High Risk', value: data.risk_distribution.high_risk_count, color: getRiskColor('high') }
  ];

  const businessTypeData = [
    { name: 'Retail', count: data.business_type_distribution.retail_count, percentage: data.business_type_distribution.retail_percentage },
    { name: 'Manufacturing', count: data.business_type_distribution.manufacturing_count, percentage: data.business_type_distribution.manufacturing_percentage },
    { name: 'Services', count: data.business_type_distribution.services_count, percentage: data.business_type_distribution.services_percentage },
    { name: 'B2B', count: data.business_type_distribution.b2b_count, percentage: data.business_type_distribution.b2b_percentage }
  ];

  const healthBadge = getHealthScoreBadge(data.overall_portfolio_health_score);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Portfolio Summary Report</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Period: {new Date(data.metadata.reporting_period_start).toLocaleDateString()} - {new Date(data.metadata.reporting_period_end).toLocaleDateString()}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => onExport?.('csv')}>
            <Download className="h-4 w-4 mr-2" />
            CSV
          </Button>
          <Button size="sm" onClick={() => onExport?.('pdf')} className="bg-emerald-500 hover:bg-emerald-600">
            <Download className="h-4 w-4 mr-2" />
            PDF
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="border-0 bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950 dark:to-emerald-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-emerald-700 dark:text-emerald-300">Total MSMEs</CardTitle>
            <Building2 className="h-4 w-4 text-emerald-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-900 dark:text-emerald-100">{data.total_msmes}</div>
            <p className="text-xs text-emerald-600 dark:text-emerald-400">
              {data.active_msmes} active, {data.inactive_msmes} inactive
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">Avg Credit Score</CardTitle>
            <BarChart3 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">{data.average_credit_score.toFixed(0)}</div>
            <p className="text-xs text-blue-600 dark:text-blue-400">
              Portfolio average
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">Portfolio Health</CardTitle>
            <CheckCircle className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getHealthScoreColor(data.overall_portfolio_health_score)}`}>
              {data.overall_portfolio_health_score.toFixed(1)}%
            </div>
            <Badge variant={healthBadge.variant} className={`text-xs ${healthBadge.className} text-white`}>
              {healthBadge.label}
            </Badge>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700 dark:text-orange-300">Growth Rate</CardTitle>
            {data.portfolio_growth_rate >= 0 ? (
              <TrendingUp className="h-4 w-4 text-orange-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-orange-600" />
            )}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">
              {data.portfolio_growth_rate >= 0 ? '+' : ''}{data.portfolio_growth_rate.toFixed(1)}%
            </div>
            <p className="text-xs text-orange-600 dark:text-orange-400">
              Year over year
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Row */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Risk Distribution Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5 text-emerald-600" />
              Risk Distribution
            </CardTitle>
            <CardDescription>Portfolio risk breakdown by MSME count</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <RechartsPieChart>
                  <Pie
                    data={riskDistributionData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {riskDistributionData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </RechartsPieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Business Type Distribution Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-emerald-600" />
              Business Type Distribution
            </CardTitle>
            <CardDescription>MSME count by business category</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={businessTypeData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="count" fill="#10b981" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* SMA Accounts Summary Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-emerald-600" />
            SMA Classification Summary
          </CardTitle>
          <CardDescription>Special Mention Account classification as per RBI guidelines</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Classification</TableHead>
                <TableHead className="text-right">Count</TableHead>
                <TableHead className="text-right">Percentage</TableHead>
                <TableHead className="text-right">Amount (₹)</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow>
                <TableCell className="font-medium">Standard Accounts</TableCell>
                <TableCell className="text-right">{data.sma_accounts_summary.standard_accounts}</TableCell>
                <TableCell className="text-right">
                  {((data.sma_accounts_summary.standard_accounts / data.total_msmes) * 100).toFixed(1)}%
                </TableCell>
                <TableCell className="text-right">-</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">SMA-0 (1-30 days)</TableCell>
                <TableCell className="text-right">{data.sma_accounts_summary.sma_0_count}</TableCell>
                <TableCell className="text-right">
                  {((data.sma_accounts_summary.sma_0_count / data.total_msmes) * 100).toFixed(1)}%
                </TableCell>
                <TableCell className="text-right">-</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">SMA-1 (31-60 days)</TableCell>
                <TableCell className="text-right">{data.sma_accounts_summary.sma_1_count}</TableCell>
                <TableCell className="text-right">
                  {((data.sma_accounts_summary.sma_1_count / data.total_msmes) * 100).toFixed(1)}%
                </TableCell>
                <TableCell className="text-right">-</TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">SMA-2 (61-90 days)</TableCell>
                <TableCell className="text-right">{data.sma_accounts_summary.sma_2_count}</TableCell>
                <TableCell className="text-right">
                  {((data.sma_accounts_summary.sma_2_count / data.total_msmes) * 100).toFixed(1)}%
                </TableCell>
                <TableCell className="text-right">-</TableCell>
              </TableRow>
              <TableRow className="border-t-2">
                <TableCell className="font-medium">Total Overdue</TableCell>
                <TableCell className="text-right">-</TableCell>
                <TableCell className="text-right">-</TableCell>
                <TableCell className="text-right font-medium">
                  ₹{data.sma_accounts_summary.total_overdue_amount.toLocaleString()}
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="font-medium">Provision Required</TableCell>
                <TableCell className="text-right">-</TableCell>
                <TableCell className="text-right">-</TableCell>
                <TableCell className="text-right font-medium text-red-600">
                  ₹{data.sma_accounts_summary.provision_required.toLocaleString()}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Key Insights and Recommendations */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Key Insights */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-emerald-600" />
              Key Insights
            </CardTitle>
            <CardDescription>Important findings from portfolio analysis</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.key_insights.map((insight, index) => (
                <div key={index} className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full mt-2 flex-shrink-0" />
                  <p className="text-sm text-gray-700 dark:text-gray-300">{insight}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recommendations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-emerald-600" />
              Recommendations
            </CardTitle>
            <CardDescription>Suggested actions for portfolio improvement</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.recommendations.map((recommendation, index) => (
                <div key={index} className="flex items-start gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                  <p className="text-sm text-gray-700 dark:text-gray-300">{recommendation}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Report Metadata */}
      <Card className="bg-gray-50 dark:bg-gray-800">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center gap-4">
              <span>Report ID: {data.metadata.report_id}</span>
              <span>Generated by: {data.metadata.generated_by}</span>
            </div>
            <span>Generated: {new Date(data.metadata.generated_at).toLocaleString()}</span>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
