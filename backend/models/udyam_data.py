from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from enum import Enum

class UdyamCategory(str, Enum):
    MICRO = "MICRO"
    SMALL = "SMALL"
    MEDIUM = "MEDIUM"

class BusinessActivity(str, Enum):
    MANUFACTURING = "MANUFACTURING"
    SERVICE = "SERVICE"
    TRADING = "TRADING"

class RegistrationStatus(str, Enum):
    ACTIVE = "ACTIVE"
    SUSPENDED = "SUSPENDED"
    CANCELLED = "CANCELLED"
    EXPIRED = "EXPIRED"

class UdyamRegistration(BaseModel):
    """Udyam Registration data model"""
    udyam_number: str = Field(..., description="Udyam Registration Number")
    msme_id: str = Field(..., description="MSME identifier")
    
    # Basic Information
    enterprise_name: str = Field(..., description="Name of the enterprise")
    enterprise_type: str = Field(..., description="Type of enterprise")
    date_of_incorporation: Optional[date] = Field(None, description="Date of incorporation")
    date_of_commencement: Optional[date] = Field(None, description="Date of commencement of business")
    
    # Classification
    udyam_category: UdyamCategory = Field(..., description="MSME category")
    business_activity: BusinessActivity = Field(..., description="Primary business activity")
    nic_code: str = Field(..., description="National Industrial Classification code")
    nic_description: str = Field(..., description="NIC code description")
    
    # Investment and Turnover
    investment_in_plant_machinery: float = Field(0.0, description="Investment in plant and machinery")
    investment_in_equipment: float = Field(0.0, description="Investment in equipment")
    total_investment: float = Field(0.0, description="Total investment")
    previous_year_turnover: float = Field(0.0, description="Previous year turnover")
    
    # Location Details
    state: str = Field(..., description="State")
    district: str = Field(..., description="District")
    address: str = Field(..., description="Complete address")
    pincode: str = Field(..., description="PIN code")
    
    # Contact Information
    mobile_number: str = Field(..., description="Mobile number")
    email_id: Optional[str] = Field(None, description="Email ID")
    
    # Identity Verification
    aadhaar_number: Optional[str] = Field(None, description="Aadhaar number (masked)")
    pan_number: str = Field(..., description="PAN number")
    aadhaar_verified: bool = Field(False, description="Aadhaar verification status")
    pan_verified: bool = Field(False, description="PAN verification status")
    
    # Registration Details
    registration_date: datetime = Field(..., description="Registration date")
    registration_status: RegistrationStatus = Field(..., description="Registration status")
    validity_date: Optional[datetime] = Field(None, description="Validity date")
    
    # Bank Details
    bank_name: Optional[str] = Field(None, description="Bank name")
    bank_account_number: Optional[str] = Field(None, description="Bank account number (masked)")
    ifsc_code: Optional[str] = Field(None, description="IFSC code")
    
    # Additional Information
    is_women_owned: bool = Field(False, description="Women-owned enterprise")
    is_sc_st_owned: bool = Field(False, description="SC/ST-owned enterprise")
    is_physically_handicapped: bool = Field(False, description="Physically handicapped owned")
    is_minority_owned: bool = Field(False, description="Minority-owned enterprise")
    
    # Compliance Status
    annual_return_filed: bool = Field(False, description="Annual return filing status")
    last_annual_return_date: Optional[date] = Field(None, description="Last annual return date")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class UdyamVerification(BaseModel):
    """Udyam verification result"""
    udyam_number: str = Field(..., description="Udyam Registration Number")
    verification_status: str = Field(..., description="Verification status")
    verification_date: datetime = Field(default_factory=datetime.utcnow)
    
    # Verification Results
    registration_valid: bool = Field(False, description="Registration validity")
    details_match: bool = Field(False, description="Details match with records")
    status_active: bool = Field(False, description="Status is active")
    
    # Verification Details
    name_match_score: float = Field(0.0, description="Name match score (0-100)")
    pan_match: bool = Field(False, description="PAN match status")
    aadhaar_match: bool = Field(False, description="Aadhaar match status")
    address_match_score: float = Field(0.0, description="Address match score (0-100)")
    
    # Risk Indicators
    multiple_registrations: bool = Field(False, description="Multiple registrations found")
    suspicious_activity: bool = Field(False, description="Suspicious activity detected")
    blacklisted: bool = Field(False, description="Blacklisted status")
    
    # Verification Score
    overall_verification_score: float = Field(0.0, description="Overall verification score (0-100)")
    confidence_level: str = Field("LOW", description="Confidence level (LOW/MEDIUM/HIGH)")
    
    verification_errors: List[str] = Field(default_factory=list, description="Verification errors")
    verification_warnings: List[str] = Field(default_factory=list, description="Verification warnings")

class UdyamCompliance(BaseModel):
    """Udyam compliance tracking"""
    udyam_number: str = Field(..., description="Udyam Registration Number")
    compliance_period: str = Field(..., description="Compliance period (YYYY)")
    
    # Filing Requirements
    annual_return_due_date: date = Field(..., description="Annual return due date")
    annual_return_filed: bool = Field(False, description="Annual return filed")
    annual_return_filing_date: Optional[date] = Field(None, description="Filing date")
    days_delayed: Optional[int] = Field(None, description="Days delayed in filing")
    
    # Turnover Declaration
    declared_turnover: float = Field(0.0, description="Declared turnover")
    turnover_growth: float = Field(0.0, description="Turnover growth from previous year")
    category_compliance: bool = Field(True, description="Category compliance based on turnover")
    
    # Investment Declaration
    declared_investment: float = Field(0.0, description="Declared investment")
    investment_growth: float = Field(0.0, description="Investment growth from previous year")
    investment_compliance: bool = Field(True, description="Investment compliance")
    
    # Compliance Score
    filing_compliance_score: float = Field(0.0, description="Filing compliance score (0-100)")
    data_accuracy_score: float = Field(0.0, description="Data accuracy score (0-100)")
    overall_compliance_score: float = Field(0.0, description="Overall compliance score (0-100)")
    
    # Penalties and Actions
    penalty_amount: float = Field(0.0, description="Penalty amount")
    warning_issued: bool = Field(False, description="Warning issued")
    action_taken: Optional[str] = Field(None, description="Action taken")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)

class UdyamAnalytics(BaseModel):
    """Comprehensive Udyam analytics"""
    udyam_number: str = Field(..., description="Udyam Registration Number")
    msme_id: str = Field(..., description="MSME identifier")
    
    # Registration Analytics
    registration_age_days: int = Field(0, description="Registration age in days")
    registration_validity: bool = Field(True, description="Registration validity")
    category_stability: bool = Field(True, description="Category stability over time")
    
    # Compliance Analytics
    compliance_history_score: float = Field(0.0, description="Historical compliance score")
    filing_consistency: float = Field(0.0, description="Filing consistency score")
    data_accuracy_trend: str = Field("stable", description="Data accuracy trend")
    
    # Business Growth Analytics
    turnover_cagr: float = Field(0.0, description="Turnover CAGR")
    investment_growth_rate: float = Field(0.0, description="Investment growth rate")
    business_expansion_indicator: bool = Field(False, description="Business expansion indicator")
    
    # Verification Analytics
    identity_verification_score: float = Field(0.0, description="Identity verification score")
    document_authenticity_score: float = Field(0.0, description="Document authenticity score")
    fraud_risk_score: float = Field(0.0, description="Fraud risk score (0-100, higher is riskier)")
    
    # Category Analysis
    category_appropriate: bool = Field(True, description="Category appropriateness")
    upgrade_eligible: bool = Field(False, description="Eligible for category upgrade")
    downgrade_risk: bool = Field(False, description="Risk of category downgrade")
    
    # Sector Analysis
    sector_performance_percentile: float = Field(50.0, description="Performance percentile in sector")
    sector_growth_alignment: bool = Field(True, description="Aligned with sector growth")
    
    # Risk Indicators
    registration_risk_flags: List[str] = Field(default_factory=list, description="Risk flags")
    compliance_risk_level: str = Field("LOW", description="Compliance risk level")
    authenticity_risk_level: str = Field("LOW", description="Authenticity risk level")
    
    # Overall Scores
    udyam_credibility_score: float = Field(0.0, description="Overall Udyam credibility score")
    business_legitimacy_score: float = Field(0.0, description="Business legitimacy score")
    
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    data_quality_score: float = Field(0.0, description="Data quality score")

class UdyamDataRequest(BaseModel):
    """Request model for Udyam data fetching"""
    udyam_number: str = Field(..., description="Udyam Registration Number")
    include_verification: bool = Field(True, description="Include verification")
    include_compliance: bool = Field(True, description="Include compliance data")
    include_analytics: bool = Field(True, description="Include analytics")
    verification_level: str = Field("BASIC", description="Verification level (BASIC/DETAILED)")

class UdyamDataResponse(BaseModel):
    """Response model for Udyam data"""
    udyam_number: str
    msme_id: Optional[str] = None
    
    registration: Optional[UdyamRegistration] = None
    verification: Optional[UdyamVerification] = None
    compliance: List[UdyamCompliance] = Field(default_factory=list)
    analytics: Optional[UdyamAnalytics] = None
    
    data_freshness: datetime = Field(default_factory=datetime.utcnow)
    source: str = Field("UDYAM_API", description="Data source")
    status: str = Field("success", description="Response status")
    message: Optional[str] = None

class UdyamHealthCheck(BaseModel):
    """Udyam data source health check"""
    udyam_number: str
    last_sync: datetime
    sync_status: str = Field(..., description="success/failed/partial")
    data_completeness: float = Field(0.0, description="Data completeness percentage")
    api_response_time: float = Field(0.0, description="API response time in seconds")
    verification_status: str = Field("PENDING", description="Verification status")
    compliance_status: str = Field("UNKNOWN", description="Compliance status")
    error_count: int = Field(0, description="Number of errors in last sync")
    next_sync_scheduled: datetime
