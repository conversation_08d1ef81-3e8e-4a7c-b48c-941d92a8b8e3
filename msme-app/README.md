# Credit Chakra MSME App

A comprehensive mobile application for MSMEs to access credit readiness scoring, training, government schemes, and behavioral nudges.

## 🎯 Project Overview

The Credit Chakra MSME App is a Flutter-based mobile application designed specifically for Micro, Small, and Medium Enterprises (MSMEs) to improve their credit readiness and access to financial services. The app provides real-time credit scoring, personalized nudges, training modules, and government scheme recommendations.

## 📱 MVP Phase Features

### 1. CCR Score Dashboard Module (Priority 1)
- **Real-time Credit Chakra Readiness (CCR) Score**: 0-100 scoring with 5 key subcomponents
- **Score Trends Visualization**: Historical score tracking with interactive charts
- **PDF Export**: Generate and share detailed score reports
- **Subcomponent Analysis**: Detailed breakdown of score factors

### 2. Nudges & Reminders Module (Priority 2)
- **Auto-generated Behavioral Nudges**: AI-powered recommendations for credit improvement
- **WhatsApp Integration**: Direct nudge delivery via WhatsApp
- **Time-based Triggers**: Scheduled reminders and notifications
- **Milestone-based Triggers**: Achievement-based nudge system

### 3. Training & Micro-Advisory Module (Priority 3)
- **SCORM-compliant Gamified Courses**: ZED Bronze, ESG 101, Credit Readiness
- **Bilingual Support**: Kannada and English language options
- **Quiz and Certification**: Interactive assessments with certificates
- **Progress Tracking**: Course completion and performance analytics

### 4. Government Scheme Navigator Module (Priority 4)
- **Personalized Recommendations**: Based on sector, location, and Udyam classification
- **Scheme Discovery**: Browse and search available government schemes
- **Bookmark Functionality**: Save schemes for later reference
- **Application Redirect**: Direct links to scheme application portals

## 🏗️ Architecture

### Backend (FastAPI)
- **Authentication**: Firebase Auth with phone OTP + Google sign-in
- **Database**: Firestore for real-time data storage
- **APIs**: RESTful endpoints for mobile app integration
- **Integrations**: GST API, NPCI UPI, Udyam sync
- **Compliance**: RBI digital lending guidelines

### Frontend (Flutter)
- **Platform**: Android-first development
- **State Management**: Provider/Riverpod for state management
- **Navigation**: Go Router for navigation
- **UI Framework**: Material Design 3
- **Offline Support**: Local caching and sync

### Infrastructure
- **Cloud Platform**: Google Cloud Platform
- **Database**: Firebase Firestore
- **Authentication**: Firebase Auth
- **Notifications**: Firebase Cloud Messaging
- **Storage**: Firebase Storage for documents and media

## 📁 Project Structure

```
msme-app/
│
├── backend/                 # FastAPI Backend for Mobile
│   ├── main.py             # Application entry point
│   ├── models/             # Pydantic data models
│   │   ├── user.py         # MSME user models
│   │   ├── ccr_score.py    # CCR scoring models
│   │   ├── nudge.py        # Nudge and reminder models
│   │   ├── training.py     # Training module models
│   │   └── scheme.py       # Government scheme models
│   ├── routes/             # API endpoints
│   │   ├── auth.py         # Authentication endpoints
│   │   ├── ccr.py          # CCR score endpoints
│   │   ├── nudges.py       # Nudge management
│   │   ├── training.py     # Training module APIs
│   │   └── schemes.py      # Government scheme APIs
│   ├── services/           # Business logic
│   │   ├── auth_service.py # Authentication service
│   │   ├── ccr_service.py  # CCR calculation service
│   │   ├── nudge_service.py # Nudge generation service
│   │   ├── training_service.py # Training management
│   │   └── scheme_service.py # Scheme recommendation
│   ├── firebase/           # Firebase integration
│   │   ├── init.py         # Firebase initialization
│   │   └── auth.py         # Firebase Auth helpers
│   └── requirements.txt    # Python dependencies
│
├── frontend/               # Flutter Mobile App
│   ├── lib/                # Dart source code
│   │   ├── main.dart       # App entry point
│   │   ├── models/         # Data models
│   │   ├── screens/        # UI screens
│   │   │   ├── auth/       # Authentication screens
│   │   │   ├── dashboard/  # CCR dashboard
│   │   │   ├── nudges/     # Nudges and reminders
│   │   │   ├── training/   # Training modules
│   │   │   └── schemes/    # Government schemes
│   │   ├── widgets/        # Reusable UI components
│   │   ├── services/       # API and business logic
│   │   ├── providers/      # State management
│   │   └── utils/          # Utility functions
│   ├── android/            # Android-specific code
│   ├── assets/             # Images, fonts, etc.
│   ├── pubspec.yaml        # Flutter dependencies
│   └── README.md           # Flutter app documentation
│
├── firestore/             # Database Configuration
│   ├── firestore.rules    # Security rules for mobile app
│   ├── indexes.json       # Firestore indexes
│   └── seed_data/         # Sample data for development
│       ├── users.json     # Sample MSME users
│       ├── schemes.json   # Government schemes data
│       └── courses.json   # Training courses data
│
└── deploy/                # Deployment Configuration
    ├── backend.yaml       # Backend deployment config
    ├── firebase.json      # Firebase configuration
    └── deploy.sh          # Deployment script
```

## 🚀 Getting Started

### Prerequisites
- Python 3.11+
- Flutter 3.16+
- Firebase Project
- Google Cloud Project
- Android Studio / VS Code

### Backend Setup
```bash
cd msme-app/backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn main:app --reload --host 0.0.0.0 --port 8001
```

### Frontend Setup
```bash
cd msme-app/frontend
flutter pub get
flutter run
```

## 🔧 Development

### Environment Variables
Create `.env` file in backend directory:
```env
FIREBASE_CREDENTIALS_PATH=path/to/firebase-credentials.json
WHATSAPP_API_KEY=your_whatsapp_api_key
GST_API_KEY=your_gst_api_key
UDYAM_API_KEY=your_udyam_api_key
JWT_SECRET_KEY=your_jwt_secret
DEBUG=True
```

### Firebase Configuration
1. Create Firebase project
2. Enable Authentication (Phone, Google)
3. Enable Firestore Database
4. Enable Cloud Messaging
5. Download configuration files

## 📊 Technical Specifications

### Authentication Flow
1. Phone OTP verification
2. Google Sign-in (optional)
3. JWT token generation
4. Secure API access

### CCR Score Calculation
- **GST Turnover Analysis**: 30% weight
- **UPI Transaction Diversity**: 25% weight
- **Business Profile Completeness**: 20% weight
- **Compliance Score**: 15% weight
- **Market Presence**: 10% weight

### Nudge Generation Algorithm
- Behavioral pattern analysis
- Score improvement opportunities
- Time-sensitive recommendations
- Personalized messaging

## 🔒 Security & Compliance

### Data Protection
- End-to-end encryption
- Secure API endpoints
- Firebase security rules
- GDPR compliance

### RBI Compliance
- Digital lending guidelines
- Data localization requirements
- Audit trail maintenance
- Consent management

## 📈 Monitoring & Analytics

### Performance Metrics
- App performance monitoring
- User engagement analytics
- Score improvement tracking
- Feature usage statistics

### Business Metrics
- User acquisition and retention
- Score improvement rates
- Training completion rates
- Scheme application success

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Follow coding standards
4. Add tests
5. Submit pull request

## 📄 License

Copyright © 2024 Credit Chakra. All rights reserved.
