import firebase_admin
from firebase_admin import credentials, firestore
import os
import logging
from typing import Optional, Dict, Any, List

logger = logging.getLogger(__name__)

# Global in-memory store for mock data persistence
_MOCK_DATA_STORE = {
    'msmes': {},
    'signals': {},
    'nudges': {}
}

class MockFirestoreClient:
    """Mock Firestore client for development without Firebase credentials"""

    def __init__(self):
        # Use global store for persistence across requests
        self._data = _MOCK_DATA_STORE
        logger.info("Using mock Firestore client for development")

    def collection(self, collection_name: str):
        return MockCollection(collection_name, self._data)

class MockCollection:
    def __init__(self, name: str, data: Dict):
        self.name = name
        self._data = data

    def document(self, doc_id: str):
        return MockDocument(self.name, doc_id, self._data)

    def add(self, data: Dict[str, Any]):
        doc_id = f"mock_{len(self._data.get(self.name, {}))}"
        self._data.setdefault(self.name, {})[doc_id] = data
        return None, MockDocument(self.name, doc_id, self._data)

    def stream(self):
        return [MockDocument(self.name, doc_id, self._data, doc_data)
                for doc_id, doc_data in self._data.get(self.name, {}).items()]

    def limit(self, count: int):
        """Mock limit method for pagination"""
        return self

    def offset(self, count: int):
        """Mock offset method for pagination"""
        return self

    def order_by(self, field: str, direction=None):
        """Mock order_by method for sorting"""
        return self

    def where(self, field: str, operator: str, value):
        """Mock where method for filtering"""
        return self

class MockDocument:
    def __init__(self, collection: str, doc_id: str, data: Dict, doc_data: Dict = None):
        self.id = doc_id
        self._collection = collection
        self._data = data
        self._doc_data = doc_data

    def set(self, data: Dict[str, Any]):
        self._data.setdefault(self._collection, {})[self.id] = data

    def update(self, data: Dict[str, Any]):
        """Update document with new data"""
        existing_data = self._data.setdefault(self._collection, {}).get(self.id, {})
        existing_data.update(data)
        self._data[self._collection][self.id] = existing_data

    def get(self):
        doc_data = self._data.get(self._collection, {}).get(self.id)
        return MockDocumentSnapshot(self.id, doc_data)

    def to_dict(self):
        return self._doc_data or {}

    def collection(self, subcollection_name: str):
        """Get a subcollection for this document"""
        # Create a nested structure for subcollections
        subcoll_key = f"{self._collection}/{self.id}/{subcollection_name}"
        return MockCollection(subcoll_key, self._data)

class MockDocumentSnapshot:
    def __init__(self, doc_id: str, data: Dict = None):
        self.id = doc_id
        self._data = data or {}

    @property
    def exists(self):
        return self._data is not None

    def to_dict(self):
        return self._data

class FirebaseClient:
    _instance: Optional['FirebaseClient'] = None
    _db: Optional[firestore.Client] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(FirebaseClient, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.initialized = True
            self._initialize_firebase()
    
    def _initialize_firebase(self):
        """Initialize Firebase Admin SDK"""
        try:
            # Check if we're in development mode without credentials
            if not os.getenv('FIREBASE_CREDENTIALS_PATH') and not os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
                logger.warning("No Firebase credentials found, using mock client for development")
                self._db = MockFirestoreClient()
                return

            # Check if Firebase is already initialized
            firebase_admin.get_app()
        except ValueError:
            # Firebase not initialized, so initialize it
            cred_path = os.getenv('FIREBASE_CREDENTIALS_PATH')

            if cred_path and os.path.exists(cred_path):
                # Use service account key file
                cred = credentials.Certificate(cred_path)
                firebase_admin.initialize_app(cred)
            else:
                # Use default credentials (for Cloud Run/GCP environments)
                firebase_admin.initialize_app()
        except Exception as e:
            logger.warning(f"Failed to initialize Firebase, using mock client: {e}")
            self._db = MockFirestoreClient()
            return

        # Initialize Firestore client
        try:
            self._db = firestore.client()
            logger.info("Firestore client initialized successfully")
        except Exception as e:
            logger.warning(f"Failed to initialize Firestore, using mock client: {e}")
            self._db = MockFirestoreClient()
    
    @property
    def db(self) -> firestore.Client:
        """Get Firestore client instance"""
        if self._db is None:
            raise RuntimeError("Firebase not properly initialized")
        return self._db

# Global instance
firebase_client = FirebaseClient()

def get_firestore_client() -> firestore.Client:
    """Get Firestore client instance"""
    return firebase_client.db
