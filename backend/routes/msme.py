from fastapi import APIRouter, HTTPException, status, Depends
from typing import List, Optional, Any
from datetime import datetime, UTC
from pydantic import BaseModel, Field, validator
import uuid
import logging

from models.msme import MSMEProfile, MSMECreate, MSMEUpdate, RiskBand
from models.signal import SignalSource
from firebase.init import get_firestore_client
from services.scoring import calculate_msme_score, recalculate_msme_score
from utils.error_handler import handle_common_exceptions, ValidationError, MSMENotFoundError

logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/", response_model=MSMEProfile, status_code=status.HTTP_201_CREATED)
@handle_common_exceptions
async def create_msme(msme_data: MSMECreate):
    """Create a new MSME profile with proper validation and error handling"""
    # Input validation
    if not msme_data.name or not msme_data.name.strip():
        raise ValidationError("MSME name cannot be empty")

    if len(msme_data.name) > 100:
        raise ValidationError("MSME name cannot exceed 100 characters")

    if not msme_data.business_type or msme_data.business_type not in ["retail", "b2b", "services", "manufacturing"]:
        raise ValidationError("Invalid business type")

    if not msme_data.location or not msme_data.location.strip():
        raise ValidationError("Location cannot be empty")

    db = get_firestore_client()

    # Generate unique ID
    msme_id = str(uuid.uuid4())

    # Create MSME profile
    msme_profile = MSMEProfile(
        msme_id=msme_id,
        name=msme_data.name.strip(),
        business_type=msme_data.business_type,
        location=msme_data.location.strip(),
        tags=msme_data.tags or [],
        created_at=datetime.now(UTC),
        score=0.0,  # Initial score
        risk_band=RiskBand.RED  # Initial risk band
    )

    # Save to Firestore
    doc_ref = db.collection('msmes').document(msme_id)
    doc_ref.set(msme_profile.model_dump())

    logger.info(f"Created MSME profile: {msme_id}")
    return msme_profile



@router.get("/{msme_id}", response_model=MSMEProfile)
async def get_msme(msme_id: str):
    """Get MSME profile by ID"""
    try:
        # Use centralized data service for consistency with portfolio endpoint
        from services.data_service import data_service
        data_service.initialize_data()

        msme_data = data_service.get_msme_by_id(msme_id)

        if not msme_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )

        # Convert MSMEData to MSMEProfile format
        from datetime import datetime

        # Convert created_at string to datetime if needed
        created_at = msme_data.created_at
        if isinstance(created_at, str):
            try:
                created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            except:
                created_at = datetime.now(UTC)

        profile_data = {
            'msme_id': msme_data.msme_id,
            'name': msme_data.name,
            'business_type': msme_data.business_type,
            'location': msme_data.location,
            'created_at': created_at,
            'score': msme_data.score,
            'risk_band': msme_data.risk_band,
            'tags': msme_data.tags,
            'gst_number': msme_data.gst_number,
            'gst_compliance': msme_data.gst_compliance,
            'banking_health': msme_data.banking_health,
            'monthly_turnover': msme_data.monthly_turnover,
            'digital_score': msme_data.digital_score
        }

        return MSMEProfile(**profile_data)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve MSME profile: {str(e)}"
        )

@router.get("/", response_model=List[MSMEProfile])
async def list_msmes(limit: int = 10, offset: int = 0):
    """List all MSME profiles with pagination"""
    try:
        db = get_firestore_client()
        
        query = db.collection('msmes').limit(limit).offset(offset)
        docs = query.stream()
        
        msmes = []
        for doc in docs:
            data = doc.to_dict()
            msmes.append(MSMEProfile(**data))
        
        return msmes
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list MSME profiles: {str(e)}"
        )

@router.put("/{msme_id}", response_model=MSMEProfile)
async def update_msme(msme_id: str, update_data: MSMEUpdate):
    """Update MSME profile"""
    try:
        db = get_firestore_client()
        
        doc_ref = db.collection('msmes').document(msme_id)
        doc = doc_ref.get()
        
        if not doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Update only provided fields
        update_dict = {k: v for k, v in update_data.model_dump().items() if v is not None}
        doc_ref.update(update_dict)
        
        # Return updated profile
        updated_doc = doc_ref.get()
        data = updated_doc.to_dict()
        return MSMEProfile(**data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update MSME profile: {str(e)}"
        )

class SignalInput(BaseModel):
    source: SignalSource
    value: Any
    metadata: Optional[dict] = Field(default_factory=dict)
    timestamp: Optional[datetime] = None

@router.post("/{msme_id}/signals", response_model=dict, status_code=status.HTTP_201_CREATED)
async def add_signal_to_msme(msme_id: str, signal_input: SignalInput):
    """Add new signal to MSME and recalculate score"""
    try:
        db = get_firestore_client()

        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()

        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )

        # Generate unique signal ID
        signal_id = str(uuid.uuid4())

        # Use provided timestamp or current UTC time
        timestamp = signal_input.timestamp or datetime.now(UTC)

        # Normalize the signal value
        from services.scoring import normalize_signal
        normalized_value = normalize_signal(signal_input.source, signal_input.value)

        # Create signal object
        signal_data = {
            'signal_id': signal_id,
            'msme_id': msme_id,
            'source': signal_input.source.value,
            'value': signal_input.value,
            'normalized': normalized_value,
            'timestamp': timestamp.isoformat(),
            'metadata': signal_input.metadata
        }

        # Save signal to Firestore
        signal_ref = msme_ref.collection('signals').document(signal_id)
        signal_ref.set(signal_data)

        # Recalculate score
        score_result = await recalculate_msme_score(msme_id)

        return {
            'signal_id': signal_id,
            'msme_id': msme_id,
            'signal_data': signal_data,
            'score_update': score_result
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add signal: {str(e)}"
        )

@router.get("/{msme_id}/signals", response_model=List[dict])
async def get_msme_signals(msme_id: str, limit: Optional[int] = 50):
    """Get signals for an MSME ordered by timestamp descending"""
    try:
        db = get_firestore_client()

        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()

        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )

        # Get signals ordered by timestamp descending
        signals_ref = msme_ref.collection('signals').order_by('timestamp', direction='DESCENDING')

        if limit:
            signals_ref = signals_ref.limit(limit)

        signals_docs = signals_ref.stream()

        signals = []
        for doc in signals_docs:
            signal_data = doc.to_dict()
            signals.append(signal_data)

        return signals

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch signals: {str(e)}"
        )

@router.get("/{msme_id}/score", response_model=dict)
async def get_msme_score(msme_id: str):
    """Get calculated health score for MSME"""
    try:
        db = get_firestore_client()

        # Get MSME profile
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()

        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )

        msme_data = msme_doc.to_dict()

        # Get all signals
        signals_ref = msme_ref.collection('signals')
        signals_docs = signals_ref.order_by('timestamp', direction='DESCENDING').stream()

        signals = []
        for doc in signals_docs:
            signals.append(doc.to_dict())

        # Calculate current score using the health score engine
        from services.health_score import calculate_health_score
        score_details = calculate_health_score(signals)

        return {
            'msme_id': msme_id,
            'msme_name': msme_data.get('name'),
            'current_score': score_details['score'],
            'risk_band': score_details['risk_band'],
            'score_breakdown': score_details['breakdown'],
            'signals_count': len(signals),
            'last_updated': datetime.now(UTC).isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get MSME score: {str(e)}"
        )

@router.post("/{msme_id}/nudge", response_model=dict, status_code=status.HTTP_201_CREATED)
async def trigger_nudge(msme_id: str, nudge_data: dict):
    """Trigger and log nudge message for MSME"""
    try:
        db = get_firestore_client()

        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()

        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )

        # Generate unique nudge ID
        nudge_id = str(uuid.uuid4())

        # Create nudge record
        nudge_record = {
            'nudge_id': nudge_id,
            'msme_id': msme_id,
            'message': nudge_data.get('message', ''),
            'trigger_type': nudge_data.get('trigger_type', 'manual'),
            'medium': nudge_data.get('medium', 'system'),
            'sent_at': datetime.now(UTC).isoformat(),
            'status': 'sent',
            'metadata': nudge_data.get('metadata', {})
        }

        # Save nudge to Firestore
        nudge_ref = msme_ref.collection('nudges').document(nudge_id)
        nudge_ref.set(nudge_record)

        # TODO: Implement actual nudge sending logic based on medium
        # For now, just log the nudge

        return {
            'nudge_id': nudge_id,
            'msme_id': msme_id,
            'message': nudge_record['message'],
            'status': 'sent',
            'sent_at': nudge_record['sent_at']
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to trigger nudge: {str(e)}"
        )
