# Dependencies
node_modules/
*/node_modules/
frontend/node_modules/

# Python Virtual Environment (All variations)
backend/venv/
backend/.venv/
venv/
.venv/
env/
.env/
*/venv/
*/.venv/
**/venv/
**/.venv/

# Environment Variables (IMPORTANT - Keep secrets safe!)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
backend/.env
frontend/.env
frontend/.env.local
frontend/.env.development.local
frontend/.env.test.local
frontend/.env.production.local

# Firebase & Google Cloud Credentials
firebase-adminsdk-*.json
service-account-*.json
google-credentials.json
firebase-config.json
.firebase/
.firebaserc

# API Keys and Secrets
secrets.json
config/secrets.json
.secrets
api-keys.json

# Build outputs
frontend/.next/
frontend/out/
frontend/build/
backend/__pycache__/
backend/*.pyc
backend/**/__pycache__/
backend/**/*.pyc
*.pyc
__pycache__/
.pytest_cache/

# TypeScript
*.tsbuildinfo
tsconfig.tsbuildinfo

# Logs
*.log
logs/
backend/logs/
frontend/logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Database files
*.db
*.sqlite
*.sqlite3
database.db

# Backup files
*.bak
*.backup
*.old

# Package manager lock files (optional - uncomment if needed)
# package-lock.json
# yarn.lock

# Local development files
.local
local/
dev/
development/

# Testing
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Documentation builds
docs/_build/
site/

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
