#!/usr/bin/env python3
"""
Script to fix deprecated datetime.now(datetime.UTC) usage throughout the codebase.

This script replaces datetime.now(datetime.UTC) with datetime.now(datetime.UTC) and
adds the necessary imports.

Author: Credit Chakra Team
Version: 1.0.0
"""

import os
import re
from pathlib import Path


def fix_datetime_in_file(file_path: Path) -> bool:
    """
    Fix datetime.now(datetime.UTC) usage in a single file.
    
    Args:
        file_path: Path to the file to fix
        
    Returns:
        bool: True if file was modified, False otherwise
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Replace datetime.now(datetime.UTC) with datetime.now(datetime.UTC)
        content = re.sub(r'datetime\.utcnow\(\)', 'datetime.now(datetime.UTC)', content)
        
        # Add timezone import if needed and datetime.UTC is used
        if 'datetime.now(datetime.UTC)' in content and 'from datetime import' in content:
            # Check if timezone is already imported
            if 'timezone' not in content or 'UTC' not in content:
                # Find the datetime import line and add timezone
                content = re.sub(
                    r'from datetime import ([^,\n]+)',
                    lambda m: f'from datetime import {m.group(1)}, timezone' if 'timezone' not in m.group(1) else m.group(0),
                    content
                )
                # Replace datetime.UTC with timezone.utc for compatibility
                content = content.replace('datetime.UTC', 'timezone.utc')
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
        
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False


def main():
    """Main function to fix datetime usage in all Python files."""
    backend_dir = Path(__file__).parent.parent
    python_files = list(backend_dir.rglob('*.py'))
    
    # Exclude virtual environment and cache directories
    python_files = [
        f for f in python_files 
        if 'venv' not in str(f) and '__pycache__' not in str(f) and '.pytest_cache' not in str(f)
    ]
    
    modified_files = []
    
    for file_path in python_files:
        if fix_datetime_in_file(file_path):
            modified_files.append(file_path)
            print(f"Fixed: {file_path}")
    
    print(f"\nFixed {len(modified_files)} files:")
    for file_path in modified_files:
        print(f"  - {file_path}")


if __name__ == "__main__":
    main()
