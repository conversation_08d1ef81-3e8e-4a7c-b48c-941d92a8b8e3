"""
Pytest configuration and fixtures for Credit Chakra backend testing.

This module provides comprehensive test fixtures and configuration for
testing the Credit Chakra backend services, models, and API endpoints.

Author: Credit Chakra Team
Version: 1.0.0
"""
import asyncio
import pytest
from datetime import datetime, timezone, date, timedelta
from typing import Dict, List, Any
from unittest.mock import Mock, AsyncMock
from fastapi.testclient import TestClient

from main import app
from models.msme import MSMEProfile, RiskBand, BusinessType
from models.signal import Signal, SignalSource
from models.risk_monitoring import (
    RiskEvent, RiskEventType, SeverityLevel, SMAClassification,
    PortfolioRiskMetrics, RiskAlert, SMAProgressionData
)
from services.data_service import CentralizedDataService, MSMEData


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def client():
    """Create a test client for the FastAPI application."""
    return TestClient(app)


@pytest.fixture
def mock_firestore():
    """Mock Firestore client for testing."""
    mock_client = Mock()
    mock_collection = Mock()
    mock_document = Mock()
    
    mock_client.collection.return_value = mock_collection
    mock_collection.document.return_value = mock_document
    mock_collection.stream.return_value = []
    mock_document.get.return_value = Mock(exists=True, to_dict=lambda: {})
    mock_document.set = Mock()
    mock_document.update = Mock()
    
    return mock_client


@pytest.fixture
def sample_msme_data():
    """Sample MSME data for testing."""
    return MSMEData(
        msme_id="test_msme_001",
        name="Test Electronics Store",
        business_type="retail",
        location="Mumbai, Maharashtra",
        score=75.5,
        risk_band="green",
        gst_compliance=88,
        banking_health=82,
        monthly_turnover=1500000,
        digital_score=78,
        gst_number="27AABCS1234C1Z5",
        created_at=datetime.now(timezone.utc).isoformat(),
        tags=["electronics", "retail", "mumbai"]
    )


@pytest.fixture
def sample_msme_profile():
    """Sample MSME profile for testing."""
    return MSMEProfile(
        name="Test Manufacturing Co",
        business_type=BusinessType.MANUFACTURING,
        location="Pune, Maharashtra",
        score=680.0,
        risk_band=RiskBand.YELLOW,
        gst_compliance=75.0,
        banking_health=68.0,
        monthly_turnover=2500000.0,
        digital_score=65.0,
        gst_number="27AABCT5678D2Z6",
        tags=["manufacturing", "textiles"]
    )


@pytest.fixture
def sample_signals():
    """Sample signals for testing."""
    return [
        Signal(
            msme_id="test_msme_001",
            source=SignalSource.GST,
            value=1500000,
            normalized=0.75,
            timestamp=datetime.now(timezone.utc),
            metadata={"turnover_month": "2024-01"}
        ),
        Signal(
            msme_id="test_msme_001",
            source=SignalSource.UPI,
            value={"transaction_count": 450, "volume": 800000},
            normalized=0.68,
            timestamp=datetime.now(timezone.utc),
            metadata={"payment_diversity": "high"}
        ),
        Signal(
            msme_id="test_msme_001",
            source=SignalSource.REVIEWS,
            value={"average_rating": 4.2, "review_count": 156},
            normalized=0.82,
            timestamp=datetime.now(timezone.utc),
            metadata={"platform": "google_reviews"}
        )
    ]


@pytest.fixture
def sample_risk_event():
    """Sample risk event for testing."""
    return RiskEvent(
        event_id="risk_event_001",
        msme_id="test_msme_001",
        event_type=RiskEventType.SCORE_DROP,
        severity=SeverityLevel.MEDIUM,
        title="Credit Score Drop Detected",
        description="MSME credit score dropped by 15 points in the last 7 days",
        impact_score=65.0,
        metadata={
            "previous_score": 75.5,
            "current_score": 60.5,
            "drop_percentage": 19.9,
            "time_period": "7_days"
        },
        exposure_amount=2500000.0,
        acknowledged=False
    )


@pytest.fixture
def sample_portfolio_metrics():
    """Sample portfolio metrics for testing."""
    return PortfolioRiskMetrics(
        total_msmes=20,
        total_exposure=50000000.0,
        avg_risk_score=72.5,
        risk_distribution={"green": 12, "yellow": 5, "red": 3},
        sma_distribution={"standard": 18, "sma_0": 1, "sma_1": 1, "sma_2": 0, "npa": 0},
        npa_ratio=0.0,
        sma_ratio=0.1,
        portfolio_health_score=78.5,
        concentration_metrics={"max_single_exposure": 0.12, "sector_concentration": 0.35},
        sector_concentration={"retail": 0.4, "manufacturing": 0.35, "services": 0.25},
        geographic_concentration={"maharashtra": 0.45, "karnataka": 0.25, "gujarat": 0.3},
        trend_indicators={"score_trend_30d": 2.3, "npa_trend_30d": 0.0},
        score_trend_30d=2.3,
        npa_trend_30d=0.0
    )


@pytest.fixture
def sample_risk_alert():
    """Sample risk alert for testing."""
    return RiskAlert(
        alert_id="alert_001",
        msme_id="test_msme_001",
        msme_name="Test Electronics Store",
        alert_type=RiskEventType.PAYMENT_DELAY,
        severity=SeverityLevel.HIGH,
        message="Payment delay detected - 45 days past due",
        current_score=60.5,
        risk_band="yellow",
        dpd_classification=SMAClassification.SMA_1,
        days_past_due=45,
        acknowledged=False
    )


@pytest.fixture
def sample_sma_progression():
    """Sample SMA progression data for testing."""
    return SMAProgressionData(
        msme_id="test_msme_001",
        msme_name="Test Electronics Store",
        business_type="retail",
        current_classification=SMAClassification.SMA_1,
        days_past_due=45,
        outstanding_amount=2500000.0,
        last_payment_date=date.today() - timedelta(days=45),
        classification_history=[
            {
                "classification": "standard",
                "date": (date.today() - timedelta(days=60)).isoformat(),
                "days_past_due": 0
            },
            {
                "classification": "sma_0",
                "date": (date.today() - timedelta(days=30)).isoformat(),
                "days_past_due": 15
            },
            {
                "classification": "sma_1",
                "date": date.today().isoformat(),
                "days_past_due": 45
            }
        ],
        stress_indicators=["payment_delays", "reduced_turnover", "irregular_transactions"],
        early_warning_signals=["declining_gst_compliance", "reduced_digital_payments"],
        rbi_reporting_required=True,
        provision_required=125000.0,
        next_review_date=date.today() + timedelta(days=7)
    )


@pytest.fixture
def mock_data_service():
    """Mock data service for testing."""
    service = Mock(spec=CentralizedDataService)
    service.initialize_data = Mock()
    service.get_all_msmes = Mock(return_value=[])
    service.get_msme_by_id = Mock(return_value=None)
    service.get_msmes_by_risk_band = Mock(return_value=[])
    service.get_msmes_by_business_type = Mock(return_value=[])
    service.get_portfolio_analytics = Mock(return_value={
        "total_msmes": 20,
        "risk_distribution": {"green": 12, "yellow": 5, "red": 3},
        "business_type_distribution": {"retail": 8, "manufacturing": 7, "services": 5},
        "average_score": 72.5,
        "total_signals": 240,
        "triggered_escalations": 7,
        "last_updated": datetime.now(timezone.utc).isoformat()
    })
    return service


@pytest.fixture
def test_database_data():
    """Test database with realistic MSME data."""
    return {
        "msmes": [
            {
                "msme_id": "msme_001",
                "name": "श्री गणेश इलेक्ट्रॉनिक्स",
                "business_type": "retail",
                "location": "Mumbai, Maharashtra",
                "score": 78.5,
                "risk_band": "green",
                "gst_compliance": 92,
                "banking_health": 88,
                "monthly_turnover": 2850000,
                "digital_score": 85,
                "gst_number": "27AABCS1234C1Z5",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "tags": ["electronics", "retail"]
            },
            {
                "msme_id": "msme_002",
                "name": "Rajesh Textiles Pvt Ltd",
                "business_type": "manufacturing",
                "location": "Surat, Gujarat",
                "score": 45.2,
                "risk_band": "yellow",
                "gst_compliance": 68,
                "banking_health": 55,
                "monthly_turnover": 1200000,
                "digital_score": 42,
                "gst_number": "24AABCT5678D2Z6",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "tags": ["textiles", "manufacturing"]
            },
            {
                "msme_id": "msme_003",
                "name": "Digital Solutions Hub",
                "business_type": "services",
                "location": "Bangalore, Karnataka",
                "score": 25.8,
                "risk_band": "red",
                "gst_compliance": 35,
                "banking_health": 28,
                "monthly_turnover": 450000,
                "digital_score": 65,
                "gst_number": "29AABCD9012E3Z7",
                "created_at": datetime.now(timezone.utc).isoformat(),
                "tags": ["it", "services"]
            }
        ],
        "signals": [
            {
                "signal_id": "signal_001",
                "msme_id": "msme_001",
                "source": "gst",
                "value": 2850000,
                "normalized": 0.85,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "metadata": {"month": "2024-01"}
            },
            {
                "signal_id": "signal_002",
                "msme_id": "msme_001",
                "source": "upi",
                "value": {"transaction_count": 520, "volume": 1200000},
                "normalized": 0.78,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "metadata": {"diversity_score": 0.82}
            }
        ]
    }


@pytest.fixture(autouse=True)
def reset_singletons():
    """Reset singleton instances between tests."""
    # Reset any singleton instances here if needed
    yield
    # Cleanup after test


# Test utilities
class TestUtils:
    """Utility functions for testing."""
    
    @staticmethod
    def create_test_msme(
        msme_id: str = "test_msme",
        name: str = "Test Business",
        business_type: str = "retail",
        risk_band: str = "green",
        score: float = 75.0
    ) -> MSMEData:
        """Create a test MSME with specified parameters."""
        return MSMEData(
            msme_id=msme_id,
            name=name,
            business_type=business_type,
            location="Test City, Test State",
            score=score,
            risk_band=risk_band,
            gst_compliance=80,
            banking_health=75,
            monthly_turnover=1500000,
            digital_score=70,
            gst_number=f"27AABCT{msme_id[-4:]}Z5",
            created_at=datetime.now(timezone.utc).isoformat(),
            tags=["test", business_type]
        )
    
    @staticmethod
    def assert_msme_data_valid(msme_data: MSMEData) -> None:
        """Assert that MSME data is valid."""
        assert msme_data.msme_id is not None
        assert len(msme_data.name) > 0
        assert msme_data.business_type in ["retail", "manufacturing", "services", "b2b"]
        assert msme_data.risk_band in ["green", "yellow", "red"]
        assert 0 <= msme_data.score <= 100
        assert 0 <= msme_data.gst_compliance <= 100
        assert 0 <= msme_data.banking_health <= 100
        assert msme_data.monthly_turnover >= 0
        assert 0 <= msme_data.digital_score <= 100


@pytest.fixture
def test_utils():
    """Provide test utilities."""
    return TestUtils


@pytest.fixture
def msme_id(client):
    """Create a test MSME and return its ID."""
    msme_data = {
        "name": "Test MSME Business",
        "business_type": "retail",
        "location": "Mumbai, Maharashtra",
        "tags": ["retail", "electronics"]
    }

    response = client.post("/msme/", json=msme_data)
    if response.status_code == 201:
        return response.json()['msme_id']
    else:
        # Return a default test ID if creation fails
        return "test_msme_001"
