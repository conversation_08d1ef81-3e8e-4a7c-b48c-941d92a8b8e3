"""
AI-Powered Nudge Generation Engine for Credit Chakra
Generates intelligent, contextual nudges based on MSME behavior and risk patterns
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

logger = logging.getLogger(__name__)

class NudgeType(Enum):
    PAYMENT_REMINDER = "payment_reminder"
    COMPLIANCE_ALERT = "compliance_alert"
    CREDIT_OPPORTUNITY = "credit_opportunity"
    RISK_MITIGATION = "risk_mitigation"
    BEHAVIORAL_IMPROVEMENT = "behavioral_improvement"
    SEASONAL_ADVISORY = "seasonal_advisory"
    DIGITAL_ADOPTION = "digital_adoption"

class NudgePriority(Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFORMATIONAL = "informational"

class NudgeChannel(Enum):
    SMS = "sms"
    EMAIL = "email"
    WHATSAPP = "whatsapp"
    IN_APP = "in_app"
    PHONE_CALL = "phone_call"

@dataclass
class AIGeneratedNudge:
    id: str
    msme_id: str
    type: NudgeType
    priority: NudgePriority
    title: str
    message: str
    recommended_channels: List[NudgeChannel]
    optimal_timing: datetime
    expected_impact: float
    confidence_score: float
    personalization_factors: Dict[str, Any]
    follow_up_actions: List[str]
    success_metrics: Dict[str, Any]
    created_at: datetime
    expires_at: Optional[datetime] = None

@dataclass
class NudgePersonalization:
    communication_style: str  # formal, friendly, urgent, supportive
    preferred_language: str
    optimal_time_of_day: str
    response_history: Dict[str, float]
    behavioral_triggers: List[str]
    risk_tolerance: str

class AINudgeEngine:
    """
    Advanced AI-powered nudge generation engine with behavioral analysis
    """
    
    def __init__(self):
        self.behavioral_models = self._initialize_behavioral_models()
        self.personalization_engine = self._initialize_personalization_engine()
        self.timing_optimizer = self._initialize_timing_optimizer()
        self.impact_predictor = self._initialize_impact_predictor()
        self.nudge_templates = self._load_nudge_templates()
        
    def _initialize_behavioral_models(self) -> Dict[str, Any]:
        """Initialize behavioral analysis models"""
        return {
            'payment_behavior': {
                'early_payers': {'threshold': 0.8, 'nudge_style': 'appreciation'},
                'on_time_payers': {'threshold': 0.6, 'nudge_style': 'reminder'},
                'late_payers': {'threshold': 0.3, 'nudge_style': 'urgent'},
                'irregular_payers': {'threshold': 0.4, 'nudge_style': 'supportive'}
            },
            'digital_engagement': {
                'high_adopters': {'score': 0.8, 'nudge_focus': 'advanced_features'},
                'moderate_adopters': {'score': 0.5, 'nudge_focus': 'feature_education'},
                'low_adopters': {'score': 0.2, 'nudge_focus': 'basic_onboarding'}
            },
            'risk_response': {
                'proactive': {'response_time': 24, 'nudge_style': 'collaborative'},
                'reactive': {'response_time': 72, 'nudge_style': 'directive'},
                'resistant': {'response_time': 168, 'nudge_style': 'incentivized'}
            }
        }
    
    def _initialize_personalization_engine(self) -> Dict[str, Any]:
        """Initialize personalization engine"""
        return {
            'communication_preferences': {
                'formal': {'tone': 'professional', 'structure': 'detailed'},
                'friendly': {'tone': 'conversational', 'structure': 'casual'},
                'urgent': {'tone': 'direct', 'structure': 'concise'},
                'supportive': {'tone': 'empathetic', 'structure': 'encouraging'}
            },
            'cultural_adaptations': {
                'hindi': {'greetings': 'Namaste', 'formality': 'high'},
                'english': {'greetings': 'Dear', 'formality': 'medium'},
                'regional': {'greetings': 'Respected', 'formality': 'high'}
            }
        }
    
    def _initialize_timing_optimizer(self) -> Dict[str, Any]:
        """Initialize timing optimization models"""
        return {
            'business_hours': {
                'manufacturing': {'start': 9, 'end': 18, 'peak': [10, 15]},
                'retail': {'start': 10, 'end': 20, 'peak': [11, 16]},
                'services': {'start': 9, 'end': 17, 'peak': [10, 14]},
                'technology': {'start': 10, 'end': 19, 'peak': [11, 16]}
            },
            'response_patterns': {
                'morning': {'response_rate': 0.7, 'engagement': 0.8},
                'afternoon': {'response_rate': 0.6, 'engagement': 0.7},
                'evening': {'response_rate': 0.5, 'engagement': 0.6}
            }
        }
    
    def _initialize_impact_predictor(self) -> Dict[str, Any]:
        """Initialize impact prediction models"""
        return {
            'nudge_effectiveness': {
                'payment_reminder': {'base_success': 0.65, 'factors': ['timing', 'personalization']},
                'compliance_alert': {'base_success': 0.75, 'factors': ['urgency', 'consequences']},
                'credit_opportunity': {'base_success': 0.45, 'factors': ['eligibility', 'timing']},
                'risk_mitigation': {'base_success': 0.55, 'factors': ['severity', 'support']}
            }
        }
    
    def _load_nudge_templates(self) -> Dict[str, Dict[str, str]]:
        """Load nudge message templates"""
        return {
            'payment_reminder': {
                'friendly': "Hi {name}! Just a gentle reminder that your payment of ₹{amount} is due on {date}. We're here to help if you need any assistance! 😊",
                'formal': "Dear {name}, This is to remind you that your payment of ₹{amount} is scheduled for {date}. Please ensure timely payment to maintain your excellent credit standing.",
                'urgent': "URGENT: {name}, your payment of ₹{amount} is overdue by {days} days. Please contact us immediately to avoid penalties."
            },
            'compliance_alert': {
                'friendly': "Hi {name}! Your {document} filing is due soon. Let's get this sorted together! Need help? We're just a call away! 📋",
                'formal': "Dear {name}, Please be informed that your {document} filing is due on {date}. Ensure compliance to avoid regulatory penalties.",
                'urgent': "CRITICAL: {name}, your {document} filing is overdue. Immediate action required to avoid penalties of ₹{penalty}."
            },
            'credit_opportunity': {
                'friendly': "Great news {name}! Based on your excellent track record, you're eligible for a credit limit increase of ₹{amount}! 🎉",
                'formal': "Dear {name}, We are pleased to inform you about your eligibility for enhanced credit facilities based on your performance.",
                'supportive': "Hi {name}, your business growth is impressive! We'd love to support your expansion with additional credit facilities."
            }
        }
    
    async def generate_intelligent_nudges(self, msme_data: Dict[str, Any]) -> List[AIGeneratedNudge]:
        """
        Generate AI-powered intelligent nudges for an MSME
        """
        try:
            msme_id = msme_data.get('id', 'unknown')
            logger.info(f"Generating intelligent nudges for MSME: {msme_id}")
            
            # Analyze MSME behavior and context
            behavioral_profile = await self._analyze_behavioral_profile(msme_data)
            personalization = await self._create_personalization_profile(msme_data, behavioral_profile)
            
            # Generate different types of nudges
            nudges = []
            
            # Payment-related nudges
            payment_nudges = await self._generate_payment_nudges(msme_data, behavioral_profile, personalization)
            nudges.extend(payment_nudges)
            
            # Compliance nudges
            compliance_nudges = await self._generate_compliance_nudges(msme_data, behavioral_profile, personalization)
            nudges.extend(compliance_nudges)
            
            # Opportunity nudges
            opportunity_nudges = await self._generate_opportunity_nudges(msme_data, behavioral_profile, personalization)
            nudges.extend(opportunity_nudges)
            
            # Risk mitigation nudges
            risk_nudges = await self._generate_risk_mitigation_nudges(msme_data, behavioral_profile, personalization)
            nudges.extend(risk_nudges)
            
            # Prioritize and optimize nudges
            optimized_nudges = await self._optimize_nudge_sequence(nudges, behavioral_profile)
            
            return optimized_nudges[:5]  # Return top 5 nudges
            
        except Exception as e:
            logger.error(f"Error generating nudges for MSME {msme_data.get('id')}: {str(e)}")
            return []
    
    async def _analyze_behavioral_profile(self, msme_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze MSME behavioral patterns"""
        score = msme_data.get('score', 50)
        business_type = msme_data.get('business_type', 'Unknown')
        
        # Mock behavioral analysis - would use actual ML models
        payment_behavior = 'on_time' if score > 70 else 'late' if score > 40 else 'irregular'
        digital_engagement = 'high' if score > 75 else 'moderate' if score > 50 else 'low'
        risk_response = 'proactive' if score > 80 else 'reactive' if score > 60 else 'resistant'
        
        return {
            'payment_behavior': payment_behavior,
            'digital_engagement': digital_engagement,
            'risk_response': risk_response,
            'business_type': business_type,
            'score_trend': 'stable',  # Would be calculated from historical data
            'communication_responsiveness': 0.7
        }
    
    async def _create_personalization_profile(self, msme_data: Dict[str, Any], behavioral_profile: Dict[str, Any]) -> NudgePersonalization:
        """Create personalization profile for nudges"""
        # Determine communication style based on behavior
        if behavioral_profile['risk_response'] == 'resistant':
            style = 'supportive'
        elif behavioral_profile['payment_behavior'] == 'late':
            style = 'urgent'
        elif behavioral_profile['digital_engagement'] == 'high':
            style = 'friendly'
        else:
            style = 'formal'
        
        return NudgePersonalization(
            communication_style=style,
            preferred_language='english',  # Would be determined from user data
            optimal_time_of_day='morning',  # Would be learned from response patterns
            response_history={'sms': 0.6, 'email': 0.4, 'whatsapp': 0.8},
            behavioral_triggers=['payment_due', 'score_change', 'compliance_deadline'],
            risk_tolerance='medium'
        )
    
    async def _generate_payment_nudges(self, msme_data: Dict[str, Any], behavioral_profile: Dict[str, Any], personalization: NudgePersonalization) -> List[AIGeneratedNudge]:
        """Generate payment-related nudges"""
        nudges = []
        
        # Mock payment nudge - would be based on actual payment data
        if behavioral_profile['payment_behavior'] in ['late', 'irregular']:
            nudge = AIGeneratedNudge(
                id=f"payment_{datetime.now().timestamp()}",
                msme_id=msme_data.get('id', 'unknown'),
                type=NudgeType.PAYMENT_REMINDER,
                priority=NudgePriority.HIGH,
                title="Payment Reminder",
                message=self._personalize_message('payment_reminder', personalization.communication_style, {
                    'name': msme_data.get('business_name', 'Valued Customer'),
                    'amount': '2,50,000',
                    'date': (datetime.now() + timedelta(days=3)).strftime('%d %B %Y')
                }),
                recommended_channels=[NudgeChannel.WHATSAPP, NudgeChannel.SMS],
                optimal_timing=self._calculate_optimal_timing(behavioral_profile, personalization),
                expected_impact=0.75,
                confidence_score=0.82,
                personalization_factors={
                    'communication_style': personalization.communication_style,
                    'behavioral_pattern': behavioral_profile['payment_behavior']
                },
                follow_up_actions=['schedule_call', 'send_payment_link'],
                success_metrics={'payment_completion': 0.75, 'response_rate': 0.65},
                created_at=datetime.now(timezone.utc)
            )
            nudges.append(nudge)
        
        return nudges
    
    async def _generate_compliance_nudges(self, msme_data: Dict[str, Any], behavioral_profile: Dict[str, Any], personalization: NudgePersonalization) -> List[AIGeneratedNudge]:
        """Generate compliance-related nudges"""
        # Mock implementation
        return []
    
    async def _generate_opportunity_nudges(self, msme_data: Dict[str, Any], behavioral_profile: Dict[str, Any], personalization: NudgePersonalization) -> List[AIGeneratedNudge]:
        """Generate opportunity-related nudges"""
        # Mock implementation
        return []
    
    async def _generate_risk_mitigation_nudges(self, msme_data: Dict[str, Any], behavioral_profile: Dict[str, Any], personalization: NudgePersonalization) -> List[AIGeneratedNudge]:
        """Generate risk mitigation nudges"""
        # Mock implementation
        return []
    
    async def _optimize_nudge_sequence(self, nudges: List[AIGeneratedNudge], behavioral_profile: Dict[str, Any]) -> List[AIGeneratedNudge]:
        """Optimize nudge sequence and timing"""
        # Sort by priority and expected impact
        return sorted(nudges, key=lambda x: (x.priority.value, -x.expected_impact))
    
    def _personalize_message(self, template_type: str, style: str, variables: Dict[str, str]) -> str:
        """Personalize nudge message based on style and variables"""
        template = self.nudge_templates.get(template_type, {}).get(style, "Default message for {name}")
        return template.format(**variables)
    
    def _calculate_optimal_timing(self, behavioral_profile: Dict[str, Any], personalization: NudgePersonalization) -> datetime:
        """Calculate optimal timing for nudge delivery"""
        # Mock implementation - would use ML models for timing optimization
        base_time = datetime.now() + timedelta(hours=2)
        return base_time

# Global instance
ai_nudge_engine = AINudgeEngine()
