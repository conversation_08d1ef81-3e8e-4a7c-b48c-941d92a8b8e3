'use client';

import React from 'react';
import { Nudge } from '@/types';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  MessageSquare, 
  AlertTriangle, 
  TrendingDown,
  Mail,
  MessageCircle,
  Smartphone,
  Clock,
  CheckCircle,
  XCircle,
  Send
} from 'lucide-react';

interface NudgeTimelineProps {
  nudges: Nudge[];
  loading?: boolean;
}

const getTriggerIcon = (triggerType: string) => {
  const iconProps = { className: "h-4 w-4" };
  
  switch (triggerType) {
    case 'score_drop':
      return <TrendingDown {...iconProps} />;
    case 'manual':
      return <MessageSquare {...iconProps} />;
    case 'risk_alert':
      return <AlertTriangle {...iconProps} />;
    default:
      return <Send {...iconProps} />;
  }
};

const getMediumIcon = (medium: string) => {
  const iconProps = { className: "h-3 w-3" };
  
  switch (medium) {
    case 'whatsapp':
      return <MessageCircle {...iconProps} />;
    case 'email':
      return <Mail {...iconProps} />;
    case 'sms':
      return <Smartphone {...iconProps} />;
    default:
      return <Send {...iconProps} />;
  }
};

const getTriggerColor = (triggerType: string) => {
  switch (triggerType) {
    case 'score_drop':
      return 'bg-red-500/10 text-red-500 border-red-200';
    case 'manual':
      return 'bg-blue-500/10 text-blue-500 border-blue-200';
    case 'risk_alert':
      return 'bg-orange-500/10 text-orange-500 border-orange-200';
    default:
      return 'bg-gray-500/10 text-gray-500 border-gray-200';
  }
};

const getMediumBadgeVariant = (medium: string) => {
  switch (medium) {
    case 'whatsapp':
      return 'default';
    case 'email':
      return 'secondary';
    case 'sms':
      return 'outline';
    default:
      return 'secondary';
  }
};

const getStatusIcon = (status: string) => {
  const iconProps = { className: "h-3 w-3" };
  
  switch (status) {
    case 'sent':
      return <Send {...iconProps} className="text-blue-500" />;
    case 'delivered':
      return <CheckCircle {...iconProps} className="text-green-500" />;
    case 'failed':
      return <XCircle {...iconProps} className="text-red-500" />;
    default:
      return <Clock {...iconProps} className="text-gray-500" />;
  }
};

const formatTriggerType = (triggerType: string): string => {
  switch (triggerType) {
    case 'score_drop':
      return 'Score Drop Alert';
    case 'manual':
      return 'Manual Nudge';
    case 'risk_alert':
      return 'Risk Alert';
    default:
      return 'Nudge';
  }
};

const formatMedium = (medium: string): string => {
  switch (medium) {
    case 'whatsapp':
      return 'WhatsApp';
    case 'email':
      return 'Email';
    case 'sms':
      return 'SMS';
    default:
      return medium.toUpperCase();
  }
};

const formatDate = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
  
  if (diffInHours < 1) {
    return 'Just now';
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  } else if (diffInHours < 168) { // 7 days
    const days = Math.floor(diffInHours / 24);
    return `${days}d ago`;
  } else {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  }
};

export function NudgeTimeline({ nudges, loading = false }: NudgeTimelineProps) {
  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="w-6 h-6 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (nudges.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <MessageSquare className="h-12 w-12 mx-auto mb-3 text-gray-400" />
          <p className="text-gray-500 mb-2">No nudges sent yet</p>
          <p className="text-sm text-gray-400">
            Nudges will appear here when sent to this MSME
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {nudges.map((nudge, index) => (
        <Card key={nudge.nudge_id} className="border-l-4 border-l-transparent hover:border-l-blue-500 transition-colors">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <div className={`p-2 rounded-lg border ${getTriggerColor(nudge.trigger_type)}`}>
                {getTriggerIcon(nudge.trigger_type)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-medium text-sm">
                    {formatTriggerType(nudge.trigger_type)}
                  </h4>
                  <Badge variant={getMediumBadgeVariant(nudge.medium)} className="text-xs">
                    <span className="mr-1">{getMediumIcon(nudge.medium)}</span>
                    {formatMedium(nudge.medium)}
                  </Badge>
                </div>
                
                <p className="text-sm text-gray-700 mb-2 leading-relaxed">
                  {nudge.message}
                </p>
                
                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <span>{formatDate(nudge.sent_at)}</span>
                  <span>•</span>
                  <div className="flex items-center gap-1">
                    {getStatusIcon(nudge.status)}
                    <span className="capitalize">{nudge.status}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
