rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // MSME profiles - authenticated users can read/write their own data
    match /msmes/{msmeId} {
      allow read, write: if request.auth != null;

      // Signals subcollection
      match /signals/{signalId} {
        allow read, write: if request.auth != null;
      }

      // Nudges subcollection
      match /nudges/{nudgeId} {
        allow read, write: if request.auth != null;
      }
    }

    // Admin users can access all data (for dashboard/analytics)
    match /{document=**} {
      allow read, write: if request.auth != null &&
        request.auth.token.admin == true;
    }

    // Public read access for specific endpoints (if needed)
    match /public/{document=**} {
      allow read: if true;
    }
  }
}
