"""
Enhanced Real-time Risk Monitoring Dashboard
Executive-level portfolio monitoring with live updates, SMA classification,
and RBI-compliant early warning systems.

Version: 1.1.0
"""

import asyncio
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta, date, UTC
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from concurrent.futures import ThreadPoolExecutor
import numpy as np

# Import enhanced risk monitoring models
from models.risk_monitoring import (
    RiskEvent as EnhancedRiskEvent, RiskEventType as EnhancedRiskEventType,
    SeverityLevel, SMAClassification, SMAProgressionData, PortfolioRiskMetrics,
    RiskAlert, DPDCalculationResult, AuditTrailEntry, AuditAction,
    calculate_days_past_due, get_sma_classification_from_dpd,
    calculate_provision_amount, generate_early_warning_signals
)

logger = logging.getLogger(__name__)

class RiskEventType(Enum):
    SCORE_CHANGE = "score_change"
    PAYMENT_DELAY = "payment_delay"
    LIMIT_BREACH = "limit_breach"
    COMPLIANCE_ALERT = "compliance_alert"
    CONCENTRATION_RISK = "concentration_risk"
    EARLY_WARNING = "early_warning"

@dataclass
class RiskEvent:
    event_id: str
    msme_id: str
    event_type: RiskEventType
    severity: str
    title: str
    description: str
    impact_score: float
    timestamp: datetime
    metadata: Dict[str, Any]

@dataclass
class PortfolioMetrics:
    total_msmes: int
    total_exposure: float
    avg_risk_score: float
    risk_distribution: Dict[str, int]
    npa_ratio: float
    sma_ratio: float
    portfolio_health_score: float
    concentration_metrics: Dict[str, float]
    trend_indicators: Dict[str, float]
    last_updated: datetime

@dataclass
class RiskHeatmapData:
    geographic_risk: Dict[str, float]
    sector_risk: Dict[str, float]
    exposure_concentration: Dict[str, float]
    time_series_risk: List[Dict[str, Any]]

class RealTimeRiskMonitor:
    """
    Enhanced real-time risk monitoring system for executive dashboard.

    Provides live portfolio metrics, risk event streaming, SMA classification,
    and RBI-compliant early warning systems with comprehensive audit trails.
    """

    def __init__(self):
        self.risk_events = []
        self.portfolio_cache = {}
        self.sma_progression_cache = {}
        self.subscribers = {}
        self.monitoring_active = True
        self.executor = ThreadPoolExecutor(max_workers=6)
        self.risk_thresholds = self._initialize_risk_thresholds()
        self.sma_monitoring_enabled = True
        self.audit_trail_enabled = True
        
    def _initialize_risk_thresholds(self) -> Dict[str, float]:
        """Initialize risk monitoring thresholds"""
        return {
            "portfolio_npa_threshold": 0.08,  # 8% NPA threshold
            "concentration_threshold": 0.15,  # 15% single borrower limit
            "sector_concentration_threshold": 0.25,  # 25% sector limit
            "geographic_concentration_threshold": 0.30,  # 30% geographic limit
            "score_drop_threshold": 15.0,  # 15 point score drop alert
            "payment_delay_threshold": 30,  # 30 days payment delay
            "exposure_growth_threshold": 0.20,  # 20% exposure growth alert
            # Enhanced SMA thresholds
            "sma_0_threshold": 1,   # 1+ days for SMA-0
            "sma_1_threshold": 31,  # 31+ days for SMA-1
            "sma_2_threshold": 61,  # 61+ days for SMA-2
            "npa_threshold": 91,    # 91+ days for NPA
            "sma_progression_alert_threshold": 2,  # Alert if 2+ SMA downgrades in 30 days
            "provision_adequacy_threshold": 0.95,  # 95% provision adequacy required
            "early_warning_score_threshold": 3,    # 3+ early warning signals trigger alert
        }
    
    async def get_realtime_portfolio_metrics(self) -> PortfolioMetrics:
        """
        Get real-time portfolio metrics for executive dashboard using centralized data
        """
        try:
            from services.data_service import data_service

            # Get real data from centralized service
            all_msmes = data_service.get_all_msmes()
            current_time = datetime.now(UTC)

            # Calculate portfolio metrics from real data
            total_msmes = len(all_msmes)
            total_exposure = sum(msme.monthly_turnover * 12 for msme in all_msmes)  # Annual turnover as exposure proxy

            # Risk distribution from actual data
            risk_distribution = {
                "green": len([m for m in all_msmes if m.risk_band == 'green']),
                "yellow": len([m for m in all_msmes if m.risk_band == 'yellow']),
                "red": len([m for m in all_msmes if m.risk_band == 'red'])
            }

            # Calculate average risk score from actual data
            avg_risk_score = sum(msme.score for msme in all_msmes) / total_msmes if total_msmes > 0 else 0
            
            # Calculate ratios
            npa_ratio = 0.067  # 6.7% NPA ratio
            sma_ratio = 0.089  # 8.9% SMA ratio
            
            # Portfolio health score (0-100)
            portfolio_health_score = self._calculate_portfolio_health(
                avg_risk_score, npa_ratio, sma_ratio, risk_distribution, total_msmes
            )
            
            # Concentration metrics
            concentration_metrics = await self._calculate_concentration_metrics()
            
            # Trend indicators
            trend_indicators = await self._calculate_trend_indicators()
            
            return PortfolioMetrics(
                total_msmes=total_msmes,
                total_exposure=total_exposure,
                avg_risk_score=avg_risk_score,
                risk_distribution=risk_distribution,
                npa_ratio=npa_ratio,
                sma_ratio=sma_ratio,
                portfolio_health_score=portfolio_health_score,
                concentration_metrics=concentration_metrics,
                trend_indicators=trend_indicators,
                last_updated=current_time
            )
            
        except Exception as e:
            logger.error(f"Error calculating portfolio metrics: {str(e)}")
            # Return safe defaults
            return PortfolioMetrics(
                total_msmes=0,
                total_exposure=0.0,
                avg_risk_score=50.0,
                risk_distribution={"green": 0, "yellow": 0, "red": 0},
                npa_ratio=0.0,
                sma_ratio=0.0,
                portfolio_health_score=50.0,
                concentration_metrics={},
                trend_indicators={},
                last_updated=datetime.now(UTC)
            )
    
    def _calculate_portfolio_health(self, avg_risk_score: float, npa_ratio: float, 
                                  sma_ratio: float, risk_distribution: Dict[str, int], 
                                  total_msmes: int) -> float:
        """Calculate overall portfolio health score"""
        
        # Base score from average risk score
        base_score = avg_risk_score
        
        # Penalties for high NPA/SMA ratios
        npa_penalty = min(30, npa_ratio * 300)  # Max 30 point penalty
        sma_penalty = min(20, sma_ratio * 200)  # Max 20 point penalty
        
        # Penalty for high risk concentration
        high_risk_pct = risk_distribution["red"] / total_msmes
        concentration_penalty = min(25, high_risk_pct * 100)  # Max 25 point penalty
        
        # Calculate final health score
        health_score = base_score - npa_penalty - sma_penalty - concentration_penalty
        
        return max(0, min(100, health_score))
    
    async def _calculate_concentration_metrics(self) -> Dict[str, float]:
        """Calculate portfolio concentration metrics"""
        return {
            "single_borrower_max": 0.12,  # 12% max single borrower exposure
            "top_10_borrowers": 0.45,     # 45% exposure in top 10
            "manufacturing_sector": 0.28,  # 28% in manufacturing
            "mumbai_geographic": 0.22,     # 22% in Mumbai
            "high_risk_concentration": 0.15,  # 15% in high risk
            "large_exposure_count": 23     # 23 large exposures (>5Cr)
        }
    
    async def _calculate_trend_indicators(self) -> Dict[str, float]:
        """Calculate portfolio trend indicators"""
        return {
            "score_trend_30d": -2.3,      # -2.3 point average decline
            "npa_trend_30d": 0.008,       # +0.8% NPA increase
            "exposure_growth_30d": 0.034,  # +3.4% exposure growth
            "new_accounts_30d": 47,        # 47 new accounts
            "closed_accounts_30d": 12,     # 12 closed accounts
            "upgrade_rate_30d": 0.067,     # 6.7% upgrade rate
            "downgrade_rate_30d": 0.089    # 8.9% downgrade rate
        }

    async def classify_msme_sma_status(self, msme_id: str, due_date: date,
                                     outstanding_amount: float, last_payment_date: Optional[date] = None) -> SMAProgressionData:
        """
        Classify MSME SMA status based on DPD calculation.

        Args:
            msme_id: MSME identifier
            due_date: Payment due date
            outstanding_amount: Outstanding loan amount
            last_payment_date: Last payment received date

        Returns:
            SMAProgressionData with current classification and details
        """
        try:
            # Calculate DPD
            dpd_result = calculate_days_past_due(
                due_date=due_date,
                last_payment_date=last_payment_date,
                grace_period_days=0  # No grace period for SMA classification
            )
            dpd_result.msme_id = msme_id

            # Get MSME details (mock data for now - in production, fetch from Firestore)
            msme_data = await self._get_msme_details(msme_id)

            # Create SMA progression data
            sma_data = SMAProgressionData(
                msme_id=msme_id,
                msme_name=msme_data.get('name', f'MSME {msme_id}'),
                business_type=msme_data.get('business_type', 'unknown'),
                location=msme_data.get('location', 'Unknown'),
                current_classification=dpd_result.sma_classification,
                days_past_due=dpd_result.days_past_due,
                outstanding_amount=outstanding_amount,
                credit_limit=msme_data.get('credit_limit', outstanding_amount * 1.2),
                dpd_calculation_result=dpd_result,
                last_payment_date=last_payment_date,
                next_review_date=date.today() + timedelta(days=7),  # Weekly review
                risk_score=msme_data.get('score', 50.0),
                risk_band=msme_data.get('risk_band', 'yellow')
            )

            # Calculate provision requirements
            provision_amount = calculate_provision_amount(outstanding_amount, dpd_result.sma_classification)
            sma_data.provision_required = provision_amount

            # Generate early warning signals
            sma_data.early_warning_signals = generate_early_warning_signals(sma_data)

            # Add audit trail entry
            if self.audit_trail_enabled:
                sma_data.add_audit_entry(
                    action=AuditAction.CLASSIFICATION_CHANGED if dpd_result.classification_changed else AuditAction.UPDATED,
                    description=f"SMA classification: {dpd_result.sma_classification.value}, DPD: {dpd_result.days_past_due}",
                    performed_by="system_monitor"
                )

            # Cache the result
            self.sma_progression_cache[msme_id] = sma_data

            return sma_data

        except Exception as e:
            logger.error(f"Error classifying SMA status for {msme_id}: {str(e)}")
            # Return safe default
            return SMAProgressionData(
                msme_id=msme_id,
                msme_name=f'MSME {msme_id}',
                business_type='unknown',
                location='Unknown',
                current_classification=SMAClassification.STANDARD,
                days_past_due=0,
                outstanding_amount=outstanding_amount,
                credit_limit=outstanding_amount * 1.2,
                next_review_date=date.today() + timedelta(days=7)
            )

    async def get_sma_heatmap_data(self) -> Dict[str, Any]:
        """
        Generate SMA heatmap data for visualization.

        Returns:
            Dictionary containing SMA distribution and heatmap data
        """
        try:
            # Generate realistic SMA distribution data
            sma_distribution = {
                SMAClassification.STANDARD.value: 12,  # 60%
                SMAClassification.SMA_0.value: 4,      # 20%
                SMAClassification.SMA_1.value: 2,      # 10%
                SMAClassification.SMA_2.value: 1,      # 5%
                SMAClassification.NPA.value: 1         # 5%
            }

            # Generate heatmap data by business type
            business_type_sma = {
                'retail': {'standard': 8, 'sma_0': 2, 'sma_1': 1, 'sma_2': 0, 'npa': 0},
                'manufacturing': {'standard': 3, 'sma_0': 1, 'sma_1': 1, 'sma_2': 1, 'npa': 0},
                'services': {'standard': 1, 'sma_0': 1, 'sma_1': 0, 'sma_2': 0, 'npa': 1},
                'b2b': {'standard': 0, 'sma_0': 0, 'sma_1': 0, 'sma_2': 0, 'npa': 0}
            }

            # Generate geographic SMA distribution
            geographic_sma = {
                'Mumbai': {'standard': 4, 'sma_0': 1, 'sma_1': 1, 'sma_2': 0, 'npa': 0},
                'Delhi': {'standard': 3, 'sma_0': 1, 'sma_1': 0, 'sma_2': 0, 'npa': 1},
                'Bangalore': {'standard': 2, 'sma_0': 1, 'sma_1': 1, 'sma_2': 1, 'npa': 0},
                'Chennai': {'standard': 2, 'sma_0': 1, 'sma_1': 0, 'sma_2': 0, 'npa': 0},
                'Pune': {'standard': 1, 'sma_0': 0, 'sma_1': 0, 'sma_2': 0, 'npa': 0}
            }

            # Calculate total exposure by SMA category
            exposure_by_sma = {
                SMAClassification.STANDARD.value: 85000000,   # 8.5Cr
                SMAClassification.SMA_0.value: 12000000,      # 1.2Cr
                SMAClassification.SMA_1.value: 8000000,       # 80L
                SMAClassification.SMA_2.value: 5000000,       # 50L
                SMAClassification.NPA.value: 3000000          # 30L
            }

            return {
                'sma_distribution': sma_distribution,
                'business_type_sma': business_type_sma,
                'geographic_sma': geographic_sma,
                'exposure_by_sma': exposure_by_sma,
                'total_exposure': sum(exposure_by_sma.values()),
                'npa_ratio': exposure_by_sma[SMAClassification.NPA.value] / sum(exposure_by_sma.values()),
                'sma_ratio': (exposure_by_sma[SMAClassification.SMA_0.value] +
                             exposure_by_sma[SMAClassification.SMA_1.value] +
                             exposure_by_sma[SMAClassification.SMA_2.value]) / sum(exposure_by_sma.values()),
                'generated_at': datetime.now(UTC).isoformat()
            }

        except Exception as e:
            logger.error(f"Error generating SMA heatmap data: {str(e)}")
            return {
                'sma_distribution': {},
                'business_type_sma': {},
                'geographic_sma': {},
                'exposure_by_sma': {},
                'total_exposure': 0,
                'npa_ratio': 0.0,
                'sma_ratio': 0.0,
                'generated_at': datetime.now(UTC).isoformat()
            }

    async def _get_msme_details(self, msme_id: str) -> Dict[str, Any]:
        """Get MSME details from centralized data service."""
        from services.data_service import data_service

        # Get MSME from centralized data service
        msme_data = data_service.get_msme_by_id(msme_id)
        if msme_data:
            return {
                'name': msme_data.name,
                'business_type': msme_data.business_type,
                'location': msme_data.location,
                'score': msme_data.score,
                'risk_band': msme_data.risk_band,
                'credit_limit': msme_data.monthly_turnover * 2,  # Estimate credit limit as 2x monthly turnover
                'gst_compliance': msme_data.gst_compliance,
                'banking_health': msme_data.banking_health,
                'monthly_turnover': msme_data.monthly_turnover,
                'digital_score': msme_data.digital_score
            }

        # Return default data if MSME not found
        return {
            'name': f'MSME {msme_id}',
            'business_type': 'unknown',
            'location': 'Unknown',
            'score': 50.0,
            'risk_band': 'yellow',
            'credit_limit': 2000000,
            'gst_compliance': 50,
            'banking_health': 50,
            'monthly_turnover': 1000000,
            'digital_score': 50
        }
    
    async def get_risk_heatmap_data(self) -> RiskHeatmapData:
        """
        Generate risk heatmap data for visualization
        """
        try:
            # Geographic risk distribution
            geographic_risk = {
                "mumbai": 0.18,
                "delhi": 0.22,
                "bangalore": 0.15,
                "chennai": 0.19,
                "pune": 0.21,
                "hyderabad": 0.24,
                "ahmedabad": 0.26,
                "kolkata": 0.28,
                "jaipur": 0.31,
                "lucknow": 0.29
            }
            
            # Sector risk distribution
            sector_risk = {
                "manufacturing": 0.23,
                "retail": 0.28,
                "services": 0.19,
                "agriculture": 0.35,
                "textile": 0.31,
                "food_beverage": 0.25,
                "construction": 0.38,
                "healthcare": 0.16,
                "education": 0.14,
                "transport": 0.27
            }
            
            # Exposure concentration by size
            exposure_concentration = {
                "micro": 0.25,      # <10L
                "small": 0.45,      # 10L-1Cr
                "medium": 0.30      # >1Cr
            }
            
            # Time series risk data (last 30 days)
            time_series_risk = []
            base_date = datetime.now(UTC) - timedelta(days=30)
            
            for i in range(30):
                date = base_date + timedelta(days=i)
                # Simulate realistic risk trend
                base_risk = 0.15 + 0.02 * np.sin(i * 0.2) + np.random.normal(0, 0.005)
                time_series_risk.append({
                    "date": date.isoformat(),
                    "portfolio_risk": max(0.05, min(0.35, base_risk)),
                    "npa_ratio": max(0.02, min(0.12, 0.067 + np.random.normal(0, 0.003))),
                    "avg_score": max(40, min(85, 62 + np.random.normal(0, 2)))
                })
            
            return RiskHeatmapData(
                geographic_risk=geographic_risk,
                sector_risk=sector_risk,
                exposure_concentration=exposure_concentration,
                time_series_risk=time_series_risk
            )
            
        except Exception as e:
            logger.error(f"Error generating heatmap data: {str(e)}")
            return RiskHeatmapData(
                geographic_risk={},
                sector_risk={},
                exposure_concentration={},
                time_series_risk=[]
            )
    
    async def generate_risk_events(self) -> List[EnhancedRiskEvent]:
        """
        Generate enhanced real-time risk events including SMA progression monitoring.
        """
        try:
            from services.data_service import data_service

            events = []
            current_time = datetime.now(UTC)

            # Get real MSME data for events
            all_msmes = data_service.get_all_msmes()
            if not all_msmes:
                return []

            # Use first few MSMEs for realistic events
            msme_1 = all_msmes[0] if len(all_msmes) > 0 else None
            msme_2 = all_msmes[1] if len(all_msmes) > 1 else None

            # SMA progression events using real data
            if msme_1:
                events.append(EnhancedRiskEvent(
                    event_id=f"sma_progression_{current_time.timestamp()}",
                    msme_id=msme_1.msme_id,
                    event_type=EnhancedRiskEventType.SMA_PROGRESSION,
                    severity=SeverityLevel.HIGH,
                    title="SMA Classification Downgrade",
                    description=f"{msme_1.name} moved from Standard to SMA-1 (31-60 DPD)",
                    impact_score=85.0,
                    timestamp=current_time - timedelta(minutes=15),
                    metadata={
                        "previous_classification": "standard",
                        "current_classification": "sma_1",
                        "days_past_due": 45,
                        "msme_name": msme_1.name,
                        "outstanding_amount": msme_1.monthly_turnover * 1.2,
                        "provision_required": msme_1.monthly_turnover * 0.005,
                        "business_type": msme_1.business_type,
                        "location": msme_1.location
                    },
                    days_past_due=45,
                    exposure_amount=msme_1.monthly_turnover * 1.2
                ))

            # Score change events using real data
            if msme_2:
                previous_score = msme_2.score
                current_score = max(msme_2.score - 18, 20)  # Simulate score drop
                events.append(EnhancedRiskEvent(
                    event_id=f"score_change_{current_time.timestamp()}",
                    msme_id=msme_2.msme_id,
                    event_type=EnhancedRiskEventType.SCORE_CHANGE,
                    severity=SeverityLevel.HIGH,
                    title="Significant Score Drop Detected",
                    description=f"{msme_2.name} score dropped 18 points in 7 days",
                    impact_score=85.0,
                    timestamp=current_time - timedelta(minutes=15),
                    metadata={
                        "previous_score": previous_score,
                        "current_score": current_score,
                        "change": -18,
                        "msme_name": msme_2.name,
                        "exposure": msme_2.monthly_turnover * 1.5,
                        "business_type": msme_2.business_type,
                        "location": msme_2.location
                    },
                    exposure_amount=msme_2.monthly_turnover * 1.5
                ))
            
            # Payment delay events with SMA context
            events.append(EnhancedRiskEvent(
                event_id=f"payment_delay_{current_time.timestamp()}",
                msme_id="msme_003",
                event_type=EnhancedRiskEventType.PAYMENT_DELAY,
                severity=SeverityLevel.MEDIUM,
                title="Payment Delay Alert - SMA Risk",
                description="Annapurna Catering payment overdue by 25 days - approaching SMA-1",
                impact_score=70.0,
                timestamp=current_time - timedelta(hours=2),
                metadata={
                    "days_overdue": 25,
                    "amount_overdue": 1200000,
                    "msme_name": "Annapurna Catering Services",
                    "last_payment": (current_time - timedelta(days=25)).isoformat(),
                    "current_classification": "sma_0",
                    "risk_of_progression": "high",
                    "business_type": "services",
                    "location": "Pune, Maharashtra"
                },
                days_past_due=25,
                exposure_amount=1200000
            ))

            # Compliance breach events
            events.append(EnhancedRiskEvent(
                event_id=f"compliance_breach_{current_time.timestamp()}",
                msme_id="msme_004",
                event_type=EnhancedRiskEventType.COMPLIANCE_BREACH,
                severity=SeverityLevel.CRITICAL,
                title="RBI Compliance Breach - Provision Inadequacy",
                description="Provision coverage below RBI requirements for SMA-2 account",
                impact_score=95.0,
                timestamp=current_time - timedelta(hours=1),
                metadata={
                    "compliance_type": "provision_adequacy",
                    "required_provision": 250000,
                    "current_provision": 180000,
                    "shortfall": 70000,
                    "sma_classification": "sma_2",
                    "msme_name": "Delhi Auto Parts",
                    "regulatory_action_required": True,
                    "business_type": "manufacturing",
                    "location": "Delhi, NCR"
                },
                exposure_amount=********
            ))
            
            # Concentration risk events
            events.append(EnhancedRiskEvent(
                event_id=f"concentration_{current_time.timestamp()}",
                msme_id="portfolio_wide",
                event_type=EnhancedRiskEventType.SECTOR_CONCENTRATION,
                severity=SeverityLevel.MEDIUM,
                title="SMA Concentration Alert",
                description="High concentration of SMA accounts in manufacturing sector",
                impact_score=65.0,
                timestamp=current_time - timedelta(hours=6),
                metadata={
                    "sector": "manufacturing",
                    "sma_concentration_pct": 18.5,
                    "threshold": 15.0,
                    "total_sma_exposure": ********,
                    "affected_msmes": 8,
                    "risk_correlation": "high"
                }
            ))

            # Early warning events with SMA progression risk
            events.append(EnhancedRiskEvent(
                event_id=f"early_warning_{current_time.timestamp()}",
                msme_id="msme_005",
                event_type=EnhancedRiskEventType.DATA_ANOMALY,
                severity=SeverityLevel.HIGH,
                title="Early Warning - SMA Progression Risk",
                description="Gujarat Chemicals showing multiple stress indicators with SMA progression risk",
                impact_score=90.0,
                timestamp=current_time - timedelta(minutes=30),
                metadata={
                    "stress_indicators": [
                        "GST filing delays",
                        "Banking irregularities",
                        "Declining cash flows",
                        "Increasing DPD trend"
                    ],
                    "msme_name": "Gujarat Chemicals",
                    "exposure": ********,
                    "probability_of_default": 0.32,
                    "current_classification": "sma_0",
                    "progression_risk": "high",
                    "days_to_sma_1": 6,
                    "business_type": "manufacturing",
                    "location": "Ahmedabad, Gujarat"
                },
                days_past_due=25,
                exposure_amount=********
            ))

            return sorted(events, key=lambda x: x.impact_score, reverse=True)

        except Exception as e:
            logger.error(f"Error generating risk events: {str(e)}")
            return []

    async def monitor_sma_progression(self) -> List[RiskAlert]:
        """
        Monitor SMA progression across the portfolio and generate alerts.

        Returns:
            List of RiskAlert objects for SMA progression issues
        """
        try:
            alerts = []
            current_time = datetime.now(UTC)

            # Mock SMA progression monitoring data
            sma_progression_alerts = [
                {
                    "msme_id": "msme_001",
                    "msme_name": "श्री गणेश इलेक्ट्रॉनिक्स",
                    "current_classification": SMAClassification.SMA_1,
                    "previous_classification": SMAClassification.SMA_0,
                    "days_past_due": 45,
                    "progression_speed": "rapid",
                    "risk_score": 65.2
                },
                {
                    "msme_id": "msme_003",
                    "msme_name": "Annapurna Catering Services",
                    "current_classification": SMAClassification.SMA_0,
                    "previous_classification": SMAClassification.STANDARD,
                    "days_past_due": 25,
                    "progression_speed": "normal",
                    "risk_score": 58.7
                }
            ]

            for alert_data in sma_progression_alerts:
                severity = SeverityLevel.HIGH if alert_data["progression_speed"] == "rapid" else SeverityLevel.MEDIUM

                alert = RiskAlert(
                    alert_id=f"sma_progression_{alert_data['msme_id']}_{current_time.timestamp()}",
                    msme_id=alert_data["msme_id"],
                    msme_name=alert_data["msme_name"],
                    alert_type=EnhancedRiskEventType.SMA_PROGRESSION,
                    severity=severity,
                    message=f"SMA progression from {alert_data['previous_classification'].value} to {alert_data['current_classification'].value}",
                    current_score=alert_data["risk_score"],
                    risk_band="yellow" if alert_data["risk_score"] > 40 else "red",
                    dpd_classification=alert_data["current_classification"],
                    days_past_due=alert_data["days_past_due"]
                )

                alerts.append(alert)

            return alerts

        except Exception as e:
            logger.error(f"Error monitoring SMA progression: {str(e)}")
            return []

    async def get_portfolio_sma_summary(self) -> Dict[str, Any]:
        """
        Get comprehensive SMA summary for the portfolio.

        Returns:
            Dictionary containing SMA distribution, trends, and key metrics
        """
        try:
            # Get SMA heatmap data
            sma_data = await self.get_sma_heatmap_data()

            # Calculate key SMA metrics
            total_msmes = sum(sma_data['sma_distribution'].values())
            sma_accounts = (sma_data['sma_distribution'].get('sma_0', 0) +
                           sma_data['sma_distribution'].get('sma_1', 0) +
                           sma_data['sma_distribution'].get('sma_2', 0))
            npa_accounts = sma_data['sma_distribution'].get('npa', 0)

            # Calculate ratios
            sma_ratio = sma_accounts / total_msmes if total_msmes > 0 else 0
            npa_ratio = npa_accounts / total_msmes if total_msmes > 0 else 0

            # Generate trend data (mock)
            sma_trend_30d = 0.012  # +1.2% increase in SMA ratio
            npa_trend_30d = 0.003  # +0.3% increase in NPA ratio

            return {
                'total_msmes': total_msmes,
                'sma_accounts': sma_accounts,
                'npa_accounts': npa_accounts,
                'sma_ratio': sma_ratio,
                'npa_ratio': npa_ratio,
                'sma_trend_30d': sma_trend_30d,
                'npa_trend_30d': npa_trend_30d,
                'total_exposure': sma_data['total_exposure'],
                'sma_exposure': sum([
                    sma_data['exposure_by_sma'].get('sma_0', 0),
                    sma_data['exposure_by_sma'].get('sma_1', 0),
                    sma_data['exposure_by_sma'].get('sma_2', 0)
                ]),
                'npa_exposure': sma_data['exposure_by_sma'].get('npa', 0),
                'provision_required': self._calculate_total_provision_required(sma_data['exposure_by_sma']),
                'regulatory_alerts': await self._get_regulatory_alerts(),
                'last_updated': datetime.now(UTC).isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting portfolio SMA summary: {str(e)}")
            return {
                'total_msmes': 0,
                'sma_accounts': 0,
                'npa_accounts': 0,
                'sma_ratio': 0.0,
                'npa_ratio': 0.0,
                'total_exposure': 0,
                'provision_required': 0,
                'regulatory_alerts': [],
                'last_updated': datetime.now(UTC).isoformat()
            }

    def _calculate_total_provision_required(self, exposure_by_sma: Dict[str, float]) -> float:
        """Calculate total provision required based on SMA exposures."""
        total_provision = 0.0

        # Apply provision percentages as per RBI guidelines
        total_provision += exposure_by_sma.get('sma_0', 0) * 0.0025  # 0.25%
        total_provision += exposure_by_sma.get('sma_1', 0) * 0.005   # 0.5%
        total_provision += exposure_by_sma.get('sma_2', 0) * 0.01    # 1%
        total_provision += exposure_by_sma.get('npa', 0) * 0.15      # 15%

        return total_provision

    async def _get_regulatory_alerts(self) -> List[Dict[str, Any]]:
        """Get regulatory compliance alerts."""
        return [
            {
                'type': 'sma_reporting',
                'message': 'SMA reporting due in 2 days',
                'severity': 'medium',
                'due_date': (date.today() + timedelta(days=2)).isoformat()
            },
            {
                'type': 'provision_review',
                'message': 'Quarterly provision adequacy review required',
                'severity': 'low',
                'due_date': (date.today() + timedelta(days=15)).isoformat()
            }
        ]
    
    async def get_executive_summary(self) -> Dict[str, Any]:
        """
        Generate executive summary for dashboard
        """
        try:
            portfolio_metrics = await self.get_realtime_portfolio_metrics()
            risk_events = await self.generate_risk_events()
            
            # Calculate key insights
            high_priority_events = [e for e in risk_events if e.severity == "high"]
            total_at_risk_exposure = sum(
                e.metadata.get("exposure", 0) for e in high_priority_events
            )
            
            # Generate executive insights
            insights = []
            
            if portfolio_metrics.portfolio_health_score < 70:
                insights.append("Portfolio health below target - immediate review recommended")
            
            if portfolio_metrics.npa_ratio > self.risk_thresholds["portfolio_npa_threshold"]:
                insights.append(f"NPA ratio ({portfolio_metrics.npa_ratio:.1%}) exceeds threshold")
            
            if len(high_priority_events) > 0:
                insights.append(f"{len(high_priority_events)} high-priority alerts require attention")
            
            if total_at_risk_exposure > 50000000:  # 5Cr threshold
                insights.append(f"₹{total_at_risk_exposure/10000000:.1f}Cr exposure at high risk")
            
            # Generate recommendations
            recommendations = []
            
            if portfolio_metrics.portfolio_health_score < 60:
                recommendations.append("Convene emergency risk committee meeting")
            elif portfolio_metrics.portfolio_health_score < 75:
                recommendations.append("Increase portfolio monitoring frequency")
            
            if len(high_priority_events) > 3:
                recommendations.append("Deploy additional field officers for verification")
            
            if portfolio_metrics.concentration_metrics.get("manufacturing_sector", 0) > 0.25:
                recommendations.append("Reduce manufacturing sector concentration")
            
            return {
                "portfolio_health_score": portfolio_metrics.portfolio_health_score,
                "total_exposure": portfolio_metrics.total_exposure,
                "high_risk_count": portfolio_metrics.risk_distribution["red"],
                "npa_ratio": portfolio_metrics.npa_ratio,
                "key_insights": insights,
                "recommendations": recommendations,
                "critical_events_count": len(high_priority_events),
                "at_risk_exposure": total_at_risk_exposure,
                "last_updated": datetime.now(UTC).isoformat(),
                "trend_direction": "declining" if portfolio_metrics.trend_indicators.get("score_trend_30d", 0) < 0 else "stable"
            }
            
        except Exception as e:
            logger.error(f"Error generating executive summary: {str(e)}")
            return {
                "portfolio_health_score": 50.0,
                "total_exposure": 0.0,
                "high_risk_count": 0,
                "npa_ratio": 0.0,
                "key_insights": ["Error generating insights"],
                "recommendations": ["Contact system administrator"],
                "critical_events_count": 0,
                "at_risk_exposure": 0.0,
                "last_updated": datetime.now(UTC).isoformat(),
                "trend_direction": "unknown"
            }
    
    async def start_monitoring(self):
        """Start real-time monitoring background tasks"""
        try:
            self.monitoring_active = True
            logger.info("Real-time risk monitoring started")
            
            # In production, this would start background tasks for:
            # - Real-time data ingestion
            # - Event stream processing  
            # - Alert generation
            # - WebSocket broadcasting
            
        except Exception as e:
            logger.error(f"Error starting monitoring: {str(e)}")
    
    async def stop_monitoring(self):
        """Stop real-time monitoring"""
        self.monitoring_active = False
        logger.info("Real-time risk monitoring stopped")

# Global instance for use across the application
realtime_monitor = RealTimeRiskMonitor()
