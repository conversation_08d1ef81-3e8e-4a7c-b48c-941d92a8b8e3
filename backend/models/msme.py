from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from enum import Enum

class RiskBand(str, Enum):
    GREEN = "green"
    YELLOW = "yellow"
    RED = "red"

class BusinessType(str, Enum):
    RETAIL = "retail"
    B2B = "b2b"
    MANUFACTURING = "manufacturing"
    SERVICES = "services"

class MSMEProfile(BaseModel):
    msme_id: Optional[str] = None
    name: str = Field(..., min_length=1, max_length=200)
    business_type: BusinessType
    location: str = Field(..., min_length=1, max_length=100)
    created_at: Optional[datetime] = None
    score: Optional[float] = Field(None, ge=0, le=1000)
    risk_band: Optional[RiskBand] = None
    tags: List[str] = Field(default_factory=list)

    # Enhanced fields for banking/financial industry standards
    gst_number: Optional[str] = None
    gst_compliance: Optional[float] = Field(None, ge=0, le=100)
    banking_health: Optional[float] = Field(None, ge=0, le=100)
    monthly_turnover: Optional[float] = Field(None, ge=0)
    digital_score: Optional[float] = Field(None, ge=0, le=100)

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        extra = "ignore"  # Ignore extra fields from Firestore

class MSMECreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    business_type: BusinessType
    location: str = Field(..., min_length=1, max_length=100)
    tags: List[str] = Field(default_factory=list)

class MSMEUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    business_type: Optional[BusinessType] = None
    location: Optional[str] = Field(None, min_length=1, max_length=100)
    tags: Optional[List[str]] = None

    # Enhanced fields for banking/financial industry standards
    gst_number: Optional[str] = None
    gst_compliance: Optional[float] = Field(None, ge=0, le=100)
    banking_health: Optional[float] = Field(None, ge=0, le=100)
    monthly_turnover: Optional[float] = Field(None, ge=0)
    digital_score: Optional[float] = Field(None, ge=0, le=100)
