"""
Enhanced Dashboard routes for Credit Chakra portfolio management.

This module provides API endpoints for portfolio dashboard functionality including:
- Portfolio summary with pagination and filtering
- Analytics data for business insights
- Risk monitoring and SMA classification endpoints
- Real-time risk event streaming
- RBI compliance monitoring

Author: Credit Chakra Team
Version: 1.1.0
"""
from __future__ import annotations

from datetime import datetime, timezone, timedelta, UTC, date
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, HTTPException, Query, status
from pydantic import BaseModel

from services.data_service import data_service
from services.realtime_risk_monitor import realtime_monitor
from models.risk_monitoring import (
    SMAProgressionData, RiskAlert, PortfolioRiskMetrics,
    SMAClassification, RiskEvent, DPDCalculationResult
)
from utils.error_handler import handle_common_exceptions
from utils.pagination import PaginatedResponse, get_portfolio_summary, paginate_msme_portfolio

router = APIRouter()

@router.get("/portfolio", response_model=PaginatedResponse)
@handle_common_exceptions
async def get_portfolio_summary(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    risk_band: Optional[str] = Query(None, description="Filter by risk band"),
    business_type: Optional[str] = Query(None, description="Filter by business type"),
    search: Optional[str] = Query(None, description="Search term"),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("desc", description="Sort order (asc/desc)")
):
    """Get paginated portfolio summary with filtering and sorting"""
    # Use centralized data service for consistent data
    data_service.initialize_data()
    msmes = data_service.get_all_msmes()

    # Convert MSMEData to portfolio format
    portfolio = []
    for msme in msmes:
        portfolio_entry = {
            'msme_id': msme.msme_id,
            'name': msme.name,
            'business_type': msme.business_type,
            'location': msme.location,
            'current_score': msme.score,
            'risk_band': msme.risk_band,
            'score_trend': 'stable',  # TODO: Calculate actual trend
            'signals_count': 12,  # Mock signal count
            'recent_nudges': 0,  # Mock nudge count
            'last_signal_date': msme.created_at,
            'created_at': msme.created_at,
            'tags': msme.tags,
            # Enhanced fields for banking/financial industry standards
            'gst_compliance': msme.gst_compliance,
            'banking_health': msme.banking_health,
            'monthly_turnover': msme.monthly_turnover,
            'digital_score': msme.digital_score
        }
        portfolio.append(portfolio_entry)

    # Apply pagination, filtering, and sorting
    return paginate_msme_portfolio(
        data=portfolio,
        page=page,
        limit=limit,
        risk_band=risk_band,
        business_type=business_type,
        search=search,
        sort_by=sort_by,
        sort_order=sort_order
    )

@router.get("/simple-test")
async def simple_test_endpoint():
    """Simple test endpoint"""
    return {"message": "Simple test endpoint working"}

@router.get("/portfolio/legacy")
async def get_portfolio_summary_legacy():
    """Legacy endpoint for backward compatibility - returns all MSMEs without pagination"""
    # Use centralized data service for consistent data
    data_service.initialize_data()
    msmes = data_service.get_all_msmes()

    portfolio = []
    for msme in msmes:
        portfolio_entry = {
            'msme_id': msme.msme_id,
            'name': msme.name,
            'business_type': msme.business_type,
            'location': msme.location,
            'current_score': msme.score,
            'risk_band': msme.risk_band,
            'score_trend': 'stable',
            'signals_count': 12,
            'recent_nudges': 0,
            'last_signal_date': msme.created_at,
            'created_at': msme.created_at,
            'tags': msme.tags,
            'gst_compliance': msme.gst_compliance,
            'banking_health': msme.banking_health,
            'monthly_turnover': msme.monthly_turnover,
            'digital_score': msme.digital_score
        }
        portfolio.append(portfolio_entry)

    # Sort by risk band (red first) and then by score
    risk_order = {'red': 0, 'yellow': 1, 'green': 2}
    portfolio.sort(key=lambda x: (risk_order.get(x['risk_band'], 3), -x['current_score']))

    return portfolio

def _calculate_score_trend(signals: List[Dict[str, Any]]) -> str:
    """Calculate score trend based on recent signals"""
    if len(signals) < 2:
        return "stable"
    
    # Get scores from last two signal periods
    recent_signals = signals[:5]  # Last 5 signals
    older_signals = signals[5:10] if len(signals) > 5 else []
    
    if not older_signals:
        return "stable"
    
    recent_score = calculate_health_score(recent_signals)['score']
    older_score = calculate_health_score(older_signals)['score']
    
    score_diff = recent_score - older_score
    
    if score_diff > 5:
        return "improving"
    elif score_diff < -5:
        return "declining"
    else:
        return "stable"

@router.get("/portfolio/summary", response_model=Dict[str, Any])
@handle_common_exceptions
async def get_portfolio_summary_stats():
    """Get portfolio summary statistics for dashboard overview"""
    data_service.initialize_data()
    msmes = data_service.get_all_msmes()

    # Convert to dict format for summary calculation
    portfolio_data = []
    for msme in msmes:
        portfolio_data.append({
            'current_score': msme.score,
            'risk_band': msme.risk_band,
            'business_type': msme.business_type
        })

    return get_portfolio_summary(portfolio_data)

@router.get("/analytics", response_model=Dict[str, Any])
@handle_common_exceptions
async def get_dashboard_analytics():
    """Get dashboard analytics and summary statistics"""
    # Use centralized data service for consistent analytics
    return data_service.get_portfolio_analytics()

# Enhanced analytics models
class ScoreTrendData(BaseModel):
    period: str
    average_score: float
    high_risk_count: int
    medium_risk_count: int
    low_risk_count: int

class SignalPatternData(BaseModel):
    source: str
    total_signals: int
    avg_per_msme: float
    trend: str
    last_7_days: int

class NudgeMetricsData(BaseModel):
    total_sent: int
    delivery_rate: float
    response_rate: float
    effectiveness_score: float
    by_medium: Dict[str, Dict[str, int]]

class BusinessPerformanceData(BaseModel):
    business_type: str
    avg_score: float
    msme_count: int
    risk_distribution: Dict[str, int]
    trend: str

@router.get("/analytics/trends", response_model=List[ScoreTrendData])
async def get_score_trends(period: Optional[str] = "30d"):
    """Get score trends over time"""
    try:
        # Mock data for now - in production, this would query historical data
        trends = []
        days = 30 if period == "30d" else 7 if period == "7d" else 90

        for i in range(days):
            date = datetime.now(UTC) - timedelta(days=days-i-1)
            trends.append(ScoreTrendData(
                period=date.strftime("%Y-%m-%d"),
                average_score=650 + (i * 2) + ((-1) ** i * 10),
                high_risk_count=3 + (i % 3),
                medium_risk_count=8 + (i % 5),
                low_risk_count=12 + (i % 4)
            ))

        return trends

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get score trends: {str(e)}"
        )

@router.get("/analytics/signals", response_model=List[SignalPatternData])
async def get_signal_patterns():
    """Get signal ingestion patterns"""
    try:
        db = get_firestore_client()

        # Get all signals across all MSMEs
        signal_sources = ['gst', 'upi', 'reviews', 'instagram', 'justdial']
        patterns = []

        for source in signal_sources:
            # In a real implementation, this would query the database
            # For now, return mock data
            patterns.append(SignalPatternData(
                source=source,
                total_signals=100 + hash(source) % 100,
                avg_per_msme=5.0 + hash(source) % 5,
                trend="increasing" if hash(source) % 3 == 0 else "stable",
                last_7_days=10 + hash(source) % 15
            ))

        return patterns

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get signal patterns: {str(e)}"
        )

@router.get("/analytics/nudges", response_model=NudgeMetricsData)
async def get_nudge_metrics():
    """Get nudge effectiveness metrics"""
    try:
        db = get_firestore_client()

        # In a real implementation, this would aggregate nudge data
        # For now, return mock data
        return NudgeMetricsData(
            total_sent=234,
            delivery_rate=94.5,
            response_rate=23.8,
            effectiveness_score=78.2,
            by_medium={
                "whatsapp": {"sent": 145, "delivered": 142, "responded": 38},
                "email": {"sent": 67, "delivered": 61, "responded": 12},
                "sms": {"sent": 22, "delivered": 19, "responded": 6}
            }
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get nudge metrics: {str(e)}"
        )

@router.get("/analytics/business-performance", response_model=List[BusinessPerformanceData])
async def get_business_performance():
    """Get business type performance analysis"""
    try:
        db = get_firestore_client()

        # Get all MSMEs grouped by business type
        msmes_ref = db.collection('msmes')
        msmes_docs = msmes_ref.stream()

        business_data = {}

        for msme_doc in msmes_docs:
            msme_data = msme_doc.to_dict()
            business_type = msme_data.get('business_type', 'unknown')
            score = msme_data.get('score', 0)
            risk_band = msme_data.get('risk_band', 'red')

            if business_type not in business_data:
                business_data[business_type] = {
                    'scores': [],
                    'risk_distribution': {'green': 0, 'yellow': 0, 'red': 0}
                }

            business_data[business_type]['scores'].append(score)
            business_data[business_type]['risk_distribution'][risk_band] += 1

        performance = []
        for business_type, data in business_data.items():
            if data['scores']:
                avg_score = sum(data['scores']) / len(data['scores'])
                msme_count = len(data['scores'])

                performance.append(BusinessPerformanceData(
                    business_type=business_type,
                    avg_score=round(avg_score, 1),
                    msme_count=msme_count,
                    risk_distribution=data['risk_distribution'],
                    trend="improving" if avg_score > 600 else "stable"
                ))

        return performance

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get business performance: {str(e)}"
        )


# Risk Monitoring Endpoints

@router.get("/risk-monitor/sma-heatmap", response_model=Dict[str, Any])
@handle_common_exceptions
async def get_sma_heatmap():
    """
    Get SMA heatmap data for Risk Monitor dashboard.

    Returns comprehensive SMA distribution data including:
    - SMA classification distribution
    - Business type breakdown
    - Geographic distribution
    - Exposure analysis
    """
    try:
        heatmap_data = await realtime_monitor.get_sma_heatmap_data()
        return {
            "status": "success",
            "data": heatmap_data,
            "timestamp": datetime.now(UTC).isoformat()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get SMA heatmap data: {str(e)}"
        )


@router.get("/risk-monitor/portfolio-summary", response_model=Dict[str, Any])
@handle_common_exceptions
async def get_portfolio_sma_summary():
    """
    Get comprehensive portfolio SMA summary.

    Returns portfolio-level SMA metrics including ratios, trends,
    provision requirements, and regulatory alerts.
    """
    try:
        summary = await realtime_monitor.get_portfolio_sma_summary()
        return {
            "status": "success",
            "data": summary,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get portfolio SMA summary: {str(e)}"
        )





@router.get("/risk-monitor/sma-progression", response_model=List[RiskAlert])
@handle_common_exceptions
async def get_sma_progression_alerts():
    """
    Get SMA progression alerts for monitoring dashboard.

    Returns list of SMA progression alerts including classification changes,
    early warning signals, and progression risk indicators.
    """
    try:
        alerts = await realtime_monitor.monitor_sma_progression()
        return alerts
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get SMA progression alerts: {str(e)}"
        )


@router.get("/risk-monitor/real-time-events", response_model=List[RiskEvent])
@handle_common_exceptions
async def get_real_time_risk_events():
    """
    Get real-time risk events for monitoring dashboard.

    Returns list of current risk events including SMA progression,
    score changes, payment delays, and compliance breaches.
    """
    try:
        events = await realtime_monitor.generate_risk_events()
        return events
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get real-time risk events: {str(e)}"
        )


@router.post("/risk-monitor/classify-sma/{msme_id}")
@handle_common_exceptions
async def classify_msme_sma(
    msme_id: str,
    due_date: str = Query(..., description="Payment due date (YYYY-MM-DD)"),
    outstanding_amount: float = Query(..., description="Outstanding amount in INR"),
    last_payment_date: Optional[str] = Query(None, description="Last payment date (YYYY-MM-DD)")
):
    """
    Classify MSME SMA status based on DPD calculation.

    Performs real-time SMA classification for a specific MSME
    based on payment due date and outstanding amount.
    """
    try:
        # Parse dates
        due_date_parsed = datetime.strptime(due_date, "%Y-%m-%d").date()
        last_payment_parsed = None
        if last_payment_date:
            last_payment_parsed = datetime.strptime(last_payment_date, "%Y-%m-%d").date()

        # Perform SMA classification
        sma_data = await realtime_monitor.classify_msme_sma_status(
            msme_id=msme_id,
            due_date=due_date_parsed,
            outstanding_amount=outstanding_amount,
            last_payment_date=last_payment_parsed
        )

        return {
            "status": "success",
            "data": sma_data,
            "timestamp": datetime.now(UTC).isoformat()
        }

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid date format: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to classify SMA status: {str(e)}"
        )


@router.get("/risk-monitor/portfolio-metrics", response_model=Dict[str, Any])
@handle_common_exceptions
async def get_real_time_portfolio_metrics():
    """
    Get real-time portfolio risk metrics.

    Returns comprehensive portfolio health indicators including
    risk distribution, concentration metrics, and trend indicators.
    """
    try:
        metrics = await realtime_monitor.get_realtime_portfolio_metrics()

        # Convert dataclass to dict for JSON serialization
        metrics_dict = {
            "total_msmes": metrics.total_msmes,
            "total_exposure": metrics.total_exposure,
            "avg_risk_score": metrics.avg_risk_score,
            "risk_distribution": metrics.risk_distribution,
            "npa_ratio": metrics.npa_ratio,
            "sma_ratio": metrics.sma_ratio,
            "portfolio_health_score": metrics.portfolio_health_score,
            "concentration_metrics": metrics.concentration_metrics,
            "trend_indicators": metrics.trend_indicators,
            "last_updated": metrics.last_updated.isoformat()
        }

        return {
            "status": "success",
            "data": metrics_dict,
            "timestamp": datetime.now(UTC).isoformat()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get portfolio metrics: {str(e)}"
        )
