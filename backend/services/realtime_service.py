"""
Real-time update service for Credit Chakra application
Prepares infrastructure for WebSocket connections and live data updates
"""
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
import asyncio
import json
import logging
from enum import Enum

logger = logging.getLogger(__name__)

class EventType(Enum):
    """Types of real-time events"""
    MSME_CREATED = "msme_created"
    MSME_UPDATED = "msme_updated"
    MSME_DELETED = "msme_deleted"
    SCORE_UPDATED = "score_updated"
    RISK_BAND_CHANGED = "risk_band_changed"
    SIGNAL_ADDED = "signal_added"
    NUDGE_SENT = "nudge_sent"
    PORTFOLIO_SUMMARY_UPDATED = "portfolio_summary_updated"
    SYSTEM_ALERT = "system_alert"

@dataclass
class RealtimeEvent:
    """Real-time event structure"""
    event_type: EventType
    data: Dict[str, Any]
    timestamp: datetime
    event_id: str
    user_id: Optional[str] = None
    msme_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'event_type': self.event_type.value,
            'data': self.data,
            'timestamp': self.timestamp.isoformat(),
            'event_id': self.event_id,
            'user_id': self.user_id,
            'msme_id': self.msme_id
        }
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict())

class EventSubscription:
    """Subscription to real-time events"""
    
    def __init__(
        self,
        subscription_id: str,
        user_id: str,
        event_types: List[EventType],
        callback: Callable[[RealtimeEvent], None],
        filters: Optional[Dict[str, Any]] = None
    ):
        self.subscription_id = subscription_id
        self.user_id = user_id
        self.event_types = event_types
        self.callback = callback
        self.filters = filters or {}
        self.created_at = datetime.now(timezone.utc)
        self.last_activity = datetime.now(timezone.utc)
    
    def matches_event(self, event: RealtimeEvent) -> bool:
        """Check if event matches subscription criteria"""
        # Check event type
        if event.event_type not in self.event_types:
            return False
        
        # Check user filter
        if self.filters.get('user_id') and event.user_id != self.filters['user_id']:
            return False
        
        # Check MSME filter
        if self.filters.get('msme_id') and event.msme_id != self.filters['msme_id']:
            return False
        
        # Check risk band filter
        if self.filters.get('risk_band'):
            event_risk_band = event.data.get('risk_band')
            if event_risk_band != self.filters['risk_band']:
                return False
        
        return True

class RealtimeEventManager:
    """Manages real-time events and subscriptions"""
    
    def __init__(self):
        self.subscriptions: Dict[str, EventSubscription] = {}
        self.event_history: List[RealtimeEvent] = []
        self.max_history_size = 1000
        
    def subscribe(
        self,
        subscription_id: str,
        user_id: str,
        event_types: List[EventType],
        callback: Callable[[RealtimeEvent], None],
        filters: Optional[Dict[str, Any]] = None
    ) -> EventSubscription:
        """Subscribe to real-time events"""
        subscription = EventSubscription(
            subscription_id=subscription_id,
            user_id=user_id,
            event_types=event_types,
            callback=callback,
            filters=filters
        )
        
        self.subscriptions[subscription_id] = subscription
        logger.info(f"Created subscription {subscription_id} for user {user_id}")
        
        return subscription
    
    def unsubscribe(self, subscription_id: str) -> bool:
        """Unsubscribe from real-time events"""
        if subscription_id in self.subscriptions:
            del self.subscriptions[subscription_id]
            logger.info(f"Removed subscription {subscription_id}")
            return True
        return False
    
    async def publish_event(self, event: RealtimeEvent) -> None:
        """Publish event to all matching subscriptions"""
        # Add to history
        self.event_history.append(event)
        if len(self.event_history) > self.max_history_size:
            self.event_history = self.event_history[-self.max_history_size:]
        
        # Notify subscribers
        for subscription in self.subscriptions.values():
            if subscription.matches_event(event):
                try:
                    subscription.callback(event)
                    subscription.last_activity = datetime.now(timezone.utc)
                except Exception as e:
                    logger.error(f"Error in subscription callback {subscription.subscription_id}: {e}")
    
    def get_event_history(
        self,
        user_id: Optional[str] = None,
        event_types: Optional[List[EventType]] = None,
        limit: int = 100
    ) -> List[RealtimeEvent]:
        """Get recent event history"""
        filtered_events = self.event_history
        
        if user_id:
            filtered_events = [e for e in filtered_events if e.user_id == user_id]
        
        if event_types:
            filtered_events = [e for e in filtered_events if e.event_type in event_types]
        
        return filtered_events[-limit:]
    
    def cleanup_inactive_subscriptions(self, max_inactive_minutes: int = 30) -> int:
        """Remove inactive subscriptions"""
        cutoff_time = datetime.now(timezone.utc).timestamp() - (max_inactive_minutes * 60)
        inactive_subs = [
            sub_id for sub_id, sub in self.subscriptions.items()
            if sub.last_activity.timestamp() < cutoff_time
        ]
        
        for sub_id in inactive_subs:
            del self.subscriptions[sub_id]
        
        logger.info(f"Cleaned up {len(inactive_subs)} inactive subscriptions")
        return len(inactive_subs)

class MSMEEventEmitter:
    """Emits MSME-specific events for real-time updates"""
    
    def __init__(self, event_manager: RealtimeEventManager):
        self.event_manager = event_manager
    
    async def emit_msme_created(self, msme_data: Dict[str, Any], user_id: str) -> None:
        """Emit MSME creation event"""
        event = RealtimeEvent(
            event_type=EventType.MSME_CREATED,
            data=msme_data,
            timestamp=datetime.now(timezone.utc),
            event_id=f"msme_created_{msme_data.get('msme_id')}_{int(datetime.now(timezone.utc).timestamp())}",
            user_id=user_id,
            msme_id=msme_data.get('msme_id')
        )
        await self.event_manager.publish_event(event)
    
    async def emit_score_updated(
        self,
        msme_id: str,
        old_score: float,
        new_score: float,
        old_risk_band: str,
        new_risk_band: str,
        user_id: str
    ) -> None:
        """Emit score update event"""
        event_data = {
            'msme_id': msme_id,
            'old_score': old_score,
            'new_score': new_score,
            'old_risk_band': old_risk_band,
            'new_risk_band': new_risk_band,
            'score_change': new_score - old_score
        }
        
        event = RealtimeEvent(
            event_type=EventType.SCORE_UPDATED,
            data=event_data,
            timestamp=datetime.now(timezone.utc),
            event_id=f"score_updated_{msme_id}_{int(datetime.now(timezone.utc).timestamp())}",
            user_id=user_id,
            msme_id=msme_id
        )
        await self.event_manager.publish_event(event)
        
        # Also emit risk band change if it changed
        if old_risk_band != new_risk_band:
            await self.emit_risk_band_changed(msme_id, old_risk_band, new_risk_band, user_id)
    
    async def emit_risk_band_changed(
        self,
        msme_id: str,
        old_risk_band: str,
        new_risk_band: str,
        user_id: str
    ) -> None:
        """Emit risk band change event"""
        event_data = {
            'msme_id': msme_id,
            'old_risk_band': old_risk_band,
            'new_risk_band': new_risk_band,
            'severity': 'high' if new_risk_band == 'red' else 'medium' if new_risk_band == 'yellow' else 'low'
        }
        
        event = RealtimeEvent(
            event_type=EventType.RISK_BAND_CHANGED,
            data=event_data,
            timestamp=datetime.now(timezone.utc),
            event_id=f"risk_changed_{msme_id}_{int(datetime.now(timezone.utc).timestamp())}",
            user_id=user_id,
            msme_id=msme_id
        )
        await self.event_manager.publish_event(event)
    
    async def emit_signal_added(
        self,
        msme_id: str,
        signal_data: Dict[str, Any],
        user_id: str
    ) -> None:
        """Emit signal addition event"""
        event_data = {
            'msme_id': msme_id,
            'signal_source': signal_data.get('source'),
            'signal_value': signal_data.get('value'),
            'signal_id': signal_data.get('signal_id')
        }
        
        event = RealtimeEvent(
            event_type=EventType.SIGNAL_ADDED,
            data=event_data,
            timestamp=datetime.now(timezone.utc),
            event_id=f"signal_added_{msme_id}_{int(datetime.now(timezone.utc).timestamp())}",
            user_id=user_id,
            msme_id=msme_id
        )
        await self.event_manager.publish_event(event)
    
    async def emit_portfolio_summary_updated(
        self,
        summary_data: Dict[str, Any],
        user_id: str
    ) -> None:
        """Emit portfolio summary update event"""
        event = RealtimeEvent(
            event_type=EventType.PORTFOLIO_SUMMARY_UPDATED,
            data=summary_data,
            timestamp=datetime.now(timezone.utc),
            event_id=f"portfolio_updated_{int(datetime.now(timezone.utc).timestamp())}",
            user_id=user_id
        )
        await self.event_manager.publish_event(event)

class WebSocketManager:
    """Manages WebSocket connections for real-time updates"""
    
    def __init__(self, event_manager: RealtimeEventManager):
        self.event_manager = event_manager
        self.connections: Dict[str, Any] = {}  # WebSocket connections
    
    async def connect(self, websocket, user_id: str, connection_id: str) -> None:
        """Handle new WebSocket connection"""
        self.connections[connection_id] = {
            'websocket': websocket,
            'user_id': user_id,
            'connected_at': datetime.now(timezone.utc)
        }
        
        # Subscribe to events for this user
        def send_event(event: RealtimeEvent):
            asyncio.create_task(self._send_to_websocket(connection_id, event))
        
        self.event_manager.subscribe(
            subscription_id=f"ws_{connection_id}",
            user_id=user_id,
            event_types=list(EventType),
            callback=send_event,
            filters={'user_id': user_id}
        )
        
        logger.info(f"WebSocket connected: {connection_id} for user {user_id}")
    
    async def disconnect(self, connection_id: str) -> None:
        """Handle WebSocket disconnection"""
        if connection_id in self.connections:
            del self.connections[connection_id]
            self.event_manager.unsubscribe(f"ws_{connection_id}")
            logger.info(f"WebSocket disconnected: {connection_id}")
    
    async def _send_to_websocket(self, connection_id: str, event: RealtimeEvent) -> None:
        """Send event to specific WebSocket connection"""
        if connection_id in self.connections:
            try:
                websocket = self.connections[connection_id]['websocket']
                await websocket.send_text(event.to_json())
            except Exception as e:
                logger.error(f"Error sending to WebSocket {connection_id}: {e}")
                await self.disconnect(connection_id)

# Global instances
realtime_event_manager = RealtimeEventManager()
msme_event_emitter = MSMEEventEmitter(realtime_event_manager)
websocket_manager = WebSocketManager(realtime_event_manager)
