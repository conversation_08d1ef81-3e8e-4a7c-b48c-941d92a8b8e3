/**
 * Performance optimization utilities for AI Copilot
 * Handles message virtualization, state management, and performance monitoring
 */

import { useCallback, useMemo, useRef, useEffect, useState } from 'react';
import { ChatMessage } from '@/types/copilot';

// Local performance metrics interface for component monitoring
interface ComponentPerformanceMetrics {
  component_name: string;
  render_time_ms: number;
  memory_usage_mb: number;
  api_call_count: number;
  cache_hit_rate: number;
  error_count: number;
  user_interactions: number;
  timestamp: Date;
}

// Performance monitoring hook
export function usePerformanceMonitor(componentName: string) {
  const startTime = useRef<number>(Date.now());
  const renderCount = useRef<number>(0);
  const lastUpdateTime = useRef<number>(0);
  const [metrics, setMetrics] = useState<ComponentPerformanceMetrics | null>(null);

  // Throttled metrics update to prevent infinite loops
  const updateMetrics = useCallback(() => {
    const now = Date.now();

    // Only update metrics every 1 second to prevent infinite loops
    if (now - lastUpdateTime.current < 1000) {
      return;
    }

    renderCount.current += 1;

    // Measure render time
    const renderTime = now - startTime.current;

    // Update metrics
    const newMetrics: ComponentPerformanceMetrics = {
      component_name: componentName,
      render_time_ms: renderTime,
      memory_usage_mb: (performance as any).memory?.usedJSHeapSize / 1024 / 1024 || 0,
      api_call_count: 0, // Will be updated by API calls
      cache_hit_rate: 0, // Will be updated by cache usage
      error_count: 0, // Will be updated by error handlers
      user_interactions: renderCount.current,
      timestamp: new Date()
    };

    setMetrics(newMetrics);
    lastUpdateTime.current = now;

    // Log performance warnings
    if (renderTime > 100) {
      console.warn(`Slow render detected in ${componentName}: ${renderTime}ms`);
    }

    startTime.current = now;
  }, [componentName]);

  // Only run on mount and when componentName changes
  useEffect(() => {
    updateMetrics();
  }, [componentName]);

  // Update render count on every render without triggering state updates
  useEffect(() => {
    renderCount.current += 1;
  });

  return metrics;
}

// Message virtualization for large message lists
export function useMessageVirtualization(
  messages: ChatMessage[],
  containerHeight: number = 400,
  itemHeight: number = 100
) {
  const [scrollTop, setScrollTop] = useState(0);
  const [visibleRange, setVisibleRange] = useState({ start: 0, end: 10 });

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const newScrollTop = e.currentTarget.scrollTop;
    setScrollTop(newScrollTop);
  }, []);

  // Calculate visible message range
  const { visibleMessages, totalHeight, offsetY } = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 2,
      messages.length
    );

    const visibleMessages = messages.slice(startIndex, endIndex);
    const totalHeight = messages.length * itemHeight;
    const offsetY = startIndex * itemHeight;

    setVisibleRange({ start: startIndex, end: endIndex });

    return { visibleMessages, totalHeight, offsetY };
  }, [messages, scrollTop, containerHeight, itemHeight]);

  return {
    visibleMessages,
    totalHeight,
    offsetY,
    handleScroll,
    visibleRange
  };
}

// Debounced state updates for better performance
export function useDebouncedState<T>(
  initialValue: T,
  delay: number = 300
): [T, (value: T) => void, T] {
  const [value, setValue] = useState<T>(initialValue);
  const [debouncedValue, setDebouncedValue] = useState<T>(initialValue);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const updateValue = useCallback((newValue: T) => {
    setValue(newValue);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setDebouncedValue(newValue);
    }, delay);
  }, [delay]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return [value, updateValue, debouncedValue];
}

// Optimized message cache
class MessageCache {
  private cache = new Map<string, ChatMessage>();
  private maxSize = 1000;
  private accessOrder = new Map<string, number>();

  set(key: string, message: ChatMessage): void {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const oldestKey = this.getOldestKey();
      if (oldestKey) {
        this.cache.delete(oldestKey);
        this.accessOrder.delete(oldestKey);
      }
    }

    this.cache.set(key, message);
    this.accessOrder.set(key, Date.now());
  }

  get(key: string): ChatMessage | undefined {
    const message = this.cache.get(key);
    if (message) {
      this.accessOrder.set(key, Date.now());
    }
    return message;
  }

  has(key: string): boolean {
    return this.cache.has(key);
  }

  clear(): void {
    this.cache.clear();
    this.accessOrder.clear();
  }

  private getOldestKey(): string | undefined {
    let oldestKey: string | undefined;
    let oldestTime = Infinity;

    for (const [key, time] of this.accessOrder) {
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: 0 // Would need to track hits/misses
    };
  }
}

// Global message cache instance
export const messageCache = new MessageCache();

// Optimized message rendering hook
export function useOptimizedMessages(messages: ChatMessage[]) {
  const [processedMessages, setProcessedMessages] = useState<ChatMessage[]>([]);
  const processingRef = useRef<boolean>(false);
  const lastProcessedHash = useRef<string>('');

  // Create a stable hash of messages to prevent unnecessary processing
  const messagesHash = useMemo(() => {
    return messages.map(m => `${m.id}-${m.timestamp.getTime()}`).join('|');
  }, [messages]);

  useEffect(() => {
    // Skip processing if messages haven't actually changed
    if (messagesHash === lastProcessedHash.current || processingRef.current) {
      return;
    }

    const processMessages = async () => {
      processingRef.current = true;
      lastProcessedHash.current = messagesHash;

      try {
        // Process messages in chunks to avoid blocking the UI
        const chunkSize = 50;
        const processed: ChatMessage[] = [];

        for (let i = 0; i < messages.length; i += chunkSize) {
          const chunk = messages.slice(i, i + chunkSize);

          // Process chunk
          const processedChunk = chunk.map(message => {
            const cacheKey = `${message.id}-${message.timestamp.getTime()}`;

            // Check cache first
            const cached = messageCache.get(cacheKey);
            if (cached) {
              return cached;
            }

            // Process message (validate, sanitize, etc.)
            const processedMessage: ChatMessage = {
              ...message,
              timestamp: message.timestamp instanceof Date ? message.timestamp : new Date(message.timestamp)
            };

            // Cache processed message
            messageCache.set(cacheKey, processedMessage);

            return processedMessage;
          });

          processed.push(...processedChunk);

          // Yield control to prevent blocking
          await new Promise(resolve => setTimeout(resolve, 0));
        }

        setProcessedMessages(processed);
      } finally {
        processingRef.current = false;
      }
    };

    processMessages();
  }, [messagesHash, messages]);

  return processedMessages;
}

// Performance-optimized scroll hook
export function useOptimizedScroll(
  scrollRef: React.RefObject<HTMLDivElement>,
  dependencies: any[] = []
) {
  const lastScrollTime = useRef<number>(0);
  const dependenciesHash = useRef<string>('');

  const scrollToBottom = useCallback(() => {
    if (!scrollRef.current) return;

    const element = scrollRef.current;
    const isNearBottom = element.scrollTop + element.clientHeight >= element.scrollHeight - 100;

    // Only auto-scroll if user is near bottom
    if (isNearBottom) {
      requestAnimationFrame(() => {
        element.scrollTo({
          top: element.scrollHeight,
          behavior: 'smooth'
        });
      });
    }
  }, [scrollRef]);

  const scrollToTop = useCallback(() => {
    if (!scrollRef.current) return;

    requestAnimationFrame(() => {
      scrollRef.current?.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
  }, [scrollRef]);

  // Create a stable hash of dependencies to prevent infinite loops
  const currentDependenciesHash = useMemo(() => {
    return JSON.stringify(dependencies);
  }, [dependencies]);

  // Auto-scroll when dependencies actually change (not just array reference)
  useEffect(() => {
    const now = Date.now();

    // Throttle scroll updates to prevent excessive scrolling
    if (now - lastScrollTime.current < 100) {
      return;
    }

    // Only scroll if dependencies actually changed
    if (currentDependenciesHash !== dependenciesHash.current) {
      dependenciesHash.current = currentDependenciesHash;
      lastScrollTime.current = now;
      scrollToBottom();
    }
  }, [currentDependenciesHash, scrollToBottom]);

  return { scrollToBottom, scrollToTop };
}

// Throttled API calls to prevent spam
export function useThrottledApiCall<T extends any[], R>(
  apiCall: (...args: T) => Promise<R>,
  delay: number = 1000
) {
  const lastCallTime = useRef<number>(0);
  const pendingCall = useRef<Promise<R> | null>(null);

  return useCallback(async (...args: T): Promise<R> => {
    const now = Date.now();
    const timeSinceLastCall = now - lastCallTime.current;

    // If we have a pending call, return it
    if (pendingCall.current) {
      return pendingCall.current;
    }

    // If enough time has passed, make the call immediately
    if (timeSinceLastCall >= delay) {
      lastCallTime.current = now;
      pendingCall.current = apiCall(...args);
      
      try {
        const result = await pendingCall.current;
        return result;
      } finally {
        pendingCall.current = null;
      }
    }

    // Otherwise, wait and then make the call
    const waitTime = delay - timeSinceLastCall;
    await new Promise(resolve => setTimeout(resolve, waitTime));
    
    lastCallTime.current = Date.now();
    pendingCall.current = apiCall(...args);
    
    try {
      const result = await pendingCall.current;
      return result;
    } finally {
      pendingCall.current = null;
    }
  }, [apiCall, delay]);
}

// Memory usage monitoring
export function useMemoryMonitor() {
  const [memoryUsage, setMemoryUsage] = useState<{
    used: number;
    total: number;
    percentage: number;
  } | null>(null);

  useEffect(() => {
    const updateMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const used = memory.usedJSHeapSize / 1024 / 1024; // MB
        const total = memory.totalJSHeapSize / 1024 / 1024; // MB
        const percentage = (used / total) * 100;

        setMemoryUsage({ used, total, percentage });

        // Warn if memory usage is high
        if (percentage > 80) {
          console.warn(`High memory usage detected: ${percentage.toFixed(1)}%`);
        }
      }
    };

    updateMemoryUsage();
    const interval = setInterval(updateMemoryUsage, 5000); // Check every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return memoryUsage;
}
