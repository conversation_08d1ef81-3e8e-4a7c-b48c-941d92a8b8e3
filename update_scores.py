#!/usr/bin/env python3
"""
Script to manually update MSME scores based on signals
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def calculate_score(signals):
    """Calculate MSME score based on signals"""
    weights = {
        "gst": 0.3,
        "upi": 0.25,
        "reviews": 0.2,
        "justdial": 0.1,
        "instagram": 0.1,
        "maps": 0.05
    }
    
    total_score = 0
    total_weight = 0
    
    for signal in signals:
        source = signal.get('source')
        normalized = signal.get('normalized', 0)
        
        if source in weights:
            total_score += normalized * weights[source]
            total_weight += weights[source]
    
    if total_weight > 0:
        return min(1000.0, (total_score / total_weight) * 1000)
    return 0.0

def determine_risk_band(score):
    """Determine risk band based on score"""
    if score >= 700:
        return "green"
    elif score >= 400:
        return "yellow"
    else:
        return "red"

def update_msme_score(msme_id, score, risk_band):
    """Update MSME score via API"""
    update_data = {
        "score": score,
        "risk_band": risk_band
    }
    response = requests.put(f"{BASE_URL}/msme/{msme_id}", json=update_data)
    if response.status_code == 200:
        print(f"Updated MSME {msme_id}: score={score:.1f}, risk_band={risk_band}")
        return True
    else:
        print(f"Failed to update MSME {msme_id}: {response.text}")
        return False

def main():
    print("📊 Updating MSME scores based on signals...")
    
    # Get all MSMEs
    response = requests.get(f"{BASE_URL}/msme/")
    if response.status_code != 200:
        print(f"Failed to get MSMEs: {response.text}")
        return
    
    msmes = response.json()
    print(f"Found {len(msmes)} MSMEs")
    
    for msme in msmes:
        msme_id = msme['msme_id']
        name = msme['name']
        
        # Get signals for this MSME
        signals_response = requests.get(f"{BASE_URL}/api/signals/{msme_id}")
        if signals_response.status_code == 200:
            signals = signals_response.json()
            print(f"\n🏢 {name}: {len(signals)} signals")
            
            # Calculate new score
            new_score = calculate_score(signals)
            new_risk_band = determine_risk_band(new_score)
            
            # Update MSME
            update_msme_score(msme_id, new_score, new_risk_band)
        else:
            print(f"Failed to get signals for {name}: {signals_response.text}")
    
    print(f"\n✅ Score update complete!")

if __name__ == "__main__":
    main()
