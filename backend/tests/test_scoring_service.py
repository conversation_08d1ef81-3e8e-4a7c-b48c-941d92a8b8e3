"""
Tests for the credit scoring service.

This module tests the scoring algorithms, signal normalization,
and risk band determination functionality.

Author: Credit Chakra Team
Version: 1.0.0
"""
import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timezone

from services.scoring import (
    normalize_signal, calculate_msme_score, determine_risk_band,
    recalculate_msme_score, SIGNAL_WEIGHTS
)
from models.signal import SignalSource
from models.msme import RiskBand


class TestSignalNormalization:
    """Test cases for signal normalization functions."""
    
    def test_gst_signal_normalization(self):
        """Test GST signal normalization."""
        # Test low turnover (0-10L range)
        assert normalize_signal(SignalSource.GST, 500000) == pytest.approx(0.15, rel=1e-2)
        assert normalize_signal(SignalSource.GST, 1000000) == pytest.approx(0.3, rel=1e-2)
        
        # Test medium turnover (10L-50L range)
        assert normalize_signal(SignalSource.GST, 2500000) == pytest.approx(0.45, rel=1e-2)
        assert normalize_signal(SignalSource.GST, 5000000) == pytest.approx(0.7, rel=1e-2)
        
        # Test high turnover (50L+ range)
        assert normalize_signal(SignalSource.GST, 7500000) == pytest.approx(0.775, rel=1e-2)
        assert normalize_signal(SignalSource.GST, 15000000) == pytest.approx(1.0, rel=1e-2)
        
        # Test edge cases
        assert normalize_signal(SignalSource.GST, 0) == 0.0
        assert normalize_signal(SignalSource.GST, -1000) == 0.0
        assert normalize_signal(SignalSource.GST, "invalid") == 0.0
    
    def test_upi_signal_normalization(self):
        """Test UPI signal normalization."""
        # Test valid UPI data
        upi_data = {"transaction_count": 500, "volume": 250000}
        normalized = normalize_signal(SignalSource.UPI, upi_data)
        expected = (min(1.0, 500/1000) + min(1.0, 250000/500000)) / 2
        assert normalized == pytest.approx(expected, rel=1e-2)
        
        # Test high transaction data
        high_upi_data = {"transaction_count": 1500, "volume": 750000}
        normalized = normalize_signal(SignalSource.UPI, high_upi_data)
        assert normalized == pytest.approx(1.0, rel=1e-2)
        
        # Test edge cases
        assert normalize_signal(SignalSource.UPI, {}) == 0.0
        assert normalize_signal(SignalSource.UPI, {"transaction_count": 0}) == 0.0
        assert normalize_signal(SignalSource.UPI, "invalid") == 0.0
    
    def test_reviews_signal_normalization(self):
        """Test reviews signal normalization."""
        # Test good reviews
        reviews_data = {"average_rating": 4.5, "review_count": 100}
        normalized = normalize_signal(SignalSource.REVIEWS, reviews_data)
        rating_score = min(1.0, 4.5 / 5.0)
        count_weight = min(1.0, 100 / 50)
        expected = rating_score * (0.7 + 0.3 * count_weight)
        assert normalized == pytest.approx(expected, rel=1e-2)
        
        # Test poor reviews
        poor_reviews = {"average_rating": 2.0, "review_count": 10}
        normalized = normalize_signal(SignalSource.REVIEWS, poor_reviews)
        assert normalized < 0.5
        
        # Test edge cases
        assert normalize_signal(SignalSource.REVIEWS, {}) == 0.0
        assert normalize_signal(SignalSource.REVIEWS, {"average_rating": 0}) == 0.0
        assert normalize_signal(SignalSource.REVIEWS, "invalid") == 0.0
    
    def test_justdial_signal_normalization(self):
        """Test JustDial signal normalization."""
        # Test valid JustDial data
        justdial_data = {"listing_quality": 0.8, "contact_responses": 0.6}
        normalized = normalize_signal(SignalSource.JUSTDIAL, justdial_data)
        expected = (0.8 + 0.6) / 2
        assert normalized == pytest.approx(expected, rel=1e-2)
        
        # Test edge cases
        assert normalize_signal(SignalSource.JUSTDIAL, {}) == 0.0
        assert normalize_signal(SignalSource.JUSTDIAL, "invalid") == 0.0
    
    def test_instagram_signal_normalization(self):
        """Test Instagram signal normalization."""
        # Test valid Instagram data
        instagram_data = {"followers": 5000, "engagement_rate": 0.05, "post_frequency": 0.7}
        normalized = normalize_signal(SignalSource.INSTAGRAM, instagram_data)
        assert 0 <= normalized <= 1
        
        # Test edge cases
        assert normalize_signal(SignalSource.INSTAGRAM, {}) == 0.0
        assert normalize_signal(SignalSource.INSTAGRAM, "invalid") == 0.0
    
    def test_maps_signal_normalization(self):
        """Test Maps signal normalization."""
        # Test valid Maps data
        maps_data = {"rating": 4.2, "reviews": 80, "photos": 15}
        normalized = normalize_signal(SignalSource.MAPS, maps_data)
        assert 0 <= normalized <= 1
        
        # Test edge cases
        assert normalize_signal(SignalSource.MAPS, {}) == 0.0
        assert normalize_signal(SignalSource.MAPS, "invalid") == 0.0


class TestScoreCalculation:
    """Test cases for MSME score calculation."""
    
    def test_calculate_msme_score_empty_signals(self):
        """Test score calculation with no signals."""
        score = calculate_msme_score([])
        assert score == 0.0
    
    def test_calculate_msme_score_single_signal(self):
        """Test score calculation with single signal."""
        signals = [
            {
                "source": "gst",
                "normalized": 0.8,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        ]
        score = calculate_msme_score(signals)
        expected = 0.8 * SIGNAL_WEIGHTS[SignalSource.GST] * 1000
        assert score == pytest.approx(expected, rel=1e-2)
    
    def test_calculate_msme_score_multiple_signals(self):
        """Test score calculation with multiple signals."""
        signals = [
            {
                "source": "gst",
                "normalized": 0.8,
                "timestamp": datetime.now(timezone.utc).isoformat()
            },
            {
                "source": "upi",
                "normalized": 0.7,
                "timestamp": datetime.now(timezone.utc).isoformat()
            },
            {
                "source": "reviews",
                "normalized": 0.9,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        ]
        score = calculate_msme_score(signals)
        
        # Calculate expected weighted score
        total_weight = (SIGNAL_WEIGHTS[SignalSource.GST] + 
                       SIGNAL_WEIGHTS[SignalSource.UPI] + 
                       SIGNAL_WEIGHTS[SignalSource.REVIEWS])
        weighted_score = (0.8 * SIGNAL_WEIGHTS[SignalSource.GST] + 
                         0.7 * SIGNAL_WEIGHTS[SignalSource.UPI] + 
                         0.9 * SIGNAL_WEIGHTS[SignalSource.REVIEWS])
        expected = (weighted_score / total_weight) * 1000
        
        assert score == pytest.approx(expected, rel=1e-2)
    
    def test_calculate_msme_score_latest_signals_only(self):
        """Test that only latest signals per source are used."""
        old_timestamp = datetime(2024, 1, 1).isoformat()
        new_timestamp = datetime(2024, 2, 1).isoformat()
        
        signals = [
            {
                "source": "gst",
                "normalized": 0.5,
                "timestamp": old_timestamp
            },
            {
                "source": "gst",
                "normalized": 0.8,
                "timestamp": new_timestamp
            }
        ]
        score = calculate_msme_score(signals)
        
        # Should use the newer signal (0.8)
        expected = 0.8 * SIGNAL_WEIGHTS[SignalSource.GST] * 1000
        assert score == pytest.approx(expected, rel=1e-2)
    
    def test_calculate_msme_score_invalid_signals(self):
        """Test score calculation with invalid signals."""
        signals = [
            {
                "source": "invalid_source",
                "normalized": 0.8,
                "timestamp": datetime.now(timezone.utc).isoformat()
            },
            {
                "source": "gst",
                "normalized": 0.7,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        ]
        score = calculate_msme_score(signals)
        
        # Should only use the valid GST signal
        expected = 0.7 * SIGNAL_WEIGHTS[SignalSource.GST] * 1000
        assert score == pytest.approx(expected, rel=1e-2)
    
    def test_calculate_msme_score_max_limit(self):
        """Test that score doesn't exceed 1000."""
        signals = [
            {
                "source": "gst",
                "normalized": 1.0,
                "timestamp": datetime.now(timezone.utc).isoformat()
            },
            {
                "source": "upi",
                "normalized": 1.0,
                "timestamp": datetime.now(timezone.utc).isoformat()
            },
            {
                "source": "reviews",
                "normalized": 1.0,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        ]
        score = calculate_msme_score(signals)
        assert score <= 1000.0


class TestRiskBandDetermination:
    """Test cases for risk band determination."""
    
    def test_determine_risk_band_green(self):
        """Test green risk band determination."""
        assert determine_risk_band(700) == RiskBand.GREEN
        assert determine_risk_band(850) == RiskBand.GREEN
        assert determine_risk_band(1000) == RiskBand.GREEN
    
    def test_determine_risk_band_yellow(self):
        """Test yellow risk band determination."""
        assert determine_risk_band(400) == RiskBand.YELLOW
        assert determine_risk_band(550) == RiskBand.YELLOW
        assert determine_risk_band(699) == RiskBand.YELLOW
    
    def test_determine_risk_band_red(self):
        """Test red risk band determination."""
        assert determine_risk_band(0) == RiskBand.RED
        assert determine_risk_band(200) == RiskBand.RED
        assert determine_risk_band(399) == RiskBand.RED
    
    def test_determine_risk_band_edge_cases(self):
        """Test risk band determination edge cases."""
        assert determine_risk_band(399.9) == RiskBand.RED
        assert determine_risk_band(400.0) == RiskBand.YELLOW
        assert determine_risk_band(699.9) == RiskBand.YELLOW
        assert determine_risk_band(700.0) == RiskBand.GREEN


class TestScoreRecalculation:
    """Test cases for MSME score recalculation."""
    
    @pytest.mark.asyncio
    async def test_recalculate_msme_score_success(self, mock_firestore):
        """Test successful score recalculation."""
        # Mock Firestore data
        mock_signals = [
            Mock(to_dict=lambda: {
                "source": "gst",
                "normalized": 0.8,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }),
            Mock(to_dict=lambda: {
                "source": "upi", 
                "normalized": 0.7,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        ]
        
        mock_firestore.collection.return_value.document.return_value.collection.return_value.stream.return_value = mock_signals
        
        with patch('services.scoring.get_firestore_client', return_value=mock_firestore):
            result = await recalculate_msme_score("test_msme_id")
        
        assert "new_score" in result
        assert "new_risk_band" in result
        assert "signals_processed" in result
        assert result["signals_processed"] == 2
        
        # Verify Firestore update was called
        mock_firestore.collection.return_value.document.return_value.update.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_recalculate_msme_score_no_signals(self, mock_firestore):
        """Test score recalculation with no signals."""
        mock_firestore.collection.return_value.document.return_value.collection.return_value.stream.return_value = []
        
        with patch('services.scoring.get_firestore_client', return_value=mock_firestore):
            result = await recalculate_msme_score("test_msme_id")
        
        assert result["new_score"] == 0.0
        assert result["new_risk_band"] == RiskBand.RED.value
        assert result["signals_processed"] == 0
    
    @pytest.mark.asyncio
    async def test_recalculate_msme_score_firestore_error(self, mock_firestore):
        """Test score recalculation with Firestore error."""
        mock_firestore.collection.side_effect = Exception("Firestore error")
        
        with patch('services.scoring.get_firestore_client', return_value=mock_firestore):
            result = await recalculate_msme_score("test_msme_id")
        
        assert "error" in result
        assert "Firestore error" in result["error"]


class TestSignalWeights:
    """Test cases for signal weight configuration."""
    
    def test_signal_weights_sum(self):
        """Test that signal weights sum to 1.0."""
        total_weight = sum(SIGNAL_WEIGHTS.values())
        assert total_weight == pytest.approx(1.0, rel=1e-2)
    
    def test_signal_weights_coverage(self):
        """Test that all signal sources have weights."""
        for source in SignalSource:
            assert source in SIGNAL_WEIGHTS
    
    def test_signal_weights_values(self):
        """Test that signal weights are reasonable."""
        for source, weight in SIGNAL_WEIGHTS.items():
            assert 0 < weight <= 1.0
        
        # GST should have highest weight (official data)
        assert SIGNAL_WEIGHTS[SignalSource.GST] >= max(
            SIGNAL_WEIGHTS[source] for source in SignalSource if source != SignalSource.GST
        )
        
        # Maps should have lowest weight (least reliable)
        assert SIGNAL_WEIGHTS[SignalSource.MAPS] <= min(
            SIGNAL_WEIGHTS[source] for source in SignalSource if source != SignalSource.MAPS
        )
