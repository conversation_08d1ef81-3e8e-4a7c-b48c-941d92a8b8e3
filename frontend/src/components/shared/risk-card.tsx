/**
 * Shared Risk Card Component
 * Eliminates duplication across portfolio, risk monitor, and analytics components
 */
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, Shield, TrendingUp, TrendingDown } from 'lucide-react';

interface RiskCardProps {
  riskLevel: 'green' | 'yellow' | 'red';
  count: number;
  totalCount: number;
  title?: string;
  showProgress?: boolean;
  showTrend?: boolean;
  trendDirection?: 'up' | 'down' | 'stable';
  trendValue?: number;
  className?: string;
  onClick?: () => void;
}

const riskConfig = {
  green: {
    label: 'Low Risk',
    icon: Shield,
    bgGradient: 'from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20',
    textColor: 'text-green-700 dark:text-green-400',
    iconBg: 'bg-green-500/10',
    progressColor: 'bg-green-500'
  },
  yellow: {
    label: 'Medium Risk',
    icon: TrendingUp,
    bgGradient: 'from-yellow-50 to-yellow-100 dark:from-yellow-950/20 dark:to-yellow-900/20',
    textColor: 'text-yellow-700 dark:text-yellow-400',
    iconBg: 'bg-yellow-500/10',
    progressColor: 'bg-yellow-500'
  },
  red: {
    label: 'High Risk',
    icon: AlertTriangle,
    bgGradient: 'from-red-50 to-red-100 dark:from-red-950/20 dark:to-red-900/20',
    textColor: 'text-red-700 dark:text-red-400',
    iconBg: 'bg-red-500/10',
    progressColor: 'bg-red-500'
  }
};

export function RiskCard({
  riskLevel,
  count,
  totalCount,
  title,
  showProgress = true,
  showTrend = false,
  trendDirection = 'stable',
  trendValue,
  className = '',
  onClick
}: RiskCardProps) {
  const config = riskConfig[riskLevel];
  const percentage = totalCount > 0 ? Math.round((count / totalCount) * 100) : 0;
  const Icon = config.icon;

  const getTrendIcon = () => {
    switch (trendDirection) {
      case 'up':
        return <TrendingUp className="h-3 w-3" />;
      case 'down':
        return <TrendingDown className="h-3 w-3" />;
      default:
        return null;
    }
  };

  const getTrendColor = () => {
    switch (trendDirection) {
      case 'up':
        return riskLevel === 'red' ? 'text-red-600' : 'text-green-600';
      case 'down':
        return riskLevel === 'red' ? 'text-green-600' : 'text-red-600';
      default:
        return 'text-muted-foreground';
    }
  };

  return (
    <Card 
      className={`border-0 shadow-lg bg-gradient-to-br ${config.bgGradient} hover:shadow-xl transition-all duration-200 ${onClick ? 'cursor-pointer' : ''} ${className}`}
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <CardTitle className={`flex items-center gap-2 ${config.textColor}`}>
          <div className={`p-2 ${config.iconBg} rounded-lg`}>
            <Icon className="h-5 w-5" />
          </div>
          {title || config.label}
          {showTrend && trendValue !== undefined && (
            <Badge variant="secondary" className={`ml-auto ${getTrendColor()}`}>
              {getTrendIcon()}
              {Math.abs(trendValue)}%
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className={`text-3xl font-bold ${config.textColor} mb-2`}>
          {count.toLocaleString()}
        </div>
        <p className={`text-sm ${config.textColor.replace('700', '600').replace('400', '500')} mb-3`}>
          {percentage}% of portfolio
        </p>
        {showProgress && (
          <Progress 
            value={percentage} 
            className="h-2"
            style={{
              '--progress-background': config.progressColor.replace('bg-', '')
            } as React.CSSProperties}
          />
        )}
      </CardContent>
    </Card>
  );
}

interface RiskDistributionProps {
  riskDistribution: {
    green: number;
    yellow: number;
    red: number;
  };
  totalCount: number;
  showTrends?: boolean;
  trends?: {
    green?: number;
    yellow?: number;
    red?: number;
  };
  onRiskClick?: (riskLevel: 'green' | 'yellow' | 'red') => void;
  className?: string;
}

export function RiskDistribution({
  riskDistribution,
  totalCount,
  showTrends = false,
  trends = {},
  onRiskClick,
  className = ''
}: RiskDistributionProps) {
  const getTrendDirection = (value?: number): 'up' | 'down' | 'stable' => {
    if (!value) return 'stable';
    return value > 0 ? 'up' : value < 0 ? 'down' : 'stable';
  };

  return (
    <div className={`grid gap-6 md:grid-cols-3 ${className}`}>
      <RiskCard
        riskLevel="green"
        count={riskDistribution.green}
        totalCount={totalCount}
        showTrend={showTrends}
        trendDirection={getTrendDirection(trends.green)}
        trendValue={trends.green}
        onClick={() => onRiskClick?.('green')}
      />
      <RiskCard
        riskLevel="yellow"
        count={riskDistribution.yellow}
        totalCount={totalCount}
        showTrend={showTrends}
        trendDirection={getTrendDirection(trends.yellow)}
        trendValue={trends.yellow}
        onClick={() => onRiskClick?.('yellow')}
      />
      <RiskCard
        riskLevel="red"
        count={riskDistribution.red}
        totalCount={totalCount}
        showTrend={showTrends}
        trendDirection={getTrendDirection(trends.red)}
        trendValue={trends.red}
        onClick={() => onRiskClick?.('red')}
      />
    </div>
  );
}

interface RiskBadgeProps {
  riskLevel: 'green' | 'yellow' | 'red';
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

export function RiskBadge({ 
  riskLevel, 
  size = 'md', 
  showIcon = true, 
  className = '' 
}: RiskBadgeProps) {
  const config = riskConfig[riskLevel];
  const Icon = config.icon;

  const sizeClasses = {
    sm: 'text-xs px-2 py-1',
    md: 'text-sm px-3 py-1',
    lg: 'text-base px-4 py-2'
  };

  const iconSizes = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };

  return (
    <Badge 
      variant="secondary" 
      className={`${sizeClasses[size]} ${config.textColor} bg-opacity-20 border-0 ${className}`}
      style={{
        backgroundColor: `${config.progressColor.replace('bg-', '')}20`
      }}
    >
      {showIcon && <Icon className={`${iconSizes[size]} mr-1`} />}
      {config.label}
    </Badge>
  );
}
