'use client';

/**
 * Error Boundary Component for Risk Monitor
 * 
 * Provides graceful error handling for the Risk Monitor dashboard
 * with proper fallback UI and error reporting capabilities.
 * 
 * @component
 * @example
 * ```tsx
 * <ErrorBoundary>
 *   <RiskMonitorComponent />
 * </ErrorBoundary>
 * ```
 * 
 * Author: Credit Chakra Team
 * Version: 1.0.0
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertTriangle, RefreshCw, Shield } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class RiskMonitorErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Risk Monitor Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Report error to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      // Add error reporting service integration here
      console.error('Production error in Risk Monitor:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack
      });
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="container mx-auto px-6 py-8 space-y-6">
          {/* Error Header */}
          <div className="flex items-center justify-between border-b border-red-100 pb-6">
            <div className="flex items-center gap-6">
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-red-100 rounded-xl">
                    <AlertTriangle className="h-8 w-8 text-red-700" />
                  </div>
                  <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-red-700 to-red-600 bg-clip-text text-transparent">
                    Risk Monitor Error
                  </h1>
                </div>
                <p className="text-lg text-muted-foreground ml-14">
                  An error occurred while loading the risk monitoring dashboard
                </p>
              </div>
            </div>
            <Button
              onClick={this.handleRetry}
              variant="outline"
              className="border-red-200 text-red-700 hover:bg-red-50 transition-colors shadow-sm"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>

          {/* Error Details */}
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <div className="space-y-2">
                <p className="font-medium">Something went wrong with the Risk Monitor dashboard.</p>
                <p className="text-sm">
                  {this.state.error?.message || 'An unexpected error occurred'}
                </p>
                <div className="flex gap-2 mt-4">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={this.handleRetry}
                    className="border-red-300 text-red-700 hover:bg-red-100"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => window.location.reload()}
                    className="border-red-300 text-red-700 hover:bg-red-100"
                  >
                    Reload Page
                  </Button>
                </div>
              </div>
            </AlertDescription>
          </Alert>

          {/* Fallback Dashboard */}
          <Card className="border-red-200">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-700">
                <Shield className="h-5 w-5" />
                Risk Monitor Unavailable
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <AlertTriangle className="h-12 w-12 mx-auto mb-3 opacity-50 text-red-400" />
                <p className="mb-2">The Risk Monitor dashboard is temporarily unavailable.</p>
                <p className="text-sm">Please try refreshing the page or contact support if the issue persists.</p>
              </div>
            </CardContent>
          </Card>

          {/* Development Error Details */}
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <Card className="border-yellow-200 bg-yellow-50">
              <CardHeader>
                <CardTitle className="text-yellow-800">Development Error Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 text-sm">
                  <div>
                    <h4 className="font-medium text-yellow-800 mb-2">Error Message:</h4>
                    <pre className="bg-yellow-100 p-3 rounded text-yellow-900 overflow-auto">
                      {this.state.error.message}
                    </pre>
                  </div>
                  {this.state.error.stack && (
                    <div>
                      <h4 className="font-medium text-yellow-800 mb-2">Stack Trace:</h4>
                      <pre className="bg-yellow-100 p-3 rounded text-yellow-900 overflow-auto text-xs">
                        {this.state.error.stack}
                      </pre>
                    </div>
                  )}
                  {this.state.errorInfo?.componentStack && (
                    <div>
                      <h4 className="font-medium text-yellow-800 mb-2">Component Stack:</h4>
                      <pre className="bg-yellow-100 p-3 rounded text-yellow-900 overflow-auto text-xs">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default RiskMonitorErrorBoundary;
