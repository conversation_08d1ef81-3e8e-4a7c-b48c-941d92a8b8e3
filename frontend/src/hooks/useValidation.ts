/**
 * React hook for form validation and input sanitization
 * Provides real-time validation with security-focused sanitization
 */

import { useState, useCallback, useMemo } from 'react';
import {
  ValidationResult,
  validateBusinessName,
  validateEmail,
  validatePhone,
  validateGST,
  validateSearchTerm,
  validateChatMessage,
  validateNudgeMessage,
  validateNumeric,
  validateText,
  sanitizeInput
} from '@/lib/validation';

export type ValidationType = 
  | 'businessName'
  | 'email'
  | 'phone'
  | 'gst'
  | 'searchTerm'
  | 'chatMessage'
  | 'nudgeMessage'
  | 'numeric'
  | 'text';

export interface ValidationConfig {
  type: ValidationType;
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
}

export interface FieldValidation {
  value: string;
  sanitizedValue: string;
  isValid: boolean;
  errors: string[];
  touched: boolean;
}

export interface UseValidationReturn {
  fields: Record<string, FieldValidation>;
  validateField: (fieldName: string, value: string) => void;
  validateAllFields: () => boolean;
  resetField: (fieldName: string) => void;
  resetAllFields: () => void;
  getFieldProps: (fieldName: string) => {
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
    onBlur: () => void;
    'aria-invalid': boolean;
    'aria-describedby': string;
  };
  isFormValid: boolean;
  hasErrors: boolean;
}

/**
 * Hook for managing form validation with security-focused input sanitization
 */
export function useValidation(
  fieldConfigs: Record<string, ValidationConfig>
): UseValidationReturn {
  const [fields, setFields] = useState<Record<string, FieldValidation>>(() => {
    const initialFields: Record<string, FieldValidation> = {};
    
    Object.keys(fieldConfigs).forEach(fieldName => {
      initialFields[fieldName] = {
        value: '',
        sanitizedValue: '',
        isValid: !fieldConfigs[fieldName].required,
        errors: [],
        touched: false
      };
    });
    
    return initialFields;
  });

  // Validation function selector
  const getValidationFunction = useCallback((type: ValidationType, config: ValidationConfig) => {
    switch (type) {
      case 'businessName':
        return validateBusinessName;
      case 'email':
        return validateEmail;
      case 'phone':
        return validatePhone;
      case 'gst':
        return validateGST;
      case 'searchTerm':
        return validateSearchTerm;
      case 'chatMessage':
        return validateChatMessage;
      case 'nudgeMessage':
        return validateNudgeMessage;
      case 'numeric':
        return (value: string) => validateNumeric(value, config.min, config.max);
      case 'text':
        return (value: string) => validateText(value, config.minLength || 0, config.maxLength || 500);
      default:
        return (value: string) => ({
          isValid: true,
          sanitizedValue: sanitizeInput(value),
          errors: []
        });
    }
  }, []);

  // Validate a single field
  const validateField = useCallback((fieldName: string, value: string) => {
    const config = fieldConfigs[fieldName];
    if (!config) return;

    const validationFn = getValidationFunction(config.type, config);
    let result: ValidationResult;

    // Handle required field validation
    if (config.required && !value.trim()) {
      result = {
        isValid: false,
        sanitizedValue: '',
        errors: ['This field is required']
      };
    } else if (!value.trim() && !config.required) {
      result = {
        isValid: true,
        sanitizedValue: '',
        errors: []
      };
    } else {
      result = validationFn(value);
    }

    setFields(prev => ({
      ...prev,
      [fieldName]: {
        value,
        sanitizedValue: result.sanitizedValue,
        isValid: result.isValid,
        errors: result.errors,
        touched: true
      }
    }));
  }, [fieldConfigs, getValidationFunction]);

  // Validate all fields
  const validateAllFields = useCallback(() => {
    let allValid = true;

    setFields(prev => {
      const newFields = { ...prev };

      Object.keys(fieldConfigs).forEach(fieldName => {
        const field = newFields[fieldName];
        const config = fieldConfigs[fieldName];
        const validationFn = getValidationFunction(config.type, config);

        let result: ValidationResult;

        if (config.required && !field.value.trim()) {
          result = {
            isValid: false,
            sanitizedValue: '',
            errors: ['This field is required']
          };
        } else if (!field.value.trim() && !config.required) {
          result = {
            isValid: true,
            sanitizedValue: '',
            errors: []
          };
        } else {
          result = validationFn(field.value);
        }

        newFields[fieldName] = {
          ...field,
          sanitizedValue: result.sanitizedValue,
          isValid: result.isValid,
          errors: result.errors,
          touched: true
        };

        if (!result.isValid) {
          allValid = false;
        }
      });

      return newFields;
    });

    return allValid;
  }, [fieldConfigs, getValidationFunction]);

  // Reset a single field
  const resetField = useCallback((fieldName: string) => {
    const config = fieldConfigs[fieldName];
    if (!config) return;

    setFields(prev => ({
      ...prev,
      [fieldName]: {
        value: '',
        sanitizedValue: '',
        isValid: !config.required,
        errors: [],
        touched: false
      }
    }));
  }, [fieldConfigs]);

  // Reset all fields
  const resetAllFields = useCallback(() => {
    setFields(prev => {
      const newFields = { ...prev };
      
      Object.keys(fieldConfigs).forEach(fieldName => {
        const config = fieldConfigs[fieldName];
        newFields[fieldName] = {
          value: '',
          sanitizedValue: '',
          isValid: !config.required,
          errors: [],
          touched: false
        };
      });
      
      return newFields;
    });
  }, [fieldConfigs]);

  // Get props for form fields
  const getFieldProps = useCallback((fieldName: string) => {
    const field = fields[fieldName];
    
    return {
      value: field?.value || '',
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        validateField(fieldName, e.target.value);
      },
      onBlur: () => {
        // Re-validate on blur to ensure touched state is set
        if (field) {
          validateField(fieldName, field.value);
        }
      },
      'aria-invalid': field?.touched && !field?.isValid,
      'aria-describedby': `${fieldName}-error`
    };
  }, [fields, validateField]);

  // Computed values
  const isFormValid = useMemo(() => {
    return Object.values(fields).every(field => field.isValid);
  }, [fields]);

  const hasErrors = useMemo(() => {
    return Object.values(fields).some(field => field.touched && !field.isValid);
  }, [fields]);

  return {
    fields,
    validateField,
    validateAllFields,
    resetField,
    resetAllFields,
    getFieldProps,
    isFormValid,
    hasErrors
  };
}

/**
 * Hook for simple input validation (single field)
 */
export function useInputValidation(
  type: ValidationType,
  config: Omit<ValidationConfig, 'type'> = {}
) {
  const fullConfig = { type, ...config };
  const validation = useValidation({ input: fullConfig });
  
  return {
    value: validation.fields.input?.value || '',
    sanitizedValue: validation.fields.input?.sanitizedValue || '',
    isValid: validation.fields.input?.isValid ?? true,
    errors: validation.fields.input?.errors || [],
    touched: validation.fields.input?.touched || false,
    validate: (value: string) => validation.validateField('input', value),
    reset: () => validation.resetField('input'),
    getProps: () => validation.getFieldProps('input')
  };
}
