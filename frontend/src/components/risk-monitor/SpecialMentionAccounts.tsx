'use client';

/**
 * Special Mention Accounts (SMA) Component for Credit Chakra Risk Monitor
 * 
 * This component provides comprehensive SMA heatmap visualization for credit
 * officers following RBI compliance guidelines.
 *
 * Features:
 * - Interactive SMA heatmap with DPD classifications
 * - Portfolio overview with key SMA metrics
 * - Automated trigger indicators with emerald color palette
 * - Real-time data synchronization with 2-second load requirement
 * - Responsive design matching existing dashboard components
 * 
 * @component
 * @example
 * ```tsx
 * <SpecialMentionAccounts />
 * ```
 * 
 * Author: Credit Chakra Team
 * Version: 1.0.0
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertTriangle,
  Clock,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  MapPin,
  Building2,
  IndianRupee,
  Activity,
  Shield,
  FileText
} from 'lucide-react';

// Types for SMA data
interface SMADistribution {
  standard: number;
  sma_0: number;
  sma_1: number;
  sma_2: number;
  npa: number;
}

interface SMAHeatmapData {
  sma_distribution: SMADistribution;
  business_type_sma: Record<string, SMADistribution>;
  geographic_sma: Record<string, SMADistribution>;
  exposure_by_sma: Record<string, number>;
  total_exposure: number;
  npa_ratio: number;
  sma_ratio: number;
  generated_at: string;
}



interface PortfolioSummary {
  total_msmes: number;
  sma_accounts: number;
  npa_accounts: number;
  sma_ratio: number;
  npa_ratio: number;
  sma_trend_30d: number;
  npa_trend_30d: number;
  total_exposure: number;
  sma_exposure: number;
  npa_exposure: number;
  provision_required: number;
  regulatory_alerts: Array<{
    type: string;
    message: string;
    severity: string;
    due_date: string;
  }>;
  last_updated: string;
}

export const SpecialMentionAccounts = React.memo(() => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [heatmapData, setHeatmapData] = useState<SMAHeatmapData | null>(null);

  const [portfolioSummary, setPortfolioSummary] = useState<PortfolioSummary | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch SMA data from API with enhanced error handling
  const fetchSMAData = useCallback(async () => {
    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      console.log('SpecialMentionAccounts: Fetching data from API_BASE_URL:', API_BASE_URL);

      // Add timeout to prevent hanging requests
      const fetchWithTimeout = (url: string, timeout = 10000) => {
        return Promise.race([
          fetch(url),
          new Promise<never>((_, reject) =>
            setTimeout(() => reject(new Error('Request timeout')), timeout)
          )
        ]);
      };

      const [heatmapResponse, summaryResponse] = await Promise.all([
        fetchWithTimeout(`${API_BASE_URL}/api/dashboard/risk-monitor/sma-heatmap`),
        fetchWithTimeout(`${API_BASE_URL}/api/dashboard/risk-monitor/portfolio-summary`)
      ]);

      if (!heatmapResponse.ok || !summaryResponse.ok) {
        const errorDetails = [
          !heatmapResponse.ok && `Heatmap: ${heatmapResponse.status}`,
          !summaryResponse.ok && `Summary: ${summaryResponse.status}`
        ].filter(Boolean).join(', ');
        throw new Error(`API Error - ${errorDetails}`);
      }

      const [heatmapResult, summaryResult] = await Promise.all([
        heatmapResponse.json(),
        summaryResponse.json()
      ]);

      // Validate data structure before setting state
      if (heatmapResult?.data) {
        console.log('SpecialMentionAccounts: Heatmap data received:', heatmapResult.data);
        setHeatmapData(heatmapResult.data);
      }
      if (summaryResult?.data) {
        console.log('SpecialMentionAccounts: Portfolio summary received:', summaryResult.data);
        setPortfolioSummary(summaryResult.data);
      }

      console.log('SpecialMentionAccounts: Data fetch completed successfully');
      setError(null);

    } catch (err) {
      console.error('SMA data fetch error:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch SMA data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    console.log('SpecialMentionAccounts: Component mounted, starting data fetch...');
    fetchSMAData();
  }, [fetchSMAData]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchSMAData();
  }, [fetchSMAData]);

  // Memoized helper functions for performance
  // Severity color function removed for optimization

  const getSMAColor = useCallback((classification: string) => {
    switch (classification) {
      case 'standard': return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'sma_0': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'sma_1': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'sma_2': return 'bg-red-100 text-red-800 border-red-200';
      case 'npa': return 'bg-red-200 text-red-900 border-red-300';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }, []);

  const formatCurrency = useCallback((amount: number) => {
    if (!amount || isNaN(amount)) return '₹0';

    if (amount >= 10000000) {
      return `₹${(amount / 10000000).toFixed(1)}Cr`;
    } else if (amount >= 100000) {
      return `₹${(amount / 100000).toFixed(1)}L`;
    } else {
      return `₹${amount.toLocaleString()}`;
    }
  }, []);

  // Calculate SMA distribution percentages with proper null checks
  const smaPercentages = useMemo(() => {
    if (!heatmapData?.sma_distribution) return null;

    try {
      const distribution = heatmapData.sma_distribution;
      const total = Object.values(distribution).reduce((sum, count) => sum + (count || 0), 0);

      if (total === 0) return null;

      return Object.entries(distribution).reduce((acc, [key, value]) => {
        acc[key] = total > 0 ? ((value || 0) / total) * 100 : 0;
        return acc;
      }, {} as Record<string, number>);
    } catch (error) {
      console.error('Error calculating SMA percentages:', error);
      return null;
    }
  }, [heatmapData]);

  console.log('SpecialMentionAccounts: Render - loading:', loading, 'error:', error, 'heatmapData:', !!heatmapData, 'portfolioSummary:', !!portfolioSummary);

  if (loading) {
    console.log('SpecialMentionAccounts: Rendering loading state');
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        
        <div className="grid gap-6 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-3">
                <Skeleton className="h-6 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-4 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>
        
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-4">
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
        <div className="flex justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            className="border-red-300 text-red-700 hover:bg-red-100"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  if (!heatmapData) {
    return (
      <div className="space-y-4">
        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            No SMA data available. Please check your connection and try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">

      {/* Portfolio Metrics Overview - Reduced Spacing */}
      {portfolioSummary && (
        <div className="grid gap-4 md:grid-cols-4">
          {/* Portfolio Health - Compact Container */}
          <Card className="border-emerald-200 bg-gradient-to-br from-emerald-50 to-emerald-100 shadow-sm hover:shadow-md transition-all duration-200">
            <CardHeader className="pb-2 px-4 pt-4">
              <CardTitle className="flex items-center gap-2 text-emerald-700 text-sm">
                <div className="p-1.5 bg-emerald-500/10 rounded-lg">
                  <Shield className="h-4 w-4" />
                </div>
                Portfolio Health
              </CardTitle>
            </CardHeader>
            <CardContent className="px-4 pb-4">
              <div className="text-2xl font-bold text-emerald-700 mb-1">
                {portfolioSummary.total_msmes}
              </div>
              <p className="text-xs text-emerald-600 font-medium">
                Total MSMEs
              </p>
            </CardContent>
          </Card>

          {/* SMA Accounts - Compact */}
          <Card className="border-yellow-200 bg-gradient-to-br from-yellow-50 to-yellow-100 shadow-sm hover:shadow-md transition-shadow">
            <CardHeader className="pb-2 px-4 pt-4">
              <CardTitle className="flex items-center gap-2 text-yellow-700 text-sm">
                <div className="p-1.5 bg-yellow-500/10 rounded-lg">
                  <Clock className="h-4 w-4" />
                </div>
                SMA Accounts
              </CardTitle>
            </CardHeader>
            <CardContent className="px-4 pb-4">
              <div className="text-2xl font-bold text-yellow-700 mb-1">
                {portfolioSummary.sma_accounts}
              </div>
              <p className="text-xs text-yellow-600 mb-2">
                {(portfolioSummary.sma_ratio * 100).toFixed(1)}% of portfolio
              </p>
              <div className="flex items-center gap-1">
                {portfolioSummary.sma_trend_30d > 0 ? (
                  <TrendingUp className="h-3 w-3 text-red-500" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-green-500" />
                )}
                <span className={`text-xs font-medium ${portfolioSummary.sma_trend_30d > 0 ? 'text-red-500' : 'text-green-500'}`}>
                  {Math.abs(portfolioSummary.sma_trend_30d * 100).toFixed(1)}% (30d)
                </span>
              </div>
            </CardContent>
          </Card>

          {/* NPA Ratio - Compact Container */}
          <Card className="border-red-200 bg-gradient-to-br from-red-50 to-red-100 shadow-sm hover:shadow-md transition-all duration-200">
            <CardHeader className="pb-2 px-4 pt-4">
              <CardTitle className="flex items-center gap-2 text-red-700 text-sm">
                <div className="p-1.5 bg-red-500/10 rounded-lg">
                  <AlertTriangle className="h-4 w-4" />
                </div>
                NPA Ratio
              </CardTitle>
            </CardHeader>
            <CardContent className="px-4 pb-4">
              <div className="text-2xl font-bold text-red-700 mb-1">
                {portfolioSummary.npa_accounts}
              </div>
              <p className="text-xs text-red-600 font-medium mb-2">
                {(portfolioSummary.npa_ratio * 100).toFixed(1)}% of portfolio
              </p>
              <div className="flex items-center gap-1">
                {portfolioSummary.npa_trend_30d > 0 ? (
                  <TrendingUp className="h-3 w-3 text-red-500" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-green-500" />
                )}
                <span className={`text-xs font-medium ${portfolioSummary.npa_trend_30d > 0 ? 'text-red-500' : 'text-green-500'}`}>
                  {Math.abs(portfolioSummary.npa_trend_30d * 100).toFixed(1)}% (30d)
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Provision Required - Compact */}
          <Card className="border-slate-200 bg-gradient-to-br from-slate-50 to-slate-100 shadow-sm hover:shadow-md transition-shadow">
            <CardHeader className="pb-2 px-4 pt-4">
              <CardTitle className="flex items-center gap-2 text-slate-700 text-sm">
                <div className="p-1.5 bg-slate-500/10 rounded-lg">
                  <IndianRupee className="h-4 w-4" />
                </div>
                Provision Required
              </CardTitle>
            </CardHeader>
            <CardContent className="px-4 pb-4">
              <div className="text-2xl font-bold text-slate-700 mb-1">
                {formatCurrency(portfolioSummary.provision_required)}
              </div>
              <p className="text-xs text-slate-600">
                RBI compliance
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* SMA Heatmap and Analysis */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <div className="flex items-center justify-between">
          <TabsList className="grid grid-cols-2 bg-emerald-50 border border-emerald-200 p-1 rounded-lg">
            <TabsTrigger
              value="overview"
              className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white transition-all duration-200"
            >
              SMA Overview
            </TabsTrigger>
            <TabsTrigger
              value="heatmap"
              className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white transition-all duration-200"
            >
              Heatmap Analysis
            </TabsTrigger>
          </TabsList>
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            variant="outline"
            size="sm"
            className="border-emerald-300 text-emerald-700 hover:bg-emerald-50 transition-colors"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
        </div>

        <TabsContent value="overview" className="space-y-6">
          {/* SMA Distribution Chart */}
          {heatmapData && smaPercentages && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-emerald-600" />
                  SMA Classification Distribution
                </CardTitle>
                <CardDescription>
                  Current portfolio distribution across SMA categories
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {heatmapData?.sma_distribution && Object.entries(heatmapData.sma_distribution).map(([classification, count]) => (
                    <div key={classification} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge className={getSMAColor(classification)}>
                            {classification.toUpperCase().replace('_', '-')}
                          </Badge>
                          <span className="font-medium">{count} accounts</span>
                        </div>
                        <span className="text-sm text-muted-foreground">
                          {smaPercentages[classification].toFixed(1)}%
                        </span>
                      </div>
                      <Progress
                        value={smaPercentages[classification]}
                        className="h-2"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Exposure Analysis */}
          {heatmapData && (
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <IndianRupee className="h-5 w-5 text-emerald-600" />
                    Exposure by SMA Category
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {heatmapData?.exposure_by_sma && Object.entries(heatmapData.exposure_by_sma).map(([classification, exposure]) => (
                      <div key={classification} className="flex items-center justify-between">
                        <Badge className={getSMAColor(classification)}>
                          {classification.toUpperCase().replace('_', '-')}
                        </Badge>
                        <span className="font-medium">{formatCurrency(exposure)}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-emerald-600" />
                    Key Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Total Exposure</span>
                      <span className="font-medium">{formatCurrency(heatmapData.total_exposure)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">SMA Ratio</span>
                      <span className="font-medium">{(heatmapData.sma_ratio * 100).toFixed(2)}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">NPA Ratio</span>
                      <span className="font-medium">{(heatmapData.npa_ratio * 100).toFixed(2)}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Last Updated</span>
                      <span className="font-medium text-xs">
                        {new Date(heatmapData.generated_at).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="heatmap" className="space-y-6">
          {/* SMA Distribution Heatmaps - Side by Side */}
          {heatmapData && (
            <div className="grid gap-6 md:grid-cols-2">
              {/* Business Type Heatmap */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5 text-emerald-600" />
                    SMA Distribution by Business Type
                  </CardTitle>
                  <CardDescription>
                    Risk concentration across different business sectors
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {heatmapData?.business_type_sma && Object.entries(heatmapData.business_type_sma).map(([businessType, distribution]) => (
                      <div key={businessType} className="space-y-2">
                        <h4 className="font-medium capitalize">{businessType}</h4>
                        <div className="grid grid-cols-5 gap-2">
                          {Object.entries(distribution).map(([classification, count]) => (
                            <div key={classification} className="text-center">
                              <div className={`p-2 rounded text-xs font-medium ${getSMAColor(classification)}`}>
                                {count}
                              </div>
                              <div className="text-xs text-muted-foreground mt-1">
                                {classification.toUpperCase().replace('_', '-')}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Geographic Heatmap */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5 text-emerald-600" />
                    SMA Distribution by Location
                  </CardTitle>
                  <CardDescription>
                    Geographic risk concentration analysis
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {heatmapData?.geographic_sma && Object.entries(heatmapData.geographic_sma).map(([location, distribution]) => (
                      <div key={location} className="space-y-2">
                        <h4 className="font-medium">{location}</h4>
                        <div className="grid grid-cols-5 gap-2">
                          {Object.entries(distribution).map(([classification, count]) => (
                            <div key={classification} className="text-center">
                              <div className={`p-2 rounded text-xs font-medium ${getSMAColor(classification)}`}>
                                {count}
                              </div>
                              <div className="text-xs text-muted-foreground mt-1">
                                {classification.toUpperCase().replace('_', '-')}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>


      </Tabs>
    </div>
  );
});
