from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class GSTReturnType(str, Enum):
    GSTR1 = "GSTR1"
    GSTR3B = "GSTR3B"
    GSTR9 = "GSTR9"
    GSTR9C = "GSTR9C"

class GSTReturnStatus(str, Enum):
    FILED = "FILED"
    NOT_FILED = "NOT_FILED"
    LATE_FILED = "LATE_FILED"
    PENDING = "PENDING"

class GSTComplianceStatus(str, Enum):
    COMPLIANT = "COMPLIANT"
    NON_COMPLIANT = "NON_COMPLIANT"
    PARTIALLY_COMPLIANT = "PARTIALLY_COMPLIANT"

class GSTTurnoverData(BaseModel):
    """GST Turnover data from GSTR-1 and GSTR-3B"""
    gstin: str = Field(..., description="GST Identification Number")
    period: str = Field(..., description="Return period (MMYYYY)")
    return_type: GSTReturnType
    
    # Turnover Details
    total_turnover: float = Field(0.0, description="Total turnover for the period")
    taxable_turnover: float = Field(0.0, description="Taxable turnover")
    exempt_turnover: float = Field(0.0, description="Exempt turnover")
    export_turnover: float = Field(0.0, description="Export turnover")
    
    # Tax Details
    total_tax_liability: float = Field(0.0, description="Total tax liability")
    cgst_liability: float = Field(0.0, description="CGST liability")
    sgst_liability: float = Field(0.0, description="SGST liability")
    igst_liability: float = Field(0.0, description="IGST liability")
    cess_liability: float = Field(0.0, description="Cess liability")
    
    # Input Tax Credit
    total_itc_claimed: float = Field(0.0, description="Total ITC claimed")
    itc_utilized: float = Field(0.0, description="ITC utilized")
    itc_carried_forward: float = Field(0.0, description="ITC carried forward")
    
    # Payment Details
    cash_payment: float = Field(0.0, description="Tax paid in cash")
    itc_payment: float = Field(0.0, description="Tax paid through ITC")
    
    filing_date: Optional[datetime] = Field(None, description="Date of filing")
    due_date: datetime = Field(..., description="Due date for filing")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class GSTComplianceRecord(BaseModel):
    """GST Compliance tracking record"""
    gstin: str = Field(..., description="GST Identification Number")
    period: str = Field(..., description="Return period (MMYYYY)")
    return_type: GSTReturnType
    
    status: GSTReturnStatus
    compliance_status: GSTComplianceStatus
    
    due_date: datetime
    filing_date: Optional[datetime] = None
    days_delayed: Optional[int] = Field(None, description="Days delayed in filing")
    
    penalty_amount: float = Field(0.0, description="Penalty imposed for late filing")
    interest_amount: float = Field(0.0, description="Interest charged")
    
    # Compliance Metrics
    filing_score: float = Field(0.0, description="Filing compliance score (0-100)")
    payment_score: float = Field(0.0, description="Payment compliance score (0-100)")
    overall_score: float = Field(0.0, description="Overall compliance score (0-100)")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

class GSTPaymentPattern(BaseModel):
    """GST Payment pattern analysis"""
    gstin: str = Field(..., description="GST Identification Number")
    period: str = Field(..., description="Return period (MMYYYY)")
    
    # Payment Behavior
    total_liability: float = Field(0.0, description="Total tax liability")
    amount_paid: float = Field(0.0, description="Amount actually paid")
    outstanding_amount: float = Field(0.0, description="Outstanding tax amount")
    
    payment_date: Optional[datetime] = Field(None, description="Date of payment")
    payment_method: Optional[str] = Field(None, description="Payment method used")
    
    # Payment Patterns
    avg_payment_delay: float = Field(0.0, description="Average payment delay in days")
    payment_consistency: float = Field(0.0, description="Payment consistency score (0-100)")
    cash_vs_itc_ratio: float = Field(0.0, description="Ratio of cash payment to ITC utilization")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)

class GSTAnalytics(BaseModel):
    """Comprehensive GST analytics for an MSME"""
    gstin: str = Field(..., description="GST Identification Number")
    msme_id: str = Field(..., description="MSME identifier")
    
    # Turnover Analytics
    avg_monthly_turnover: float = Field(0.0, description="Average monthly turnover")
    turnover_growth_rate: float = Field(0.0, description="Turnover growth rate (%)")
    turnover_volatility: float = Field(0.0, description="Turnover volatility coefficient")
    seasonal_index: float = Field(0.0, description="Seasonal variation index")
    
    # Compliance Analytics
    filing_compliance_rate: float = Field(0.0, description="Filing compliance rate (%)")
    payment_compliance_rate: float = Field(0.0, description="Payment compliance rate (%)")
    avg_filing_delay: float = Field(0.0, description="Average filing delay in days")
    avg_payment_delay: float = Field(0.0, description="Average payment delay in days")
    
    # ITC Analytics
    itc_utilization_rate: float = Field(0.0, description="ITC utilization rate (%)")
    itc_efficiency: float = Field(0.0, description="ITC efficiency score (0-100)")
    avg_itc_balance: float = Field(0.0, description="Average ITC balance")
    
    # Risk Indicators
    compliance_risk_score: float = Field(0.0, description="Compliance risk score (0-100)")
    payment_risk_score: float = Field(0.0, description="Payment risk score (0-100)")
    overall_gst_score: float = Field(0.0, description="Overall GST health score (0-100)")
    
    # Trend Analysis
    turnover_trend: str = Field("stable", description="Turnover trend (increasing/decreasing/stable)")
    compliance_trend: str = Field("stable", description="Compliance trend")
    
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    data_quality_score: float = Field(0.0, description="Data quality and completeness score")

class GSTDataRequest(BaseModel):
    """Request model for GST data fetching"""
    gstin: str = Field(..., description="GST Identification Number")
    from_period: str = Field(..., description="Start period (MMYYYY)")
    to_period: str = Field(..., description="End period (MMYYYY)")
    return_types: List[GSTReturnType] = Field(default=[GSTReturnType.GSTR3B])
    include_analytics: bool = Field(True, description="Include analytics calculation")

class GSTDataResponse(BaseModel):
    """Response model for GST data"""
    gstin: str
    msme_id: Optional[str] = None
    
    turnover_data: List[GSTTurnoverData] = Field(default_factory=list)
    compliance_records: List[GSTComplianceRecord] = Field(default_factory=list)
    payment_patterns: List[GSTPaymentPattern] = Field(default_factory=list)
    analytics: Optional[GSTAnalytics] = None
    
    data_freshness: datetime = Field(default_factory=datetime.utcnow)
    source: str = Field("GSTN_API", description="Data source")
    status: str = Field("success", description="Response status")
    message: Optional[str] = None

class GSTHealthCheck(BaseModel):
    """GST data source health check"""
    gstin: str
    last_sync: datetime
    sync_status: str = Field(..., description="success/failed/partial")
    data_completeness: float = Field(0.0, description="Data completeness percentage")
    api_response_time: float = Field(0.0, description="API response time in seconds")
    error_count: int = Field(0, description="Number of errors in last sync")
    next_sync_scheduled: datetime
    
class GSTConfiguration(BaseModel):
    """GST integration configuration"""
    api_endpoint: str = Field(..., description="GSTN API endpoint")
    client_id: str = Field(..., description="API client ID")
    client_secret: str = Field(..., description="API client secret")
    
    sync_frequency: int = Field(24, description="Sync frequency in hours")
    retry_attempts: int = Field(3, description="Number of retry attempts")
    timeout_seconds: int = Field(30, description="API timeout in seconds")
    
    rate_limit_per_minute: int = Field(60, description="API rate limit per minute")
    batch_size: int = Field(10, description="Batch size for bulk operations")
    
    enable_real_time_sync: bool = Field(False, description="Enable real-time synchronization")
    enable_webhook_notifications: bool = Field(True, description="Enable webhook notifications")
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
