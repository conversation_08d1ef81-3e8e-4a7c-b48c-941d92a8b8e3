'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Skeleton } from '@/components/ui/skeleton';
import {
  FileText,
  Download,
  BarChart3,
  Shield,
  TrendingUp,
  Building2,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  Calendar as CalendarIcon
} from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { api } from '@/lib/api';

interface ReportType {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<any>;
  category: 'portfolio' | 'risk' | 'compliance' | 'performance';
  estimatedTime: string;
  lastGenerated?: string;
}

const reportTypes: ReportType[] = [
  {
    id: 'portfolio_summary',
    name: 'Portfolio Summary Report',
    description: 'Comprehensive portfolio overview with risk distribution and SMA analysis',
    icon: Building2,
    category: 'portfolio',
    estimatedTime: '2-3 minutes',
    lastGenerated: '2024-12-30'
  },
  {
    id: 'risk_exposure',
    name: 'Risk Exposure Report',
    description: 'Geographic and sector-wise risk exposure analysis with regulatory alerts',
    icon: AlertTriangle,
    category: 'risk',
    estimatedTime: '3-4 minutes',
    lastGenerated: '2024-12-29'
  },
  {
    id: 'compliance_regulatory',
    name: 'Compliance & Regulatory Report',
    description: 'RBI compliance status and regulatory reporting readiness',
    icon: Shield,
    category: 'compliance',
    estimatedTime: '2-3 minutes',
    lastGenerated: '2024-12-28'
  },
  {
    id: 'performance_trends',
    name: 'Performance Trends Report',
    description: 'Portfolio performance trends and MSME lifecycle analytics',
    icon: TrendingUp,
    category: 'performance',
    estimatedTime: '4-5 minutes',
    lastGenerated: '2024-12-27'
  },
  {
    id: 'detailed_msme_profile',
    name: 'Detailed MSME Profile Report',
    description: 'Individual MSME comprehensive risk assessment and history',
    icon: FileText,
    category: 'portfolio',
    estimatedTime: '1-2 minutes',
    lastGenerated: '2024-12-26'
  }
];

export function ReportsDashboard() {
  const [selectedReport, setSelectedReport] = useState<string>('portfolio_summary');
  const [dateRange, setDateRange] = useState<{from: Date, to: Date}>({
    from: new Date(2024, 0, 1), // January 1, 2024
    to: new Date() // Today
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [activeCategory, setActiveCategory] = useState<string>('all');

  const filteredReports = reportTypes.filter(report => 
    activeCategory === 'all' || report.category === activeCategory
  );

  const handleGenerateReport = async (reportId: string, format: 'pdf' | 'csv') => {
    setIsGenerating(true);
    try {
      console.log(`Generating ${reportId} in ${format} format`);

      // Format dates for API
      const startDate = dateRange.from.toISOString().split('T')[0];
      const endDate = dateRange.to.toISOString().split('T')[0];

      // Call export API
      const exportResponse = await api.exportReport(
        reportId,
        format,
        startDate,
        endDate,
        undefined, // msmeId - only needed for detailed MSME profile
        'dashboard_user'
      );

      if (exportResponse.file_url) {
        // Create download link
        const link = document.createElement('a');
        link.href = `http://localhost:8001${exportResponse.file_url}`;
        link.download = `${reportId}_${new Date().toISOString().split('T')[0]}.${format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        console.log('Report generated successfully:', exportResponse);
      } else {
        throw new Error('No download URL received');
      }

    } catch (error) {
      console.error('Error generating report:', error);
      // Fallback to mock download for development
      const blob = new Blob([`Mock ${format.toUpperCase()} report data`], {
        type: format === 'pdf' ? 'application/pdf' : 'text/csv'
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${reportId}_${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Reports Dashboard</h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            Generate comprehensive reports for portfolio analysis and regulatory compliance
          </p>
        </div>
        <Badge variant="secondary" className="bg-emerald-50 text-emerald-700 border-emerald-200">
          {reportTypes.length} Report Types
        </Badge>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="border-0 bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-950 dark:to-emerald-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-emerald-700 dark:text-emerald-300">Available Reports</CardTitle>
            <FileText className="h-4 w-4 text-emerald-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-emerald-900 dark:text-emerald-100">{reportTypes.length}</div>
            <p className="text-xs text-emerald-600 dark:text-emerald-400">Ready to generate</p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950 dark:to-blue-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-700 dark:text-blue-300">Export Formats</CardTitle>
            <Download className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900 dark:text-blue-100">2</div>
            <p className="text-xs text-blue-600 dark:text-blue-400">PDF & CSV</p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950 dark:to-purple-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-700 dark:text-purple-300">Avg Generation Time</CardTitle>
            <Clock className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900 dark:text-purple-100">2-3</div>
            <p className="text-xs text-purple-600 dark:text-purple-400">minutes</p>
          </CardContent>
        </Card>

        <Card className="border-0 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950 dark:to-orange-900">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-700 dark:text-orange-300">Last Generated</CardTitle>
            <RefreshCw className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-900 dark:text-orange-100">Today</div>
            <p className="text-xs text-orange-600 dark:text-orange-400">Portfolio Summary</p>
          </CardContent>
        </Card>
      </div>

      {/* Report Generation Interface */}
      <Card className="border-0 shadow-lg">
        <CardHeader className="border-b border-gray-100 dark:border-gray-800">
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-emerald-600" />
            Generate Reports
          </CardTitle>
          <CardDescription>
            Select report type, date range, and export format to generate comprehensive reports
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          {/* Category Filter */}
          <div className="mb-6">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Filter by Category
            </label>
            <div className="flex gap-2 flex-wrap">
              {['all', 'portfolio', 'risk', 'compliance', 'performance'].map((category) => (
                <Button
                  key={category}
                  variant={activeCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setActiveCategory(category)}
                  className={cn(
                    "capitalize",
                    activeCategory === category && "bg-emerald-500 hover:bg-emerald-600"
                  )}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          {/* Date Range Selection */}
          <div className="mb-6">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
              Reporting Period
            </label>
            <div className="flex gap-4">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? format(dateRange.from, "PPP") : "Pick start date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={dateRange.from}
                    onSelect={(date) => date && setDateRange(prev => ({ ...prev, from: date }))}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.to ? format(dateRange.to, "PPP") : "Pick end date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={dateRange.to}
                    onSelect={(date) => date && setDateRange(prev => ({ ...prev, to: date }))}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          {/* Report Selection */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredReports.map((report) => {
              const IconComponent = report.icon;
              return (
                <Card 
                  key={report.id} 
                  className={cn(
                    "cursor-pointer transition-all duration-200 hover:shadow-md",
                    selectedReport === report.id 
                      ? "ring-2 ring-emerald-500 bg-emerald-50 dark:bg-emerald-950" 
                      : "hover:bg-gray-50 dark:hover:bg-gray-800"
                  )}
                  onClick={() => setSelectedReport(report.id)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <IconComponent className="h-5 w-5 text-emerald-600" />
                      <Badge variant="secondary" className="text-xs">
                        {report.category}
                      </Badge>
                    </div>
                    <CardTitle className="text-sm font-medium">{report.name}</CardTitle>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      {report.description}
                    </p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>Est. {report.estimatedTime}</span>
                      {report.lastGenerated && (
                        <span>Last: {report.lastGenerated}</span>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Generate Buttons */}
          <div className="mt-6 pt-6 border-t border-gray-100 dark:border-gray-800">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100">
                  {reportTypes.find(r => r.id === selectedReport)?.name}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Generate and download in your preferred format
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={() => handleGenerateReport(selectedReport, 'pdf')}
                  disabled={isGenerating}
                  className="bg-emerald-500 hover:bg-emerald-600"
                >
                  {isGenerating ? (
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Download className="mr-2 h-4 w-4" />
                  )}
                  PDF Report
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleGenerateReport(selectedReport, 'csv')}
                  disabled={isGenerating}
                >
                  {isGenerating ? (
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Download className="mr-2 h-4 w-4" />
                  )}
                  CSV Export
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
