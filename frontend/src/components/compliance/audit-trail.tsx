'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search,
  Filter,
  Download,
  Calendar,
  User,
  Activity,
  FileText,
  Shield,
  Eye,
  Edit,
  Trash2,
  Plus,
  AlertTriangle
} from 'lucide-react';

interface AuditEvent {
  event_id: string;
  timestamp: string;
  user_id: string;
  user_name: string;
  action: string;
  resource_type: string;
  resource_id: string;
  description: string;
  ip_address: string;
  user_agent: string;
  status: 'SUCCESS' | 'FAILED' | 'WARNING';
  details: Record<string, unknown>;
}

interface AuditTrailProps {
  msmeId?: string;
  resourceType?: string;
  showFilters?: boolean;
}

export default function AuditTrail({ 
  msmeId, 
  resourceType, 
  showFilters = true 
}: AuditTrailProps) {
  const [auditEvents, setAuditEvents] = useState<AuditEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedAction, setSelectedAction] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedUser, setSelectedUser] = useState('all');
  const [dateRange, setDateRange] = useState('7d');

  const fetchAuditEvents = useCallback(async () => {
    try {
      setLoading(true);
      
      // Mock audit events data
      const mockEvents: AuditEvent[] = [
        {
          event_id: 'AUD_001',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          user_id: 'USR_001',
          user_name: 'John Doe',
          action: 'VIEW_FINANCIAL_DATA',
          resource_type: 'FINANCIAL_METRICS',
          resource_id: msmeId || 'MSME_001',
          description: 'Viewed financial metrics dashboard',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          status: 'SUCCESS',
          details: {
            metrics_accessed: ['liquidity', 'profitability', 'leverage'],
            duration_seconds: 45
          }
        },
        {
          event_id: 'AUD_002',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          user_id: 'USR_002',
          user_name: 'Jane Smith',
          action: 'UPDATE_COMPLIANCE_STATUS',
          resource_type: 'COMPLIANCE',
          resource_id: msmeId || 'MSME_001',
          description: 'Updated RBI compliance status',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          status: 'SUCCESS',
          details: {
            previous_status: 'PARTIAL_COMPLIANCE',
            new_status: 'COMPLIANT',
            compliance_areas: ['KYC', 'AML']
          }
        },
        {
          event_id: 'AUD_003',
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          user_id: 'USR_003',
          user_name: 'Mike Johnson',
          action: 'EXPORT_DATA',
          resource_type: 'GST_DATA',
          resource_id: msmeId || 'MSME_001',
          description: 'Exported GST data for compliance review',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          status: 'SUCCESS',
          details: {
            export_format: 'PDF',
            records_count: 156,
            file_size_mb: 2.3
          }
        },
        {
          event_id: 'AUD_004',
          timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
          user_id: 'USR_001',
          user_name: 'John Doe',
          action: 'FAILED_LOGIN_ATTEMPT',
          resource_type: 'AUTHENTICATION',
          resource_id: 'AUTH_001',
          description: 'Failed login attempt - invalid credentials',
          ip_address: '************',
          user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          status: 'FAILED',
          details: {
            reason: 'invalid_credentials',
            attempt_count: 3
          }
        },
        {
          event_id: 'AUD_005',
          timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
          user_id: 'USR_004',
          user_name: 'Sarah Wilson',
          action: 'CREATE_ALERT',
          resource_type: 'COMPLIANCE_ALERT',
          resource_id: 'ALERT_001',
          description: 'Created compliance alert for consent renewal',
          ip_address: '*************',
          user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          status: 'SUCCESS',
          details: {
            alert_type: 'DATA_PRIVACY',
            severity: 'MEDIUM',
            due_date: '2024-01-15'
          }
        },
        {
          event_id: 'AUD_006',
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          user_id: 'SYS_001',
          user_name: 'System',
          action: 'AUTOMATED_BACKUP',
          resource_type: 'DATA_BACKUP',
          resource_id: 'BACKUP_001',
          description: 'Automated daily backup completed',
          ip_address: '127.0.0.1',
          user_agent: 'System/1.0',
          status: 'SUCCESS',
          details: {
            backup_size_gb: 15.7,
            duration_minutes: 23,
            backup_location: 's3://compliance-backups/daily/'
          }
        }
      ];
      
      setAuditEvents(mockEvents);
    } catch (error) {
      console.error('Error fetching audit events:', error);
    } finally {
      setLoading(false);
    }
  }, [msmeId, resourceType, selectedAction, selectedStatus, selectedUser, dateRange]);

  useEffect(() => {
    fetchAuditEvents();
  }, [fetchAuditEvents]);

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'VIEW_FINANCIAL_DATA':
      case 'VIEW_COMPLIANCE_DATA':
        return <Eye className="h-4 w-4 text-blue-500" />;
      case 'UPDATE_COMPLIANCE_STATUS':
      case 'UPDATE_FINANCIAL_DATA':
        return <Edit className="h-4 w-4 text-green-500" />;
      case 'EXPORT_DATA':
        return <Download className="h-4 w-4 text-purple-500" />;
      case 'CREATE_ALERT':
        return <Plus className="h-4 w-4 text-orange-500" />;
      case 'DELETE_DATA':
        return <Trash2 className="h-4 w-4 text-red-500" />;
      case 'FAILED_LOGIN_ATTEMPT':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'AUTOMATED_BACKUP':
        return <Shield className="h-4 w-4 text-green-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return <Badge variant="default" className="bg-green-100 text-green-800">Success</Badge>;
      case 'FAILED':
        return <Badge variant="destructive">Failed</Badge>;
      case 'WARNING':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Warning</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  const formatTimeAgo = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const filteredEvents = auditEvents.filter(event => {
    const matchesSearch = searchTerm === '' || 
      event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.user_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.action.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesAction = selectedAction === 'all' || event.action === selectedAction;
    const matchesStatus = selectedStatus === 'all' || event.status === selectedStatus;
    const matchesUser = selectedUser === 'all' || event.user_id === selectedUser;
    
    return matchesSearch && matchesAction && matchesStatus && matchesUser;
  });

  const uniqueActions = [...new Set(auditEvents.map(event => event.action))];
  const uniqueUsers = [...new Set(auditEvents.map(event => ({ id: event.user_id, name: event.user_name })))];

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
        <div className="space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Audit Trail</h3>
          <p className="text-muted-foreground">Complete activity log and compliance tracking</p>
        </div>
        <Button variant="outline" onClick={() => console.log('Export audit log')}>
          <Download className="h-4 w-4 mr-2" />
          Export Log
        </Button>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-5">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search events..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Action</label>
                <Select value={selectedAction} onValueChange={setSelectedAction}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Actions</SelectItem>
                    {uniqueActions.map(action => (
                      <SelectItem key={action} value={action}>
                        {action.replace(/_/g, ' ')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="SUCCESS">Success</SelectItem>
                    <SelectItem value="FAILED">Failed</SelectItem>
                    <SelectItem value="WARNING">Warning</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">User</label>
                <Select value={selectedUser} onValueChange={setSelectedUser}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Users</SelectItem>
                    {uniqueUsers.map(user => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Date Range</label>
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1d">Last 24 hours</SelectItem>
                    <SelectItem value="7d">Last 7 days</SelectItem>
                    <SelectItem value="30d">Last 30 days</SelectItem>
                    <SelectItem value="90d">Last 90 days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Audit Events */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Activity Log
          </CardTitle>
          <CardDescription>
            Showing {filteredEvents.length} of {auditEvents.length} events
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredEvents.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No Events Found</h3>
              <p>No audit events match the current filters.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredEvents.map((event) => (
                <div key={event.event_id} className="flex items-start gap-3 p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                  <div className="flex-shrink-0 mt-1">
                    {getActionIcon(event.action)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-sm">{event.description}</h4>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(event.status)}
                        <span className="text-xs text-muted-foreground">
                          {formatTimeAgo(event.timestamp)}
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-4 text-xs text-muted-foreground mb-2">
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span>{event.user_name}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        <span>{formatTimestamp(event.timestamp)}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Activity className="h-3 w-3" />
                        <span>{event.action.replace(/_/g, ' ')}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2 text-xs">
                        <Badge variant="outline" className="text-xs">
                          {event.resource_type.replace(/_/g, ' ')}
                        </Badge>
                        <span className="text-muted-foreground">IP: {event.ip_address}</span>
                      </div>
                      
                      {Object.keys(event.details).length > 0 && (
                        <Button variant="ghost" size="sm" className="h-6 text-xs">
                          View Details
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
