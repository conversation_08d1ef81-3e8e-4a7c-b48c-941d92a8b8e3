# Credit Chakra Component Hierarchy

## Overview

This document outlines the component structure and hierarchy for the Credit Chakra frontend application, built with Next.js 14, React, and TypeScript.

## Directory Structure

```
frontend/src/
├── components/
│   ├── shared/           # Reusable shared components
│   ├── portfolio/        # Portfolio-specific components
│   ├── msme/            # MSME detail components
│   ├── risk/            # Risk monitoring components
│   ├── analytics/       # Analytics and reporting components
│   └── ui/              # Base UI components (shadcn/ui)
├── hooks/               # Custom React hooks
├── lib/                 # Utility libraries
├── types/               # TypeScript type definitions
└── pages/               # Next.js pages
```

## Component Categories

### 1. Shared Components (`/components/shared/`)

#### RiskCard (`risk-card.tsx`)
**Purpose**: Displays risk-related information in a consistent card format
**Props**:
```typescript
interface RiskCardProps {
  riskLevel: 'green' | 'yellow' | 'red';
  count: number;
  totalCount: number;
  title?: string;
  showProgress?: boolean;
  showTrend?: boolean;
  trendDirection?: 'up' | 'down' | 'stable';
  trendValue?: number;
  className?: string;
  onClick?: () => void;
}
```
**Usage**: Portfolio overview, risk monitoring, analytics dashboards
**Dependencies**: Card, Progress, Badge (UI components)

#### RiskDistribution (`risk-card.tsx`)
**Purpose**: Displays risk distribution across portfolio
**Props**:
```typescript
interface RiskDistributionProps {
  riskDistribution: { green: number; yellow: number; red: number };
  totalCount: number;
  showTrends?: boolean;
  trends?: { green?: number; yellow?: number; red?: number };
  onRiskClick?: (riskLevel: RiskBand) => void;
  className?: string;
}
```

#### RiskBadge (`risk-card.tsx`)
**Purpose**: Consistent risk level badge display
**Props**:
```typescript
interface RiskBadgeProps {
  riskLevel: 'green' | 'yellow' | 'red';
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}
```

#### DataFetcher (`data-fetcher.tsx`)
**Purpose**: Standardizes loading, error, and empty states
**Props**:
```typescript
interface DataFetcherProps<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  onRetry?: () => void;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  emptyState?: EmptyStateConfig;
  children: (data: T) => React.ReactNode;
  className?: string;
}
```

#### LoadingSkeleton (`data-fetcher.tsx`)
**Purpose**: Provides skeleton loading states for different content types
**Props**:
```typescript
interface LoadingSkeletonProps {
  type: 'card' | 'table' | 'chart' | 'list';
  count?: number;
  className?: string;
}
```

### 2. Portfolio Components (`/components/portfolio/`)

#### PortfolioDashboard (`portfolio-dashboard.tsx`)
**Purpose**: Main portfolio overview and management interface
**Key Features**:
- Portfolio overview with risk distribution
- Top opportunities and actions section
- MSME table with filtering and search
- Pagination and sorting
**Dependencies**: 
- `useOptimizedPortfolio` hook
- Shared components (RiskDistribution, DataFetcher)
- UI components (Table, Card, Button, Input)

#### OptimizedPortfolioDashboard (`optimized-portfolio-dashboard.tsx`)
**Purpose**: Performance-optimized version with advanced features
**Key Features**:
- Memoized computations
- Efficient pagination
- Advanced filtering
- Real-time updates preparation
**Performance Optimizations**:
- `useMemo` for expensive calculations
- `useCallback` for event handlers
- Optimized re-rendering patterns

### 3. MSME Components (`/components/msme/`)

#### MSMEDetail (`msme-detail.tsx`)
**Purpose**: Detailed view of individual MSME
**Key Features**:
- Score analysis and breakdown
- Signal history and timeline
- Nudge management
- Business profile information

#### BulkNudgeModal (`bulk-nudge-modal.tsx`)
**Purpose**: Bulk nudge operations across multiple MSMEs
**Key Features**:
- MSME selection and filtering
- Nudge template management
- Batch processing

### 4. Risk Components (`/components/risk/`)

#### AdvancedRiskMonitor (`advanced-risk-monitor.tsx`)
**Purpose**: Comprehensive risk monitoring and early warning system
**Key Features**:
- Real-time risk alerts
- SMA (Special Mention Account) monitoring
- Stress testing scenarios
- RBI compliance tracking
**Tabs**:
- Overview: Risk metrics and recent events
- Alerts: Risk alert management
- SMA: Special mention account monitoring
- Stress: Stress testing scenarios
- Compliance: Regulatory compliance dashboard

### 5. Analytics Components (`/components/analytics/`)

#### AnalyticsDashboard (`analytics-dashboard.tsx`)
**Purpose**: Portfolio analytics and insights
**Key Features**:
- Performance metrics
- Trend analysis
- Business intelligence
- Downloadable reports

### 6. UI Components (`/components/ui/`)

Base UI components from shadcn/ui:
- `Card`, `CardContent`, `CardHeader`, `CardTitle`
- `Button`, `Badge`, `Progress`
- `Table`, `TableBody`, `TableCell`, `TableHead`, `TableHeader`, `TableRow`
- `Tabs`, `TabsContent`, `TabsList`, `TabsTrigger`
- `Input`, `Select`, `SelectContent`, `SelectItem`, `SelectTrigger`, `SelectValue`
- `Skeleton`, `Tooltip`, `Modal`

## Custom Hooks (`/hooks/`)

### useOptimizedData (`useOptimizedData.ts`)

#### useOptimizedPortfolio
**Purpose**: Optimized data fetching for portfolio data
**Features**:
- Client-side caching with TTL
- Pagination support
- Filtering and search
- Request cancellation
- Memoized computations

**Return Type**:
```typescript
interface UseOptimizedPortfolioReturn {
  msmes: MSME[];
  analytics: Analytics | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  currentPage: number;
  totalPages: number;
  paginatedMsmes: MSME[];
  setCurrentPage: (page: number) => void;
  filteredMsmes: MSME[];
  setFilter: (filter: Partial<MSME>) => void;
  clearFilter: () => void;
}
```

#### useOptimizedMSME
**Purpose**: Optimized data fetching for individual MSME data
**Features**:
- Caching with TTL
- Error handling
- Loading states

## Data Flow Patterns

### 1. Portfolio Data Flow
```
API (/dashboard/portfolio) 
  → useOptimizedPortfolio hook 
  → DataCache (5min TTL)
  → PortfolioDashboard component
  → RiskDistribution + Table components
```

### 2. MSME Detail Data Flow
```
API (/msme/{id}) 
  → useOptimizedMSME hook 
  → DataCache (5min TTL)
  → MSMEDetail component
  → Score/Signal/Nudge sub-components
```

### 3. Real-time Updates (Future)
```
WebSocket connection 
  → RealtimeEventManager 
  → Component subscriptions
  → Automatic data refresh
```

## Performance Patterns

### 1. Memoization Strategy
- **useMemo**: Expensive calculations (filtering, sorting, aggregations)
- **useCallback**: Event handlers and functions passed to children
- **React.memo**: Pure components that don't need frequent re-renders

### 2. Caching Strategy
- **Client-side cache**: 5-minute TTL for API responses
- **Request deduplication**: Prevent duplicate API calls
- **Pagination cache**: Efficient page-based loading

### 3. Loading Patterns
- **Skeleton screens**: Immediate visual feedback
- **Progressive loading**: Load critical data first
- **Error boundaries**: Graceful error handling

## Styling Patterns

### 1. Design System
- **Color Palette**: Emerald-based with blue/purple accents
- **Typography**: Consistent font sizes and weights
- **Spacing**: Tailwind CSS spacing scale
- **Components**: shadcn/ui component library

### 2. Responsive Design
- **Mobile-first**: Base styles for mobile, scale up
- **Breakpoints**: sm (640px), md (768px), lg (1024px), xl (1280px)
- **Grid layouts**: CSS Grid and Flexbox

### 3. Accessibility
- **ARIA labels**: Screen reader support
- **Keyboard navigation**: Tab order and focus management
- **Color contrast**: WCAG AA compliance

## Testing Strategy

### 1. Component Testing
- **Jest + React Testing Library**: Unit tests for components
- **Mock data**: Consistent test data across components
- **Snapshot testing**: UI regression prevention

### 2. Integration Testing
- **API integration**: Test data flow from API to components
- **User interactions**: Test complete user workflows
- **Performance testing**: Measure rendering performance

### 3. E2E Testing
- **Cypress**: End-to-end user journey testing
- **Critical paths**: Portfolio viewing, MSME details, risk monitoring

## Future Enhancements

### 1. Advanced Features
- **Real-time updates**: WebSocket integration
- **Offline support**: Service worker and caching
- **Advanced analytics**: ML-powered insights
- **Bulk operations**: Multi-select and batch actions

### 2. Performance Optimizations
- **Code splitting**: Route-based and component-based
- **Lazy loading**: Images and non-critical components
- **Virtual scrolling**: Large dataset handling
- **Web Workers**: Heavy computations

### 3. Developer Experience
- **Storybook**: Component documentation and testing
- **TypeScript strict mode**: Enhanced type safety
- **ESLint/Prettier**: Code quality and formatting
- **Automated testing**: CI/CD integration

## Best Practices

### 1. Component Design
- **Single Responsibility**: Each component has one clear purpose
- **Composition over Inheritance**: Use composition patterns
- **Props Interface**: Well-defined TypeScript interfaces
- **Default Props**: Sensible defaults for optional props

### 2. State Management
- **Local State**: useState for component-specific state
- **Shared State**: Custom hooks for cross-component state
- **Server State**: React Query patterns for API data
- **URL State**: Next.js router for navigation state

### 3. Error Handling
- **Error Boundaries**: Catch and handle component errors
- **Graceful Degradation**: Fallback UI for failed states
- **User Feedback**: Clear error messages and recovery options
- **Logging**: Comprehensive error tracking

This component hierarchy provides a scalable, maintainable foundation for the Credit Chakra application while supporting future enhancements and optimizations.
