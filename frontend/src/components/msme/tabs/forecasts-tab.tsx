'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown, 
  AlertCircle, 
  RefreshCw,
  BarChart3,
  Target,
  Calendar,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Zap,
  Brain
} from 'lucide-react';

interface ForecastData {
  msme_id: string;
  forecast_period: string;
  confidence_level: number;
  monthly_projections: {
    month: string;
    revenue_forecast: number;
    confidence_interval: {
      lower: number;
      upper: number;
    };
    growth_rate: number;
    risk_factors: string[];
  }[];
  scenario_analysis: {
    optimistic: {
      revenue_growth: number;
      probability: number;
      key_drivers: string[];
    };
    realistic: {
      revenue_growth: number;
      probability: number;
      key_drivers: string[];
    };
    pessimistic: {
      revenue_growth: number;
      probability: number;
      risk_factors: string[];
    };
  };
  seasonal_patterns: {
    peak_months: string[];
    low_months: string[];
    seasonal_variance: number;
    pattern_confidence: number;
  };
  market_trends: {
    industry_growth: number;
    market_position: string;
    competitive_pressure: number;
    opportunity_score: number;
  };
  regression_model: {
    model_accuracy: number;
    r_squared: number;
    key_variables: string[];
    last_trained: string;
  };
  last_updated: string;
}

interface ForecastsTabProps {
  msmeId: string;
}

export default function ForecastsTab({ msmeId }: ForecastsTabProps) {
  const [forecastData, setForecastData] = useState<ForecastData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchForecastData();
  }, [msmeId]);

  const fetchForecastData = async () => {
    try {
      setLoading(true);
      // Mock data - in production, this would call the actual API
      const mockData: ForecastData = {
        msme_id: msmeId,
        forecast_period: "12 months",
        confidence_level: 78.5,
        monthly_projections: [
          {
            month: "Jan 2025",
            revenue_forecast: 2850000,
            confidence_interval: { lower: 2650000, upper: 3050000 },
            growth_rate: 12.5,
            risk_factors: ["Seasonal demand", "Market volatility"]
          },
          {
            month: "Feb 2025",
            revenue_forecast: 2920000,
            confidence_interval: { lower: 2700000, upper: 3140000 },
            growth_rate: 15.2,
            risk_factors: ["Supply chain"]
          },
          {
            month: "Mar 2025",
            revenue_forecast: 3100000,
            confidence_interval: { lower: 2850000, upper: 3350000 },
            growth_rate: 18.7,
            risk_factors: []
          }
        ],
        scenario_analysis: {
          optimistic: {
            revenue_growth: 25.8,
            probability: 25,
            key_drivers: ["Market expansion", "New product launch", "Digital adoption"]
          },
          realistic: {
            revenue_growth: 15.2,
            probability: 50,
            key_drivers: ["Steady growth", "Customer retention", "Operational efficiency"]
          },
          pessimistic: {
            revenue_growth: 5.1,
            probability: 25,
            risk_factors: ["Economic downturn", "Increased competition", "Supply disruption"]
          }
        },
        seasonal_patterns: {
          peak_months: ["March", "September", "November"],
          low_months: ["June", "August"],
          seasonal_variance: 22.3,
          pattern_confidence: 85.7
        },
        market_trends: {
          industry_growth: 12.8,
          market_position: "Strong",
          competitive_pressure: 3.2,
          opportunity_score: 78.9
        },
        regression_model: {
          model_accuracy: 82.4,
          r_squared: 0.76,
          key_variables: ["GST Turnover", "Digital Payments", "Market Sentiment", "Seasonal Index"],
          last_trained: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
        },
        last_updated: new Date().toISOString()
      };
      
      setForecastData(mockData);
    } catch (error) {
      console.error('Error fetching forecast data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchForecastData();
    setRefreshing(false);
  };

  const getScenarioColor = (scenario: string) => {
    switch (scenario) {
      case 'optimistic': return 'text-green-600';
      case 'realistic': return 'text-blue-600';
      case 'pessimistic': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getScenarioIcon = (scenario: string) => {
    switch (scenario) {
      case 'optimistic': return <ArrowUpRight className="h-4 w-4 text-green-500" />;
      case 'realistic': return <Activity className="h-4 w-4 text-blue-500" />;
      case 'pessimistic': return <ArrowDownRight className="h-4 w-4 text-red-500" />;
      default: return <BarChart3 className="h-4 w-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!forecastData) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Forecast Data Available</h3>
        <p className="text-gray-500 mb-4">Forecasting data is not available for this MSME.</p>
        <Button onClick={fetchForecastData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Refresh */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Forecasts & Projections</h2>
          <p className="text-muted-foreground">Regression-based forecasting with confidence intervals</p>
        </div>
        <Button 
          variant="outline" 
          onClick={handleRefresh} 
          disabled={refreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <Brain className="h-5 w-5" />
              Model Accuracy
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 mb-2">
              {forecastData.regression_model.model_accuracy.toFixed(1)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-blue-600">R² = {forecastData.regression_model.r_squared.toFixed(2)}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700">
              <Target className="h-5 w-5" />
              Confidence Level
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 mb-2">
              {forecastData.confidence_level.toFixed(0)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Progress value={forecastData.confidence_level} className="h-2 flex-1" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <TrendingUp className="h-5 w-5" />
              Expected Growth
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 mb-2">
              {forecastData.scenario_analysis.realistic.revenue_growth.toFixed(1)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-purple-600">Realistic Scenario</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <Zap className="h-5 w-5" />
              Opportunity Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 mb-2">
              {forecastData.market_trends.opportunity_score.toFixed(0)}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-orange-600">Market Position</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="projections" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="projections">Monthly Projections</TabsTrigger>
          <TabsTrigger value="scenarios">Scenario Analysis</TabsTrigger>
          <TabsTrigger value="patterns">Seasonal Patterns</TabsTrigger>
          <TabsTrigger value="model">Model Details</TabsTrigger>
        </TabsList>

        <TabsContent value="projections" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                12-Month Revenue Projections
              </CardTitle>
              <CardDescription>Month-over-month growth projections with confidence intervals</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {forecastData.monthly_projections.map((projection, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="font-medium">{projection.month}</h4>
                      <p className="text-sm text-muted-foreground">
                        Growth: {projection.growth_rate > 0 ? '+' : ''}{projection.growth_rate.toFixed(1)}%
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold">
                        ₹{(projection.revenue_forecast / 100000).toFixed(1)}L
                      </div>
                      <div className="text-xs text-muted-foreground">
                        ₹{(projection.confidence_interval.lower / 100000).toFixed(1)}L - ₹{(projection.confidence_interval.upper / 100000).toFixed(1)}L
                      </div>
                    </div>
                  </div>
                  
                  {projection.risk_factors.length > 0 && (
                    <div className="flex gap-1 flex-wrap">
                      {projection.risk_factors.map((factor, idx) => (
                        <Badge key={idx} variant="outline" className="text-xs">
                          {factor}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scenarios" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-3">
            {Object.entries(forecastData.scenario_analysis).map(([scenario, data]) => (
              <Card key={scenario}>
                <CardHeader>
                  <CardTitle className={`flex items-center gap-2 capitalize ${getScenarioColor(scenario)}`}>
                    {getScenarioIcon(scenario)}
                    {scenario} Scenario
                  </CardTitle>
                  <CardDescription>
                    {data.probability}% probability
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${getScenarioColor(scenario)} mb-2`}>
                      {data.revenue_growth > 0 ? '+' : ''}{data.revenue_growth.toFixed(1)}%
                    </div>
                    <div className="text-sm text-muted-foreground">Revenue Growth</div>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">
                      {'key_drivers' in data ? 'Key Drivers:' : 'Risk Factors:'}
                    </h4>
                    <div className="space-y-1">
                      {('key_drivers' in data ? data.key_drivers : data.risk_factors).map((item, idx) => (
                        <div key={idx} className="text-xs text-muted-foreground">
                          • {item}
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="patterns" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Seasonal Analysis</CardTitle>
              <CardDescription>Historical seasonal patterns and variance analysis</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Peak Performance Months</h4>
                  <div className="flex gap-2 flex-wrap">
                    {forecastData.seasonal_patterns.peak_months.map((month, idx) => (
                      <Badge key={idx} variant="default" className="bg-green-100 text-green-800">
                        {month}
                      </Badge>
                    ))}
                  </div>
                  
                  <h4 className="font-medium">Low Performance Months</h4>
                  <div className="flex gap-2 flex-wrap">
                    {forecastData.seasonal_patterns.low_months.map((month, idx) => (
                      <Badge key={idx} variant="secondary" className="bg-red-100 text-red-800">
                        {month}
                      </Badge>
                    ))}
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600 mb-1">
                      {forecastData.seasonal_patterns.seasonal_variance.toFixed(1)}%
                    </div>
                    <div className="text-sm text-blue-600">Seasonal Variance</div>
                  </div>
                  
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600 mb-1">
                      {forecastData.seasonal_patterns.pattern_confidence.toFixed(0)}%
                    </div>
                    <div className="text-sm text-green-600">Pattern Confidence</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="model" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Regression Model Details</CardTitle>
              <CardDescription>Statistical model performance and key variables</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Model Performance</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Accuracy</span>
                      <span className="font-semibold">{forecastData.regression_model.model_accuracy.toFixed(1)}%</span>
                    </div>
                    <Progress value={forecastData.regression_model.model_accuracy} className="h-2" />
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm">R-Squared</span>
                      <span className="font-semibold">{forecastData.regression_model.r_squared.toFixed(3)}</span>
                    </div>
                    <Progress value={forecastData.regression_model.r_squared * 100} className="h-2" />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-medium">Key Variables</h4>
                  <div className="space-y-2">
                    {forecastData.regression_model.key_variables.map((variable, idx) => (
                      <div key={idx} className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm">{variable}</span>
                      </div>
                    ))}
                  </div>
                  
                  <div className="text-sm text-muted-foreground mt-4">
                    Last trained: {new Date(forecastData.regression_model.last_trained).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
