"""
Integration tests for Credit Chakra Risk Monitor.

This module provides end-to-end integration testing for the complete
Risk Monitor system including API endpoints, data flow, and cross-component
functionality.

Author: Credit Chakra Team
Version: 1.0.0
"""

import pytest
import asyncio
from datetime import datetime, date, timedelta
from fastapi.testclient import TestClient

from main import app
from services.realtime_risk_monitor import realtime_monitor
from models.risk_monitoring import SMAClassification


class TestRiskMonitorIntegration:
    """Integration tests for complete Risk Monitor workflow."""
    
    def setup_method(self):
        """Set up test client."""
        self.client = TestClient(app)
    
    def test_complete_sma_workflow(self):
        """Test complete SMA classification workflow."""
        # Step 1: Classify MSME SMA status
        due_date = (date.today() - timedelta(days=45)).strftime("%Y-%m-%d")
        outstanding_amount = 1000000
        
        classification_response = self.client.post(
            f"/api/dashboard/risk-monitor/classify-sma/integration_test_001?due_date={due_date}&outstanding_amount={outstanding_amount}"
        )
        
        assert classification_response.status_code == 200
        classification_data = classification_response.json()
        assert classification_data['status'] == 'success'
        assert classification_data['data']['current_classification'] == 'sma_1'
        assert classification_data['data']['days_past_due'] == 45
        
        # Step 2: Get SMA heatmap data
        heatmap_response = self.client.get("/api/dashboard/risk-monitor/sma-heatmap")
        assert heatmap_response.status_code == 200
        heatmap_data = heatmap_response.json()
        assert 'sma_distribution' in heatmap_data['data']
        assert 'exposure_by_sma' in heatmap_data['data']
        
        # Step 3: Get portfolio summary
        summary_response = self.client.get("/api/dashboard/risk-monitor/portfolio-summary")
        assert summary_response.status_code == 200
        summary_data = summary_response.json()
        assert summary_data['data']['total_msmes'] > 0
        assert 'provision_required' in summary_data['data']
        
        # Step 4: Get progression alerts
        alerts_response = self.client.get("/api/dashboard/risk-monitor/sma-progression")
        assert alerts_response.status_code == 200
        alerts_data = alerts_response.json()
        assert isinstance(alerts_data, list)
        
        # Step 5: Get real-time events
        events_response = self.client.get("/api/dashboard/risk-monitor/real-time-events")
        assert events_response.status_code == 200
        events_data = events_response.json()
        assert isinstance(events_data, list)
    
    def test_data_consistency_across_endpoints(self):
        """Test data consistency across different API endpoints."""
        # Get data from multiple endpoints
        heatmap_response = self.client.get("/api/dashboard/risk-monitor/sma-heatmap")
        summary_response = self.client.get("/api/dashboard/risk-monitor/portfolio-summary")
        metrics_response = self.client.get("/api/dashboard/risk-monitor/portfolio-metrics")
        
        assert all(r.status_code == 200 for r in [heatmap_response, summary_response, metrics_response])
        
        heatmap_data = heatmap_response.json()['data']
        summary_data = summary_response.json()['data']
        metrics_data = metrics_response.json()['data']
        
        # Check consistency between endpoints
        # Total exposure should be consistent
        assert heatmap_data['total_exposure'] == summary_data['total_exposure']
        assert summary_data['total_exposure'] == metrics_data['total_exposure']
        
        # SMA and NPA ratios should be consistent
        assert abs(heatmap_data['sma_ratio'] - summary_data['sma_ratio']) < 0.01
        assert abs(heatmap_data['npa_ratio'] - summary_data['npa_ratio']) < 0.01
    
    @pytest.mark.asyncio
    async def test_real_time_data_synchronization(self):
        """Test real-time data synchronization across services."""
        # Test that service methods return consistent data
        heatmap_data = await realtime_monitor.get_sma_heatmap_data()
        summary_data = await realtime_monitor.get_portfolio_sma_summary()
        
        # Check data consistency
        assert heatmap_data['total_exposure'] == summary_data['total_exposure']
        assert abs(heatmap_data['sma_ratio'] - summary_data['sma_ratio']) < 0.01
        assert abs(heatmap_data['npa_ratio'] - summary_data['npa_ratio']) < 0.01
    
    def test_error_handling_consistency(self):
        """Test consistent error handling across endpoints."""
        # Test invalid MSME ID
        invalid_response = self.client.post(
            "/api/dashboard/risk-monitor/classify-sma/invalid_msme?due_date=2024-01-01&outstanding_amount=1000000"
        )
        assert invalid_response.status_code == 200  # Should handle gracefully
        
        # Test invalid date format
        invalid_date_response = self.client.post(
            "/api/dashboard/risk-monitor/classify-sma/test_001?due_date=invalid-date&outstanding_amount=1000000"
        )
        assert invalid_date_response.status_code == 400
        
        # Test missing parameters
        missing_params_response = self.client.post(
            "/api/dashboard/risk-monitor/classify-sma/test_001"
        )
        assert missing_params_response.status_code == 422  # Validation error
    
    def test_performance_under_load(self):
        """Test system performance under simulated load."""
        import time
        import concurrent.futures
        
        def make_request(endpoint):
            start_time = time.time()
            response = self.client.get(endpoint)
            load_time = time.time() - start_time
            return response.status_code == 200, load_time
        
        endpoints = [
            "/api/dashboard/risk-monitor/sma-heatmap",
            "/api/dashboard/risk-monitor/portfolio-summary",
            "/api/dashboard/risk-monitor/sma-progression",
            "/api/dashboard/risk-monitor/real-time-events",
            "/api/dashboard/risk-monitor/portfolio-metrics"
        ]
        
        # Test concurrent requests to all endpoints
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            for _ in range(5):  # 5 rounds of requests
                for endpoint in endpoints:
                    futures.append(executor.submit(make_request, endpoint))
            
            results = [future.result() for future in futures]
        
        # All requests should succeed
        success_rates = [result[0] for result in results]
        load_times = [result[1] for result in results]
        
        assert all(success_rates), "Some requests failed under load"
        assert max(load_times) <= 3.0, f"Max load time {max(load_times):.2f}s exceeds 3s threshold"
        assert sum(load_times) / len(load_times) <= 2.0, "Average load time exceeds 2s threshold"
    
    def test_rbi_compliance_calculations(self):
        """Test RBI compliance calculations are accurate."""
        # Test different SMA classifications
        test_cases = [
            {"days": 0, "expected_sma": "standard", "expected_provision_pct": 0.0},
            {"days": 15, "expected_sma": "sma_0", "expected_provision_pct": 0.25},
            {"days": 45, "expected_sma": "sma_1", "expected_provision_pct": 0.5},
            {"days": 75, "expected_sma": "sma_2", "expected_provision_pct": 1.0},
            {"days": 95, "expected_sma": "npa", "expected_provision_pct": 15.0}
        ]
        
        outstanding_amount = 1000000
        
        for case in test_cases:
            due_date = (date.today() - timedelta(days=case["days"])).strftime("%Y-%m-%d")
            
            response = self.client.post(
                f"/api/dashboard/risk-monitor/classify-sma/test_rbi_{case['days']}?due_date={due_date}&outstanding_amount={outstanding_amount}"
            )
            
            assert response.status_code == 200
            data = response.json()['data']
            
            assert data['current_classification'] == case['expected_sma']
            assert data['days_past_due'] == case['days']
            
            expected_provision = outstanding_amount * (case['expected_provision_pct'] / 100)
            assert abs(data['provision_required'] - expected_provision) < 1.0
    
    def test_audit_trail_integration(self):
        """Test audit trail functionality across the system."""
        # Classify an MSME to generate audit trail
        due_date = (date.today() - timedelta(days=60)).strftime("%Y-%m-%d")
        outstanding_amount = 2000000
        
        response = self.client.post(
            f"/api/dashboard/risk-monitor/classify-sma/audit_test_001?due_date={due_date}&outstanding_amount={outstanding_amount}"
        )
        
        assert response.status_code == 200
        data = response.json()['data']
        
        # Check audit trail exists
        assert 'audit_trail' in data
        assert len(data['audit_trail']) > 0
        
        # Check audit entry structure
        audit_entry = data['audit_trail'][0]
        assert 'entry_id' in audit_entry
        assert 'action' in audit_entry
        assert 'performed_by' in audit_entry
        assert 'timestamp' in audit_entry
        assert audit_entry['entity_id'] == 'audit_test_001'
    
    def test_early_warning_signals_integration(self):
        """Test early warning signals generation and integration."""
        # Get portfolio summary to check for early warning signals
        summary_response = self.client.get("/api/dashboard/risk-monitor/portfolio-summary")
        assert summary_response.status_code == 200
        
        # Get real-time events which should include early warning signals
        events_response = self.client.get("/api/dashboard/risk-monitor/real-time-events")
        assert events_response.status_code == 200
        events_data = events_response.json()
        
        # Check for early warning events
        early_warning_events = [
            event for event in events_data 
            if event.get('event_type') in ['early_warning', 'data_anomaly']
        ]
        
        # Should have some early warning events in the system
        assert len(early_warning_events) >= 0  # May be 0 in test environment
    
    def test_cross_component_data_flow(self):
        """Test data flow between different components."""
        # Step 1: Get initial portfolio state
        initial_summary = self.client.get("/api/dashboard/risk-monitor/portfolio-summary")
        initial_data = initial_summary.json()['data']
        
        # Step 2: Classify a new MSME as SMA-2
        due_date = (date.today() - timedelta(days=70)).strftime("%Y-%m-%d")
        outstanding_amount = 5000000
        
        classification_response = self.client.post(
            f"/api/dashboard/risk-monitor/classify-sma/cross_component_test?due_date={due_date}&outstanding_amount={outstanding_amount}"
        )
        assert classification_response.status_code == 200
        
        # Step 3: Verify the classification affects portfolio metrics
        updated_summary = self.client.get("/api/dashboard/risk-monitor/portfolio-summary")
        updated_data = updated_summary.json()['data']
        
        # The provision required should reflect the new SMA-2 account
        # (This is a conceptual test - in practice, the mock data is static)
        assert updated_data['provision_required'] >= initial_data['provision_required']
    
    def test_api_response_format_consistency(self):
        """Test consistent API response formats across all endpoints."""
        endpoints_with_success_format = [
            "/api/dashboard/risk-monitor/sma-heatmap",
            "/api/dashboard/risk-monitor/portfolio-summary",
            "/api/dashboard/risk-monitor/portfolio-metrics"
        ]
        
        for endpoint in endpoints_with_success_format:
            response = self.client.get(endpoint)
            assert response.status_code == 200
            
            data = response.json()
            assert 'status' in data
            assert data['status'] == 'success'
            assert 'data' in data
            assert 'timestamp' in data
        
        # Test list endpoints
        list_endpoints = [
            "/api/dashboard/risk-monitor/sma-progression",
            "/api/dashboard/risk-monitor/real-time-events"
        ]
        
        for endpoint in list_endpoints:
            response = self.client.get(endpoint)
            assert response.status_code == 200
            
            data = response.json()
            assert isinstance(data, list)


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short"])
