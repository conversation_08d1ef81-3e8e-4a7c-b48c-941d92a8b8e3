"""
Real-time WebSocket service for Credit Chakra risk monitoring.

This module provides WebSocket-based real-time communication for:
- Risk event streaming
- Portfolio metric updates
- Alert notifications
- SMA progression monitoring

Author: Credit Chakra Team
Version: 1.0.0
"""
from __future__ import annotations

import asyncio
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Set, Any, Optional
from uuid import uuid4

from fastapi import WebSocket, WebSocketDisconnect
from pydantic import BaseModel

from models.risk_monitoring import RiskEvent, PortfolioRiskMetrics, RiskAlert, SMAProgressionData

logger = logging.getLogger(__name__)


class WebSocketMessage(BaseModel):
    """WebSocket message structure for real-time communication."""
    message_id: str
    message_type: str
    data: Dict[str, Any]
    timestamp: datetime
    client_id: Optional[str] = None


class ConnectionManager:
    """
    WebSocket connection manager for real-time risk monitoring.
    
    Manages WebSocket connections, message broadcasting, and
    client subscription management for different data streams.
    """
    
    def __init__(self):
        # Active connections by client ID
        self.active_connections: Dict[str, WebSocket] = {}
        
        # Subscription management
        self.risk_event_subscribers: Set[str] = set()
        self.portfolio_metric_subscribers: Set[str] = set()
        self.alert_subscribers: Set[str] = set()
        self.sma_progression_subscribers: Set[str] = set()
        
        # Message queues for offline clients
        self.message_queues: Dict[str, List[WebSocketMessage]] = {}
        
        # Connection metadata
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        
    async def connect(self, websocket: WebSocket, client_id: str) -> None:
        """
        Accept a new WebSocket connection.
        
        Args:
            websocket: WebSocket connection instance
            client_id: Unique client identifier
        """
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.connection_metadata[client_id] = {
            "connected_at": datetime.now(timezone.utc),
            "last_ping": datetime.now(timezone.utc),
            "subscriptions": []
        }
        
        logger.info(f"Client {client_id} connected. Total connections: {len(self.active_connections)}")
        
        # Send queued messages if any
        if client_id in self.message_queues:
            for message in self.message_queues[client_id]:
                await self.send_personal_message(message.dict(), client_id)
            del self.message_queues[client_id]
    
    def disconnect(self, client_id: str) -> None:
        """
        Remove a WebSocket connection.
        
        Args:
            client_id: Client identifier to disconnect
        """
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        
        # Remove from all subscriptions
        self.risk_event_subscribers.discard(client_id)
        self.portfolio_metric_subscribers.discard(client_id)
        self.alert_subscribers.discard(client_id)
        self.sma_progression_subscribers.discard(client_id)
        
        if client_id in self.connection_metadata:
            del self.connection_metadata[client_id]
        
        logger.info(f"Client {client_id} disconnected. Total connections: {len(self.active_connections)}")
    
    async def send_personal_message(self, message: Dict[str, Any], client_id: str) -> None:
        """
        Send a message to a specific client.
        
        Args:
            message: Message data to send
            client_id: Target client identifier
        """
        if client_id in self.active_connections:
            try:
                websocket = self.active_connections[client_id]
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to client {client_id}: {e}")
                self.disconnect(client_id)
        else:
            # Queue message for offline client
            if client_id not in self.message_queues:
                self.message_queues[client_id] = []
            
            ws_message = WebSocketMessage(
                message_id=str(uuid4()),
                message_type=message.get("type", "unknown"),
                data=message,
                timestamp=datetime.now(timezone.utc),
                client_id=client_id
            )
            self.message_queues[client_id].append(ws_message)
    
    async def broadcast_to_subscribers(self, message: Dict[str, Any], subscribers: Set[str]) -> None:
        """
        Broadcast a message to all subscribers of a specific type.
        
        Args:
            message: Message data to broadcast
            subscribers: Set of subscriber client IDs
        """
        if not subscribers:
            return
        
        disconnected_clients = []
        
        for client_id in subscribers:
            if client_id in self.active_connections:
                try:
                    websocket = self.active_connections[client_id]
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    logger.error(f"Error broadcasting to client {client_id}: {e}")
                    disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)
    
    async def subscribe_to_risk_events(self, client_id: str) -> None:
        """Subscribe client to risk event updates."""
        self.risk_event_subscribers.add(client_id)
        if client_id in self.connection_metadata:
            self.connection_metadata[client_id]["subscriptions"].append("risk_events")
        
        logger.info(f"Client {client_id} subscribed to risk events")
    
    async def subscribe_to_portfolio_metrics(self, client_id: str) -> None:
        """Subscribe client to portfolio metric updates."""
        self.portfolio_metric_subscribers.add(client_id)
        if client_id in self.connection_metadata:
            self.connection_metadata[client_id]["subscriptions"].append("portfolio_metrics")
        
        logger.info(f"Client {client_id} subscribed to portfolio metrics")
    
    async def subscribe_to_alerts(self, client_id: str) -> None:
        """Subscribe client to alert notifications."""
        self.alert_subscribers.add(client_id)
        if client_id in self.connection_metadata:
            self.connection_metadata[client_id]["subscriptions"].append("alerts")
        
        logger.info(f"Client {client_id} subscribed to alerts")
    
    async def subscribe_to_sma_progression(self, client_id: str) -> None:
        """Subscribe client to SMA progression updates."""
        self.sma_progression_subscribers.add(client_id)
        if client_id in self.connection_metadata:
            self.connection_metadata[client_id]["subscriptions"].append("sma_progression")
        
        logger.info(f"Client {client_id} subscribed to SMA progression")
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics for monitoring."""
        return {
            "total_connections": len(self.active_connections),
            "risk_event_subscribers": len(self.risk_event_subscribers),
            "portfolio_metric_subscribers": len(self.portfolio_metric_subscribers),
            "alert_subscribers": len(self.alert_subscribers),
            "sma_progression_subscribers": len(self.sma_progression_subscribers),
            "queued_messages": sum(len(queue) for queue in self.message_queues.values()),
            "active_clients": list(self.active_connections.keys())
        }


class RealtimeRiskService:
    """
    Real-time risk monitoring service with WebSocket integration.
    
    Provides real-time streaming of risk events, portfolio metrics,
    and alerts to connected clients.
    """
    
    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
        self.is_running = False
        self.update_interval = 30  # seconds
    
    async def start_monitoring(self) -> None:
        """Start the real-time monitoring service."""
        self.is_running = True
        logger.info("Real-time risk monitoring service started")
        
        # Start background tasks
        asyncio.create_task(self._portfolio_metrics_updater())
        asyncio.create_task(self._risk_event_monitor())
        asyncio.create_task(self._alert_processor())
    
    async def stop_monitoring(self) -> None:
        """Stop the real-time monitoring service."""
        self.is_running = False
        logger.info("Real-time risk monitoring service stopped")
    
    async def broadcast_risk_event(self, risk_event: RiskEvent) -> None:
        """
        Broadcast a risk event to all subscribed clients.
        
        Args:
            risk_event: Risk event to broadcast
        """
        message = {
            "type": "risk_event",
            "data": risk_event.dict(),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        await self.connection_manager.broadcast_to_subscribers(
            message, 
            self.connection_manager.risk_event_subscribers
        )
    
    async def broadcast_portfolio_metrics(self, metrics: PortfolioRiskMetrics) -> None:
        """
        Broadcast portfolio metrics to all subscribed clients.
        
        Args:
            metrics: Portfolio metrics to broadcast
        """
        message = {
            "type": "portfolio_metrics",
            "data": metrics.dict(),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        await self.connection_manager.broadcast_to_subscribers(
            message, 
            self.connection_manager.portfolio_metric_subscribers
        )
    
    async def broadcast_alert(self, alert: RiskAlert) -> None:
        """
        Broadcast an alert to all subscribed clients.
        
        Args:
            alert: Alert to broadcast
        """
        message = {
            "type": "alert",
            "data": alert.dict(),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        await self.connection_manager.broadcast_to_subscribers(
            message, 
            self.connection_manager.alert_subscribers
        )
    
    async def broadcast_sma_progression(self, sma_data: SMAProgressionData) -> None:
        """
        Broadcast SMA progression update to all subscribed clients.
        
        Args:
            sma_data: SMA progression data to broadcast
        """
        message = {
            "type": "sma_progression",
            "data": sma_data.dict(),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        await self.connection_manager.broadcast_to_subscribers(
            message, 
            self.connection_manager.sma_progression_subscribers
        )
    
    async def _portfolio_metrics_updater(self) -> None:
        """Background task to update portfolio metrics periodically."""
        while self.is_running:
            try:
                # TODO: Implement actual portfolio metrics calculation
                # This would integrate with the existing portfolio service
                await asyncio.sleep(self.update_interval)
            except Exception as e:
                logger.error(f"Error in portfolio metrics updater: {e}")
                await asyncio.sleep(5)
    
    async def _risk_event_monitor(self) -> None:
        """Background task to monitor and generate risk events."""
        while self.is_running:
            try:
                # TODO: Implement actual risk event monitoring
                # This would integrate with the existing risk monitoring service
                await asyncio.sleep(10)
            except Exception as e:
                logger.error(f"Error in risk event monitor: {e}")
                await asyncio.sleep(5)
    
    async def _alert_processor(self) -> None:
        """Background task to process and send alerts."""
        while self.is_running:
            try:
                # TODO: Implement actual alert processing
                # This would integrate with the existing alert system
                await asyncio.sleep(15)
            except Exception as e:
                logger.error(f"Error in alert processor: {e}")
                await asyncio.sleep(5)


# Global connection manager instance
connection_manager = ConnectionManager()
realtime_service = RealtimeRiskService(connection_manager)
