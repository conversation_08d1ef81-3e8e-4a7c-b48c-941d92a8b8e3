"""
Initialize demo data for Credit Chakra application with realistic Indian MSMEs
"""
import uuid
from datetime import datetime, timedelta

# Global mock data store
_MOCK_DATA_STORE = {
    'msmes': {},
    'signals': {},
    'nudges': {}
}

def init_demo_data():
    """Initialize demo data with realistic Indian MSMEs (60% green, 25% yellow, 15% red)"""
    
    # Enhanced realistic Indian MSMEs with proper risk distribution
    msmes = [
        # GREEN RISK MSMEs (12 out of 20 = 60%)
        {
            "msme_id": "msme_001",
            "name": "श्री गणेश इलेक्ट्रॉनिक्स",
            "business_type": "retail",
            "location": "Mumbai, Maharashtra",
            "created_at": "2024-01-15T10:00:00Z",
            "score": 785.0,
            "risk_band": "green",
            "tags": ["electronics", "retail", "b2c"],
            "gst_number": "27AABCS1234C1Z5",
            "gst_compliance": 92,
            "banking_health": 88,
            "monthly_turnover": 2850000,
            "digital_score": 85
        },
        {
            "msme_id": "msme_002",
            "name": "Rajesh Textiles Pvt Ltd",
            "business_type": "manufacturing",
            "location": "Surat, Gujarat",
            "created_at": "2024-01-10T08:00:00Z",
            "score": 742.0,
            "risk_band": "green",
            "tags": ["textiles", "manufacturing", "export"],
            "gst_number": "24AABCT5678D1Z8",
            "gst_compliance": 89,
            "banking_health": 91,
            "monthly_turnover": 4200000,
            "digital_score": 78
        },
        {
            "msme_id": "msme_003",
            "name": "Annapurna Catering Services",
            "business_type": "services",
            "location": "Pune, Maharashtra",
            "created_at": "2024-01-05T12:00:00Z",
            "score": 698.0,
            "risk_band": "green",
            "tags": ["catering", "food", "services"],
            "gst_number": "27AABCA9876E1Z2",
            "gst_compliance": 85,
            "banking_health": 82,
            "monthly_turnover": 1850000,
            "digital_score": 72
        },
        {
            "msme_id": "msme_004",
            "name": "Sharma Auto Parts",
            "business_type": "retail",
            "location": "Delhi, Delhi",
            "created_at": "2024-01-08T09:00:00Z",
            "score": 721.0,
            "risk_band": "green",
            "tags": ["automotive", "retail", "spare-parts"],
            "gst_number": "07AABCS4567F1Z9",
            "gst_compliance": 87,
            "banking_health": 84,
            "monthly_turnover": 2100000,
            "digital_score": 79
        },
        {
            "msme_id": "msme_005",
            "name": "Tech Solutions India",
            "business_type": "services",
            "location": "Bangalore, Karnataka",
            "created_at": "2024-01-12T14:00:00Z",
            "score": 756.0,
            "risk_band": "green",
            "tags": ["technology", "software", "services"],
            "gst_number": "29AABCT7890G1Z3",
            "gst_compliance": 94,
            "banking_health": 89,
            "monthly_turnover": 3200000,
            "digital_score": 92
        },
        {
            "msme_id": "msme_006",
            "name": "Patel Manufacturing Co",
            "business_type": "manufacturing",
            "location": "Ahmedabad, Gujarat",
            "created_at": "2024-01-03T11:00:00Z",
            "score": 689.0,
            "risk_band": "green",
            "tags": ["chemicals", "manufacturing", "b2b"],
            "gst_number": "24AABCP2345H1Z6",
            "gst_compliance": 83,
            "banking_health": 86,
            "monthly_turnover": 5100000,
            "digital_score": 68
        },
        {
            "msme_id": "msme_007",
            "name": "Golden Bakery",
            "business_type": "retail",
            "location": "Kolkata, West Bengal",
            "created_at": "2024-01-06T08:30:00Z",
            "score": 703.0,
            "risk_band": "green",
            "tags": ["bakery", "food", "retail"],
            "gst_number": "19AABCG6789I1Z4",
            "gst_compliance": 86,
            "banking_health": 81,
            "monthly_turnover": 950000,
            "digital_score": 74
        },
        {
            "msme_id": "msme_008",
            "name": "Kumar Engineering Works",
            "business_type": "manufacturing",
            "location": "Chennai, Tamil Nadu",
            "created_at": "2024-01-09T10:00:00Z",
            "score": 734.0,
            "risk_band": "green",
            "tags": ["engineering", "manufacturing", "precision"],
            "gst_number": "33AABCK3456J1Z7",
            "gst_compliance": 88,
            "banking_health": 87,
            "monthly_turnover": 2800000,
            "digital_score": 76
        },
        {
            "msme_id": "msme_009",
            "name": "Gupta Traders",
            "business_type": "retail",
            "location": "Jaipur, Rajasthan",
            "created_at": "2024-01-11T12:00:00Z",
            "score": 712.0,
            "risk_band": "green",
            "tags": ["wholesale", "trading", "general"],
            "gst_number": "08AABCG7890K1Z1",
            "gst_compliance": 84,
            "banking_health": 83,
            "monthly_turnover": 1650000,
            "digital_score": 71
        },
        {
            "msme_id": "msme_010",
            "name": "Vishwakarma Steel",
            "business_type": "manufacturing",
            "location": "Indore, Madhya Pradesh",
            "created_at": "2024-01-04T09:30:00Z",
            "score": 695.0,
            "risk_band": "green",
            "tags": ["steel", "manufacturing", "construction"],
            "gst_number": "23AABCV4567L1Z8",
            "gst_compliance": 82,
            "banking_health": 85,
            "monthly_turnover": 3800000,
            "digital_score": 69
        },
        {
            "msme_id": "msme_011",
            "name": "Digital Marketing Hub",
            "business_type": "services",
            "location": "Hyderabad, Telangana",
            "created_at": "2024-01-13T16:00:00Z",
            "score": 718.0,
            "risk_band": "green",
            "tags": ["marketing", "digital", "services"],
            "gst_number": "36AABCD8901M1Z5",
            "gst_compliance": 91,
            "banking_health": 84,
            "monthly_turnover": 1420000,
            "digital_score": 95
        },
        {
            "msme_id": "msme_012",
            "name": "Agarwal Pharmaceuticals",
            "business_type": "manufacturing",
            "location": "Lucknow, Uttar Pradesh",
            "created_at": "2024-01-07T13:30:00Z",
            "score": 681.0,
            "risk_band": "green",
            "tags": ["pharmaceuticals", "manufacturing", "healthcare"],
            "gst_number": "09AABCA5678N1Z2",
            "gst_compliance": 89,
            "banking_health": 78,
            "monthly_turnover": 2650000,
            "digital_score": 73
        },
        # YELLOW RISK MSMEs (5 out of 20 = 25%)
        {
            "msme_id": "msme_013",
            "name": "Spice Garden Restaurant",
            "business_type": "services",
            "location": "Goa, Goa",
            "created_at": "2024-01-14T11:00:00Z",
            "score": 580.0,
            "risk_band": "yellow",
            "tags": ["restaurant", "food", "hospitality"],
            "gst_number": "30AABCS9012O1Z6",
            "gst_compliance": 72,
            "banking_health": 68,
            "monthly_turnover": 1180000,
            "digital_score": 81
        },
        {
            "msme_id": "msme_014",
            "name": "Mehta Garments",
            "business_type": "retail",
            "location": "Ludhiana, Punjab",
            "created_at": "2024-01-16T09:00:00Z",
            "score": 545.0,
            "risk_band": "yellow",
            "tags": ["garments", "retail", "fashion"],
            "gst_number": "03AABCM6789P1Z3",
            "gst_compliance": 68,
            "banking_health": 71,
            "monthly_turnover": 890000,
            "digital_score": 65
        },
        {
            "msme_id": "msme_015",
            "name": "Coastal Fisheries",
            "business_type": "services",
            "location": "Kochi, Kerala",
            "created_at": "2024-01-18T07:30:00Z",
            "score": 612.0,
            "risk_band": "yellow",
            "tags": ["fisheries", "seafood", "export"],
            "gst_number": "32AABCC1234Q1Z7",
            "gst_compliance": 75,
            "banking_health": 73,
            "monthly_turnover": 1350000,
            "digital_score": 58
        },
        {
            "msme_id": "msme_016",
            "name": "Rajasthan Handicrafts",
            "business_type": "manufacturing",
            "location": "Udaipur, Rajasthan",
            "created_at": "2024-01-20T10:30:00Z",
            "score": 567.0,
            "risk_band": "yellow",
            "tags": ["handicrafts", "art", "export"],
            "gst_number": "08AABCR7890S1Z4",
            "gst_compliance": 69,
            "banking_health": 66,
            "monthly_turnover": 720000,
            "digital_score": 62
        },
        {
            "msme_id": "msme_017",
            "name": "Urban Logistics",
            "business_type": "services",
            "location": "Noida, Uttar Pradesh",
            "created_at": "2024-01-22T14:00:00Z",
            "score": 598.0,
            "risk_band": "yellow",
            "tags": ["logistics", "transport", "delivery"],
            "gst_number": "09AABCU4567T1Z1",
            "gst_compliance": 74,
            "banking_health": 69,
            "monthly_turnover": 1520000,
            "digital_score": 77
        },
        # RED RISK MSMEs (3 out of 20 = 15%)
        {
            "msme_id": "msme_018",
            "name": "Struggling Auto Parts",
            "business_type": "retail",
            "location": "Kanpur, Uttar Pradesh",
            "created_at": "2024-01-25T12:00:00Z",
            "score": 285.0,
            "risk_band": "red",
            "tags": ["automotive", "parts", "retail"],
            "gst_number": "09AABCS2345U1Z8",
            "gst_compliance": 45,
            "banking_health": 38,
            "monthly_turnover": 420000,
            "digital_score": 32
        },
        {
            "msme_id": "msme_019",
            "name": "Declining Textiles",
            "business_type": "manufacturing",
            "location": "Coimbatore, Tamil Nadu",
            "created_at": "2024-01-28T08:00:00Z",
            "score": 312.0,
            "risk_band": "red",
            "tags": ["textiles", "manufacturing", "declining"],
            "gst_number": "33AABCD5678V1Z5",
            "gst_compliance": 52,
            "banking_health": 41,
            "monthly_turnover": 580000,
            "digital_score": 28
        },
        {
            "msme_id": "msme_020",
            "name": "Risky Ventures",
            "business_type": "services",
            "location": "Bhopal, Madhya Pradesh",
            "created_at": "2024-01-30T15:00:00Z",
            "score": 298.0,
            "risk_band": "red",
            "tags": ["consulting", "services", "high-risk"],
            "gst_number": "23AABCR9012W1Z2",
            "gst_compliance": 48,
            "banking_health": 35,
            "monthly_turnover": 380000,
            "digital_score": 25
        }
    ]

    # Add MSMEs to store
    for msme in msmes:
        _MOCK_DATA_STORE['msmes'][msme['msme_id']] = msme

def get_mock_data_store():
    """Get the mock data store"""
    return _MOCK_DATA_STORE
