import asyncio
import aiohttp
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone, timedelta, date
from dateutil.relativedelta import relativedelta
import statistics

from models.account_aggregator import (
    ConsentRequest, BankAccount, Transaction, CashFlowStatement,
    BankingAnalytics, AADataRequest, AADataResponse, AAHealthCheck,
    ConsentStatus, AccountType, TransactionType, TransactionMode
)
from firebase.init import get_firestore_client

logger = logging.getLogger(__name__)

class AccountAggregatorService:
    """Service for Account Aggregator integration and analysis"""
    
    def __init__(self):
        self.aa_base_url = "https://api.sahamati.org.in"  # Mock URL - replace with actual AA API
        self.client_id = "your_aa_client_id"  # From environment variables
        self.client_secret = "your_aa_client_secret"  # From environment variables
        self.db = get_firestore_client()
        
    async def create_consent_request(self, consent_request: ConsentRequest) -> str:
        """Create consent request with Account Aggregator"""
        try:
            # In production, this would call the actual AA API
            consent_url = f"{self.aa_base_url}/consent"
            
            consent_data = {
                "ver": "1.1.2",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "txnid": consent_request.consent_id,
                "ConsentDetail": {
                    "consentStart": consent_request.consent_start.isoformat(),
                    "consentExpiry": consent_request.consent_expiry.isoformat(),
                    "consentMode": "STORE",
                    "fetchType": "PERIODIC",
                    "consentTypes": ["TRANSACTIONS", "PROFILE", "SUMMARY"],
                    "fiTypes": consent_request.fi_types,
                    "DataConsumer": {
                        "id": self.client_id,
                        "type": "FIU"
                    },
                    "Customer": {
                        "id": consent_request.customer_id
                    },
                    "Purpose": {
                        "code": "101",
                        "refUri": "https://api.rebit.org.in/aa/purpose/101.xml",
                        "text": consent_request.purpose,
                        "Category": {
                            "type": "string"
                        }
                    },
                    "FIDataRange": {
                        "from": consent_request.from_date.isoformat(),
                        "to": consent_request.to_date.isoformat()
                    },
                    "DataLife": {
                        "unit": "MONTH",
                        "value": consent_request.data_life
                    },
                    "Frequency": {
                        "unit": consent_request.frequency_unit,
                        "value": consent_request.frequency_value
                    }
                }
            }
            
            # Mock implementation - store consent request
            await self._store_consent_request(consent_request)
            
            return consent_request.consent_id
            
        except Exception as e:
            logger.error(f"Error creating consent request: {str(e)}")
            raise
    
    async def check_consent_status(self, consent_id: str) -> ConsentStatus:
        """Check consent status"""
        try:
            # In production, this would call the AA API
            consent_doc = self.db.collection('aa_consents').document(consent_id).get()
            
            if consent_doc.exists:
                consent_data = consent_doc.to_dict()
                return ConsentStatus(consent_data.get('status', ConsentStatus.PENDING))
            else:
                return ConsentStatus.PENDING
                
        except Exception as e:
            logger.error(f"Error checking consent status: {str(e)}")
            return ConsentStatus.PENDING
    
    async def fetch_account_data(self, consent_id: str, customer_id: str) -> List[BankAccount]:
        """Fetch bank account data using AA"""
        try:
            # Check consent status
            consent_status = await self.check_consent_status(consent_id)
            if consent_status != ConsentStatus.ACTIVE:
                raise Exception(f"Consent not active: {consent_status}")
            
            # Mock implementation - generate sample account data
            accounts = self._generate_mock_accounts(customer_id)
            
            # Store accounts
            for account in accounts:
                await self._store_bank_account(account)
            
            return accounts
            
        except Exception as e:
            logger.error(f"Error fetching account data: {str(e)}")
            return []
    
    async def fetch_transaction_data(self, consent_id: str, account_id: str, 
                                   from_date: date, to_date: date) -> List[Transaction]:
        """Fetch transaction data for an account"""
        try:
            # Check consent status
            consent_status = await self.check_consent_status(consent_id)
            if consent_status != ConsentStatus.ACTIVE:
                raise Exception(f"Consent not active: {consent_status}")
            
            # Mock implementation - generate sample transaction data
            transactions = self._generate_mock_transactions(account_id, from_date, to_date)
            
            # Store transactions
            for transaction in transactions:
                await self._store_transaction(transaction)
            
            return transactions
            
        except Exception as e:
            logger.error(f"Error fetching transaction data: {str(e)}")
            return []
    
    async def generate_cash_flow_statement(self, account_id: str, 
                                         period_start: date, period_end: date) -> CashFlowStatement:
        """Generate cash flow statement from transaction data"""
        try:
            # Get transactions for the period
            transactions = await self._get_stored_transactions(account_id, period_start, period_end)
            
            if not transactions:
                return CashFlowStatement(
                    account_id=account_id,
                    period_start=period_start,
                    period_end=period_end
                )
            
            # Calculate cash flows
            cash_flow = self._calculate_cash_flows(transactions, period_start, period_end)
            
            # Store cash flow statement
            await self._store_cash_flow_statement(cash_flow)
            
            return cash_flow
            
        except Exception as e:
            logger.error(f"Error generating cash flow statement: {str(e)}")
            return CashFlowStatement(
                account_id=account_id,
                period_start=period_start,
                period_end=period_end
            )
    
    async def calculate_banking_analytics(self, msme_id: str, consent_id: str) -> Optional[BankingAnalytics]:
        """Calculate comprehensive banking analytics"""
        try:
            # Get all accounts and transactions
            accounts = await self._get_stored_accounts(consent_id)
            all_transactions = []
            
            for account in accounts:
                transactions = await self._get_stored_transactions(
                    account.account_id, 
                    date.today() - timedelta(days=365), 
                    date.today()
                )
                all_transactions.extend(transactions)
            
            if not accounts or not all_transactions:
                return None
            
            # Calculate analytics
            analytics = BankingAnalytics(
                msme_id=msme_id,
                consent_id=consent_id,
                total_accounts=len(accounts),
                active_accounts=len([a for a in accounts if a.is_active]),
                account_types=list(set([a.account_type for a in accounts])),
                total_balance=sum(a.current_balance for a in accounts),
                avg_monthly_balance=self._calculate_avg_monthly_balance(all_transactions),
                min_balance=self._calculate_min_balance(all_transactions),
                max_balance=self._calculate_max_balance(all_transactions),
                balance_volatility=self._calculate_balance_volatility(all_transactions),
                total_transactions=len(all_transactions),
                avg_monthly_transactions=len(all_transactions) / 12,  # Assuming 1 year data
                avg_transaction_amount=self._calculate_avg_transaction_amount(all_transactions),
                avg_monthly_inflow=self._calculate_avg_monthly_inflow(all_transactions),
                avg_monthly_outflow=self._calculate_avg_monthly_outflow(all_transactions),
                cash_flow_volatility=self._calculate_cash_flow_volatility(all_transactions),
                working_capital_cycle=self._calculate_working_capital_cycle(all_transactions),
                digital_transaction_ratio=self._calculate_digital_transaction_ratio(all_transactions),
                upi_adoption_rate=self._calculate_upi_adoption_rate(all_transactions),
                digital_payment_growth=self._calculate_digital_payment_growth(all_transactions),
                account_utilization_rate=self._calculate_account_utilization_rate(accounts, all_transactions),
                overdraft_usage=self._calculate_overdraft_usage(accounts),
                bounce_rate=self._calculate_bounce_rate(all_transactions),
                irregular_transaction_flag=self._detect_irregular_transactions(all_transactions),
                cash_intensive_flag=self._detect_cash_intensive_behavior(all_transactions),
                dormant_account_flag=self._detect_dormant_accounts(accounts, all_transactions),
                seasonal_variation=self._calculate_seasonal_variation(all_transactions),
                peak_months=self._identify_peak_months(all_transactions),
                low_months=self._identify_low_months(all_transactions),
                banking_score=0.0,  # Will be calculated below
                stability_score=0.0,  # Will be calculated below
                digital_maturity_score=0.0,  # Will be calculated below
                balance_trend=self._determine_balance_trend(all_transactions),
                transaction_trend=self._determine_transaction_trend(all_transactions),
                digital_trend=self._determine_digital_trend(all_transactions),
                data_quality_score=self._calculate_data_quality_score(accounts, all_transactions)
            )
            
            # Calculate composite scores
            analytics.banking_score = self._calculate_banking_score(analytics)
            analytics.stability_score = self._calculate_stability_score(analytics)
            analytics.digital_maturity_score = self._calculate_digital_maturity_score(analytics)
            
            # Store analytics
            await self._store_banking_analytics(analytics)
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error calculating banking analytics: {str(e)}")
            return None
    
    async def get_aa_health_check(self, consent_id: str, customer_id: str) -> AAHealthCheck:
        """Get AA data source health check"""
        try:
            # Check last sync status
            last_sync_doc = self.db.collection('aa_sync_status').document(consent_id).get()
            
            if last_sync_doc.exists:
                sync_data = last_sync_doc.to_dict()
                return AAHealthCheck(
                    consent_id=consent_id,
                    customer_id=customer_id,
                    last_sync=sync_data.get('last_sync', datetime.now(timezone.utc)),
                    sync_status=sync_data.get('sync_status', 'unknown'),
                    data_completeness=sync_data.get('data_completeness', 0.0),
                    api_response_time=sync_data.get('api_response_time', 0.0),
                    consent_status=ConsentStatus(sync_data.get('consent_status', ConsentStatus.PENDING)),
                    accounts_synced=sync_data.get('accounts_synced', 0),
                    transactions_synced=sync_data.get('transactions_synced', 0),
                    error_count=sync_data.get('error_count', 0),
                    next_sync_scheduled=sync_data.get('next_sync_scheduled', datetime.now(timezone.utc) + timedelta(hours=24))
                )
            else:
                return AAHealthCheck(
                    consent_id=consent_id,
                    customer_id=customer_id,
                    last_sync=datetime.now(timezone.utc),
                    sync_status='never_synced',
                    data_completeness=0.0,
                    api_response_time=0.0,
                    consent_status=ConsentStatus.PENDING,
                    accounts_synced=0,
                    transactions_synced=0,
                    error_count=0,
                    next_sync_scheduled=datetime.now(timezone.utc) + timedelta(hours=1)
                )
                
        except Exception as e:
            logger.error(f"Error getting AA health check: {str(e)}")
            return AAHealthCheck(
                consent_id=consent_id,
                customer_id=customer_id,
                last_sync=datetime.now(timezone.utc),
                sync_status='error',
                data_completeness=0.0,
                api_response_time=0.0,
                consent_status=ConsentStatus.PENDING,
                accounts_synced=0,
                transactions_synced=0,
                error_count=1,
                next_sync_scheduled=datetime.now(timezone.utc) + timedelta(hours=1)
            )
    
    # Helper methods for calculations
    def _calculate_avg_monthly_balance(self, transactions: List[Transaction]) -> float:
        """Calculate average monthly balance"""
        if not transactions:
            return 0.0
        
        # Group transactions by month and calculate average balance
        monthly_balances = {}
        for transaction in transactions:
            month_key = transaction.transaction_date.strftime("%Y-%m")
            if month_key not in monthly_balances:
                monthly_balances[month_key] = []
            monthly_balances[month_key].append(transaction.balance_after_transaction)
        
        if not monthly_balances:
            return 0.0
        
        month_averages = [sum(balances) / len(balances) for balances in monthly_balances.values()]
        return sum(month_averages) / len(month_averages)
    
    def _calculate_digital_transaction_ratio(self, transactions: List[Transaction]) -> float:
        """Calculate digital transaction ratio"""
        if not transactions:
            return 0.0
        
        digital_transactions = sum(1 for t in transactions if t.is_digital)
        return (digital_transactions / len(transactions)) * 100
    
    def _calculate_upi_adoption_rate(self, transactions: List[Transaction]) -> float:
        """Calculate UPI adoption rate"""
        if not transactions:
            return 0.0
        
        upi_transactions = sum(1 for t in transactions if t.mode == TransactionMode.UPI)
        return (upi_transactions / len(transactions)) * 100

    def _calculate_banking_score(self, analytics: BankingAnalytics) -> float:
        """Calculate overall banking behavior score"""
        # Weighted scoring
        weights = {
            'balance_stability': 0.25,
            'transaction_regularity': 0.20,
            'digital_adoption': 0.20,
            'account_utilization': 0.15,
            'cash_flow_health': 0.20
        }

        # Balance stability (inverse of volatility)
        balance_stability = max(0, 100 - analytics.balance_volatility)

        # Transaction regularity (based on consistency)
        transaction_regularity = min(100, analytics.avg_monthly_transactions * 2)

        # Digital adoption
        digital_adoption = analytics.digital_transaction_ratio

        # Account utilization
        account_utilization = analytics.account_utilization_rate

        # Cash flow health (positive cash flow is good)
        cash_flow_health = min(100, max(0, 50 + (analytics.avg_monthly_inflow - analytics.avg_monthly_outflow) / 10000))

        banking_score = (
            balance_stability * weights['balance_stability'] +
            transaction_regularity * weights['transaction_regularity'] +
            digital_adoption * weights['digital_adoption'] +
            account_utilization * weights['account_utilization'] +
            cash_flow_health * weights['cash_flow_health']
        )

        return round(banking_score, 2)

    def _calculate_stability_score(self, analytics: BankingAnalytics) -> float:
        """Calculate financial stability score"""
        stability_factors = []

        # Balance volatility (lower is better)
        if analytics.balance_volatility < 20:
            stability_factors.append(90)
        elif analytics.balance_volatility < 40:
            stability_factors.append(70)
        else:
            stability_factors.append(40)

        # Cash flow volatility (lower is better)
        if analytics.cash_flow_volatility < 30:
            stability_factors.append(85)
        elif analytics.cash_flow_volatility < 50:
            stability_factors.append(65)
        else:
            stability_factors.append(35)

        # Regular transaction pattern
        if analytics.avg_monthly_transactions > 50:
            stability_factors.append(80)
        elif analytics.avg_monthly_transactions > 20:
            stability_factors.append(60)
        else:
            stability_factors.append(30)

        # No irregular flags
        if not analytics.irregular_transaction_flag and not analytics.dormant_account_flag:
            stability_factors.append(90)
        else:
            stability_factors.append(50)

        return sum(stability_factors) / len(stability_factors)

    def _calculate_digital_maturity_score(self, analytics: BankingAnalytics) -> float:
        """Calculate digital maturity score"""
        digital_factors = []

        # Digital transaction ratio
        digital_factors.append(analytics.digital_transaction_ratio)

        # UPI adoption
        digital_factors.append(analytics.upi_adoption_rate)

        # Digital payment growth
        if analytics.digital_payment_growth > 10:
            digital_factors.append(90)
        elif analytics.digital_payment_growth > 0:
            digital_factors.append(70)
        else:
            digital_factors.append(40)

        # Low cash intensive behavior
        if not analytics.cash_intensive_flag:
            digital_factors.append(80)
        else:
            digital_factors.append(30)

        return sum(digital_factors) / len(digital_factors)

    # Mock data generation methods
    def _generate_mock_accounts(self, customer_id: str) -> List[BankAccount]:
        """Generate mock bank accounts"""
        import random

        accounts = []
        account_types = [AccountType.CURRENT, AccountType.SAVINGS]

        for i, acc_type in enumerate(account_types):
            account = BankAccount(
                account_id=f"ACC_{customer_id}_{i+1}",
                masked_account_number=f"XXXX{random.randint(1000, 9999)}",
                account_type=acc_type,
                bank_name=random.choice(["SBI", "HDFC", "ICICI", "Axis Bank", "PNB"]),
                bank_code=f"BANK{random.randint(100, 999)}",
                branch_name=f"Branch {random.randint(1, 100)}",
                ifsc_code=f"BANK0{random.randint(100000, 999999)}",
                account_holder_name="Business Owner",
                current_balance=random.uniform(50000, 500000),
                available_balance=random.uniform(40000, 450000),
                is_active=True
            )
            accounts.append(account)

        return accounts

    def _generate_mock_transactions(self, account_id: str, from_date: date, to_date: date) -> List[Transaction]:
        """Generate mock transactions"""
        import random
        from datetime import timedelta, timezone

        transactions = []
        current_date = from_date
        balance = random.uniform(100000, 500000)

        while current_date <= to_date:
            # Generate 1-5 transactions per day
            daily_transactions = random.randint(1, 5)

            for i in range(daily_transactions):
                transaction_type = random.choice([TransactionType.CREDIT, TransactionType.DEBIT])
                amount = random.uniform(1000, 50000)

                if transaction_type == TransactionType.DEBIT:
                    balance -= amount
                else:
                    balance += amount

                transaction = Transaction(
                    transaction_id=f"TXN_{account_id}_{current_date.strftime('%Y%m%d')}_{i+1}",
                    account_id=account_id,
                    transaction_date=datetime.combine(current_date, datetime.min.time()),
                    value_date=datetime.combine(current_date, datetime.min.time()),
                    transaction_type=transaction_type,
                    amount=amount,
                    description=random.choice([
                        "Payment to supplier", "Customer payment", "Salary payment",
                        "Utility bill", "GST payment", "Loan EMI", "Cash deposit"
                    ]),
                    reference_number=f"REF{random.randint(100000, 999999)}",
                    mode=random.choice(list(TransactionMode)),
                    balance_after_transaction=balance,
                    counterparty_name=f"Counterparty {random.randint(1, 100)}",
                    is_digital=random.choice([True, False]),
                    is_business_related=random.choice([True, False])
                )
                transactions.append(transaction)

            current_date += timedelta(days=1)

        return transactions

    def _calculate_cash_flows(self, transactions: List[Transaction],
                            period_start: date, period_end: date) -> CashFlowStatement:
        """Calculate cash flows from transactions"""
        # Filter transactions for the period
        period_transactions = [
            t for t in transactions
            if period_start <= t.transaction_date.date() <= period_end
        ]

        if not period_transactions:
            return CashFlowStatement(
                account_id=transactions[0].account_id if transactions else "",
                period_start=period_start,
                period_end=period_end
            )

        # Calculate basic flows
        total_inflows = sum(t.amount for t in period_transactions if t.transaction_type == TransactionType.CREDIT)
        total_outflows = sum(t.amount for t in period_transactions if t.transaction_type == TransactionType.DEBIT)

        # Categorize transactions (simplified categorization)
        operating_inflows = sum(t.amount for t in period_transactions
                              if t.transaction_type == TransactionType.CREDIT and t.is_business_related)
        operating_outflows = sum(t.amount for t in period_transactions
                               if t.transaction_type == TransactionType.DEBIT and t.is_business_related)

        # Digital payment metrics
        digital_transactions = [t for t in period_transactions if t.is_digital]
        upi_transactions = [t for t in period_transactions if t.mode == TransactionMode.UPI]

        return CashFlowStatement(
            account_id=period_transactions[0].account_id,
            period_start=period_start,
            period_end=period_end,
            opening_balance=period_transactions[0].balance_after_transaction - period_transactions[0].amount,
            closing_balance=period_transactions[-1].balance_after_transaction,
            total_inflows=total_inflows,
            total_outflows=total_outflows,
            net_cash_flow=total_inflows - total_outflows,
            operating_inflows=operating_inflows,
            operating_outflows=operating_outflows,
            operating_cash_flow=operating_inflows - operating_outflows,
            investment_inflows=0.0,  # Simplified
            investment_outflows=0.0,  # Simplified
            investment_cash_flow=0.0,
            financing_inflows=total_inflows - operating_inflows,
            financing_outflows=total_outflows - operating_outflows,
            financing_cash_flow=(total_inflows - operating_inflows) - (total_outflows - operating_outflows),
            digital_transaction_count=len(digital_transactions),
            digital_transaction_value=sum(t.amount for t in digital_transactions),
            digital_adoption_rate=(len(digital_transactions) / len(period_transactions)) * 100,
            upi_transaction_count=len(upi_transactions),
            upi_transaction_value=sum(t.amount for t in upi_transactions)
        )

    # Storage and retrieval methods
    async def _store_consent_request(self, consent: ConsentRequest):
        """Store consent request in Firestore"""
        try:
            doc_ref = self.db.collection('aa_consents').document(consent.consent_id)
            doc_ref.set(consent.dict())
        except Exception as e:
            logger.error(f"Error storing consent request: {str(e)}")

    async def _store_bank_account(self, account: BankAccount):
        """Store bank account in Firestore"""
        try:
            doc_ref = self.db.collection('aa_accounts').document(account.account_id)
            doc_ref.set(account.dict())
        except Exception as e:
            logger.error(f"Error storing bank account: {str(e)}")

    async def _store_transaction(self, transaction: Transaction):
        """Store transaction in Firestore"""
        try:
            doc_ref = self.db.collection('aa_transactions').document(transaction.transaction_id)
            doc_ref.set(transaction.dict())
        except Exception as e:
            logger.error(f"Error storing transaction: {str(e)}")

    async def _store_cash_flow_statement(self, cash_flow: CashFlowStatement):
        """Store cash flow statement in Firestore"""
        try:
            doc_id = f"{cash_flow.account_id}_{cash_flow.period_start}_{cash_flow.period_end}"
            doc_ref = self.db.collection('aa_cash_flows').document(doc_id)
            doc_ref.set(cash_flow.dict())
        except Exception as e:
            logger.error(f"Error storing cash flow statement: {str(e)}")

    async def _store_banking_analytics(self, analytics: BankingAnalytics):
        """Store banking analytics in Firestore"""
        try:
            doc_ref = self.db.collection('aa_analytics').document(analytics.consent_id)
            doc_ref.set(analytics.dict())
        except Exception as e:
            logger.error(f"Error storing banking analytics: {str(e)}")

    async def _get_stored_accounts(self, consent_id: str) -> List[BankAccount]:
        """Get stored accounts from Firestore"""
        try:
            # This is simplified - in practice, you'd link accounts to consent
            docs = self.db.collection('aa_accounts').limit(10).stream()
            return [BankAccount(**doc.to_dict()) for doc in docs]
        except Exception as e:
            logger.error(f"Error retrieving accounts: {str(e)}")
            return []

    async def _get_stored_transactions(self, account_id: str, from_date: date, to_date: date) -> List[Transaction]:
        """Get stored transactions from Firestore"""
        try:
            docs = self.db.collection('aa_transactions').where('account_id', '==', account_id).stream()
            transactions = [Transaction(**doc.to_dict()) for doc in docs]

            # Filter by date range
            filtered_transactions = [
                t for t in transactions
                if from_date <= t.transaction_date.date() <= to_date
            ]

            return filtered_transactions
        except Exception as e:
            logger.error(f"Error retrieving transactions: {str(e)}")
            return []
