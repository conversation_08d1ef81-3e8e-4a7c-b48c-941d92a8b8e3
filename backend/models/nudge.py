from pydantic import BaseModel, Field
from typing import Optional, Literal
from datetime import datetime
from enum import Enum

class TriggerType(str, Enum):
    SCORE_DROP = "score_drop"
    MANUAL = "manual"
    RISK_ALERT = "risk_alert"

class Medium(str, Enum):
    WHATSAPP = "whatsapp"
    EMAIL = "email"
    SMS = "sms"

class NudgeStatus(str, Enum):
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"

class NudgeRequest(BaseModel):
    trigger_type: Literal["score_drop", "manual", "risk_alert"]
    message: str = Field(..., min_length=1, max_length=1000)
    medium: Literal["whatsapp", "email", "sms"]
    metadata: Optional[dict] = Field(default_factory=dict)

class Nudge(BaseModel):
    nudge_id: str
    msme_id: str
    trigger_type: str
    message: str
    medium: str
    sent_at: datetime
    status: Literal["sent", "delivered", "failed"] = "sent"
    metadata: dict = Field(default_factory=dict)

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# Keep existing models for backward compatibility
class NudgeCreate(BaseModel):
    msme_id: str = Field(..., min_length=1)
    trigger_type: TriggerType
    message: str = Field(..., min_length=1, max_length=1000)
    medium: Medium
    metadata: Optional[dict] = Field(default_factory=dict)

class NudgeUpdate(BaseModel):
    message: Optional[str] = Field(None, min_length=1, max_length=1000)
    status: Optional[NudgeStatus] = None
    metadata: Optional[dict] = None
