'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function DebugPage() {
  const [portfolioData, setPortfolioData] = useState<Record<string, unknown>[]>([]);
  const [analyticsData, setAnalyticsData] = useState<Record<string, unknown> | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch portfolio data
        const portfolioResponse = await fetch('http://localhost:8000/api/dashboard/portfolio/legacy');
        const portfolio = await portfolioResponse.json();

        // Fetch analytics data
        const analyticsResponse = await fetch('http://localhost:8000/api/dashboard/analytics');
        const analytics = await analyticsResponse.json();
        
        setPortfolioData(portfolio);
        setAnalyticsData(analytics);
        
        console.log('Debug - Portfolio data:', portfolio);
        console.log('Debug - Analytics data:', analytics);
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
        console.error('Debug - Error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return <div className="p-8">Loading debug data...</div>;
  }

  if (error) {
    return <div className="p-8 text-red-500">Error: {error}</div>;
  }

  const riskCounts = portfolioData.reduce((acc: Record<string, number>, msme: any) => {
    const riskBand = msme.risk_band as string;
    acc[riskBand] = (acc[riskBand] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="p-8 space-y-6">
      <h1 className="text-3xl font-bold">Debug Information</h1>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Portfolio Data</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p><strong>Total MSMEs:</strong> {portfolioData.length}</p>
              <p><strong>Green Risk:</strong> {riskCounts.green || 0}</p>
              <p><strong>Yellow Risk:</strong> {riskCounts.yellow || 0}</p>
              <p><strong>Red Risk:</strong> {riskCounts.red || 0}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Analytics Data</CardTitle>
          </CardHeader>
          <CardContent>
            {analyticsData && (
              <div className="space-y-2">
                <p><strong>Total MSMEs:</strong> {(analyticsData as any).total_msmes}</p>
                <p><strong>Green:</strong> {(analyticsData as any).risk_distribution?.green}</p>
                <p><strong>Yellow:</strong> {(analyticsData as any).risk_distribution?.yellow}</p>
                <p><strong>Red:</strong> {(analyticsData as any).risk_distribution?.red}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All MSMEs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {portfolioData.map((msme: any, index) => (
              <div key={msme.msme_id} className="flex justify-between items-center p-2 border rounded">
                <span>{index + 1}. {msme.name}</span>
                <div className="flex gap-2">
                  <span className={`px-2 py-1 rounded text-xs ${
                    msme.risk_band === 'green' ? 'bg-green-100 text-green-800' :
                    msme.risk_band === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {msme.risk_band}
                  </span>
                  <span className="text-sm text-gray-600">Score: {msme.current_score}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
