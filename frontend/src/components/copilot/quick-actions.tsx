'use client';

import { memo, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  AlertTriangle,
  TrendingDown,
  FileCheck,
  BarChart3,
  Building2,
  Clock
} from 'lucide-react';
import { QuickAction, QuickActionsProps } from '@/types/copilot';

// Memoized quick actions array with proper typing
const quickActions: QuickAction[] = [
  {
    id: 'ai-risk-analysis',
    title: 'AI Risk Analysis',
    query: 'Analyze my portfolio using AI and show me the highest priority risks that need immediate attention',
    icon: AlertTriangle,
    count: '4',
    priority: 'high' as const,
    category: 'risk' as const,
    aiPowered: true,
    description: 'AI-powered risk assessment with predictive insights'
  },
  {
    id: 'score-deterioration',
    title: 'Score Deterioration',
    query: 'Which MSMEs are showing signs of score deterioration and what are the AI-predicted causes?',
    icon: TrendingDown,
    count: '6',
    priority: 'medium' as const,
    category: 'risk' as const,
    aiPowered: true,
    description: 'Machine learning analysis of score trends'
  },
  {
    id: 'smart-compliance',
    title: 'Smart Compliance',
    query: 'Show me compliance deadlines with AI-powered priority ranking and automated recommendations',
    icon: FileCheck,
    count: '12',
    priority: 'high' as const,
    category: 'compliance' as const,
    aiPowered: true,
    description: 'Intelligent compliance monitoring and alerts'
  },
  {
    id: 'predictive-analytics',
    title: 'Predictive Analytics',
    query: 'Generate AI-powered predictive analytics for portfolio performance, sector trends, and risk forecasting',
    icon: BarChart3,
    count: '',
    priority: 'medium' as const,
    category: 'analytics' as const,
    aiPowered: true,
    description: 'Advanced ML models for portfolio forecasting'
  },
  {
    id: 'behavioral-analysis',
    title: 'AI Behavioral Analysis',
    query: 'Use machine learning to identify MSMEs with unusual behavioral patterns, payment anomalies, and early warning signals',
    icon: Building2,
    count: '3',
    priority: 'medium' as const,
    category: 'analytics' as const,
    aiPowered: true,
    description: 'Advanced behavioral pattern recognition'
  },
  {
    id: 'growth-opportunities',
    title: 'Growth Opportunities',
    query: 'AI-powered identification of MSMEs ready for credit expansion and business growth opportunities',
    icon: Clock,
    count: '8',
    priority: 'low' as const,
    category: 'insights' as const,
    aiPowered: true,
    description: 'ML-driven opportunity identification'
  },
  {
    id: 'portfolio-stress-test',
    title: 'Portfolio Stress Test',
    query: 'Run AI-powered stress test scenarios with Monte Carlo simulations on current portfolio composition',
    icon: AlertTriangle,
    count: '',
    priority: 'low' as const,
    category: 'analytics' as const,
    aiPowered: true,
    description: 'Advanced stress testing with AI scenarios'
  },
  {
    id: 'sector-analysis',
    title: 'Smart Sector Analysis',
    query: 'Generate AI-powered sector-wise risk forecasts, emerging market trends, and comparative performance analysis',
    icon: TrendingDown,
    count: '',
    priority: 'low' as const,
    category: 'analytics' as const,
    aiPowered: true,
    description: 'Intelligent sector trend analysis'
  }
];

// Memoized component for better performance
const QuickActionsComponent = memo(function QuickActions({ onQuerySelect }: QuickActionsProps) {
  // Memoized priority color function
  const getPriorityColor = useCallback((priority: QuickAction['priority']) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-amber-600 bg-amber-50';
      default: return 'text-muted-foreground bg-muted';
    }
  }, []);

  // Memoized icon color function
  const getIconColor = useCallback((priority: QuickAction['priority']) => {
    switch (priority) {
      case 'high': return 'text-red-500';
      case 'medium': return 'text-amber-500';
      default: return 'text-muted-foreground';
    }
  }, []);

  return (
    <div className="h-full">
      <div className="p-4 bg-gradient-to-b from-emerald-50/50 to-transparent">
        <p className="text-sm text-emerald-700 mb-3 font-medium">
          Intelligent queries with machine learning insights
        </p>
        <div className="flex items-center gap-2 mb-4 p-2 rounded-lg bg-emerald-100/50 border border-emerald-200">
          <div className="w-2 h-2 rounded-full bg-emerald-500 animate-pulse"></div>
          <span className="text-xs text-emerald-700 font-semibold">Enhanced with AI</span>
        </div>
      </div>

      <div className="px-4 pb-4 space-y-1">
        {quickActions.map((action) => (
          <Button
            key={action.id}
            variant="ghost"
            className="w-full justify-start h-auto p-3 hover:bg-emerald-50 hover:border-emerald-200 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-1 border border-transparent rounded-lg group"
            onClick={() => onQuerySelect(action.query)}
            aria-label={`Execute ${action.title}: ${action.description}`}
          >
            <div className="flex items-start gap-3 w-full">
              <div className={`p-2 rounded-lg transition-colors duration-200 group-hover:scale-105 ${
                action.priority === 'high' ? 'bg-red-100 group-hover:bg-red-200' :
                action.priority === 'medium' ? 'bg-amber-100 group-hover:bg-amber-200' :
                'bg-emerald-100 group-hover:bg-emerald-200'
              }`}>
                <action.icon className={`h-4 w-4 ${getIconColor(action.priority)}`} />
              </div>
              <div className="flex-1 text-left">
                <div className="flex items-center gap-2 mb-1">
                  <div className="text-sm font-medium text-foreground group-hover:text-emerald-800 transition-colors">
                    {action.title}
                  </div>
                  {action.aiPowered && (
                    <div className="flex items-center gap-1 px-2 py-0.5 rounded-full bg-emerald-100 group-hover:bg-emerald-200 transition-colors">
                      <div className="w-1.5 h-1.5 rounded-full bg-emerald-500 animate-pulse"></div>
                      <span className="text-xs text-emerald-700 font-semibold">AI</span>
                    </div>
                  )}
                  </div>
                  {action.description && (
                    <div className="text-xs text-muted-foreground">
                      {action.description}
                    </div>
                  )}
                </div>
                {action.count && (
                  <div className={`text-xs px-2 py-1 rounded-full font-semibold transition-all duration-200 group-hover:scale-105 ${getPriorityColor(action.priority)}`}>
                    {action.count}
                  </div>
                )}
                <div className="text-xs text-muted-foreground group-hover:text-emerald-600 transition-colors mt-1">
                  {action.description}
                </div>
              </div>
            </Button>
          ))}
      </div>
    </div>
  );
});

// Export the memoized component
export { QuickActionsComponent as QuickActions };
