'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Signal, Nudge } from '@/types';
import { SignalTimeline } from '../signal-timeline';
import { NudgeTimeline } from '../nudge-timeline';
import { 
  Activity, 
  MessageSquare, 
  Filter,
  Calendar,
  TrendingUp,
  AlertTriangle,
  Clock,
  Search
} from 'lucide-react';

interface UnifiedSignalHistoryTabProps {
  msmeId: string;
  signals: Signal[];
  nudges: Nudge[];
  signalsLoading: boolean;
  nudgesLoading: boolean;
  onSendNudge: () => void;
}

interface TimelineEvent {
  id: string;
  type: 'signal' | 'nudge';
  timestamp: string;
  data: Signal | Nudge;
}

export function UnifiedSignalHistoryTab({ 
  msmeId, 
  signals, 
  nudges, 
  signalsLoading, 
  nudgesLoading,
  onSendNudge 
}: UnifiedSignalHistoryTabProps) {
  const [filterType, setFilterType] = useState<'all' | 'signals' | 'nudges'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | 'all'>('30d');

  // Combine and sort signals and nudges by timestamp
  const unifiedTimeline: TimelineEvent[] = React.useMemo(() => {
    const events: TimelineEvent[] = [
      ...signals.map(signal => ({
        id: signal.signal_id,
        type: 'signal' as const,
        timestamp: signal.timestamp,
        data: signal
      })),
      ...nudges.map(nudge => ({
        id: nudge.nudge_id,
        type: 'nudge' as const,
        timestamp: nudge.sent_at,
        data: nudge
      }))
    ];

    return events.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }, [signals, nudges]);

  // Filter events based on current filters
  const filteredEvents = React.useMemo(() => {
    let filtered = unifiedTimeline;

    // Filter by type
    if (filterType !== 'all') {
      const eventType = filterType === 'signals' ? 'signal' : filterType === 'nudges' ? 'nudge' : filterType;
      filtered = filtered.filter(event => event.type === eventType);
    }

    // Filter by date range
    if (dateRange !== 'all') {
      const days = parseInt(dateRange.replace('d', ''));
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      filtered = filtered.filter(event => new Date(event.timestamp) >= cutoffDate);
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(event => {
        if (event.type === 'signal') {
          const signal = event.data as Signal;
          return signal.source.toLowerCase().includes(searchTerm.toLowerCase()) ||
                 JSON.stringify(signal.value).toLowerCase().includes(searchTerm.toLowerCase());
        } else {
          const nudge = event.data as Nudge;
          return nudge.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
                 nudge.trigger_type.toLowerCase().includes(searchTerm.toLowerCase());
        }
      });
    }

    return filtered;
  }, [unifiedTimeline, filterType, dateRange, searchTerm]);

  const getEventCounts = () => {
    return {
      total: unifiedTimeline.length,
      signals: signals.length,
      nudges: nudges.length,
      filtered: filteredEvents.length
    };
  };

  const getImpactMetrics = () => {
    const signalImpacts = signals.map(s => s.normalized);
    const avgImpact = signalImpacts.length > 0 ? 
      signalImpacts.reduce((sum, impact) => sum + impact, 0) / signalImpacts.length : 0;
    
    const nudgeResponseRate = nudges.filter(n => n.status === 'delivered').length / 
      Math.max(nudges.length, 1);

    return {
      avgSignalImpact: avgImpact * 100,
      nudgeResponseRate: nudgeResponseRate * 100,
      recentActivity: unifiedTimeline.filter(e => {
        const eventDate = new Date(e.timestamp);
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return eventDate >= weekAgo;
      }).length
    };
  };

  const counts = getEventCounts();
  const metrics = getImpactMetrics();

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Signal History & Nudges</h3>
          <p className="text-sm text-muted-foreground">
            Unified timeline of all signals and nudge activities
          </p>
        </div>
        <Button
          size="sm"
          onClick={onSendNudge}
          className="flex items-center gap-2"
        >
          <MessageSquare className="h-4 w-4" />
          Send Nudge
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{counts.total}</div>
                <div className="text-xs text-muted-foreground">Total Events</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-500" />
              <div>
                <div className="text-2xl font-bold">{metrics.avgSignalImpact.toFixed(0)}%</div>
                <div className="text-xs text-muted-foreground">Avg Signal Impact</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">{metrics.nudgeResponseRate.toFixed(0)}%</div>
                <div className="text-xs text-muted-foreground">Nudge Response</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-orange-500" />
              <div>
                <div className="text-2xl font-bold">{metrics.recentActivity}</div>
                <div className="text-xs text-muted-foreground">This Week</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Filter:</span>
              <div className="flex gap-1">
                <Button
                  variant={filterType === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterType('all')}
                >
                  All ({counts.total})
                </Button>
                <Button
                  variant={filterType === 'signals' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterType('signals')}
                >
                  Signals ({counts.signals})
                </Button>
                <Button
                  variant={filterType === 'nudges' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilterType('nudges')}
                >
                  Nudges ({counts.nudges})
                </Button>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Period:</span>
              <div className="flex gap-1">
                {['7d', '30d', '90d', 'all'].map((period) => (
                  <Button
                    key={period}
                    variant={dateRange === period ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setDateRange(period as any)}
                  >
                    {period === 'all' ? 'All' : period}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Timeline Content */}
      <div className="space-y-4">
        {filterType === 'all' && (
          <div className="text-sm text-muted-foreground">
            Showing {filteredEvents.length} of {counts.total} events
          </div>
        )}

        {(signalsLoading || nudgesLoading) ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <>
            {filterType === 'all' || filterType === 'signals' ? (
              <SignalTimeline signals={signals} loading={signalsLoading} />
            ) : null}
            
            {filterType === 'all' || filterType === 'nudges' ? (
              <NudgeTimeline nudges={nudges} loading={nudgesLoading} />
            ) : null}
          </>
        )}
      </div>
    </div>
  );
}
