from fastapi import APIRouter, HTTPException, status
from typing import List, Optional
from datetime import datetime, timezone
import uuid

from models.signal import Signal, SignalCreate, SignalUpdate
from firebase.init import get_firestore_client
from services.scoring import normalize_signal

router = APIRouter()

@router.post("/add", response_model=Signal, status_code=status.HTTP_201_CREATED)
async def add_signal(signal_data: SignalCreate):
    """Add a new signal for an MSME"""
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_ref = db.collection('msmes').document(signal_data.msme_id)
        msme_doc = msme_ref.get()
        
        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Generate unique signal ID
        signal_id = str(uuid.uuid4())
        
        # Normalize the signal value
        normalized_value = normalize_signal(signal_data.source, signal_data.value)
        
        # Create signal
        signal = Signal(
            signal_id=signal_id,
            msme_id=signal_data.msme_id,
            source=signal_data.source,
            value=signal_data.value,
            normalized=normalized_value,
            timestamp=datetime.now(timezone.utc),
            metadata=signal_data.metadata
        )
        
        # Save signal to Firestore
        signal_ref = db.collection('msmes').document(signal_data.msme_id).collection('signals').document(signal_id)
        signal_ref.set(signal.dict())
        
        # TODO: Recalculate MSME score
        # await recalculate_msme_score(signal_data.msme_id)
        
        return signal
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to add signal: {str(e)}"
        )

@router.get("/{msme_id}", response_model=List[Signal])
async def get_signals(msme_id: str, source: Optional[str] = None, limit: int = 50):
    """Get signals for an MSME"""
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()
        
        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Build query
        query = db.collection('msmes').document(msme_id).collection('signals')
        
        if source:
            query = query.where('source', '==', source)
        
        query = query.order_by('timestamp', direction='DESCENDING').limit(limit)
        
        # Execute query
        docs = query.stream()
        
        signals = []
        for doc in docs:
            data = doc.to_dict()
            signals.append(Signal(**data))
        
        return signals
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve signals: {str(e)}"
        )

@router.get("/{msme_id}/{signal_id}", response_model=Signal)
async def get_signal(msme_id: str, signal_id: str):
    """Get a specific signal"""
    try:
        db = get_firestore_client()
        
        signal_ref = db.collection('msmes').document(msme_id).collection('signals').document(signal_id)
        signal_doc = signal_ref.get()
        
        if not signal_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Signal not found"
            )
        
        data = signal_doc.to_dict()
        return Signal(**data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve signal: {str(e)}"
        )
