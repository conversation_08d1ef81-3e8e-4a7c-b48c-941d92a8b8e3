import type { Metada<PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { AppLayout } from "@/components/layout/app-layout";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
  preload: true,
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
  preload: false, // Only preload primary font
});

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
};

export const metadata: Metadata = {
  title: "Credit Chakra - MSME Credit Scoring Platform",
  description: "Advanced credit scoring and risk assessment platform for MSMEs",
  keywords: ["MSME", "credit scoring", "risk assessment", "financial analytics", "portfolio management"],
  authors: [{ name: "Credit Chakra Team" }],
  robots: "index, follow",
  openGraph: {
    title: "Credit Chakra - MSME Credit Scoring Platform",
    description: "Advanced credit scoring and risk assessment platform for MSMEs",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning={true}
      >
        <AppLayout>{children}</AppLayout>
      </body>
    </html>
  );
}
