from fastapi import APIRouter, HTTPException, status, BackgroundTasks, Depends
from typing import List, Optional
from datetime import datetime, timezone, date

from models.gst_data import GSTDataRequest, GSTDataResponse
from models.account_aggregator import AADataRequest, AADataResponse, ConsentRequest
from models.udyam_data import UdyamDataRequest, UdyamDataResponse
from models.cash_flow import CashFlowRequest, CashFlowResponse
from models.financial_metrics import FinancialMetricsRequest, FinancialMetricsResponse

from services.gst_service import GSTService
from services.account_aggregator_service import AccountAggregatorService
from services.udyam_service import UdyamService
from services.cash_flow_service import CashFlowService
from services.financial_metrics_service import FinancialMetricsService

router = APIRouter()

# Initialize services
gst_service = GSTService()
aa_service = AccountAggregatorService()
udyam_service = UdyamService()
cash_flow_service = CashFlowService()
financial_metrics_service = FinancialMetricsService()

@router.post("/data-integration/comprehensive-sync", status_code=status.HTTP_202_ACCEPTED)
async def comprehensive_data_sync(msme_id: str, background_tasks: BackgroundTasks):
    """Trigger comprehensive data sync for all sources"""
    try:
        # Schedule background sync for all data sources
        background_tasks.add_task(_comprehensive_sync_background, msme_id)
        
        return {
            "message": "Comprehensive data sync initiated",
            "msme_id": msme_id,
            "status": "processing",
            "estimated_completion": "5-10 minutes"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to initiate comprehensive sync: {str(e)}"
        )

@router.get("/data-integration/{msme_id}/status", response_model=dict)
async def get_data_integration_status(msme_id: str):
    """Get data integration status for all sources"""
    try:
        # Get health checks from all services
        gst_health = await gst_service.get_gst_health_check("mock_gstin")  # Would get actual GSTIN
        aa_health = await aa_service.get_aa_health_check("mock_consent", msme_id)
        udyam_health = await udyam_service.get_udyam_health_check("mock_udyam")  # Would get actual Udyam number
        
        return {
            "msme_id": msme_id,
            "data_sources": {
                "gst": {
                    "status": gst_health.sync_status,
                    "last_sync": gst_health.last_sync,
                    "data_completeness": gst_health.data_completeness,
                    "next_sync": gst_health.next_sync_scheduled
                },
                "account_aggregator": {
                    "status": aa_health.sync_status,
                    "last_sync": aa_health.last_sync,
                    "data_completeness": aa_health.data_completeness,
                    "accounts_synced": aa_health.accounts_synced,
                    "transactions_synced": aa_health.transactions_synced
                },
                "udyam": {
                    "status": udyam_health.sync_status,
                    "last_sync": udyam_health.last_sync,
                    "verification_status": udyam_health.verification_status,
                    "compliance_status": udyam_health.compliance_status
                }
            },
            "overall_status": _calculate_overall_status([gst_health.sync_status, aa_health.sync_status, udyam_health.sync_status]),
            "last_updated": datetime.now(timezone.utc)
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get integration status: {str(e)}"
        )

@router.post("/data-integration/gst/sync", response_model=GSTDataResponse)
async def sync_gst_data(request: GSTDataRequest, background_tasks: BackgroundTasks):
    """Sync GST data for an MSME"""
    try:
        # Validate GSTIN format
        if not _validate_gstin(request.gstin):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid GSTIN format"
            )
        
        # Fetch turnover data
        turnover_data = []
        periods = gst_service._generate_period_list(request.from_period, request.to_period)
        
        for period in periods:
            for return_type in request.return_types:
                data = await gst_service.fetch_gst_turnover_data(
                    request.gstin, period, return_type
                )
                if data:
                    turnover_data.append(data)
        
        # Fetch compliance data
        compliance_records = await gst_service.fetch_gst_compliance_data(
            request.gstin, request.from_period, request.to_period
        )
        
        # Analyze payment patterns
        payment_patterns = await gst_service.analyze_gst_payment_patterns(
            request.gstin, periods
        )
        
        # Calculate analytics if requested
        analytics = None
        if request.include_analytics:
            analytics = await gst_service.calculate_gst_analytics(
                request.gstin, request.gstin
            )
        
        return GSTDataResponse(
            gstin=request.gstin,
            turnover_data=turnover_data,
            compliance_records=compliance_records,
            payment_patterns=payment_patterns,
            analytics=analytics,
            status="success",
            message=f"Successfully synced GST data for {len(periods)} periods"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to sync GST data: {str(e)}"
        )

@router.post("/data-integration/account-aggregator/consent", response_model=dict)
async def create_aa_consent(consent_request: ConsentRequest):
    """Create Account Aggregator consent request"""
    try:
        consent_id = await aa_service.create_consent_request(consent_request)
        
        return {
            "consent_id": consent_id,
            "status": "created",
            "message": "Consent request created successfully",
            "next_steps": "User needs to approve consent through their bank"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create AA consent: {str(e)}"
        )

@router.post("/data-integration/account-aggregator/sync", response_model=AADataResponse)
async def sync_aa_data(request: AADataRequest):
    """Sync Account Aggregator data"""
    try:
        # Check consent status
        consent_status = await aa_service.check_consent_status(request.consent_id)
        if consent_status.value != "ACTIVE":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Consent not active: {consent_status.value}"
            )
        
        # Fetch account data
        accounts = await aa_service.fetch_account_data(request.consent_id, request.customer_id)
        
        # Fetch transaction data for each account
        all_transactions = []
        for account in accounts:
            transactions = await aa_service.fetch_transaction_data(
                request.consent_id, account.account_id, request.from_date, request.to_date
            )
            all_transactions.extend(transactions)
        
        # Generate cash flow statements
        cash_flow_statements = []
        for account in accounts:
            cash_flow = await aa_service.generate_cash_flow_statement(
                account.account_id, request.from_date, request.to_date
            )
            cash_flow_statements.append(cash_flow)
        
        # Calculate analytics if requested
        analytics = None
        if request.include_analytics:
            analytics = await aa_service.calculate_banking_analytics(
                request.customer_id, request.consent_id
            )
        
        return AADataResponse(
            consent_id=request.consent_id,
            customer_id=request.customer_id,
            accounts=accounts,
            transactions=all_transactions,
            cash_flow_statements=cash_flow_statements,
            analytics=analytics,
            status="success",
            message=f"Successfully synced data for {len(accounts)} accounts"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to sync AA data: {str(e)}"
        )

@router.post("/data-integration/udyam/sync", response_model=UdyamDataResponse)
async def sync_udyam_data(request: UdyamDataRequest):
    """Sync Udyam registration data"""
    try:
        # Fetch registration data
        registration = await udyam_service.fetch_udyam_registration(request.udyam_number)
        
        if not registration:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Udyam registration not found"
            )
        
        # Perform verification if requested
        verification = None
        if request.include_verification:
            verification = await udyam_service.verify_udyam_registration(
                request.udyam_number, 
                registration.enterprise_name, 
                registration.pan_number
            )
        
        # Fetch compliance data if requested
        compliance_records = []
        if request.include_compliance:
            current_year = datetime.now().year
            for year in range(current_year - 2, current_year + 1):
                compliance = await udyam_service.fetch_udyam_compliance(request.udyam_number, year)
                if compliance:
                    compliance_records.append(compliance)
        
        # Calculate analytics if requested
        analytics = None
        if request.include_analytics:
            analytics = await udyam_service.calculate_udyam_analytics(
                request.udyam_number, registration.msme_id
            )
        
        return UdyamDataResponse(
            udyam_number=request.udyam_number,
            msme_id=registration.msme_id,
            registration=registration,
            verification=verification,
            compliance=compliance_records,
            analytics=analytics,
            status="success",
            message="Successfully synced Udyam data"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to sync Udyam data: {str(e)}"
        )

@router.post("/data-integration/cash-flow/analyze", response_model=CashFlowResponse)
async def analyze_cash_flow(request: CashFlowRequest):
    """Analyze cash flow for an MSME"""
    try:
        # Generate cash flow statements
        cash_flow_statements = []
        current_date = request.period_start
        
        while current_date <= request.period_end:
            # Generate monthly statements
            month_end = min(
                current_date.replace(day=28) + timedelta(days=4) - timedelta(days=current_date.replace(day=28).day),
                request.period_end
            )
            
            statement = await cash_flow_service.generate_cash_flow_statement(
                request.msme_id, current_date, month_end
            )
            if statement:
                cash_flow_statements.append(statement)
            
            # Move to next month
            current_date = month_end + timedelta(days=1)
        
        # Perform seasonal analysis if requested
        seasonal_analysis = None
        if request.include_seasonal:
            seasonal_analysis = await cash_flow_service.analyze_seasonal_patterns(
                request.msme_id, request.period_start.year
            )
        
        # Generate forecast if requested
        forecast = None
        if request.include_forecast:
            forecast = await cash_flow_service.generate_cash_flow_forecast(
                request.msme_id, request.forecast_horizon
            )
        
        # Analyze working capital if requested
        working_capital_analysis = None
        if request.include_working_capital:
            working_capital_analysis = await cash_flow_service.analyze_working_capital(
                request.msme_id, request.period_end
            )
        
        # Calculate comprehensive analytics
        analytics = await cash_flow_service.calculate_cash_flow_analytics(request.msme_id)
        
        return CashFlowResponse(
            msme_id=request.msme_id,
            cash_flow_statements=cash_flow_statements,
            seasonal_analysis=seasonal_analysis,
            forecast=forecast,
            working_capital_analysis=working_capital_analysis,
            analytics=analytics,
            status="success",
            message=f"Successfully analyzed cash flow for {len(cash_flow_statements)} periods"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to analyze cash flow: {str(e)}"
        )

@router.post("/data-integration/financial-metrics/calculate", response_model=FinancialMetricsResponse)
async def calculate_financial_metrics(request: FinancialMetricsRequest):
    """Calculate comprehensive financial metrics"""
    try:
        # Calculate comprehensive metrics
        comprehensive_metrics = await financial_metrics_service.calculate_comprehensive_metrics(
            request.msme_id, request.calculation_date
        )
        
        if not comprehensive_metrics:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Insufficient financial data to calculate metrics"
            )
        
        return FinancialMetricsResponse(
            msme_id=request.msme_id,
            comprehensive_metrics=comprehensive_metrics,
            status="success",
            message="Successfully calculated financial metrics"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to calculate financial metrics: {str(e)}"
        )

# Helper functions
def _validate_gstin(gstin: str) -> bool:
    """Validate GSTIN format"""
    if not gstin or len(gstin) != 15:
        return False
    
    import re
    pattern = r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}[Z]{1}[0-9A-Z]{1}$'
    return bool(re.match(pattern, gstin))

def _calculate_overall_status(statuses: List[str]) -> str:
    """Calculate overall integration status"""
    if all(status == "success" for status in statuses):
        return "healthy"
    elif any(status == "failed" for status in statuses):
        return "degraded"
    elif any(status == "partial" for status in statuses):
        return "partial"
    else:
        return "unknown"

async def _comprehensive_sync_background(msme_id: str):
    """Background task for comprehensive data sync"""
    try:
        # This would orchestrate syncing from all data sources
        # For now, it's a placeholder for the actual implementation
        pass
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Comprehensive sync failed for {msme_id}: {str(e)}")
