'use client';

import { useEffect, useState, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { MSME, Analytics } from '@/types';
import { api } from '@/lib/api';
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  Activity,
  Calendar,
  Users,
  Building2,
  MapPin,
  Search,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  Target,
  Zap,
  Clock,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Filter,
  Eye
} from 'lucide-react';

interface TrendData {
  period: string;
  value: number;
  change: number;
  changePercent: number;
}

interface MarketTrend {
  category: string;
  trend: 'up' | 'down' | 'stable';
  value: number;
  change: number;
  description: string;
  impact: 'high' | 'medium' | 'low';
}

interface SectorPerformance {
  sector: string;
  avgScore: number;
  growth: number;
  msmeCount: number;
  topPerformers: MSME[];
  riskTrend: 'improving' | 'declining' | 'stable';
}

interface PredictiveInsight {
  id: string;
  title: string;
  description: string;
  confidence: number;
  timeframe: string;
  impact: 'positive' | 'negative' | 'neutral';
  category: 'score' | 'risk' | 'market' | 'sector';
}

export function TrendsAnalysis() {
  const [msmes, setMsmes] = useState<MSME[]>([]);
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [selectedSector, setSelectedSector] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<MSME[]>([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  
  // Trend data states
  const [scoreTrends, setScoreTrends] = useState<TrendData[]>([]);
  const [marketTrends, setMarketTrends] = useState<MarketTrend[]>([]);
  const [sectorPerformance, setSectorPerformance] = useState<SectorPerformance[]>([]);
  const [predictiveInsights, setPredictiveInsights] = useState<PredictiveInsight[]>([]);
  
  const router = useRouter();
  const searchRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchData();
  }, [selectedPeriod]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [portfolioData, analyticsData] = await Promise.all([
        api.getPortfolio(),
        api.getAnalytics()
      ]);
      
      setMsmes(portfolioData);
      setAnalytics(analyticsData);
      
      // Generate trend data
      generateScoreTrends();
      generateMarketTrends();
      generateSectorPerformance(portfolioData);
      generatePredictiveInsights();
      
    } catch (error) {
      console.error('Failed to fetch trends data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Search functionality
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setSearchResults([]);
      setShowSearchResults(false);
      return;
    }

    const filtered = msmes.filter(msme =>
      msme.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      msme.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      msme.business_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      msme.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    );

    setSearchResults(filtered);
    setShowSearchResults(true);
  }, [searchTerm, msmes]);

  // Click outside handler
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSearchResults(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSearchSelect = (msmeId: string) => {
    setSearchTerm('');
    setShowSearchResults(false);
    router.push(`/msme/${msmeId}`);
  };

  const generateScoreTrends = () => {
    const trends: TrendData[] = [];
    const periods = selectedPeriod === '7d' ? 7 : selectedPeriod === '30d' ? 30 : 90;
    
    for (let i = periods - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      const baseScore = 650;
      const variation = Math.sin(i * 0.2) * 20 + Math.random() * 10;
      const value = baseScore + variation;
      const prevValue = baseScore + Math.sin((i + 1) * 0.2) * 20;
      const change = value - prevValue;
      
      trends.push({
        period: date.toISOString().split('T')[0],
        value: Math.round(value),
        change: Math.round(change),
        changePercent: Math.round((change / prevValue) * 100 * 10) / 10
      });
    }
    
    setScoreTrends(trends);
  };

  const generateMarketTrends = () => {
    const trends: MarketTrend[] = [
      {
        category: 'Digital Adoption',
        trend: 'up',
        value: 78,
        change: 12,
        description: 'MSMEs increasingly adopting digital payment solutions',
        impact: 'high'
      },
      {
        category: 'Credit Demand',
        trend: 'up',
        value: 65,
        change: 8,
        description: 'Rising demand for working capital loans',
        impact: 'medium'
      },
      {
        category: 'Risk Appetite',
        trend: 'down',
        value: 42,
        change: -5,
        description: 'Lenders becoming more cautious with risk assessment',
        impact: 'medium'
      },
      {
        category: 'Regulatory Compliance',
        trend: 'up',
        value: 85,
        change: 15,
        description: 'Improved compliance with GST and other regulations',
        impact: 'high'
      },
      {
        category: 'Market Volatility',
        trend: 'stable',
        value: 55,
        change: 2,
        description: 'Stable market conditions with minor fluctuations',
        impact: 'low'
      }
    ];
    
    setMarketTrends(trends);
  };

  const generateSectorPerformance = (msmeData: MSME[]) => {
    const sectors = ['retail', 'manufacturing', 'services', 'b2b'];
    const performance: SectorPerformance[] = [];
    
    sectors.forEach(sector => {
      const sectorMsmes = msmeData.filter(msme => msme.business_type === sector);
      if (sectorMsmes.length > 0) {
        const avgScore = sectorMsmes.reduce((sum, msme) => sum + msme.current_score, 0) / sectorMsmes.length;
        const topPerformers = sectorMsmes
          .sort((a, b) => b.current_score - a.current_score)
          .slice(0, 3);
        
        performance.push({
          sector,
          avgScore: Math.round(avgScore),
          growth: Math.round((Math.random() - 0.5) * 20),
          msmeCount: sectorMsmes.length,
          topPerformers,
          riskTrend: avgScore > 700 ? 'improving' : avgScore < 500 ? 'declining' : 'stable'
        });
      }
    });
    
    setSectorPerformance(performance);
  };

  const generatePredictiveInsights = () => {
    const insights: PredictiveInsight[] = [
      {
        id: '1',
        title: 'Credit Score Improvement Expected',
        description: 'Based on recent signal patterns, 15% of MSMEs are likely to see score improvements in the next 30 days.',
        confidence: 85,
        timeframe: '30 days',
        impact: 'positive',
        category: 'score'
      },
      {
        id: '2',
        title: 'Retail Sector Growth Momentum',
        description: 'Retail MSMEs showing strong digital adoption trends, indicating potential for reduced risk profiles.',
        confidence: 78,
        timeframe: '60 days',
        impact: 'positive',
        category: 'sector'
      },
      {
        id: '3',
        title: 'Seasonal Risk Increase',
        description: 'Historical patterns suggest increased payment delays during upcoming festival season.',
        confidence: 72,
        timeframe: '45 days',
        impact: 'negative',
        category: 'risk'
      },
      {
        id: '4',
        title: 'Market Expansion Opportunity',
        description: 'Manufacturing sector showing consolidation trends, creating opportunities for well-performing MSMEs.',
        confidence: 68,
        timeframe: '90 days',
        impact: 'positive',
        category: 'market'
      }
    ];
    
    setPredictiveInsights(insights);
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
      case 'improving':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down':
      case 'declining':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getInsightIcon = (category: string) => {
    switch (category) {
      case 'score': return <BarChart3 className="h-4 w-4" />;
      case 'risk': return <AlertTriangle className="h-4 w-4" />;
      case 'market': return <Activity className="h-4 w-4" />;
      case 'sector': return <Building2 className="h-4 w-4" />;
      default: return <Target className="h-4 w-4" />;
    }
  };

  const getRiskBadgeVariant = (risk: string) => {
    switch (risk) {
      case 'green': return 'default';
      case 'yellow': return 'secondary';
      case 'red': return 'destructive';
      default: return 'outline';
    }
  };

  const getRiskLabel = (risk: string) => {
    switch (risk) {
      case 'green': return 'Low';
      case 'yellow': return 'Medium';
      case 'red': return 'High';
      default: return 'Unknown';
    }
  };

  const getSectorLabel = (sector: string) => {
    switch (sector) {
      case 'retail': return 'Retail';
      case 'b2b': return 'B2B';
      case 'manufacturing': return 'Manufacturing';
      case 'services': return 'Services';
      default: return sector;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
          <div className="grid gap-6 md:grid-cols-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-2">
          <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
            Market Trends & Analysis
          </h1>
          <p className="text-muted-foreground">
            Comprehensive market insights, sector performance, and predictive analytics
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="sm"
            onClick={fetchData}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Search Bar */}
      <div ref={searchRef} className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search MSMEs by name, location, or business type..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10 shadow-sm"
        />

        {/* Search Results Dropdown */}
        {showSearchResults && searchResults.length > 0 && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
            {searchResults.slice(0, 8).map((msme) => (
              <div
                key={msme.msme_id}
                className="p-3 hover:bg-muted cursor-pointer border-b last:border-b-0"
                onClick={() => handleSearchSelect(msme.msme_id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-sm">{msme.name}</h4>
                      <Badge variant={getRiskBadgeVariant(msme.risk_band)} className="text-xs">
                        {getRiskLabel(msme.risk_band)} Risk
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-xs text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Building2 className="h-3 w-3" />
                        {msme.business_type}
                      </span>
                      <span className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {msme.location}
                      </span>
                      <span className="flex items-center gap-1">
                        <BarChart3 className="h-3 w-3" />
                        Score: {msme.current_score}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {searchResults.length > 8 && (
              <div className="p-2 text-center text-sm text-muted-foreground border-t">
                {searchResults.length - 8} more results...
              </div>
            )}
          </div>
        )}

        {/* No Results Message */}
        {showSearchResults && searchResults.length === 0 && searchTerm.trim() !== '' && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-lg shadow-lg z-50 p-4 text-center text-sm text-muted-foreground">
            No MSMEs found matching "{searchTerm}"
          </div>
        )}
      </div>

      {/* Overview Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700 dark:text-blue-400">
              <div className="p-2 bg-blue-500/10 rounded-lg">
                <TrendingUp className="h-5 w-5" />
              </div>
              Score Trend
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-700 dark:text-blue-400 mb-2">
              {scoreTrends.length > 0 ? scoreTrends[scoreTrends.length - 1].value : 0}
            </div>
            <div className="flex items-center gap-1 text-sm">
              {scoreTrends.length > 0 && scoreTrends[scoreTrends.length - 1].change > 0 ? (
                <>
                  <ArrowUpRight className="h-3 w-3 text-green-500" />
                  <span className="text-green-600">+{scoreTrends[scoreTrends.length - 1].change} points</span>
                </>
              ) : (
                <>
                  <ArrowDownRight className="h-3 w-3 text-red-500" />
                  <span className="text-red-600">{scoreTrends.length > 0 ? scoreTrends[scoreTrends.length - 1].change : 0} points</span>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700 dark:text-green-400">
              <div className="p-2 bg-green-500/10 rounded-lg">
                <Activity className="h-5 w-5" />
              </div>
              Market Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-700 dark:text-green-400 mb-2">
              {Math.round(marketTrends.reduce((sum, trend) => sum + trend.value, 0) / marketTrends.length)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              <CheckCircle className="h-3 w-3 text-green-500" />
              <span className="text-green-600">Stable conditions</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-950/20 dark:to-purple-900/20">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700 dark:text-purple-400">
              <div className="p-2 bg-purple-500/10 rounded-lg">
                <Building2 className="h-5 w-5" />
              </div>
              Top Sector
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 dark:text-purple-400 mb-2">
              {sectorPerformance.length > 0 ?
                getSectorLabel(sectorPerformance.sort((a, b) => b.avgScore - a.avgScore)[0].sector) :
                'N/A'
              }
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Target className="h-3 w-3 text-purple-500" />
              <span className="text-purple-600">Best performing</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-950/20 dark:to-orange-900/20">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-700 dark:text-orange-400">
              <div className="p-2 bg-orange-500/10 rounded-lg">
                <Zap className="h-5 w-5" />
              </div>
              Insights
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-orange-700 dark:text-orange-400 mb-2">
              {predictiveInsights.length}
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Clock className="h-3 w-3 text-orange-500" />
              <span className="text-orange-600">Active predictions</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="market" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="market">Market Trends</TabsTrigger>
          <TabsTrigger value="sectors">Sector Analysis</TabsTrigger>
          <TabsTrigger value="predictions">Predictions</TabsTrigger>
          <TabsTrigger value="timeline">Timeline View</TabsTrigger>
        </TabsList>

        <TabsContent value="market" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Market Trends */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Market Indicators
                </CardTitle>
                <CardDescription>
                  Key market trends affecting MSME credit landscape
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {marketTrends.map((trend, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        {getTrendIcon(trend.trend)}
                        <div>
                          <p className="font-medium text-sm">{trend.category}</p>
                          <p className="text-xs text-muted-foreground">{trend.description}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center gap-2">
                          <span className="font-semibold">{trend.value}%</span>
                          <Badge variant={trend.impact === 'high' ? 'destructive' : trend.impact === 'medium' ? 'secondary' : 'default'} className="text-xs">
                            {trend.impact}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          {trend.change > 0 ? '+' : ''}{trend.change}% change
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Score Trend Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Portfolio Score Trends
                </CardTitle>
                <CardDescription>
                  Average credit score movement over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between text-sm">
                    <span>Current Average</span>
                    <span className="font-semibold">
                      {scoreTrends.length > 0 ? scoreTrends[scoreTrends.length - 1].value : 0}
                    </span>
                  </div>
                  <div className="h-48 flex items-end justify-between gap-1">
                    {scoreTrends.slice(-10).map((trend, index) => (
                      <div key={index} className="flex-1 flex flex-col items-center">
                        <div
                          className="w-full bg-blue-500 rounded-t transition-all hover:bg-blue-600"
                          style={{
                            height: `${((trend.value - 500) / 300) * 100}%`,
                            minHeight: '8px'
                          }}
                          title={`${trend.value} (${trend.change > 0 ? '+' : ''}${trend.change})`}
                        />
                        <span className="text-xs text-muted-foreground mt-1 rotate-45 origin-left">
                          {new Date(trend.period).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                        </span>
                      </div>
                    ))}
                  </div>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>Range: 500-800</span>
                    <span>Period: {selectedPeriod}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="sectors" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {sectorPerformance.map((sector, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      {getSectorLabel(sector.sector)}
                    </div>
                    <div className="flex items-center gap-2">
                      {getTrendIcon(sector.riskTrend)}
                      <Badge variant={sector.growth > 0 ? 'default' : 'secondary'} className="text-xs">
                        {sector.growth > 0 ? '+' : ''}{sector.growth}%
                      </Badge>
                    </div>
                  </CardTitle>
                  <CardDescription>
                    {sector.msmeCount} MSMEs • Avg Score: {sector.avgScore}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-center">
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">{sector.avgScore}</div>
                        <div className="text-sm text-blue-600">Avg Score</div>
                      </div>
                      <div className="p-3 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">{sector.msmeCount}</div>
                        <div className="text-sm text-green-600">Total MSMEs</div>
                      </div>
                    </div>

                    <Separator />

                    <div>
                      <h4 className="font-medium text-sm mb-2">Top Performers</h4>
                      <div className="space-y-2">
                        {sector.topPerformers.map((msme, idx) => (
                          <div key={idx} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                            <div>
                              <p className="text-sm font-medium">{msme.name}</p>
                              <p className="text-xs text-muted-foreground">{msme.location}</p>
                            </div>
                            <div className="text-right">
                              <div className="text-sm font-semibold">{msme.current_score}</div>
                              <Badge variant={getRiskBadgeVariant(msme.risk_band)} className="text-xs">
                                {getRiskLabel(msme.risk_band)}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="predictions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Predictive Insights
              </CardTitle>
              <CardDescription>
                AI-powered predictions based on historical data and market trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                {predictiveInsights.map((insight) => (
                  <div
                    key={insight.id}
                    className={`p-4 border rounded-lg ${
                      insight.impact === 'positive' ? 'border-green-200 bg-green-50' :
                      insight.impact === 'negative' ? 'border-red-200 bg-red-50' :
                      'border-gray-200 bg-gray-50'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-2">
                        {getInsightIcon(insight.category)}
                        <h4 className="font-semibold text-sm">{insight.title}</h4>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {insight.confidence}% confidence
                      </Badge>
                    </div>

                    <p className="text-sm text-muted-foreground mb-3">
                      {insight.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2 text-xs">
                        <Clock className="h-3 w-3" />
                        <span>{insight.timeframe}</span>
                      </div>
                      <Badge
                        variant={insight.impact === 'positive' ? 'default' : insight.impact === 'negative' ? 'destructive' : 'secondary'}
                        className="text-xs"
                      >
                        {insight.impact} impact
                      </Badge>
                    </div>

                    <div className="mt-3">
                      <Progress value={insight.confidence} className="h-2" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Timeline Analysis
              </CardTitle>
              <CardDescription>
                Historical view of key events and trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-4">
                  {scoreTrends.slice(-15).reverse().map((trend, index) => (
                    <div key={index} className="flex items-center gap-4 p-3 border-l-2 border-blue-200 bg-blue-50/50 rounded-r-lg">
                      <div className="flex-shrink-0">
                        <div className="w-3 h-3 bg-blue-500 rounded-full -ml-2"></div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <p className="font-medium text-sm">
                            Portfolio Score: {trend.value}
                          </p>
                          <span className="text-xs text-muted-foreground">
                            {new Date(trend.period).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          {trend.change > 0 ? (
                            <ArrowUpRight className="h-3 w-3 text-green-500" />
                          ) : trend.change < 0 ? (
                            <ArrowDownRight className="h-3 w-3 text-red-500" />
                          ) : (
                            <Minus className="h-3 w-3 text-gray-500" />
                          )}
                          <span className={`text-xs ${
                            trend.change > 0 ? 'text-green-600' :
                            trend.change < 0 ? 'text-red-600' :
                            'text-gray-600'
                          }`}>
                            {trend.change > 0 ? '+' : ''}{trend.change} points ({trend.changePercent > 0 ? '+' : ''}{trend.changePercent}%)
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
