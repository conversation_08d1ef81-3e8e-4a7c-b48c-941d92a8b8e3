'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Database,
  TrendingUp,
  Activity,
  X,
  Clock
} from 'lucide-react';

interface RefreshJob {
  job_id: string;
  data_source: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  progress: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  retry_count: number;
}

interface RefreshStatusProps {
  msmeId: string;
  dataSource?: string;
  showControls?: boolean;
  compact?: boolean;
}

export default function RefreshStatus({ 
  msmeId, 
  dataSource, 
  showControls = true, 
  compact = false 
}: RefreshStatusProps) {
  const [refreshJobs, setRefreshJobs] = useState<RefreshJob[]>([]);
  const [systemHealth, setSystemHealth] = useState<Record<string, unknown> | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchRefreshStatus = useCallback(async () => {
    try {
      // Mock data - in production, this would call the actual API
      const mockJobs: RefreshJob[] = [
        {
          job_id: 'gst_001_20241201_143022',
          data_source: 'GST',
          status: 'COMPLETED',
          progress: 100,
          created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          started_at: new Date(Date.now() - 2 * 60 * 60 * 1000 + 30000).toISOString(),
          completed_at: new Date(Date.now() - 2 * 60 * 60 * 1000 + 180000).toISOString(),
          retry_count: 0
        },
        {
          job_id: 'banking_001_20241201_143522',
          data_source: 'ACCOUNT_AGGREGATOR',
          status: 'IN_PROGRESS',
          progress: 65,
          created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          started_at: new Date(Date.now() - 25 * 60 * 1000).toISOString(),
          retry_count: 0
        },
        {
          job_id: 'udyam_001_20241201_144022',
          data_source: 'UDYAM',
          status: 'PENDING',
          progress: 0,
          created_at: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
          retry_count: 0
        }
      ];

      const mockSystemHealth = {
        system_status: 'HEALTHY',
        last_24_hours: {
          total_jobs: 45,
          completed_jobs: 42,
          failed_jobs: 2,
          success_rate: 93.33
        },
        queue_status: {
          total_queued: 3,
          active_jobs: 1,
          by_priority: {
            CRITICAL: 0,
            HIGH: 1,
            MEDIUM: 2,
            LOW: 0
          }
        }
      };

      // Filter by data source if specified
      const filteredJobs = dataSource 
        ? mockJobs.filter(job => job.data_source === dataSource)
        : mockJobs;

      setRefreshJobs(filteredJobs);
      setSystemHealth(mockSystemHealth);
    } catch (error) {
      console.error('Error fetching refresh status:', error);
    } finally {
      setLoading(false);
    }
  }, [msmeId, dataSource]);

  useEffect(() => {
    fetchRefreshStatus();
    const interval = setInterval(fetchRefreshStatus, 5000); // Poll every 5 seconds
    return () => clearInterval(interval);
  }, [fetchRefreshStatus]);

  const scheduleRefresh = async (sources: string[] = []) => {
    try {
      setRefreshing(true);
      // Mock API call - in production, this would call the actual API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Add new pending job
      const newJob: RefreshJob = {
        job_id: `${sources[0] || 'all'}_${msmeId}_${Date.now()}`,
        data_source: sources[0] || 'ALL',
        status: 'PENDING',
        progress: 0,
        created_at: new Date().toISOString(),
        retry_count: 0
      };
      
      setRefreshJobs(prev => [newJob, ...prev]);
      
      // Simulate job progression
      setTimeout(() => {
        setRefreshJobs(prev => prev.map(job => 
          job.job_id === newJob.job_id 
            ? { ...job, status: 'IN_PROGRESS' as const, started_at: new Date().toISOString() }
            : job
        ));
      }, 2000);
      
    } catch (error) {
      console.error('Error scheduling refresh:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const cancelJob = async (jobId: string) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setRefreshJobs(prev => prev.map(job => 
        job.job_id === jobId 
          ? { ...job, status: 'CANCELLED' as const, completed_at: new Date().toISOString() }
          : job
      ));
    } catch (error) {
      console.error('Error cancelling job:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'IN_PROGRESS':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'FAILED':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'CANCELLED':
        return <X className="h-4 w-4 text-gray-500" />;
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
      case 'IN_PROGRESS':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">In Progress</Badge>;
      case 'FAILED':
        return <Badge variant="destructive">Failed</Badge>;
      case 'CANCELLED':
        return <Badge variant="outline">Cancelled</Badge>;
      case 'PENDING':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatDuration = (startTime?: string, endTime?: string) => {
    if (!startTime) return '-';
    
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const diffInSeconds = Math.floor((end.getTime() - start.getTime()) / 1000);
    
    if (diffInSeconds < 60) return `${diffInSeconds}s`;
    const minutes = Math.floor(diffInSeconds / 60);
    const seconds = diffInSeconds % 60;
    return `${minutes}m ${seconds}s`;
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const hours = Math.floor(diffInMinutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
        <div className="h-32 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (compact) {
    const activeJobs = refreshJobs.filter(job => job.status === 'IN_PROGRESS');
    const lastCompleted = refreshJobs.find(job => job.status === 'COMPLETED');
    
    return (
      <Card className="border-0 shadow-sm">
        <CardContent className="pt-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Data Refresh</span>
            </div>
            <div className="flex items-center gap-2">
              {activeJobs.length > 0 ? (
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  {activeJobs.length} Active
                </Badge>
              ) : (
                <Badge variant="default" className="bg-green-100 text-green-800">
                  Up to Date
                </Badge>
              )}
              {showControls && (
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => scheduleRefresh(dataSource ? [dataSource] : [])}
                  disabled={refreshing}
                >
                  <RefreshCw className={`h-3 w-3 ${refreshing ? 'animate-spin' : ''}`} />
                </Button>
              )}
            </div>
          </div>
          {lastCompleted && (
            <p className="text-xs text-muted-foreground mt-2">
              Last updated: {formatTimeAgo(lastCompleted.completed_at!)}
            </p>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      {showControls && (
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">Data Refresh Status</h3>
            <p className="text-muted-foreground">Monitor and control data refresh operations</p>
          </div>
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              onClick={() => scheduleRefresh(dataSource ? [dataSource] : [])}
              disabled={refreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              {dataSource ? `Refresh ${dataSource}` : 'Refresh All'}
            </Button>
          </div>
        </div>
      )}

      {/* System Health Summary */}
      {systemHealth && !dataSource && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card className="border-0 shadow-sm">
            <CardContent className="pt-4">
              <div className="flex items-center gap-2 mb-2">
                <Activity className="h-4 w-4 text-blue-500" />
                <span className="text-sm font-medium">System Status</span>
              </div>
              <div className="text-lg font-bold">
                {String(systemHealth.system_status || 'Unknown')}
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-0 shadow-sm">
            <CardContent className="pt-4">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">Success Rate</span>
              </div>
              <div className="text-lg font-bold">
                {((systemHealth.last_24_hours as any)?.success_rate?.toFixed(1) || '0.0')}%
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-0 shadow-sm">
            <CardContent className="pt-4">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="h-4 w-4 text-yellow-500" />
                <span className="text-sm font-medium">Queue</span>
              </div>
              <div className="text-lg font-bold">
                {(systemHealth.queue_status as any)?.total_queued || 0}
              </div>
            </CardContent>
          </Card>
          
          <Card className="border-0 shadow-sm">
            <CardContent className="pt-4">
              <div className="flex items-center gap-2 mb-2">
                <RefreshCw className="h-4 w-4 text-purple-500" />
                <span className="text-sm font-medium">Active Jobs</span>
              </div>
              <div className="text-lg font-bold">
                {(systemHealth.queue_status as any)?.active_jobs || 0}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Refresh Jobs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5" />
            Recent Refresh Jobs
          </CardTitle>
          <CardDescription>
            {dataSource ? `${dataSource} data refresh history` : 'All data source refresh history'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {refreshJobs.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No refresh jobs found
            </div>
          ) : (
            <div className="space-y-4">
              {refreshJobs.map((job) => (
                <div key={job.job_id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(job.status)}
                    <div>
                      <div className="font-medium">{job.data_source}</div>
                      <div className="text-sm text-muted-foreground">
                        Started {formatTimeAgo(job.created_at)}
                        {job.started_at && ` • Duration: ${formatDuration(job.started_at, job.completed_at)}`}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    {job.status === 'IN_PROGRESS' && (
                      <div className="flex items-center gap-2">
                        <Progress value={job.progress} className="w-20 h-2" />
                        <span className="text-sm font-medium">{job.progress}%</span>
                      </div>
                    )}
                    
                    {getStatusBadge(job.status)}
                    
                    {job.status === 'IN_PROGRESS' && showControls && (
                      <Button 
                        size="sm" 
                        variant="outline" 
                        onClick={() => cancelJob(job.job_id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
