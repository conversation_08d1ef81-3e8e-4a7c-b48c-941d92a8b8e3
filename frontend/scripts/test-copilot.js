#!/usr/bin/env node

/**
 * Comprehensive test script for AI Copilot functionality
 * Validates performance, compatibility, and functionality requirements
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🤖 AI Copilot Test Suite');
console.log('========================\n');

// Test configuration
const testConfig = {
  performance: {
    maxLoadTime: 2000, // 2 seconds
    maxMemoryUsage: 100 * 1024 * 1024, // 100MB
    maxRenderTime: 1000, // 1 second
  },
  compatibility: {
    nodeVersion: '18.0.0',
    reactVersion: '18.0.0',
  },
  coverage: {
    threshold: 80, // 80% coverage
  },
};

// Helper functions
function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    const output = execSync(command, { 
      encoding: 'utf8', 
      stdio: 'pipe',
      cwd: process.cwd()
    });
    console.log(`✅ ${description} - PASSED\n`);
    return { success: true, output };
  } catch (error) {
    console.log(`❌ ${description} - FAILED`);
    console.log(`Error: ${error.message}\n`);
    return { success: false, error: error.message };
  }
}

function checkFileExists(filePath, description) {
  console.log(`📁 Checking ${description}...`);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${description} - EXISTS\n`);
    return true;
  } else {
    console.log(`❌ ${description} - MISSING\n`);
    return false;
  }
}

function validatePackageJson() {
  console.log('📦 Validating package.json dependencies...');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const requiredDeps = [
      'react',
      'react-dom',
      'typescript',
      'lucide-react',
      '@radix-ui/react-scroll-area',
    ];
    
    const missingDeps = requiredDeps.filter(dep => 
      !packageJson.dependencies?.[dep] && !packageJson.devDependencies?.[dep]
    );
    
    if (missingDeps.length === 0) {
      console.log('✅ All required dependencies found\n');
      return true;
    } else {
      console.log(`❌ Missing dependencies: ${missingDeps.join(', ')}\n`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Error reading package.json: ${error.message}\n`);
    return false;
  }
}

function validateTypeScript() {
  console.log('🔍 Validating TypeScript configuration...');
  
  const tsConfigExists = checkFileExists('tsconfig.json', 'TypeScript config');
  if (!tsConfigExists) return false;
  
  try {
    const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
    const requiredOptions = ['strict', 'noImplicitAny', 'noImplicitReturns'];
    
    const compilerOptions = tsConfig.compilerOptions || {};
    const missingOptions = requiredOptions.filter(option => !compilerOptions[option]);
    
    if (missingOptions.length === 0) {
      console.log('✅ TypeScript configuration is valid\n');
      return true;
    } else {
      console.log(`❌ Missing TypeScript options: ${missingOptions.join(', ')}\n`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Error reading tsconfig.json: ${error.message}\n`);
    return false;
  }
}

function validateComponentStructure() {
  console.log('🏗️  Validating component structure...');
  
  const requiredFiles = [
    'src/components/copilot/copilot-page.tsx',
    'src/components/copilot/enhanced-message.tsx',
    'src/components/copilot/message-list.tsx',
    'src/components/copilot/quick-actions.tsx',
    'src/components/copilot/insights-feed.tsx',
    'src/components/copilot/query-suggestions.tsx',
    'src/types/copilot.ts',
    'src/lib/performance.ts',
  ];
  
  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
  
  if (missingFiles.length === 0) {
    console.log('✅ All required component files exist\n');
    return true;
  } else {
    console.log(`❌ Missing files: ${missingFiles.join(', ')}\n`);
    return false;
  }
}

function validateTestFiles() {
  console.log('🧪 Validating test files...');
  
  const testFiles = [
    'src/components/copilot/__tests__/copilot-page.test.tsx',
    'src/components/copilot/__tests__/enhanced-message.test.tsx',
    'src/components/copilot/__tests__/performance.test.tsx',
  ];
  
  const missingTests = testFiles.filter(file => !fs.existsSync(file));
  
  if (missingTests.length === 0) {
    console.log('✅ All test files exist\n');
    return true;
  } else {
    console.log(`❌ Missing test files: ${missingTests.join(', ')}\n`);
    return false;
  }
}

function runLinting() {
  return runCommand(
    'npm run lint -- src/components/copilot/',
    'Running ESLint on Copilot components'
  );
}

function runTypeChecking() {
  return runCommand(
    'npx tsc --noEmit --project tsconfig.json',
    'Running TypeScript type checking'
  );
}

function runUnitTests() {
  return runCommand(
    'npm test -- src/components/copilot/__tests__/ --coverage --watchAll=false',
    'Running unit tests with coverage'
  );
}

function runPerformanceTests() {
  return runCommand(
    'npm test -- src/components/copilot/__tests__/performance.test.tsx --watchAll=false',
    'Running performance tests'
  );
}

function validateShadCNCompatibility() {
  console.log('🎨 Validating ShadCN UI compatibility...');
  
  try {
    // Check if ShadCN components are properly imported
    const copilotPageContent = fs.readFileSync('src/components/copilot/copilot-page.tsx', 'utf8');
    const requiredImports = [
      '@/components/ui/button',
      '@/components/ui/input',
    ];
    
    const missingImports = requiredImports.filter(imp => !copilotPageContent.includes(imp));
    
    if (missingImports.length === 0) {
      console.log('✅ ShadCN UI components properly imported\n');
      return true;
    } else {
      console.log(`❌ Missing ShadCN imports: ${missingImports.join(', ')}\n`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Error validating ShadCN compatibility: ${error.message}\n`);
    return false;
  }
}

function validateEmeraldTheme() {
  console.log('🎨 Validating emerald color palette consistency...');
  
  try {
    const copilotFiles = [
      'src/components/copilot/copilot-page.tsx',
      'src/components/copilot/quick-actions.tsx',
      'src/components/copilot/enhanced-message.tsx',
    ];
    
    let emeraldUsageCount = 0;
    
    copilotFiles.forEach(file => {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        const emeraldMatches = content.match(/emerald-\d+/g) || [];
        emeraldUsageCount += emeraldMatches.length;
      }
    });
    
    if (emeraldUsageCount >= 10) {
      console.log(`✅ Emerald theme consistently applied (${emeraldUsageCount} usages)\n`);
      return true;
    } else {
      console.log(`❌ Insufficient emerald theme usage (${emeraldUsageCount} usages)\n`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Error validating emerald theme: ${error.message}\n`);
    return false;
  }
}

function generateTestReport(results) {
  console.log('\n📊 TEST REPORT');
  console.log('==============\n');
  
  const totalTests = Object.keys(results).length;
  const passedTests = Object.values(results).filter(result => result).length;
  const failedTests = totalTests - passedTests;
  
  console.log(`Total Tests: ${totalTests}`);
  console.log(`Passed: ${passedTests}`);
  console.log(`Failed: ${failedTests}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%\n`);
  
  // Detailed results
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}`);
  });
  
  console.log('\n');
  
  if (failedTests === 0) {
    console.log('🎉 ALL TESTS PASSED! AI Copilot is ready for production.');
  } else {
    console.log(`⚠️  ${failedTests} test(s) failed. Please review and fix the issues.`);
  }
  
  return failedTests === 0;
}

// Main test execution
async function runAllTests() {
  console.log('Starting comprehensive AI Copilot validation...\n');
  
  const results = {};
  
  // File structure validation
  results['Package Dependencies'] = validatePackageJson();
  results['TypeScript Configuration'] = validateTypeScript();
  results['Component Structure'] = validateComponentStructure();
  results['Test Files'] = validateTestFiles();
  
  // Code quality checks
  const lintResult = runLinting();
  results['ESLint'] = lintResult.success;
  
  const typeCheckResult = runTypeChecking();
  results['TypeScript Compilation'] = typeCheckResult.success;
  
  // UI/UX validation
  results['ShadCN Compatibility'] = validateShadCNCompatibility();
  results['Emerald Theme Consistency'] = validateEmeraldTheme();
  
  // Test execution
  const unitTestResult = runUnitTests();
  results['Unit Tests'] = unitTestResult.success;
  
  const performanceTestResult = runPerformanceTests();
  results['Performance Tests'] = performanceTestResult.success;
  
  // Generate final report
  const allTestsPassed = generateTestReport(results);
  
  // Exit with appropriate code
  process.exit(allTestsPassed ? 0 : 1);
}

// Run the test suite
runAllTests().catch(error => {
  console.error('❌ Test suite failed with error:', error);
  process.exit(1);
});
