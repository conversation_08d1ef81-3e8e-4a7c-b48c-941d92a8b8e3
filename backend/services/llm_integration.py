"""
LLM Integration Service for Credit Chakra AI Copilot
Provides a structured interface for future LLM integration while maintaining current mock functionality
"""

import os
import json
import logging
import asyncio
import hashlib
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class LLMResponse:
    """Structured LLM response"""
    content: str
    confidence: float
    reasoning: Optional[str] = None
    suggested_actions: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None

class LLMIntegrationService:
    """
    Enterprise-grade LLM Integration Service with multi-provider support
    Enhanced with advanced context awareness and domain expertise
    """

    def __init__(self):
        self.use_llm = self._check_llm_availability()
        self.model_name = os.getenv('LLM_MODEL', 'gpt-4')
        self.api_key = os.getenv('LLM_API_KEY')
        self.provider = os.getenv('LLM_PROVIDER', 'openai')  # openai, anthropic, local
        self.max_retries = 3
        self.timeout = 30
        self.cache = {}  # Enhanced response cache with TTL
        self.rate_limiter = {}  # Rate limiting per provider
        self.conversation_memory = {}  # Enhanced conversation context
        self.domain_knowledge = self._load_domain_knowledge()
        self.user_preferences = {}  # User-specific preferences and patterns

        if self.use_llm:
            logger.info(f"Enhanced LLM integration enabled with {self.provider} model: {self.model_name}")
        else:
            logger.info("Using enhanced mock responses with domain expertise (LLM not configured)")

    def _check_llm_availability(self) -> bool:
        """Check if LLM integration is available and configured"""
        # Check for API keys and required packages
        api_key = os.getenv('LLM_API_KEY')
        if not api_key:
            return False

        # Try to import LLM libraries based on provider
        try:
            provider = os.getenv('LLM_PROVIDER', 'openai')
            if provider == 'openai':
                import openai
                return True
            elif provider == 'anthropic':
                import anthropic
                return True
            # Add support for other providers
        except ImportError:
            logger.warning(f"LLM provider {provider} not available")
            return False

        return False  # Set to True when LLM is properly configured

    def _load_domain_knowledge(self) -> Dict[str, Any]:
        """Load credit domain knowledge and terminology"""
        return {
            "credit_terms": {
                "npa": "Non-Performing Asset - loans overdue by 90+ days",
                "sma": "Special Mention Account - early warning categories",
                "crilc": "Central Repository of Information on Large Credits",
                "raroc": "Risk-Adjusted Return on Capital",
                "pd": "Probability of Default",
                "lgd": "Loss Given Default",
                "ead": "Exposure at Default"
            },
            "risk_bands": {
                "high": "Score < 40, requires immediate attention",
                "medium": "Score 40-70, monitor closely",
                "low": "Score > 70, stable portfolio"
            },
            "regulatory_frameworks": [
                "RBI Guidelines", "Basel III", "CRILC Reporting", "NPA Classification"
            ],
            "business_sectors": [
                "Manufacturing", "Retail", "Services", "Agriculture", "Technology"
            ]
        }

    def _enhance_context_with_domain_knowledge(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance context with relevant domain knowledge"""
        enhanced_context = context.copy()

        # Add domain knowledge based on query content
        query_lower = query.lower()

        # Add relevant credit terms
        relevant_terms = {}
        for term, definition in self.domain_knowledge["credit_terms"].items():
            if term in query_lower:
                relevant_terms[term] = definition

        if relevant_terms:
            enhanced_context["relevant_credit_terms"] = relevant_terms

        # Add risk band context if score mentioned
        if any(word in query_lower for word in ["score", "risk", "rating"]):
            enhanced_context["risk_bands"] = self.domain_knowledge["risk_bands"]

        # Add regulatory context if compliance mentioned
        if any(word in query_lower for word in ["compliance", "regulatory", "rbi"]):
            enhanced_context["regulatory_frameworks"] = self.domain_knowledge["regulatory_frameworks"]

        return enhanced_context

    def _update_conversation_memory(self, user_id: str, query: str, context: Dict[str, Any]):
        """Update conversation memory for better context awareness"""
        if user_id not in self.conversation_memory:
            self.conversation_memory[user_id] = {
                "recent_queries": [],
                "topics_discussed": set(),
                "user_preferences": {}
            }

        # Store recent queries (keep last 5)
        self.conversation_memory[user_id]["recent_queries"].append({
            "query": query,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "context_keys": list(context.keys())
        })

        if len(self.conversation_memory[user_id]["recent_queries"]) > 5:
            self.conversation_memory[user_id]["recent_queries"].pop(0)

        # Extract topics from query
        query_lower = query.lower()
        for topic in ["risk", "compliance", "score", "portfolio", "analytics"]:
            if topic in query_lower:
                self.conversation_memory[user_id]["topics_discussed"].add(topic)

    async def generate_response(
        self,
        query: str,
        context: Dict[str, Any],
        system_prompt: Optional[str] = None
    ) -> LLMResponse:
        """
        Generate enhanced response with domain expertise and context awareness
        """
        # Enhance context with domain knowledge
        enhanced_context = self._enhance_context_with_domain_knowledge(query, context)

        # Update conversation memory
        user_id = context.get('user_id', 'default')
        self._update_conversation_memory(user_id, query, enhanced_context)

        if self.use_llm:
            return await self._generate_llm_response(query, enhanced_context, system_prompt)
        else:
            return await self._generate_enhanced_mock_response(query, enhanced_context)
    
    async def _generate_llm_response(
        self,
        query: str,
        context: Dict[str, Any],
        system_prompt: Optional[str] = None
    ) -> LLMResponse:
        """
        Generate response using actual LLM with enterprise-grade features
        """
        try:
            # Check cache first
            cache_key = self._generate_cache_key(query, context)
            if cache_key in self.cache:
                logger.info("Returning cached LLM response")
                return self.cache[cache_key]

            # Rate limiting check
            if not self._check_rate_limit():
                logger.warning("Rate limit exceeded, using fallback")
                return await self._generate_enhanced_mock_response(query, context)

            # Multi-provider LLM integration
            if self.provider == 'openai':
                response = await self._call_openai(query, context, system_prompt)
            elif self.provider == 'anthropic':
                response = await self._call_anthropic(query, context, system_prompt)
            else:
                response = await self._call_local_model(query, context, system_prompt)

            # Cache successful responses
            self.cache[cache_key] = response
            return response

        except Exception as e:
            logger.error(f"LLM generation failed: {str(e)}")
            return await self._generate_enhanced_mock_response(query, context)

    async def _call_openai(self, query: str, context: Dict[str, Any], system_prompt: Optional[str]) -> LLMResponse:
        """Call OpenAI API with retry logic"""
        import openai

        client = openai.AsyncOpenAI(api_key=self.api_key, timeout=self.timeout)

        messages = [
            {"role": "system", "content": system_prompt or self._get_default_system_prompt()},
            {"role": "user", "content": self._format_query_with_context(query, context)}
        ]

        for attempt in range(self.max_retries):
            try:
                response = await client.chat.completions.create(
                    model=self.model_name,
                    messages=messages,
                    temperature=0.7,
                    max_tokens=1500,
                    top_p=0.9
                )

                return LLMResponse(
                    content=response.choices[0].message.content,
                    confidence=0.9,
                    reasoning="Generated by OpenAI",
                    metadata={
                        "model": self.model_name,
                        "tokens": response.usage.total_tokens,
                        "provider": "openai",
                        "attempt": attempt + 1
                    }
                )
            except Exception as e:
                if attempt == self.max_retries - 1:
                    raise e
                await asyncio.sleep(2 ** attempt)  # Exponential backoff

    async def _call_anthropic(self, query: str, context: Dict[str, Any], system_prompt: Optional[str]) -> LLMResponse:
        """Call Anthropic API with retry logic"""
        import anthropic

        client = anthropic.AsyncAnthropic(api_key=self.api_key, timeout=self.timeout)

        for attempt in range(self.max_retries):
            try:
                response = await client.messages.create(
                    model=self.model_name,
                    max_tokens=1500,
                    temperature=0.7,
                    system=system_prompt or self._get_default_system_prompt(),
                    messages=[{"role": "user", "content": self._format_query_with_context(query, context)}]
                )

                return LLMResponse(
                    content=response.content[0].text,
                    confidence=0.9,
                    reasoning="Generated by Anthropic",
                    metadata={
                        "model": self.model_name,
                        "tokens": response.usage.input_tokens + response.usage.output_tokens,
                        "provider": "anthropic",
                        "attempt": attempt + 1
                    }
                )
            except Exception as e:
                if attempt == self.max_retries - 1:
                    raise e
                await asyncio.sleep(2 ** attempt)

    async def _call_local_model(self, query: str, context: Dict[str, Any], system_prompt: Optional[str]) -> LLMResponse:
        """Call local model (placeholder for future implementation)"""
        # Fallback to enhanced mock for now
        return await self._generate_enhanced_mock_response(query, context)
    
    def _generate_cache_key(self, query: str, context: Dict[str, Any]) -> str:
        """Generate cache key for response caching"""
        import hashlib
        content = f"{query}_{str(context.get('user_intent', ''))}"
        return hashlib.md5(content.encode()).hexdigest()

    def _check_rate_limit(self) -> bool:
        """Check if we're within rate limits"""
        current_time = datetime.now(timezone.utc)
        provider_key = f"{self.provider}_last_call"

        if provider_key in self.rate_limiter:
            time_diff = (current_time - self.rate_limiter[provider_key]).total_seconds()
            if time_diff < 1:  # 1 second between calls
                return False

        self.rate_limiter[provider_key] = current_time
        return True

    def _get_default_system_prompt(self) -> str:
        """Get enhanced system prompt for Credit Chakra AI Copilot"""
        return """You are an expert AI assistant for Credit Chakra, a sophisticated MSME credit risk assessment platform. You specialize in:

1. MSME Credit Risk Analysis: Analyze portfolio health, risk distribution, and individual MSME creditworthiness
2. RBI Compliance: Monitor regulatory requirements, deadlines, and compliance status
3. Financial Analytics: Interpret GST data, banking patterns, UPI transactions, and business performance
4. Predictive Insights: Identify trends, early warning signals, and portfolio optimization opportunities
5. Indian Market Context: Understand MSME ecosystem, regulatory landscape, and market dynamics

Provide clear, actionable insights with specific recommendations. Use data-driven analysis and maintain professional tone. Always consider RBI guidelines and Indian financial regulations in your responses."""

    def _format_query_with_context(self, query: str, context: Dict[str, Any]) -> str:
        """Format query with relevant context for better LLM understanding"""
        formatted_query = f"Query: {query}\n\n"

        if context.get('conversation_history'):
            formatted_query += "Recent conversation context:\n"
            for msg in context['conversation_history'][-3:]:  # Last 3 messages
                formatted_query += f"- {msg.get('type', 'user')}: {msg.get('content', '')[:100]}...\n"
            formatted_query += "\n"

        if context.get('portfolio_summary'):
            formatted_query += f"Portfolio Summary: {context['portfolio_summary']}\n\n"

        if context.get('user_intent'):
            formatted_query += f"User Intent: {context['user_intent']}\n\n"

        formatted_query += "Please provide a comprehensive, actionable response based on the Credit Chakra platform context."
        return formatted_query

    async def _generate_enhanced_mock_response(
        self,
        query: str,
        context: Dict[str, Any]
    ) -> LLMResponse:
        """
        Generate enhanced mock response with intelligent pattern matching
        """
        query_lower = query.lower()

        # Enhanced pattern matching with multiple intent detection
        risk_keywords = ['risk', 'alert', 'danger', 'default', 'declining', 'deteriorating', 'warning']
        compliance_keywords = ['compliance', 'deadline', 'regulatory', 'filing', 'rbi', 'crilc', 'udyam']
        score_keywords = ['score', 'rating', 'credit', 'trend', 'improvement', 'decline', 'analysis']
        portfolio_keywords = ['portfolio', 'overview', 'summary', 'dashboard', 'total', 'accounts']
        sector_keywords = ['sector', 'industry', 'manufacturing', 'services', 'trading', 'technology']
        prediction_keywords = ['predict', 'forecast', 'future', 'likely', 'probability', 'trend']

        # Calculate intent scores
        risk_score = sum(1 for word in risk_keywords if word in query_lower)
        compliance_score = sum(1 for word in compliance_keywords if word in query_lower)
        score_score = sum(1 for word in score_keywords if word in query_lower)
        portfolio_score = sum(1 for word in portfolio_keywords if word in query_lower)
        sector_score = sum(1 for word in sector_keywords if word in query_lower)
        prediction_score = sum(1 for word in prediction_keywords if word in query_lower)

        # Determine primary intent
        intent_scores = {
            'risk': risk_score,
            'compliance': compliance_score,
            'score': score_score,
            'portfolio': portfolio_score,
            'sector': sector_score,
            'prediction': prediction_score
        }

        primary_intent = max(intent_scores, key=intent_scores.get)
        max_score = intent_scores[primary_intent]

        # Generate response based on primary intent
        if primary_intent == 'risk' and max_score > 0:
            content = self._generate_risk_analysis_response(context)
            confidence = 0.85 + (max_score * 0.05)
            suggested_actions = [
                "Review high-risk accounts immediately",
                "Schedule field verification visits",
                "Adjust credit limits and exposure",
                "Implement enhanced monitoring"
            ]
        elif primary_intent == 'compliance' and max_score > 0:
            content = self._generate_compliance_response(context)
            confidence = 0.90 + (max_score * 0.03)
            suggested_actions = [
                "Check upcoming regulatory deadlines",
                "Review filing status and documentation",
                "Update compliance tracking systems",
                "Schedule regulatory review meetings"
            ]
        elif primary_intent == 'score' and max_score > 0:
            content = self._generate_score_analysis_response(context)
            confidence = 0.80 + (max_score * 0.04)
            suggested_actions = [
                "Analyze detailed score trends",
                "Identify improvement opportunities",
                "Review scoring parameter weights",
                "Generate score improvement plans"
            ]
        elif primary_intent == 'sector' and max_score > 0:
            content = self._generate_sector_analysis_response(context)
            confidence = 0.82 + (max_score * 0.04)
            suggested_actions = [
                "Deep-dive into sector performance",
                "Compare with industry benchmarks",
                "Identify sector-specific risks",
                "Optimize sector allocation"
            ]
        elif primary_intent == 'prediction' and max_score > 0:
            content = self._generate_predictive_analysis_response(context)
            confidence = 0.88 + (max_score * 0.03)
            suggested_actions = [
                "Review predictive model outputs",
                "Validate forecast assumptions",
                "Plan proactive interventions",
                "Monitor prediction accuracy"
            ]
        else:
            content = self._generate_general_response(query, context)
            confidence = 0.70
            suggested_actions = [
                "Explore portfolio overview",
                "Check priority alerts",
                "Review compliance status",
                "Analyze performance trends"
            ]

        return LLMResponse(
            content=content,
            confidence=min(confidence, 0.95),  # Cap confidence at 95%
            reasoning=f"Enhanced AI pattern matching - Primary intent: {primary_intent} (confidence: {max_score})",
            suggested_actions=suggested_actions,
            metadata={
                "response_type": "enhanced_ai_mock",
                "primary_intent": primary_intent,
                "intent_confidence": max_score,
                "query_length": len(query),
                "context_keys": list(context.keys()) if context else [],
                "processing_time_ms": 150  # Simulated processing time
            }
        )
    
    def _generate_risk_analysis_response(self, context: Dict[str, Any]) -> str:
        """Generate risk-focused response"""
        return """**Risk Analysis Summary**

Based on current portfolio data and AI analysis:

**Critical Risk Indicators:**
• 4 MSMEs showing deteriorating financial health (GST compliance drops, declining UPI transactions)
• 2 accounts with payment delays exceeding 30 days
• 1 MSME with significant score drop (-45 points in 30 days)

**Portfolio Risk Distribution:**
• High Risk: 20% (₹2.4 Cr exposure) - Immediate attention required
• Medium Risk: 45% (₹5.8 Cr exposure) - Enhanced monitoring
• Low Risk: 35% (₹4.2 Cr exposure) - Standard monitoring

**AI-Powered Insights:**
• Manufacturing sector stress detected (3 accounts)
• Geographic concentration risk in Mumbai region
• Digital payment adoption correlates with improved scores

**Priority Actions:**
1. **Immediate**: Review Rajesh Textiles (Score: 420, Exposure: ₹85L)
2. **This Week**: Field verification for 2 declining accounts
3. **Strategic**: Diversify sector exposure, reduce manufacturing concentration

**Predictive Alerts:**
• 2 additional accounts likely to move to high-risk in next 30 days
• Seasonal stress expected in Q4 for textile MSMEs

Would you like detailed analysis for specific high-risk accounts or sector-wise breakdown?"""
    
    def _generate_compliance_response(self, context: Dict[str, Any]) -> str:
        """Generate compliance-focused response"""
        return """**Compliance Status & Regulatory Overview**

**Critical Deadlines (Next 30 Days):**
• **CRILC Filing**: 7 days remaining - 12 accounts (₹8.2 Cr exposure)
• **Annual Returns**: 15 days remaining - 8 accounts requiring GST reconciliation
• **RBI MSME Reporting**: 21 days remaining - Portfolio-wide submission
• **Udyam Registration Renewal**: 25 days - 3 MSMEs expiring

**Compliance Health Score: 92/100**
• **Compliant**: 17 accounts (85%)
• **Minor Issues**: 2 accounts (documentation pending)
• **Critical**: 1 account (overdue CRILC filing)

**Regulatory Risk Assessment:**
• **Low Risk**: Current compliance trajectory sustainable
• **Emerging Risk**: New RBI guidelines on MSME classification (effective Q1 2025)
• **Sector Risk**: Manufacturing MSMEs facing enhanced ESG reporting requirements

**AI-Recommended Actions:**
1. **Immediate**: Complete overdue CRILC filing for Sharma Industries
2. **This Week**: Batch process 12 pending CRILC submissions
3. **Strategic**: Implement automated compliance tracking for new RBI guidelines

**Automation Opportunities:**
• GST data auto-sync can reduce manual reconciliation by 70%
• Udyam registration monitoring can prevent last-minute renewals
• RBI reporting templates can be pre-populated from existing data

**Compliance Cost Optimization:**
• Current manual effort: ~40 hours/month
• Potential automation savings: ~28 hours/month
• ROI on compliance automation: 3.2x within 6 months

Need assistance with specific filings or want to explore automation options?"""
    
    def _generate_score_analysis_response(self, context: Dict[str, Any]) -> str:
        """Generate score analysis response"""
        return """**AI-Powered Credit Score Analysis**

**Portfolio Score Intelligence:**
• **Average Score**: 672/1000 (+15 points vs. last month)
• **Score Velocity**: **** points/week (positive trend)
• **Volatility Index**: 12.4 (stable portfolio)
• **Declining Accounts**: 6 MSMEs (-8.2% of portfolio)

**Risk Band Distribution & Exposure:**
• **750+ (Low Risk)**: 25% | ₹3.1 Cr exposure | Avg. LTV: 65%
• **650-749 (Medium Risk)**: 40% | ₹5.2 Cr exposure | Avg. LTV: 58%
• **550-649 (High Risk)**: 25% | ₹3.8 Cr exposure | Avg. LTV: 45%
• **<550 (Critical Risk)**: 10% | ₹1.2 Cr exposure | Avg. LTV: 30%

**AI-Driven Score Insights:**
• **Top Performers**: Digital-first MSMEs with consistent UPI transactions
• **Improvement Drivers**: GST compliance (+25 pts), Banking health (+18 pts)
• **Risk Factors**: Seasonal cash flow (-15 pts), Delayed payments (-22 pts)

**Sector Performance Matrix:**
• **Manufacturing**: 645 avg. (+8 pts) - Recovery phase
• **Services**: 698 avg. (+22 pts) - Strong growth
• **Trading**: 661 avg. (+12 pts) - Stable performance
• **Technology**: 742 avg. (+18 pts) - Outperforming

**Predictive Score Modeling:**
• **Next 30 Days**: 3 accounts likely to improve (+25-40 pts)
• **Risk Alerts**: 2 accounts may decline (-20-35 pts)
• **Portfolio Forecast**: +8 points average improvement expected

**Score Optimization Opportunities:**
1. **Digital Payment Adoption**: 4 MSMEs can gain +30 pts
2. **GST Compliance**: 2 MSMEs can gain +25 pts
3. **Banking Relationship**: 3 MSMEs can gain +20 pts

**Machine Learning Insights:**
• Payment pattern analysis shows 85% accuracy in predicting score changes
• Seasonal adjustments improve prediction accuracy by 12%
• Multi-parameter scoring reduces false positives by 23%

Want detailed score breakdown for specific MSMEs or sector deep-dive analysis?"""
    
    def _generate_general_response(self, query: str, context: Dict[str, Any]) -> str:
        """Generate general response"""
        return f"""**AI Analysis for: "{query}"**

**Portfolio Intelligence Dashboard:**
• **Total MSMEs**: 20 active accounts (₹12.4 Cr total exposure)
• **Portfolio Health**: 78/100 (Above industry average of 72)
• **AI Monitoring**: Real-time with predictive alerts
• **Data Freshness**: Last updated 2 minutes ago

**Available AI-Powered Analysis:**
• **Risk Assessment**: ML-driven early warning system
• **Compliance Intelligence**: Automated regulatory tracking
• **Score Analytics**: Multi-parameter trend analysis with forecasting
• **Sector Insights**: Comparative performance with market benchmarks
• **Behavioral Analytics**: Payment pattern recognition and prediction

**Intelligent Quick Actions:**
• **Smart Alerts**: View AI-prioritized high-risk accounts
• **Compliance Radar**: Check upcoming deadlines with auto-reminders
• **Trend Analysis**: Analyze score trajectories with ML predictions
• **Portfolio Optimization**: Generate AI-recommended actions
• **Automated Reports**: Export insights with executive summaries

**AI Capabilities at Your Service:**
• **Natural Language Queries**: Ask complex questions in plain English
• **Predictive Modeling**: Forecast portfolio performance and risks
• **Automated Insights**: Discover hidden patterns in your data
• **Regulatory Intelligence**: Stay ahead of compliance requirements
• **Decision Support**: Get data-driven recommendations for credit decisions

**Recent AI Discoveries:**
• Identified 3 MSMEs with improving fundamentals (potential limit increases)
• Detected seasonal pattern affecting 40% of manufacturing accounts
• Found correlation between digital adoption and score improvement (+23% accuracy)

**How can I help you unlock deeper insights from your portfolio data?**

*Try asking: "Which MSMEs are most likely to default?" or "Show me compliance risks for next quarter"*"""

    def _generate_sector_analysis_response(self, context: Dict[str, Any]) -> str:
        """Generate sector-focused analysis response"""
        return """**AI-Powered Sector Performance Analysis**

**Sector Distribution & Performance:**
• **Manufacturing**: 8 MSMEs (40%) | Avg Score: 645 | Exposure: ₹4.8 Cr
• **Services**: 6 MSMEs (30%) | Avg Score: 698 | Exposure: ₹3.6 Cr
• **Trading**: 4 MSMEs (20%) | Avg Score: 661 | Exposure: ₹2.4 Cr
• **Technology**: 2 MSMEs (10%) | Avg Score: 742 | Exposure: ₹1.6 Cr

**Sector Risk Intelligence:**
• **High Risk Concentration**: Manufacturing (3 high-risk accounts)
• **Emerging Opportunities**: Technology sector showing 18% growth
• **Seasonal Patterns**: Trading sector peaks in Q3-Q4
• **Regulatory Impact**: Manufacturing facing new ESG compliance requirements

**Comparative Benchmarking:**
• **Manufacturing**: -12 pts vs. industry average (cyclical downturn)
• **Services**: +25 pts vs. industry average (digital transformation)
• **Trading**: +8 pts vs. industry average (stable performance)
• **Technology**: +45 pts vs. industry average (sector leadership)

**AI-Detected Sector Trends:**
• **Manufacturing Recovery**: Early indicators suggest Q2 2025 upturn
• **Services Expansion**: 3 MSMEs planning geographic expansion
• **Trading Digitization**: 2 MSMEs adopting e-commerce platforms
• **Technology Innovation**: 1 MSME developing AI-powered solutions

**Risk Mitigation Strategies:**
1. **Diversification**: Reduce manufacturing exposure from 40% to 30%
2. **Sector Rotation**: Increase technology allocation by 5%
3. **Seasonal Hedging**: Implement counter-cyclical lending for trading
4. **ESG Preparation**: Support manufacturing MSMEs with compliance

**Predictive Sector Outlook (Next 6 Months):**
• **Manufacturing**: Gradual recovery expected (+15-20 pts average)
• **Services**: Continued strong performance (+10-15 pts)
• **Trading**: Stable with seasonal volatility (±5 pts)
• **Technology**: Sustained growth trajectory (+20-25 pts)

Want detailed analysis for a specific sector or cross-sector risk assessment?"""

    def _generate_predictive_analysis_response(self, context: Dict[str, Any]) -> str:
        """Generate predictive analysis response"""
        return """**AI Predictive Analytics & Forecasting**

**Portfolio Forecast (Next 90 Days):**
• **Overall Score Trend**: +12 points expected (confidence: 87%)
• **Risk Migration**: 2 accounts likely to improve, 1 may deteriorate
• **Default Probability**: 0.8% portfolio-wide (below 1.2% industry average)
• **Exposure at Risk**: ₹95L (7.7% of portfolio) - manageable level

**Machine Learning Predictions:**
• **High Confidence (>90%)**: 3 MSMEs will improve scores significantly
• **Medium Confidence (70-90%)**: 2 MSMEs may face payment delays
• **Low Confidence (<70%)**: 1 MSME showing mixed signals

**Predictive Risk Alerts:**
• **Rajesh Textiles**: 78% probability of score decline (-25 pts) in 30 days
• **Mumbai Electronics**: 85% probability of improvement (+30 pts) in 45 days
• **Sharma Trading**: 65% probability of seasonal stress in Q4

**Behavioral Pattern Analysis:**
• **Payment Patterns**: 92% accuracy in predicting next payment date
• **Cash Flow Cycles**: Identified optimal lending windows for 15 MSMEs
• **Digital Adoption**: Strong correlation with score improvement (+0.85)

**Seasonal Forecasting:**
• **Q4 2024**: Expected 8% increase in manufacturing stress
• **Q1 2025**: Predicted recovery in services sector (+15% performance)
• **Q2 2025**: Anticipated technology sector expansion (+25% growth)

**Early Warning System:**
• **30-Day Alerts**: 2 MSMEs showing early stress indicators
• **60-Day Outlook**: 1 MSME may require intervention
• **90-Day Forecast**: Portfolio health expected to improve by 5%

**AI Model Performance:**
• **Prediction Accuracy**: 89% for 30-day forecasts
• **False Positive Rate**: 12% (industry standard: 18%)
• **Model Confidence**: High for 85% of predictions

**Recommended Proactive Actions:**
1. **Immediate**: Contact Rajesh Textiles for early intervention
2. **This Month**: Prepare credit limit increase for Mumbai Electronics
3. **Next Quarter**: Implement seasonal support for trading MSMEs
4. **Strategic**: Enhance data collection for improved predictions

**Predictive Insights Dashboard:**
• Real-time risk scoring with 24-hour refresh cycles
• Automated alerts for significant probability changes
• Trend analysis with 6-month rolling forecasts
• Scenario modeling for stress testing

Want specific predictions for individual MSMEs or scenario analysis for different market conditions?"""
    
    def _get_default_system_prompt(self) -> str:
        """Get default system prompt for LLM"""
        return """You are an advanced AI assistant for Credit Chakra, India's leading MSME credit scoring and monitoring platform.

Your role is to provide world-class credit intelligence to help credit managers make data-driven decisions for their MSME portfolio.

**Core Capabilities:**
- **AI-Powered Risk Assessment**: Machine learning-driven early warning systems with predictive analytics
- **Regulatory Intelligence**: Automated compliance tracking with RBI, CRILC, and Udyam requirements
- **Advanced Credit Scoring**: Multi-parameter analysis with real-time score forecasting
- **Sector Intelligence**: Comparative performance analysis with industry benchmarking
- **Predictive Modeling**: 90-day forecasts with confidence intervals and scenario analysis
- **Portfolio Optimization**: Data-driven recommendations for exposure management

**Response Standards:**
- Provide specific, quantified insights with confidence levels
- Include actionable recommendations with priority levels
- Reference relevant RBI guidelines and compliance requirements
- Use Indian financial terminology and market context
- Highlight both opportunities and risks with equal emphasis
- Support decisions with data-driven reasoning

**Data Context:**
- Portfolio exposure amounts in Indian Rupees (₹)
- Credit scores on 1000-point scale with risk bands
- Compliance with Indian regulatory framework
- Sector-wise performance tracking
- Geographic risk distribution across Indian markets

Always maintain professional tone while delivering insights that enable proactive portfolio management and regulatory compliance."""

# Global instance
llm_service = LLMIntegrationService()
