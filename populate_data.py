#!/usr/bin/env python3
"""
Script to populate Credit Chakra with sample MSME data and signals
"""
import requests
import json
import time

BASE_URL = "http://localhost:8000"

# Sample MSME data
msmes = [
    {
        "name": "Tech Solutions Pvt Ltd",
        "business_type": "services",
        "location": "Bangalore, Karnataka",
        "tags": ["technology", "software", "startup"]
    },
    {
        "name": "Mumbai Textiles Co",
        "business_type": "manufacturing",
        "location": "Mumbai, Maharashtra",
        "tags": ["textiles", "export", "established"]
    },
    {
        "name": "Green Grocers Delhi",
        "business_type": "retail",
        "location": "Delhi, NCR",
        "tags": ["grocery", "organic", "local"]
    },
    {
        "name": "AutoParts Supply Chain",
        "business_type": "b2b",
        "location": "Chennai, Tamil Nadu",
        "tags": ["automotive", "supply-chain", "b2b"]
    },
    {
        "name": "Pune Food Processing",
        "business_type": "manufacturing",
        "location": "Pune, Maharashtra",
        "tags": ["food-processing", "fmcg", "regional"]
    }
]

def create_msme(msme_data):
    """Create an MSME and return the ID"""
    response = requests.post(f"{BASE_URL}/msme/", json=msme_data)
    if response.status_code == 201:
        data = response.json()
        print(f"Created MSME: {data['name']} (ID: {data['msme_id']})")
        return data['msme_id']
    else:
        print(f"Failed to create MSME: {response.text}")
        return None

def add_signal(msme_id, source, value, metadata):
    """Add a signal to an MSME"""
    signal_data = {
        "msme_id": msme_id,
        "source": source,
        "value": value,
        "metadata": metadata
    }
    response = requests.post(f"{BASE_URL}/api/signals/add", json=signal_data)
    if response.status_code == 201:
        data = response.json()
        print(f"Added {source} signal: normalized={data['normalized']:.3f}")
        return True
    else:
        print(f"Failed to add signal: {response.text}")
        return False

def main():
    print("🚀 Populating Credit Chakra with sample data...")
    
    # Create MSMEs and store their IDs
    msme_ids = []
    for msme in msmes:
        msme_id = create_msme(msme)
        if msme_id:
            msme_ids.append(msme_id)
        time.sleep(0.5)  # Small delay
    
    print(f"\n📊 Created {len(msme_ids)} MSMEs")
    
    if len(msme_ids) >= 5:
        # Add signals to Tech Solutions (high performer)
        print(f"\n💼 Adding signals to Tech Solutions...")
        add_signal(msme_ids[0], "gst", 2500000, {"period": "Q4-2024", "currency": "INR"})
        add_signal(msme_ids[0], "upi", {"transaction_count": 450, "volume": 1800000}, {"period": "Q4-2024"})
        add_signal(msme_ids[0], "reviews", {"average_rating": 4.3, "review_count": 87}, {"platform": "Google Reviews"})
        
        # Add signals to Mumbai Textiles (excellent performer)
        print(f"\n🏭 Adding signals to Mumbai Textiles...")
        add_signal(msme_ids[1], "gst", 8500000, {"period": "Q4-2024", "currency": "INR"})
        add_signal(msme_ids[1], "reviews", {"average_rating": 4.7, "review_count": 156}, {"platform": "Google Reviews"})
        add_signal(msme_ids[1], "upi", {"transaction_count": 320, "volume": 2200000}, {"period": "Q4-2024"})
        
        # Add signals to Green Grocers (low performer)
        print(f"\n🛒 Adding signals to Green Grocers...")
        add_signal(msme_ids[2], "gst", 650000, {"period": "Q4-2024", "currency": "INR"})
        add_signal(msme_ids[2], "reviews", {"average_rating": 3.8, "review_count": 23}, {"platform": "Google Reviews"})
        
        # Add signals to AutoParts (medium performer)
        print(f"\n🔧 Adding signals to AutoParts...")
        add_signal(msme_ids[3], "gst", 4200000, {"period": "Q4-2024", "currency": "INR"})
        add_signal(msme_ids[3], "upi", {"transaction_count": 280, "volume": 950000}, {"period": "Q4-2024"})
        
        # Add signals to Pune Food Processing (medium performer)
        print(f"\n🍽️ Adding signals to Pune Food Processing...")
        add_signal(msme_ids[4], "gst", 3200000, {"period": "Q4-2024", "currency": "INR"})
        add_signal(msme_ids[4], "justdial", {"listing_verified": True, "rating": 4.1, "reviews": 45}, {"platform": "JustDial"})
    
    print(f"\n✅ Data population complete!")
    print(f"🌐 Visit http://localhost:3001 to see the dashboard")
    print(f"📊 Check portfolio: curl {BASE_URL}/dashboard/portfolio")

if __name__ == "__main__":
    main()
