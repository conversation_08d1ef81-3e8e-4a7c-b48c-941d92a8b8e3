'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  TrendingUp,
  TrendingDown,
  AlertCircle,
  CheckCircle,
  RefreshCw,
  DollarSign,
  Calendar,
  BarChart3,
  Target,
  Zap,
  Shield,
  Activity,
  Wallet,

  Calculator,
  Award
} from 'lucide-react';

interface FinancialMetrics {
  overall_financial_score: number;
  financial_grade: string;
  overall_risk_level: string;
  industry_percentile: number;
  liquidity_metrics: {
    current_ratio: number;
    quick_ratio: number;
    cash_ratio: number;
    working_capital: number;
    overall_liquidity_score: number;
    liquidity_risk_level: string;
  };
  profitability_metrics: {
    gross_profit_margin: number;
    net_profit_margin: number;
    operating_profit_margin: number;
    return_on_assets: number;
    return_on_equity: number;
    overall_profitability_score: number;
    profitability_trend: string;
  };
  leverage_metrics: {
    debt_to_equity_ratio: number;
    debt_to_assets_ratio: number;
    debt_service_coverage_ratio: number;
    interest_coverage_ratio: number;
    overall_leverage_score: number;
    leverage_risk_level: string;
  };
  efficiency_metrics: {
    asset_turnover: number;
    inventory_turnover: number;
    receivables_turnover: number;
    days_sales_outstanding: number;
    cash_conversion_cycle: number;
    overall_efficiency_score: number;
    efficiency_trend: string;
  };
  improvement_areas: string[];
  action_items: string[];
  data_completeness: number;
  calculation_confidence: number;
  last_updated: string;
}

interface FinancialMetricsTabProps {
  msmeId: string;
}

export default function FinancialMetricsTab({ msmeId }: FinancialMetricsTabProps) {
  const [metrics, setMetrics] = useState<FinancialMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchFinancialMetrics();
  }, [msmeId]);

  const fetchFinancialMetrics = async () => {
    try {
      setLoading(true);
      // Mock data - in production, this would call the actual API
      const mockData: FinancialMetrics = {
        overall_financial_score: 78.5,
        financial_grade: "B+",
        overall_risk_level: "MEDIUM",
        industry_percentile: 72.3,
        liquidity_metrics: {
          current_ratio: 2.1,
          quick_ratio: 1.4,
          cash_ratio: 0.3,
          working_capital: 850000,
          overall_liquidity_score: 82.4,
          liquidity_risk_level: "LOW"
        },
        profitability_metrics: {
          gross_profit_margin: 28.5,
          net_profit_margin: 12.3,
          operating_profit_margin: 18.7,
          return_on_assets: 9.2,
          return_on_equity: 16.8,
          overall_profitability_score: 79.1,
          profitability_trend: "improving"
        },
        leverage_metrics: {
          debt_to_equity_ratio: 0.65,
          debt_to_assets_ratio: 0.39,
          debt_service_coverage_ratio: 1.8,
          interest_coverage_ratio: 4.2,
          overall_leverage_score: 74.6,
          leverage_risk_level: "MEDIUM"
        },
        efficiency_metrics: {
          asset_turnover: 1.3,
          inventory_turnover: 5.2,
          receivables_turnover: 8.1,
          days_sales_outstanding: 45,
          cash_conversion_cycle: 68,
          overall_efficiency_score: 71.8,
          efficiency_trend: "stable"
        },
        improvement_areas: ["Debt Management", "Operational Efficiency"],
        action_items: [
          "Reduce debt-to-equity ratio to below 0.5",
          "Improve inventory turnover through better management",
          "Optimize collection processes to reduce DSO"
        ],
        data_completeness: 92.5,
        calculation_confidence: 88.7,
        last_updated: new Date().toISOString()
      };

      setMetrics(mockData);
    } catch (error) {
      console.error('Error fetching financial metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchFinancialMetrics();
    setRefreshing(false);
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <BarChart3 className="h-4 w-4 text-blue-500" />;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };



  const getRiskBadge = (risk: string) => {
    switch (risk) {
      case 'LOW': return <Badge variant="default" className="bg-green-100 text-green-800">Low Risk</Badge>;
      case 'MEDIUM': return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Medium Risk</Badge>;
      case 'HIGH': return <Badge variant="destructive">High Risk</Badge>;
      case 'CRITICAL': return <Badge variant="destructive" className="bg-red-800">Critical Risk</Badge>;
      default: return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getGradeBadge = (grade: string) => {
    const gradeColors = {
      'A+': 'bg-green-100 text-green-800',
      'A': 'bg-green-100 text-green-800',
      'A-': 'bg-green-100 text-green-800',
      'B+': 'bg-blue-100 text-blue-800',
      'B': 'bg-blue-100 text-blue-800',
      'B-': 'bg-blue-100 text-blue-800',
      'C+': 'bg-yellow-100 text-yellow-800',
      'C': 'bg-yellow-100 text-yellow-800',
      'C-': 'bg-yellow-100 text-yellow-800',
      'D': 'bg-orange-100 text-orange-800',
      'F': 'bg-red-100 text-red-800'
    };

    return (
      <Badge variant="outline" className={gradeColors[grade as keyof typeof gradeColors] || 'bg-gray-100 text-gray-800'}>
        Grade {grade}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Financial Metrics Available</h3>
        <p className="text-gray-500 mb-4">Financial data is not available for this MSME.</p>
        <Button onClick={fetchFinancialMetrics}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Refresh */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Financial Metrics Dashboard</h2>
          <p className="text-muted-foreground">Comprehensive financial health analysis</p>
        </div>
        <Button
          variant="outline"
          onClick={handleRefresh}
          disabled={refreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <Award className="h-5 w-5" />
              Overall Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 mb-2">
              {metrics.overall_financial_score.toFixed(1)}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getGradeBadge(metrics.financial_grade)}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700">
              <Target className="h-5 w-5" />
              Industry Rank
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 mb-2">
              {metrics.industry_percentile.toFixed(0)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-green-600">
                {metrics.industry_percentile >= 75 ? 'Top Quartile' :
                 metrics.industry_percentile >= 50 ? 'Above Average' :
                 metrics.industry_percentile >= 25 ? 'Below Average' : 'Bottom Quartile'}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <Shield className="h-5 w-5" />
              Risk Level
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 mb-2">
              {metrics.overall_risk_level}
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getRiskBadge(metrics.overall_risk_level)}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <Calculator className="h-5 w-5" />
              Data Quality
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 mb-2">
              {metrics.data_completeness.toFixed(0)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Progress value={metrics.data_completeness} className="h-2 flex-1" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics Tabs */}
      <Tabs defaultValue="liquidity" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="liquidity">Liquidity</TabsTrigger>
          <TabsTrigger value="profitability">Profitability</TabsTrigger>
          <TabsTrigger value="leverage">Leverage</TabsTrigger>
          <TabsTrigger value="efficiency">Efficiency</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="liquidity" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wallet className="h-5 w-5" />
                Liquidity Metrics
              </CardTitle>
              <CardDescription>Short-term financial health and cash position</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Key Ratios</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Current Ratio</span>
                      <span className="font-semibold">{metrics.liquidity_metrics.current_ratio.toFixed(2)}</span>
                    </div>
                    <Progress value={Math.min(100, (metrics.liquidity_metrics.current_ratio / 3) * 100)} className="h-2" />

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Quick Ratio</span>
                      <span className="font-semibold">{metrics.liquidity_metrics.quick_ratio.toFixed(2)}</span>
                    </div>
                    <Progress value={Math.min(100, (metrics.liquidity_metrics.quick_ratio / 2) * 100)} className="h-2" />

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Cash Ratio</span>
                      <span className="font-semibold">{metrics.liquidity_metrics.cash_ratio.toFixed(2)}</span>
                    </div>
                    <Progress value={Math.min(100, (metrics.liquidity_metrics.cash_ratio / 1) * 100)} className="h-2" />
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Assessment</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Working Capital</span>
                      <span className="font-medium">₹{(metrics.liquidity_metrics.working_capital / 100000).toFixed(1)}L</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Liquidity Score</span>
                      <span className={`font-medium ${getScoreColor(metrics.liquidity_metrics.overall_liquidity_score)}`}>
                        {metrics.liquidity_metrics.overall_liquidity_score.toFixed(1)}/100
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Risk Level</span>
                      {getRiskBadge(metrics.liquidity_metrics.liquidity_risk_level)}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="profitability" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Profitability Metrics
              </CardTitle>
              <CardDescription>Revenue generation and profit efficiency</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-3">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    {metrics.profitability_metrics.gross_profit_margin.toFixed(1)}%
                  </div>
                  <div className="text-sm text-green-600">Gross Margin</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600 mb-1">
                    {metrics.profitability_metrics.net_profit_margin.toFixed(1)}%
                  </div>
                  <div className="text-sm text-blue-600">Net Margin</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600 mb-1">
                    {metrics.profitability_metrics.return_on_assets.toFixed(1)}%
                  </div>
                  <div className="text-sm text-purple-600">ROA</div>
                </div>
              </div>

              <Separator />

              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Return Metrics</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Return on Assets (ROA)</span>
                      <span className="font-medium">{metrics.profitability_metrics.return_on_assets.toFixed(1)}%</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Return on Equity (ROE)</span>
                      <span className="font-medium">{metrics.profitability_metrics.return_on_equity.toFixed(1)}%</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Operating Margin</span>
                      <span className="font-medium">{metrics.profitability_metrics.operating_profit_margin.toFixed(1)}%</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Performance</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Profitability Score</span>
                      <span className={`font-medium ${getScoreColor(metrics.profitability_metrics.overall_profitability_score)}`}>
                        {metrics.profitability_metrics.overall_profitability_score.toFixed(1)}/100
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Trend</span>
                      <div className="flex items-center gap-1">
                        {getTrendIcon(metrics.profitability_metrics.profitability_trend)}
                        <span className="font-medium capitalize">{metrics.profitability_metrics.profitability_trend}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="leverage" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Leverage Metrics
              </CardTitle>
              <CardDescription>Debt management and financial leverage</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Debt Ratios</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Debt-to-Equity</span>
                      <span className="font-semibold">{metrics.leverage_metrics.debt_to_equity_ratio.toFixed(2)}</span>
                    </div>
                    <Progress value={Math.min(100, (metrics.leverage_metrics.debt_to_equity_ratio / 2) * 100)} className="h-2" />

                    <div className="flex items-center justify-between">
                      <span className="text-sm">Debt-to-Assets</span>
                      <span className="font-semibold">{metrics.leverage_metrics.debt_to_assets_ratio.toFixed(2)}</span>
                    </div>
                    <Progress value={Math.min(100, (metrics.leverage_metrics.debt_to_assets_ratio / 1) * 100)} className="h-2" />
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Coverage Ratios</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>DSCR</span>
                      <span className="font-medium">{metrics.leverage_metrics.debt_service_coverage_ratio.toFixed(2)}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Interest Coverage</span>
                      <span className="font-medium">{metrics.leverage_metrics.interest_coverage_ratio.toFixed(2)}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Leverage Score</span>
                      <span className={`font-medium ${getScoreColor(metrics.leverage_metrics.overall_leverage_score)}`}>
                        {metrics.leverage_metrics.overall_leverage_score.toFixed(1)}/100
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Risk Level</span>
                      {getRiskBadge(metrics.leverage_metrics.leverage_risk_level)}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="efficiency" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Efficiency Metrics
              </CardTitle>
              <CardDescription>Asset utilization and operational efficiency</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-3">
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-2xl font-bold text-orange-600 mb-1">
                    {metrics.efficiency_metrics.asset_turnover.toFixed(1)}x
                  </div>
                  <div className="text-sm text-orange-600">Asset Turnover</div>
                </div>
                <div className="text-center p-4 bg-teal-50 rounded-lg">
                  <div className="text-2xl font-bold text-teal-600 mb-1">
                    {metrics.efficiency_metrics.days_sales_outstanding.toFixed(0)}
                  </div>
                  <div className="text-sm text-teal-600">DSO (days)</div>
                </div>
                <div className="text-center p-4 bg-indigo-50 rounded-lg">
                  <div className="text-2xl font-bold text-indigo-600 mb-1">
                    {metrics.efficiency_metrics.cash_conversion_cycle.toFixed(0)}
                  </div>
                  <div className="text-sm text-indigo-600">CCC (days)</div>
                </div>
              </div>

              <Separator />

              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Turnover Ratios</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Inventory Turnover</span>
                      <span className="font-medium">{metrics.efficiency_metrics.inventory_turnover.toFixed(1)}x</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Receivables Turnover</span>
                      <span className="font-medium">{metrics.efficiency_metrics.receivables_turnover.toFixed(1)}x</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Performance</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Efficiency Score</span>
                      <span className={`font-medium ${getScoreColor(metrics.efficiency_metrics.overall_efficiency_score)}`}>
                        {metrics.efficiency_metrics.overall_efficiency_score.toFixed(1)}/100
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Trend</span>
                      <div className="flex items-center gap-1">
                        {getTrendIcon(metrics.efficiency_metrics.efficiency_trend)}
                        <span className="font-medium capitalize">{metrics.efficiency_metrics.efficiency_trend}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-orange-700">
                  <AlertCircle className="h-5 w-5" />
                  Improvement Areas
                </CardTitle>
                <CardDescription>Areas requiring attention</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {metrics.improvement_areas.map((area, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <AlertCircle className="h-4 w-4 text-orange-500" />
                    <span>{area}</span>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-blue-700">
                  <CheckCircle className="h-5 w-5" />
                  Action Items
                </CardTitle>
                <CardDescription>Recommended actions</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {metrics.action_items.map((item, index) => (
                  <div key={index} className="flex items-start gap-2 text-sm">
                    <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5" />
                    <span>{item}</span>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Overall Financial Health
              </CardTitle>
              <CardDescription>Comprehensive assessment summary</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-lg font-bold text-blue-600 mb-1">
                    {metrics.liquidity_metrics.overall_liquidity_score.toFixed(0)}
                  </div>
                  <div className="text-sm text-blue-600">Liquidity</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-lg font-bold text-green-600 mb-1">
                    {metrics.profitability_metrics.overall_profitability_score.toFixed(0)}
                  </div>
                  <div className="text-sm text-green-600">Profitability</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-lg font-bold text-purple-600 mb-1">
                    {metrics.leverage_metrics.overall_leverage_score.toFixed(0)}
                  </div>
                  <div className="text-sm text-purple-600">Leverage</div>
                </div>
                <div className="text-center p-4 bg-orange-50 rounded-lg">
                  <div className="text-lg font-bold text-orange-600 mb-1">
                    {metrics.efficiency_metrics.overall_efficiency_score.toFixed(0)}
                  </div>
                  <div className="text-sm text-orange-600">Efficiency</div>
                </div>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Overall Financial Score</h4>
                  <p className="text-sm text-muted-foreground">Weighted average of all metrics</p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold">{metrics.overall_financial_score.toFixed(1)}/100</div>
                  {getGradeBadge(metrics.financial_grade)}
                </div>
              </div>
              <Progress value={metrics.overall_financial_score} className="h-3" />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Data Quality Footer */}
      <Card className="bg-muted/30">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                Last updated: {new Date(metrics.last_updated).toLocaleDateString()}
              </span>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Data Completeness:</span>
                <Badge variant="outline">{metrics.data_completeness.toFixed(0)}%</Badge>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Confidence:</span>
                <Badge variant="outline">{metrics.calculation_confidence.toFixed(0)}%</Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}