/**
 * Jest setup file for Credit Chakra frontend testing.
 * 
 * This file configures the testing environment with necessary polyfills,
 * mocks, and global test utilities for React Testing Library and Jest.
 * 
 * @fileoverview Jest setup and configuration
 * <AUTHOR> Chakra Team
 * @version 1.0.0
 */

import React from 'react';
import '@testing-library/jest-dom';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
    getAll: jest.fn(),
    has: jest.fn(),
    keys: jest.fn(),
    values: jest.fn(),
    entries: jest.fn(),
    forEach: jest.fn(),
    toString: jest.fn(),
  }),
  usePathname: () => '/test-path',
}));

// Mock Recharts for chart components
jest.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: { children: React.ReactNode }) => children,
  BarChart: ({ children }: { children: React.ReactNode }) => {
    return { type: 'div', props: { 'data-testid': 'bar-chart', children } };
  },
  Bar: () => ({ type: 'div', props: { 'data-testid': 'bar' } }),
  XAxis: () => ({ type: 'div', props: { 'data-testid': 'x-axis' } }),
  YAxis: () => ({ type: 'div', props: { 'data-testid': 'y-axis' } }),
  CartesianGrid: () => ({ type: 'div', props: { 'data-testid': 'cartesian-grid' } }),
  Tooltip: () => ({ type: 'div', props: { 'data-testid': 'tooltip' } }),
  PieChart: ({ children }: { children: React.ReactNode }) => {
    return { type: 'div', props: { 'data-testid': 'pie-chart', children } };
  },
  Pie: () => ({ type: 'div', props: { 'data-testid': 'pie' } }),
  Cell: () => ({ type: 'div', props: { 'data-testid': 'cell' } }),
  LineChart: ({ children }: { children: React.ReactNode }) => {
    return { type: 'div', props: { 'data-testid': 'line-chart', children } };
  },
  Line: () => ({ type: 'div', props: { 'data-testid': 'line' } }),
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => {
  const MockIcon = ({ className, ...props }: any) =>
    React.createElement('div', { 'data-testid': 'mock-icon', className, ...props });

  return new Proxy({}, {
    get: (target, prop) => {
      if (typeof prop === 'string') {
        return MockIcon;
      }
      return target[prop as keyof typeof target];
    }
  });
});

// Mock WebSocket for real-time features
const MockWebSocket = jest.fn().mockImplementation(() => ({
  close: jest.fn(),
  send: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: 1, // OPEN
}));

// Add static properties
(MockWebSocket as any).CONNECTING = 0;
(MockWebSocket as any).OPEN = 1;
(MockWebSocket as any).CLOSING = 2;
(MockWebSocket as any).CLOSED = 3;

global.WebSocket = MockWebSocket as any;

// Mock fetch for API calls
global.fetch = jest.fn();

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn(),
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// Mock console methods to reduce noise in tests
const originalError = console.error;
const originalWarn = console.warn;

beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
  
  console.warn = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('componentWillReceiveProps') ||
       args[0].includes('componentWillUpdate'))
    ) {
      return;
    }
    originalWarn.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
  console.warn = originalWarn;
});

// Global test utilities
export const mockMSMEData = {
  msme_id: 'test-msme-001',
  name: 'Test Electronics Store',
  business_type: 'retail' as const,
  location: 'Mumbai, Maharashtra',
  current_score: 75.5,
  risk_band: 'green' as const,
  score_trend: 'stable' as const,
  signals_count: 12,
  recent_nudges: 2,
  last_signal_date: '2024-01-15T10:30:00Z',
  created_at: '2024-01-01T00:00:00Z',
  last_updated: '2024-01-15T10:30:00Z',
  tags: ['electronics', 'retail'],
  gst_compliance: 88,
  banking_health: 82,
  monthly_turnover: 1500000,
  digital_score: 78,
  gst_number: '27AABCS1234C1Z5',
};

export const mockAnalyticsData = {
  total_msmes: 20,
  total_signals: 240,
  triggered_escalations: 7,
  risk_distribution: {
    green: 12,
    yellow: 5,
    red: 3,
  },
  business_type_distribution: {
    Retail: 8,
    Manufacturing: 7,
    Services: 5,
  },
  average_signals_per_msme: 12.0,
  last_updated: '2024-01-15T10:30:00Z',
};

export const mockRiskAlert = {
  id: 'alert-001',
  msme_id: 'test-msme-001',
  msme_name: 'Test Electronics Store',
  type: 'score_drop' as const,
  severity: 'medium' as const,
  message: 'Credit score dropped by 10 points',
  timestamp: '2024-01-15T10:30:00Z',
  acknowledged: false,
  dpd_classification: 'standard' as const,
  days_past_due: 0,
};

// Custom render function with providers
import { render, RenderOptions } from '@testing-library/react';
import { ReactElement } from 'react';

const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return React.createElement('div', {}, children);
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };
