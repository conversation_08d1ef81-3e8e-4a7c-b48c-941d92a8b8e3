'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import CashFlowCharts from '@/components/charts/cash-flow-charts';
import {
  TrendingUp,
  TrendingDown,
  AlertCircle,
  RefreshCw,
  DollarSign,
  Calendar,
  BarChart3,
  Target,
  Zap,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Clock
} from 'lucide-react';

interface CashFlowData {
  statements: any[];
  forecast: {
    monthly_forecasts: any[];
    projected_gaps: any[];
    surplus_periods: any[];
    net_cash_flow_forecast: number;
    confidence_level: number;
  };
  seasonal: {
    monthly_averages: Record<string, number>;
    monthly_patterns: Record<string, string>;
    seasonal_score: number;
  };
  analytics: {
    cash_flow_health_score: number;
    liquidity_score: number;
    efficiency_score: number;
    cash_flow_quality: number;
    free_cash_flow: number;
    operating_cash_flow_ratio: number;
    cash_flow_volatility: number;
    cash_flow_growth_rate: number;
    cash_flow_predictability: number;
    liquidity_risk_score: number;
  };
  working_capital: {
    working_capital: number;
    cash_conversion_cycle: number;
    days_sales_outstanding: number;
    days_inventory_outstanding: number;
    days_payable_outstanding: number;
    efficiency_score: number;
  };
  last_updated: string;
}

interface CashFlowTabProps {
  msmeId: string;
}

export default function CashFlowTab({ msmeId }: CashFlowTabProps) {
  const [cashFlowData, setCashFlowData] = useState<CashFlowData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchCashFlowData();
  }, [msmeId]);

  const fetchCashFlowData = async () => {
    try {
      setLoading(true);
      // Mock data - in production, this would call the actual API
      const mockData: CashFlowData = {
        statements: [],
        forecast: {
          monthly_forecasts: [],
          projected_gaps: [
            { month: "Aug 2024", gap_amount: 250000, severity: "MEDIUM" },
            { month: "Dec 2024", gap_amount: 180000, severity: "LOW" }
          ],
          surplus_periods: [
            { month: "Sep 2024", surplus_amount: 320000, opportunity: "HIGH" },
            { month: "Jan 2025", surplus_amount: 450000, opportunity: "HIGH" }
          ],
          net_cash_flow_forecast: 1250000,
          confidence_level: 82.5
        },
        seasonal: {
          monthly_averages: {
            "1": 850000, "2": 920000, "3": 1100000, "4": 980000,
            "5": 1050000, "6": 1200000, "7": 1150000, "8": 750000,
            "9": 1300000, "10": 1250000, "11": 1400000, "12": 800000
          },
          monthly_patterns: {
            "1": "MEDIUM", "2": "MEDIUM", "3": "HIGH", "4": "MEDIUM",
            "5": "MEDIUM", "6": "HIGH", "7": "HIGH", "8": "LOW",
            "9": "HIGH", "10": "HIGH", "11": "HIGH", "12": "LOW"
          },
          seasonal_score: 78.5
        },
        analytics: {
          cash_flow_health_score: 82.4,
          liquidity_score: 78.9,
          efficiency_score: 75.2,
          cash_flow_quality: 80.1,
          free_cash_flow: 1850000,
          operating_cash_flow_ratio: 0.35,
          cash_flow_volatility: 18.5,
          cash_flow_growth_rate: 12.3,
          cash_flow_predictability: 85.2,
          liquidity_risk_score: 22.1
        },
        working_capital: {
          working_capital: 2500000,
          cash_conversion_cycle: 68,
          days_sales_outstanding: 42,
          days_inventory_outstanding: 35,
          days_payable_outstanding: 28,
          efficiency_score: 72.8
        },
        last_updated: new Date().toISOString()
      };
      
      setCashFlowData(mockData);
    } catch (error) {
      console.error('Error fetching cash flow data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchCashFlowData();
    setRefreshing(false);
  };

  const getTrendIcon = (value: number) => {
    if (value > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (value < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <BarChart3 className="h-4 w-4 text-blue-500" />;
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadge = (score: number) => {
    if (score >= 80) return <Badge variant="default" className="bg-green-100 text-green-800">Excellent</Badge>;
    if (score >= 60) return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Good</Badge>;
    return <Badge variant="destructive">Needs Improvement</Badge>;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!cashFlowData) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Cash Flow Data Available</h3>
        <p className="text-gray-500 mb-4">Cash flow analysis data is not available for this MSME.</p>
        <Button onClick={fetchCashFlowData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Refresh */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Cash Flow Analysis</h2>
          <p className="text-muted-foreground">Comprehensive cash flow insights and forecasting</p>
        </div>
        <Button 
          variant="outline" 
          onClick={handleRefresh} 
          disabled={refreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <DollarSign className="h-5 w-5" />
              Free Cash Flow
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 mb-2">
              ₹{(cashFlowData.analytics.free_cash_flow / 100000).toFixed(1)}L
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getTrendIcon(cashFlowData.analytics.cash_flow_growth_rate)}
              <span className={cashFlowData.analytics.cash_flow_growth_rate > 0 ? 'text-green-600' : 'text-red-600'}>
                {cashFlowData.analytics.cash_flow_growth_rate > 0 ? '+' : ''}{cashFlowData.analytics.cash_flow_growth_rate.toFixed(1)}%
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700">
              <Activity className="h-5 w-5" />
              Health Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 mb-2">
              {cashFlowData.analytics.cash_flow_health_score.toFixed(0)}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getScoreBadge(cashFlowData.analytics.cash_flow_health_score)}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <Clock className="h-5 w-5" />
              Cash Cycle
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 mb-2">
              {cashFlowData.working_capital.cash_conversion_cycle} days
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-purple-600">Working Capital</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <Target className="h-5 w-5" />
              Predictability
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 mb-2">
              {cashFlowData.analytics.cash_flow_predictability.toFixed(0)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Progress value={cashFlowData.analytics.cash_flow_predictability} className="h-2 flex-1" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="forecast">Forecast</TabsTrigger>
          <TabsTrigger value="seasonal">Seasonal</TabsTrigger>
          <TabsTrigger value="working-capital">Working Capital</TabsTrigger>
          <TabsTrigger value="charts">Charts</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Cash Flow Health
                </CardTitle>
                <CardDescription>Overall cash flow performance</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {cashFlowData.analytics.cash_flow_health_score.toFixed(0)}/100
                  </div>
                  {getScoreBadge(cashFlowData.analytics.cash_flow_health_score)}
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Quality Score</span>
                    <span className="font-medium">{cashFlowData.analytics.cash_flow_quality.toFixed(0)}/100</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Volatility</span>
                    <span className="font-medium">{cashFlowData.analytics.cash_flow_volatility.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Growth Rate</span>
                    <span className="font-medium">{cashFlowData.analytics.cash_flow_growth_rate.toFixed(1)}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Liquidity Position
                </CardTitle>
                <CardDescription>Current liquidity metrics</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {cashFlowData.analytics.liquidity_score.toFixed(0)}/100
                  </div>
                  {getScoreBadge(cashFlowData.analytics.liquidity_score)}
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Free Cash Flow</span>
                    <span className="font-medium">₹{(cashFlowData.analytics.free_cash_flow / 100000).toFixed(1)}L</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Operating CF Ratio</span>
                    <span className="font-medium">{cashFlowData.analytics.operating_cash_flow_ratio.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Risk Score</span>
                    <span className="font-medium">{cashFlowData.analytics.liquidity_risk_score.toFixed(1)}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Efficiency Metrics
                </CardTitle>
                <CardDescription>Cash flow efficiency indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600 mb-2">
                    {cashFlowData.analytics.efficiency_score.toFixed(0)}/100
                  </div>
                  {getScoreBadge(cashFlowData.analytics.efficiency_score)}
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Predictability</span>
                    <span className="font-medium">{cashFlowData.analytics.cash_flow_predictability.toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Working Capital</span>
                    <span className="font-medium">₹{(cashFlowData.working_capital.working_capital / 100000).toFixed(1)}L</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Conversion Cycle</span>
                    <span className="font-medium">{cashFlowData.working_capital.cash_conversion_cycle} days</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="forecast" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-700">
                  <ArrowDownRight className="h-5 w-5" />
                  Projected Cash Flow Gaps
                </CardTitle>
                <CardDescription>Periods requiring attention</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {cashFlowData.forecast.projected_gaps.map((gap, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                      <div>
                        <p className="font-medium text-sm">{gap.month}</p>
                        <p className="text-xs text-red-600">{gap.severity} Priority</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-red-700">-₹{(gap.gap_amount / 100000).toFixed(1)}L</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-700">
                  <ArrowUpRight className="h-5 w-5" />
                  Projected Surplus Periods
                </CardTitle>
                <CardDescription>Investment opportunities</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {cashFlowData.forecast.surplus_periods.map((surplus, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div>
                        <p className="font-medium text-sm">{surplus.month}</p>
                        <p className="text-xs text-green-600">{surplus.opportunity} Opportunity</p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-green-700">+₹{(surplus.surplus_amount / 100000).toFixed(1)}L</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Forecast Summary</CardTitle>
              <CardDescription>12-month cash flow projection</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600 mb-1">
                    ₹{(cashFlowData.forecast.net_cash_flow_forecast / 100000).toFixed(1)}L
                  </div>
                  <div className="text-sm text-blue-600">Net Cash Flow Forecast</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    {cashFlowData.forecast.confidence_level.toFixed(0)}%
                  </div>
                  <div className="text-sm text-green-600">Forecast Confidence</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600 mb-1">
                    {cashFlowData.forecast.projected_gaps.length + cashFlowData.forecast.surplus_periods.length}
                  </div>
                  <div className="text-sm text-purple-600">Key Events</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seasonal" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Seasonal Cash Flow Patterns</CardTitle>
              <CardDescription>Monthly seasonal variations and trends</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-4 md:grid-cols-4">
                {Object.entries(cashFlowData.seasonal.monthly_averages).map(([month, value]) => {
                  const monthName = new Date(2023, parseInt(month) - 1).toLocaleString('default', { month: 'short' });
                  const pattern = cashFlowData.seasonal.monthly_patterns[month];
                  return (
                    <div key={month} className="text-center p-3 bg-muted/50 rounded-lg">
                      <div className="text-lg font-bold mb-1">
                        ₹{(value / 100000).toFixed(1)}L
                      </div>
                      <div className="text-sm text-muted-foreground mb-1">{monthName}</div>
                      <Badge variant={pattern === 'HIGH' ? 'default' : pattern === 'LOW' ? 'destructive' : 'secondary'} className="text-xs">
                        {pattern}
                      </Badge>
                    </div>
                  );
                })}
              </div>
              
              <Separator />
              
              <div className="text-center">
                <h4 className="font-medium mb-2">Seasonal Score</h4>
                <div className="text-2xl font-bold text-blue-600 mb-2">
                  {cashFlowData.seasonal.seasonal_score.toFixed(0)}/100
                </div>
                <Progress value={cashFlowData.seasonal.seasonal_score} className="h-3 max-w-md mx-auto" />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="working-capital" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Working Capital Analysis</CardTitle>
              <CardDescription>Working capital cycle and efficiency metrics</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Cycle Components</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Days Sales Outstanding (DSO)</span>
                      <span className="font-semibold">{cashFlowData.working_capital.days_sales_outstanding} days</span>
                    </div>
                    <Progress value={(cashFlowData.working_capital.days_sales_outstanding / 90) * 100} className="h-2" />
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Days Inventory Outstanding (DIO)</span>
                      <span className="font-semibold">{cashFlowData.working_capital.days_inventory_outstanding} days</span>
                    </div>
                    <Progress value={(cashFlowData.working_capital.days_inventory_outstanding / 90) * 100} className="h-2" />
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Days Payable Outstanding (DPO)</span>
                      <span className="font-semibold">{cashFlowData.working_capital.days_payable_outstanding} days</span>
                    </div>
                    <Progress value={(cashFlowData.working_capital.days_payable_outstanding / 90) * 100} className="h-2" />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-medium">Summary Metrics</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Working Capital</span>
                      <span className="font-medium">₹{(cashFlowData.working_capital.working_capital / 100000).toFixed(1)}L</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Cash Conversion Cycle</span>
                      <span className="font-medium">{cashFlowData.working_capital.cash_conversion_cycle} days</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Efficiency Score</span>
                      <span className={`font-medium ${getScoreColor(cashFlowData.working_capital.efficiency_score)}`}>
                        {cashFlowData.working_capital.efficiency_score.toFixed(1)}/100
                      </span>
                    </div>
                    <Progress value={cashFlowData.working_capital.efficiency_score} className="h-2" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="charts" className="space-y-6">
          <CashFlowCharts data={cashFlowData} />
        </TabsContent>
      </Tabs>

      {/* Data Quality Footer */}
      <Card className="bg-muted/30">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                Last updated: {new Date(cashFlowData.last_updated).toLocaleDateString()}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">Forecast Confidence:</span>
              <Badge variant="outline">{cashFlowData.forecast.confidence_level.toFixed(0)}%</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
