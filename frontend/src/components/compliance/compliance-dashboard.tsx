'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  FileText,
  Lock,
  Eye,
  Download,
  RefreshCw,
  Calendar,
  Activity,
  TrendingUp,
  AlertCircle
} from 'lucide-react';

interface ComplianceData {
  overall_compliance_score: number;
  compliance_status: string;
  risk_level: string;
  rbi_compliance: {
    overall_compliance_score: number;
    credit_policy_compliance: boolean;
    fair_practices_code: boolean;
    data_localization_compliance: boolean;
    kyc_compliance: boolean;
    aml_compliance: boolean;
    non_compliance_issues: string[];
  };
  data_privacy: {
    privacy_score: number;
    consent_obtained: boolean;
    data_minimization: boolean;
    encryption_at_rest: boolean;
    encryption_in_transit: boolean;
    breach_detection: boolean;
  };
  security_audit: {
    overall_security_score: number;
    critical_findings: string[];
    high_findings: string[];
    medium_findings: string[];
    low_findings: string[];
  };
  alerts: Array<{
    alert_id: string;
    alert_type: string;
    severity: string;
    title: string;
    description: string;
    detected_at: string;
    status: string;
  }>;
  last_updated: string;
}

interface ComplianceDashboardProps {
  msmeId?: string;
  portfolioView?: boolean;
}

export default function ComplianceDashboard({
  msmeId,
  portfolioView = false
}: ComplianceDashboardProps) {
  const [complianceData, setComplianceData] = useState<ComplianceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchComplianceData();
  }, [msmeId]);

  const fetchComplianceData = async () => {
    try {
      setLoading(true);
      // Mock data - in production, this would call the actual API
      const mockData: ComplianceData = {
        overall_compliance_score: 87.5,
        compliance_status: "COMPLIANT",
        risk_level: "LOW",
        rbi_compliance: {
          overall_compliance_score: 92.3,
          credit_policy_compliance: true,
          fair_practices_code: true,
          data_localization_compliance: true,
          kyc_compliance: true,
          aml_compliance: true,
          non_compliance_issues: []
        },
        data_privacy: {
          privacy_score: 85.7,
          consent_obtained: true,
          data_minimization: true,
          encryption_at_rest: true,
          encryption_in_transit: true,
          breach_detection: true
        },
        security_audit: {
          overall_security_score: 84.2,
          critical_findings: [],
          high_findings: ["Implement multi-factor authentication for admin accounts"],
          medium_findings: ["Update SSL certificates", "Review access logs quarterly"],
          low_findings: ["Update documentation", "Improve password policy"]
        },
        alerts: [
          {
            alert_id: "ALERT_001",
            alert_type: "DATA_PRIVACY",
            severity: "MEDIUM",
            title: "Consent Renewal Required",
            description: "Data processing consent expires in 30 days",
            detected_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            status: "OPEN"
          },
          {
            alert_id: "ALERT_002",
            alert_type: "CYBER_SECURITY",
            severity: "HIGH",
            title: "Security Patch Available",
            description: "Critical security update available for authentication system",
            detected_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            status: "OPEN"
          }
        ],
        last_updated: new Date().toISOString()
      };

      setComplianceData(mockData);
    } catch (error) {
      console.error('Error fetching compliance data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchComplianceData();
    setRefreshing(false);
  };

  const getComplianceStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLIANT':
        return <Badge variant="default" className="bg-green-100 text-green-800">Compliant</Badge>;
      case 'PARTIAL_COMPLIANCE':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Partial Compliance</Badge>;
      case 'NON_COMPLIANT':
        return <Badge variant="destructive">Non-Compliant</Badge>;
      default:
        return <Badge variant="outline">Under Review</Badge>;
    }
  };

  const getRiskBadge = (risk: string) => {
    switch (risk) {
      case 'LOW':
        return <Badge variant="default" className="bg-green-100 text-green-800">Low Risk</Badge>;
      case 'MEDIUM':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Medium Risk</Badge>;
      case 'HIGH':
        return <Badge variant="destructive">High Risk</Badge>;
      case 'CRITICAL':
        return <Badge variant="destructive" className="bg-red-800">Critical Risk</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'HIGH':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'MEDIUM':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'LOW':
        return <Clock className="h-4 w-4 text-blue-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'CRITICAL':
        return <Badge variant="destructive" className="bg-red-800">Critical</Badge>;
      case 'HIGH':
        return <Badge variant="destructive">High</Badge>;
      case 'MEDIUM':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Medium</Badge>;
      case 'LOW':
        return <Badge variant="outline">Low</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!complianceData) {
    return (
      <div className="text-center py-12">
        <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Compliance Data Available</h3>
        <p className="text-gray-500 mb-4">Compliance monitoring data is not available.</p>
        <Button onClick={fetchComplianceData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Compliance & Security Dashboard</h2>
          <p className="text-muted-foreground">
            {portfolioView ? 'Portfolio-wide compliance monitoring' : 'Comprehensive compliance and security status'}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => console.log('Generate report')}>
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Alert Banner */}
      {complianceData.alerts.filter(alert => alert.status === 'OPEN' && (alert.severity === 'CRITICAL' || alert.severity === 'HIGH')).length > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertTitle className="text-red-800">Attention Required</AlertTitle>
          <AlertDescription className="text-red-700">
            {complianceData.alerts.filter(alert => alert.status === 'OPEN' && (alert.severity === 'CRITICAL' || alert.severity === 'HIGH')).length} high-priority compliance issues require immediate attention.
          </AlertDescription>
        </Alert>
      )}

      {/* Summary Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <Shield className="h-5 w-5" />
              Overall Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 mb-2">
              {complianceData.overall_compliance_score.toFixed(0)}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getComplianceStatusBadge(complianceData.compliance_status)}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700">
              <FileText className="h-5 w-5" />
              RBI Compliance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 mb-2">
              {complianceData.rbi_compliance.overall_compliance_score.toFixed(0)}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              <CheckCircle className="h-3 w-3 text-green-600" />
              <span className="text-green-600">Compliant</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <Lock className="h-5 w-5" />
              Data Privacy
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 mb-2">
              {complianceData.data_privacy.privacy_score.toFixed(0)}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Eye className="h-3 w-3 text-purple-600" />
              <span className="text-purple-600">Protected</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <Activity className="h-5 w-5" />
              Security Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 mb-2">
              {complianceData.security_audit.overall_security_score.toFixed(0)}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getRiskBadge(complianceData.risk_level)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Compliance Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="rbi">RBI Compliance</TabsTrigger>
          <TabsTrigger value="privacy">Data Privacy</TabsTrigger>
          <TabsTrigger value="security">Security Audit</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Compliance Health
                </CardTitle>
                <CardDescription>Overall compliance performance</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">RBI Compliance</span>
                    <span className="font-medium">{complianceData.rbi_compliance.overall_compliance_score.toFixed(0)}%</span>
                  </div>
                  <Progress value={complianceData.rbi_compliance.overall_compliance_score} className="h-2" />

                  <div className="flex items-center justify-between">
                    <span className="text-sm">Data Privacy</span>
                    <span className="font-medium">{complianceData.data_privacy.privacy_score.toFixed(0)}%</span>
                  </div>
                  <Progress value={complianceData.data_privacy.privacy_score} className="h-2" />

                  <div className="flex items-center justify-between">
                    <span className="text-sm">Security Audit</span>
                    <span className="font-medium">{complianceData.security_audit.overall_security_score.toFixed(0)}%</span>
                  </div>
                  <Progress value={complianceData.security_audit.overall_security_score} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5" />
                  Risk Assessment
                </CardTitle>
                <CardDescription>Current risk level and factors</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-2xl font-bold mb-2">
                    {getRiskBadge(complianceData.risk_level)}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    Overall Risk Level
                  </p>
                </div>

                <Separator />

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Open Alerts</span>
                    <span className="font-medium">{complianceData.alerts.filter(a => a.status === 'OPEN').length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Critical Issues</span>
                    <span className="font-medium">{complianceData.security_audit.critical_findings.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>High Priority</span>
                    <span className="font-medium">{complianceData.security_audit.high_findings.length}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="rbi" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                RBI Compliance Status
              </CardTitle>
              <CardDescription>Reserve Bank of India regulatory compliance</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Compliance Areas</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Credit Policy Compliance</span>
                      {complianceData.rbi_compliance.credit_policy_compliance ?
                        <CheckCircle className="h-4 w-4 text-green-500" /> :
                        <XCircle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Fair Practices Code</span>
                      {complianceData.rbi_compliance.fair_practices_code ?
                        <CheckCircle className="h-4 w-4 text-green-500" /> :
                        <XCircle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Data Localization</span>
                      {complianceData.rbi_compliance.data_localization_compliance ?
                        <CheckCircle className="h-4 w-4 text-green-500" /> :
                        <XCircle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">KYC Compliance</span>
                      {complianceData.rbi_compliance.kyc_compliance ?
                        <CheckCircle className="h-4 w-4 text-green-500" /> :
                        <XCircle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">AML Compliance</span>
                      {complianceData.rbi_compliance.aml_compliance ?
                        <CheckCircle className="h-4 w-4 text-green-500" /> :
                        <XCircle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Compliance Summary</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Overall Score</span>
                      <span className="font-medium">{complianceData.rbi_compliance.overall_compliance_score.toFixed(1)}%</span>
                    </div>
                    <Progress value={complianceData.rbi_compliance.overall_compliance_score} className="h-2" />

                    <div className="flex items-center justify-between text-sm">
                      <span>Non-compliance Issues</span>
                      <span className="font-medium">{complianceData.rbi_compliance.non_compliance_issues.length}</span>
                    </div>

                    {complianceData.rbi_compliance.non_compliance_issues.length > 0 && (
                      <div className="space-y-2">
                        <h5 className="text-sm font-medium text-red-600">Issues to Address:</h5>
                        {complianceData.rbi_compliance.non_compliance_issues.map((issue, index) => (
                          <div key={index} className="flex items-center gap-2 text-sm text-red-600">
                            <AlertTriangle className="h-3 w-3" />
                            <span>{issue}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="privacy" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                Data Privacy Compliance
              </CardTitle>
              <CardDescription>Data protection and privacy measures</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Privacy Controls</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Consent Obtained</span>
                      {complianceData.data_privacy.consent_obtained ?
                        <CheckCircle className="h-4 w-4 text-green-500" /> :
                        <XCircle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Data Minimization</span>
                      {complianceData.data_privacy.data_minimization ?
                        <CheckCircle className="h-4 w-4 text-green-500" /> :
                        <XCircle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Encryption at Rest</span>
                      {complianceData.data_privacy.encryption_at_rest ?
                        <CheckCircle className="h-4 w-4 text-green-500" /> :
                        <XCircle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Encryption in Transit</span>
                      {complianceData.data_privacy.encryption_in_transit ?
                        <CheckCircle className="h-4 w-4 text-green-500" /> :
                        <XCircle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Breach Detection</span>
                      {complianceData.data_privacy.breach_detection ?
                        <CheckCircle className="h-4 w-4 text-green-500" /> :
                        <XCircle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Privacy Score</h4>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-600 mb-2">
                      {complianceData.data_privacy.privacy_score.toFixed(0)}/100
                    </div>
                    <Progress value={complianceData.data_privacy.privacy_score} className="h-3" />
                  </div>

                  <div className="space-y-2 text-sm">
                    <div className="p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center gap-2 text-green-700">
                        <CheckCircle className="h-4 w-4" />
                        <span className="font-medium">Data Protection Compliant</span>
                      </div>
                      <p className="text-green-600 text-xs mt-1">
                        All required privacy controls are in place
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Security Audit Results
              </CardTitle>
              <CardDescription>Latest security assessment findings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Security Findings</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Critical Issues</span>
                      <Badge variant={complianceData.security_audit.critical_findings.length > 0 ? "destructive" : "default"}>
                        {complianceData.security_audit.critical_findings.length}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>High Priority</span>
                      <Badge variant={complianceData.security_audit.high_findings.length > 0 ? "destructive" : "default"}>
                        {complianceData.security_audit.high_findings.length}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Medium Priority</span>
                      <Badge variant="secondary">
                        {complianceData.security_audit.medium_findings.length}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Low Priority</span>
                      <Badge variant="outline">
                        {complianceData.security_audit.low_findings.length}
                      </Badge>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Security Score</h4>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-600 mb-2">
                      {complianceData.security_audit.overall_security_score.toFixed(0)}/100
                    </div>
                    <Progress value={complianceData.security_audit.overall_security_score} className="h-3" />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Security Findings Details */}
              <div className="space-y-4">
                {complianceData.security_audit.high_findings.length > 0 && (
                  <div>
                    <h5 className="font-medium text-red-600 mb-2">High Priority Issues</h5>
                    <div className="space-y-2">
                      {complianceData.security_audit.high_findings.map((finding, index) => (
                        <div key={index} className="flex items-center gap-2 p-3 bg-red-50 rounded-lg">
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                          <span className="text-sm text-red-700">{finding}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {complianceData.security_audit.medium_findings.length > 0 && (
                  <div>
                    <h5 className="font-medium text-yellow-600 mb-2">Medium Priority Issues</h5>
                    <div className="space-y-2">
                      {complianceData.security_audit.medium_findings.map((finding, index) => (
                        <div key={index} className="flex items-center gap-2 p-3 bg-yellow-50 rounded-lg">
                          <AlertCircle className="h-4 w-4 text-yellow-500" />
                          <span className="text-sm text-yellow-700">{finding}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="alerts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Compliance Alerts
              </CardTitle>
              <CardDescription>Active compliance and security alerts</CardDescription>
            </CardHeader>
            <CardContent>
              {complianceData.alerts.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
                  <h3 className="text-lg font-medium text-green-700 mb-2">No Active Alerts</h3>
                  <p>All compliance requirements are being met.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {complianceData.alerts.map((alert) => (
                    <div key={alert.alert_id} className="flex items-start gap-3 p-4 border rounded-lg">
                      <div className="flex-shrink-0 mt-1">
                        {getSeverityIcon(alert.severity)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-sm">{alert.title}</h4>
                          <div className="flex items-center gap-2">
                            {getSeverityBadge(alert.severity)}
                            <Badge variant="outline" className="text-xs">
                              {alert.alert_type.replace('_', ' ')}
                            </Badge>
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mb-2">{alert.description}</p>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>Detected {formatTimeAgo(alert.detected_at)}</span>
                          <Badge variant={alert.status === 'OPEN' ? 'destructive' : 'default'} className="text-xs">
                            {alert.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Footer */}
      <Card className="bg-muted/30">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                Last updated: {new Date(complianceData.last_updated).toLocaleString()}
              </span>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Compliance Status:</span>
                {getComplianceStatusBadge(complianceData.compliance_status)}
              </div>
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Risk Level:</span>
                {getRiskBadge(complianceData.risk_level)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}