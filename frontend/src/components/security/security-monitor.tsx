'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Lock,
  Eye,
  Activity,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Bell,
  Users,
  Database,
  Network,
  Server,
  Wifi,
  Key
} from 'lucide-react';

interface SecurityMetrics {
  overall_security_score: number;
  threat_level: string;
  active_threats: number;
  blocked_attempts: number;
  security_incidents: number;
  last_scan: string;
  
  authentication: {
    failed_logins_24h: number;
    successful_logins_24h: number;
    mfa_enabled_users: number;
    total_users: number;
    password_strength_score: number;
  };
  
  network_security: {
    firewall_status: string;
    intrusion_attempts: number;
    blocked_ips: number;
    ssl_certificate_status: string;
    ssl_expiry_days: number;
  };
  
  data_protection: {
    encryption_coverage: number;
    backup_status: string;
    last_backup: string;
    data_integrity_score: number;
  };
  
  vulnerabilities: {
    critical: number;
    high: number;
    medium: number;
    low: number;
    last_scan: string;
  };
  
  compliance_status: {
    gdpr_compliance: boolean;
    iso27001_compliance: boolean;
    rbi_compliance: boolean;
    overall_compliance_score: number;
  };
}

interface SecurityMonitorProps {
  realTime?: boolean;
  showAlerts?: boolean;
}

export default function SecurityMonitor({ 
  realTime = true, 
  showAlerts = true 
}: SecurityMonitorProps) {
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchSecurityMetrics();
    
    if (realTime) {
      const interval = setInterval(fetchSecurityMetrics, 30000); // Refresh every 30 seconds
      return () => clearInterval(interval);
    }
  }, [realTime]);

  const fetchSecurityMetrics = async () => {
    try {
      setLoading(true);
      
      // Mock security metrics data
      const mockData: SecurityMetrics = {
        overall_security_score: 87.5,
        threat_level: "LOW",
        active_threats: 0,
        blocked_attempts: 23,
        security_incidents: 1,
        last_scan: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        
        authentication: {
          failed_logins_24h: 5,
          successful_logins_24h: 142,
          mfa_enabled_users: 18,
          total_users: 25,
          password_strength_score: 82.3
        },
        
        network_security: {
          firewall_status: "ACTIVE",
          intrusion_attempts: 12,
          blocked_ips: 8,
          ssl_certificate_status: "VALID",
          ssl_expiry_days: 89
        },
        
        data_protection: {
          encryption_coverage: 95.8,
          backup_status: "COMPLETED",
          last_backup: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          data_integrity_score: 98.2
        },
        
        vulnerabilities: {
          critical: 0,
          high: 2,
          medium: 5,
          low: 12,
          last_scan: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
        },
        
        compliance_status: {
          gdpr_compliance: true,
          iso27001_compliance: false,
          rbi_compliance: true,
          overall_compliance_score: 85.7
        }
      };
      
      setSecurityMetrics(mockData);
    } catch (error) {
      console.error('Error fetching security metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchSecurityMetrics();
    setRefreshing(false);
  };

  const getThreatLevelBadge = (level: string) => {
    switch (level) {
      case 'LOW':
        return <Badge variant="default" className="bg-green-100 text-green-800">Low Threat</Badge>;
      case 'MEDIUM':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Medium Threat</Badge>;
      case 'HIGH':
        return <Badge variant="destructive">High Threat</Badge>;
      case 'CRITICAL':
        return <Badge variant="destructive" className="bg-red-800">Critical Threat</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getStatusIcon = (status: boolean | string) => {
    if (typeof status === 'boolean') {
      return status ? 
        <CheckCircle className="h-4 w-4 text-green-500" /> : 
        <XCircle className="h-4 w-4 text-red-500" />;
    }
    
    switch (status) {
      case 'ACTIVE':
      case 'VALID':
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'INACTIVE':
      case 'INVALID':
      case 'FAILED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!securityMetrics) {
    return (
      <div className="text-center py-12">
        <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Security Data Available</h3>
        <p className="text-gray-500 mb-4">Security monitoring data is not available.</p>
        <Button onClick={fetchSecurityMetrics}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Security Monitor</h2>
          <p className="text-muted-foreground">Real-time security status and threat monitoring</p>
        </div>
        <div className="flex items-center gap-2">
          {realTime && (
            <Badge variant="outline" className="bg-green-50 text-green-700">
              <Activity className="h-3 w-3 mr-1" />
              Live
            </Badge>
          )}
          <Button 
            variant="outline" 
            onClick={handleRefresh} 
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Security Alerts */}
      {showAlerts && securityMetrics.vulnerabilities.critical > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertTitle className="text-red-800">Critical Security Alert</AlertTitle>
          <AlertDescription className="text-red-700">
            {securityMetrics.vulnerabilities.critical} critical vulnerabilities detected. Immediate action required.
          </AlertDescription>
        </Alert>
      )}

      {/* Overview Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <Shield className="h-5 w-5" />
              Security Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 mb-2">
              {securityMetrics.overall_security_score.toFixed(0)}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getThreatLevelBadge(securityMetrics.threat_level)}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700">
              <CheckCircle className="h-5 w-5" />
              Blocked Threats
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 mb-2">
              {securityMetrics.blocked_attempts}
            </div>
            <div className="flex items-center gap-1 text-sm">
              <TrendingUp className="h-3 w-3 text-green-600" />
              <span className="text-green-600">Last 24h</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <Lock className="h-5 w-5" />
              Data Protection
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 mb-2">
              {securityMetrics.data_protection.encryption_coverage.toFixed(0)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-purple-600">Encrypted</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <AlertTriangle className="h-5 w-5" />
              Vulnerabilities
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 mb-2">
              {securityMetrics.vulnerabilities.critical + securityMetrics.vulnerabilities.high}
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-orange-600">High Priority</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Security Metrics */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Authentication Security
            </CardTitle>
            <CardDescription>User authentication and access control</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">MFA Adoption</span>
                <span className="font-medium">
                  {securityMetrics.authentication.mfa_enabled_users}/{securityMetrics.authentication.total_users}
                </span>
              </div>
              <Progress 
                value={(securityMetrics.authentication.mfa_enabled_users / securityMetrics.authentication.total_users) * 100} 
                className="h-2" 
              />
              
              <div className="flex items-center justify-between text-sm">
                <span>Password Strength</span>
                <span className="font-medium">{securityMetrics.authentication.password_strength_score.toFixed(0)}%</span>
              </div>
              <Progress value={securityMetrics.authentication.password_strength_score} className="h-2" />
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Failed Logins (24h)</span>
                  <p className="font-medium text-red-600">{securityMetrics.authentication.failed_logins_24h}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Successful Logins (24h)</span>
                  <p className="font-medium text-green-600">{securityMetrics.authentication.successful_logins_24h}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Network className="h-5 w-5" />
              Network Security
            </CardTitle>
            <CardDescription>Network protection and monitoring</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Firewall Status</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(securityMetrics.network_security.firewall_status)}
                  <span className="font-medium">{securityMetrics.network_security.firewall_status}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm">SSL Certificate</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(securityMetrics.network_security.ssl_certificate_status)}
                  <span className="font-medium">
                    Expires in {securityMetrics.network_security.ssl_expiry_days} days
                  </span>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Intrusion Attempts</span>
                  <p className="font-medium text-red-600">{securityMetrics.network_security.intrusion_attempts}</p>
                </div>
                <div>
                  <span className="text-muted-foreground">Blocked IPs</span>
                  <p className="font-medium text-orange-600">{securityMetrics.network_security.blocked_ips}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Data Protection
            </CardTitle>
            <CardDescription>Data security and backup status</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Encryption Coverage</span>
                <span className="font-medium">{securityMetrics.data_protection.encryption_coverage.toFixed(1)}%</span>
              </div>
              <Progress value={securityMetrics.data_protection.encryption_coverage} className="h-2" />
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Data Integrity</span>
                <span className="font-medium">{securityMetrics.data_protection.data_integrity_score.toFixed(1)}%</span>
              </div>
              <Progress value={securityMetrics.data_protection.data_integrity_score} className="h-2" />
              
              <div className="flex items-center justify-between">
                <span className="text-sm">Backup Status</span>
                <div className="flex items-center gap-2">
                  {getStatusIcon(securityMetrics.data_protection.backup_status)}
                  <span className="font-medium">{securityMetrics.data_protection.backup_status}</span>
                </div>
              </div>
              
              <div className="text-sm text-muted-foreground">
                Last backup: {formatTimeAgo(securityMetrics.data_protection.last_backup)}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Vulnerability Assessment
            </CardTitle>
            <CardDescription>Security vulnerabilities and scan results</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="text-lg font-bold text-red-600">{securityMetrics.vulnerabilities.critical}</div>
                  <div className="text-xs text-red-600">Critical</div>
                </div>
                <div className="text-center p-3 bg-orange-50 rounded-lg">
                  <div className="text-lg font-bold text-orange-600">{securityMetrics.vulnerabilities.high}</div>
                  <div className="text-xs text-orange-600">High</div>
                </div>
                <div className="text-center p-3 bg-yellow-50 rounded-lg">
                  <div className="text-lg font-bold text-yellow-600">{securityMetrics.vulnerabilities.medium}</div>
                  <div className="text-xs text-yellow-600">Medium</div>
                </div>
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-lg font-bold text-blue-600">{securityMetrics.vulnerabilities.low}</div>
                  <div className="text-xs text-blue-600">Low</div>
                </div>
              </div>
              
              <div className="text-sm text-muted-foreground">
                Last scan: {formatTimeAgo(securityMetrics.vulnerabilities.last_scan)}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Compliance Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Compliance Status
          </CardTitle>
          <CardDescription>Regulatory compliance and standards adherence</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-4">
              <h4 className="font-medium">Compliance Standards</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">GDPR Compliance</span>
                  {getStatusIcon(securityMetrics.compliance_status.gdpr_compliance)}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">ISO 27001</span>
                  {getStatusIcon(securityMetrics.compliance_status.iso27001_compliance)}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">RBI Guidelines</span>
                  {getStatusIcon(securityMetrics.compliance_status.rbi_compliance)}
                </div>
              </div>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-medium">Overall Compliance</h4>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {securityMetrics.compliance_status.overall_compliance_score.toFixed(0)}%
                </div>
                <Progress value={securityMetrics.compliance_status.overall_compliance_score} className="h-3" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Footer */}
      <Card className="bg-muted/30">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                Last security scan: {formatTimeAgo(securityMetrics.last_scan)}
              </span>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Threat Level:</span>
                {getThreatLevelBadge(securityMetrics.threat_level)}
              </div>
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Active Threats:</span>
                <Badge variant={securityMetrics.active_threats > 0 ? "destructive" : "default"}>
                  {securityMetrics.active_threats}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
