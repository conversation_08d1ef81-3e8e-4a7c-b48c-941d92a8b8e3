"""
Reports data models for Credit Chakra MSME credit monitoring dashboard.

This module defines Pydantic models for all report types including:
- Portfolio Summary Report
- Risk Exposure Report  
- Compliance & Regulatory Report
- Performance Trends Report
- Detailed MSME Profile Report

Author: Credit Chakra Team
Version: 1.0.0
"""
from __future__ import annotations

from datetime import datetime, date
from typing import Any, Dict, List, Optional, Union
from enum import Enum

from pydantic import BaseModel, Field


class ReportType(str, Enum):
    """Enumeration of available report types"""
    PORTFOLIO_SUMMARY = "portfolio_summary"
    RISK_EXPOSURE = "risk_exposure"
    COMPLIANCE_REGULATORY = "compliance_regulatory"
    PERFORMANCE_TRENDS = "performance_trends"
    DETAILED_MSME_PROFILE = "detailed_msme_profile"


class ReportFormat(str, Enum):
    """Enumeration of available export formats"""
    PDF = "pdf"
    CSV = "csv"
    JSON = "json"


class ReportStatus(str, Enum):
    """Enumeration of report generation status"""
    PENDING = "pending"
    GENERATING = "generating"
    COMPLETED = "completed"
    FAILED = "failed"


# Base Report Models
class BaseReportMetadata(BaseModel):
    """Base metadata for all reports"""
    report_id: str = Field(..., description="Unique report identifier")
    report_type: ReportType = Field(..., description="Type of report")
    generated_at: datetime = Field(default_factory=datetime.utcnow, description="Report generation timestamp")
    generated_by: str = Field(default="system", description="User who generated the report")
    reporting_period_start: date = Field(..., description="Start date of reporting period")
    reporting_period_end: date = Field(..., description="End date of reporting period")
    status: ReportStatus = Field(default=ReportStatus.PENDING, description="Report generation status")
    total_records: int = Field(default=0, description="Total number of records processed")


# Portfolio Summary Report Models
class RiskDistribution(BaseModel):
    """Risk distribution breakdown"""
    high_risk_count: int = Field(0, description="Number of high risk MSMEs")
    high_risk_percentage: float = Field(0.0, description="Percentage of high risk MSMEs")
    medium_risk_count: int = Field(0, description="Number of medium risk MSMEs")
    medium_risk_percentage: float = Field(0.0, description="Percentage of medium risk MSMEs")
    low_risk_count: int = Field(0, description="Number of low risk MSMEs")
    low_risk_percentage: float = Field(0.0, description="Percentage of low risk MSMEs")


class SMAAccountsSummary(BaseModel):
    """SMA accounts summary with RBI classification"""
    standard_accounts: int = Field(0, description="Standard accounts count")
    sma_0_count: int = Field(0, description="SMA-0 accounts (1-30 days overdue)")
    sma_1_count: int = Field(0, description="SMA-1 accounts (31-60 days overdue)")
    sma_2_count: int = Field(0, description="SMA-2 accounts (61-90 days overdue)")
    total_overdue_amount: float = Field(0.0, description="Total overdue amount")
    provision_required: float = Field(0.0, description="Total provision required")


class BusinessTypeDistribution(BaseModel):
    """Business type distribution statistics"""
    retail_count: int = Field(0, description="Retail businesses count")
    retail_percentage: float = Field(0.0, description="Retail businesses percentage")
    manufacturing_count: int = Field(0, description="Manufacturing businesses count")
    manufacturing_percentage: float = Field(0.0, description="Manufacturing businesses percentage")
    services_count: int = Field(0, description="Services businesses count")
    services_percentage: float = Field(0.0, description="Services businesses percentage")
    b2b_count: int = Field(0, description="B2B businesses count")
    b2b_percentage: float = Field(0.0, description="B2B businesses percentage")


class PortfolioSummaryReport(BaseModel):
    """Portfolio Summary Report data model"""
    metadata: BaseReportMetadata
    
    # Core metrics
    total_msmes: int = Field(0, description="Total MSMEs in portfolio")
    active_msmes: int = Field(0, description="Active MSMEs count")
    inactive_msmes: int = Field(0, description="Inactive MSMEs count")
    
    # Risk analysis
    risk_distribution: RiskDistribution
    sma_accounts_summary: SMAAccountsSummary
    business_type_distribution: BusinessTypeDistribution
    
    # Portfolio health
    overall_portfolio_health_score: float = Field(0.0, description="Overall portfolio health score (0-100)")
    average_credit_score: float = Field(0.0, description="Average credit score across portfolio")
    portfolio_growth_rate: float = Field(0.0, description="Portfolio growth rate percentage")
    
    # Key insights
    key_insights: List[str] = Field(default_factory=list, description="Key portfolio insights")
    recommendations: List[str] = Field(default_factory=list, description="Portfolio recommendations")


# Risk Exposure Report Models
class GeographicRiskDistribution(BaseModel):
    """Geographic risk distribution by state/district"""
    state: str = Field(..., description="State name")
    district: Optional[str] = Field(None, description="District name")
    msme_count: int = Field(0, description="Number of MSMEs in location")
    high_risk_count: int = Field(0, description="High risk MSMEs in location")
    total_exposure: float = Field(0.0, description="Total exposure amount")
    risk_concentration: float = Field(0.0, description="Risk concentration percentage")


class SectorExposure(BaseModel):
    """Sector-wise exposure breakdown"""
    sector: str = Field(..., description="Business sector")
    msme_count: int = Field(0, description="Number of MSMEs in sector")
    total_exposure: float = Field(0.0, description="Total exposure amount")
    average_risk_score: float = Field(0.0, description="Average risk score for sector")
    concentration_risk: float = Field(0.0, description="Concentration risk percentage")
    regulatory_limit: float = Field(0.0, description="Regulatory exposure limit")
    breach_status: bool = Field(False, description="Whether regulatory limit is breached")


class HighRiskMSME(BaseModel):
    """High risk MSME details"""
    msme_id: str = Field(..., description="MSME identifier")
    name: str = Field(..., description="MSME name")
    business_type: str = Field(..., description="Business type")
    location: str = Field(..., description="MSME location")
    current_score: float = Field(0.0, description="Current credit score")
    risk_factors: List[str] = Field(default_factory=list, description="Specific risk factors")
    exposure_amount: float = Field(0.0, description="Exposure amount")
    days_past_due: int = Field(0, description="Days past due")
    last_review_date: date = Field(..., description="Last review date")


class RiskTrendData(BaseModel):
    """Risk trend analysis data point"""
    period: str = Field(..., description="Time period (YYYY-MM)")
    average_risk_score: float = Field(0.0, description="Average risk score for period")
    high_risk_count: int = Field(0, description="High risk MSMEs count")
    new_defaults: int = Field(0, description="New defaults in period")
    recovered_accounts: int = Field(0, description="Recovered accounts in period")


class RegulatoryThresholdAlert(BaseModel):
    """Regulatory threshold breach alert"""
    threshold_type: str = Field(..., description="Type of regulatory threshold")
    current_value: float = Field(0.0, description="Current value")
    threshold_limit: float = Field(0.0, description="Regulatory limit")
    breach_percentage: float = Field(0.0, description="Percentage of breach")
    severity: str = Field(..., description="Severity level (low/medium/high)")
    action_required: str = Field(..., description="Required action")


class RiskExposureReport(BaseModel):
    """Risk Exposure Report data model"""
    metadata: BaseReportMetadata
    
    # Geographic analysis
    geographic_distribution: List[GeographicRiskDistribution]
    top_risk_states: List[str] = Field(default_factory=list, description="Top risk states")
    
    # Sector analysis
    sector_exposure: List[SectorExposure]
    concentration_risks: List[str] = Field(default_factory=list, description="Concentration risk alerts")
    
    # High risk MSMEs
    top_10_high_risk_msmes: List[HighRiskMSME]
    
    # Trend analysis
    risk_trends_6_months: List[RiskTrendData]
    
    # Regulatory alerts
    regulatory_threshold_alerts: List[RegulatoryThresholdAlert]
    
    # Summary metrics
    total_portfolio_exposure: float = Field(0.0, description="Total portfolio exposure")
    weighted_average_risk: float = Field(0.0, description="Weighted average risk score")
    diversification_index: float = Field(0.0, description="Portfolio diversification index")


# Compliance & Regulatory Report Models
class ComplianceMetric(BaseModel):
    """Individual compliance metric"""
    metric_name: str = Field(..., description="Name of compliance metric")
    current_value: float = Field(0.0, description="Current value")
    target_value: float = Field(0.0, description="Target/required value")
    compliance_status: str = Field(..., description="Compliance status (compliant/non-compliant/warning)")
    last_updated: datetime = Field(..., description="Last update timestamp")


class GSTComplianceStatus(BaseModel):
    """GST compliance status across portfolio"""
    total_msmes_with_gst: int = Field(0, description="Total MSMEs with GST registration")
    compliant_msmes: int = Field(0, description="GST compliant MSMEs")
    non_compliant_msmes: int = Field(0, description="GST non-compliant MSMEs")
    pending_verification: int = Field(0, description="MSMEs with pending GST verification")
    compliance_rate: float = Field(0.0, description="Overall GST compliance rate")


class UdyamRegistrationStatus(BaseModel):
    """Udyam registration verification status"""
    total_msmes: int = Field(0, description="Total MSMEs")
    verified_registrations: int = Field(0, description="Verified Udyam registrations")
    pending_verification: int = Field(0, description="Pending verifications")
    invalid_registrations: int = Field(0, description="Invalid registrations")
    verification_rate: float = Field(0.0, description="Overall verification rate")


class ComplianceRegulatoryReport(BaseModel):
    """Compliance & Regulatory Report data model"""
    metadata: BaseReportMetadata

    # RBI compliance
    sma_classification_breakdown: SMAAccountsSummary
    early_warning_signals_summary: Dict[str, int] = Field(default_factory=dict, description="EWS summary per RBI guidelines")

    # GST compliance
    gst_compliance_status: GSTComplianceStatus

    # Account Aggregator compliance
    aa_data_compliance: Dict[str, Any] = Field(default_factory=dict, description="Account Aggregator compliance indicators")

    # Udyam registration
    udyam_registration_status: UdyamRegistrationStatus

    # Regulatory reporting readiness
    regulatory_readiness_checklist: List[ComplianceMetric]

    # Overall compliance score
    overall_compliance_score: float = Field(0.0, description="Overall compliance score (0-100)")
    compliance_trend: str = Field("stable", description="Compliance trend (improving/stable/declining)")


# Performance Trends Report Models
class MonthlyPerformanceMetric(BaseModel):
    """Monthly performance metrics"""
    month: str = Field(..., description="Month (YYYY-MM)")
    total_msmes: int = Field(0, description="Total MSMEs")
    new_onboarding: int = Field(0, description="New MSMEs onboarded")
    attrition_count: int = Field(0, description="MSMEs that left")
    average_score: float = Field(0.0, description="Average credit score")
    score_improvement_count: int = Field(0, description="MSMEs with score improvement")
    score_deterioration_count: int = Field(0, description="MSMEs with score deterioration")


class SignalGenerationMetrics(BaseModel):
    """Signal generation and resolution metrics"""
    total_signals_generated: int = Field(0, description="Total signals generated")
    signals_by_source: Dict[str, int] = Field(default_factory=dict, description="Signals by source")
    average_resolution_time: float = Field(0.0, description="Average resolution time in hours")
    resolution_rate: float = Field(0.0, description="Signal resolution rate percentage")


class NudgeEffectivenessMetrics(BaseModel):
    """Nudge effectiveness and response metrics"""
    total_nudges_sent: int = Field(0, description="Total nudges sent")
    delivery_rate: float = Field(0.0, description="Nudge delivery rate percentage")
    response_rate: float = Field(0.0, description="Nudge response rate percentage")
    effectiveness_by_medium: Dict[str, float] = Field(default_factory=dict, description="Effectiveness by medium")
    average_response_time: float = Field(0.0, description="Average response time in hours")


class PerformanceTrendsReport(BaseModel):
    """Performance Trends Report data model"""
    metadata: BaseReportMetadata

    # Monthly/quarterly metrics
    monthly_performance: List[MonthlyPerformanceMetric]
    quarterly_summary: Dict[str, Any] = Field(default_factory=dict, description="Quarterly performance summary")

    # MSME lifecycle metrics
    onboarding_vs_attrition_trends: Dict[str, List[int]] = Field(default_factory=dict, description="Onboarding vs attrition trends")

    # Credit score patterns
    score_improvement_patterns: Dict[str, Any] = Field(default_factory=dict, description="Score improvement patterns")
    score_deterioration_patterns: Dict[str, Any] = Field(default_factory=dict, description="Score deterioration patterns")

    # Signal and nudge metrics
    signal_generation_metrics: SignalGenerationMetrics
    nudge_effectiveness_metrics: NudgeEffectivenessMetrics

    # Year-over-year analysis
    yoy_portfolio_growth: float = Field(0.0, description="Year-over-year portfolio growth percentage")
    yoy_score_improvement: float = Field(0.0, description="Year-over-year average score improvement")

    # Key performance indicators
    key_performance_indicators: Dict[str, float] = Field(default_factory=dict, description="Key performance indicators")


# Detailed MSME Profile Report Models
class MSMEParameterScore(BaseModel):
    """Individual parameter score breakdown"""
    parameter_name: str = Field(..., description="Parameter name")
    current_score: float = Field(0.0, description="Current score")
    weight_percentage: float = Field(0.0, description="Weight in overall score")
    contribution_to_score: float = Field(0.0, description="Contribution to overall score")
    trend: str = Field("stable", description="Trend (improving/stable/declining)")
    last_updated: datetime = Field(..., description="Last update timestamp")


class SignalHistoryEntry(BaseModel):
    """Signal history entry"""
    signal_id: str = Field(..., description="Signal identifier")
    signal_type: str = Field(..., description="Signal type")
    source: str = Field(..., description="Signal source")
    generated_at: datetime = Field(..., description="Signal generation timestamp")
    severity: str = Field(..., description="Signal severity")
    status: str = Field(..., description="Signal status")
    resolution_notes: Optional[str] = Field(None, description="Resolution notes")


class NudgeHistoryEntry(BaseModel):
    """Nudge history entry"""
    nudge_id: str = Field(..., description="Nudge identifier")
    nudge_type: str = Field(..., description="Nudge type")
    medium: str = Field(..., description="Nudge medium")
    sent_at: datetime = Field(..., description="Nudge sent timestamp")
    delivery_status: str = Field(..., description="Delivery status")
    response_status: str = Field(..., description="Response status")
    effectiveness_score: float = Field(0.0, description="Effectiveness score")


class CashFlowAnalysis(BaseModel):
    """Cash flow analysis summary"""
    monthly_inflow: float = Field(0.0, description="Average monthly inflow")
    monthly_outflow: float = Field(0.0, description="Average monthly outflow")
    net_cash_flow: float = Field(0.0, description="Net cash flow")
    cash_flow_volatility: float = Field(0.0, description="Cash flow volatility index")
    seasonal_patterns: Dict[str, float] = Field(default_factory=dict, description="Seasonal patterns")


class AuditTrailEntry(BaseModel):
    """Audit trail entry"""
    timestamp: datetime = Field(..., description="Action timestamp")
    action_type: str = Field(..., description="Type of action")
    performed_by: str = Field(..., description="User who performed action")
    details: str = Field(..., description="Action details")
    impact: str = Field(..., description="Impact of action")


class DetailedMSMEProfileReport(BaseModel):
    """Detailed MSME Profile Report data model"""
    metadata: BaseReportMetadata

    # MSME basic info
    msme_id: str = Field(..., description="MSME identifier")
    msme_name: str = Field(..., description="MSME name")
    business_type: str = Field(..., description="Business type")
    location: str = Field(..., description="MSME location")

    # Comprehensive risk assessment
    overall_credit_score: float = Field(0.0, description="Overall credit score")
    risk_band: str = Field(..., description="Risk band classification")
    parameter_scores: List[MSMEParameterScore]

    # Historical data
    signal_history: List[SignalHistoryEntry]
    nudge_history: List[NudgeHistoryEntry]

    # Financial analysis
    cash_flow_analysis: CashFlowAnalysis
    upi_transaction_patterns: Dict[str, Any] = Field(default_factory=dict, description="UPI transaction patterns")

    # SMA classification timeline
    sma_classification_timeline: List[Dict[str, Any]] = Field(default_factory=list, description="SMA classification timeline")
    overdue_payment_tracking: Dict[str, Any] = Field(default_factory=dict, description="Overdue payment tracking")

    # Audit trail
    audit_trail: List[AuditTrailEntry]

    # Recommendations
    risk_mitigation_recommendations: List[str] = Field(default_factory=list, description="Risk mitigation recommendations")
    improvement_opportunities: List[str] = Field(default_factory=list, description="Improvement opportunities")


# Export and Request Models
class ReportGenerationRequest(BaseModel):
    """Request model for report generation"""
    report_type: ReportType = Field(..., description="Type of report to generate")
    reporting_period_start: date = Field(..., description="Start date of reporting period")
    reporting_period_end: date = Field(..., description="End date of reporting period")
    msme_id: Optional[str] = Field(None, description="Specific MSME ID for detailed reports")
    filters: Optional[Dict[str, Any]] = Field(None, description="Additional filters")
    export_format: ReportFormat = Field(ReportFormat.JSON, description="Export format")


class ReportExportResponse(BaseModel):
    """Response model for report export"""
    report_id: str = Field(..., description="Report identifier")
    export_format: ReportFormat = Field(..., description="Export format")
    file_url: Optional[str] = Field(None, description="Download URL for exported file")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    expires_at: Optional[datetime] = Field(None, description="Download link expiration")
    status: ReportStatus = Field(..., description="Export status")
