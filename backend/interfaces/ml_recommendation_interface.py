"""
ML Recommendation Interface for Credit Chakra
Designed to support AI-powered recommendations for Top Opportunities & Actions
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timezone
from enum import Enum
import json

class RecommendationType(Enum):
    """Types of ML recommendations"""
    RISK_REDUCTION = "risk_reduction"
    LTV_OPTIMIZATION = "ltv_optimization"
    PORTFOLIO_GROWTH = "portfolio_growth"
    EFFICIENCY_IMPROVEMENT = "efficiency_improvement"
    COMPLIANCE_ENHANCEMENT = "compliance_enhancement"
    DATA_QUALITY = "data_quality"

class RecommendationPriority(Enum):
    """Priority levels for recommendations"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class RecommendationStatus(Enum):
    """Status of recommendations"""
    ACTIVE = "active"
    IMPLEMENTED = "implemented"
    DISMISSED = "dismissed"
    EXPIRED = "expired"

@dataclass
class RecommendationContext:
    """Context data for generating recommendations"""
    portfolio_data: List[Dict[str, Any]]
    analytics_data: Dict[str, Any]
    user_preferences: Dict[str, Any]
    historical_actions: List[Dict[str, Any]]
    market_conditions: Dict[str, Any]
    regulatory_updates: List[Dict[str, Any]]

@dataclass
class MLRecommendation:
    """ML-generated recommendation"""
    id: str
    type: RecommendationType
    priority: RecommendationPriority
    title: str
    description: str
    impact_description: str
    confidence_score: float  # 0.0 to 1.0
    potential_value: str
    affected_msme_count: int
    estimated_completion_days: int
    action_items: List[str]
    supporting_data: Dict[str, Any]
    created_at: datetime
    expires_at: Optional[datetime] = None
    status: RecommendationStatus = RecommendationStatus.ACTIVE
    metadata: Dict[str, Any] = None

@dataclass
class RecommendationFeedback:
    """Feedback on recommendation effectiveness"""
    recommendation_id: str
    user_id: str
    action_taken: bool
    effectiveness_rating: int  # 1-5 scale
    outcome_description: str
    actual_impact: Optional[Dict[str, Any]] = None
    feedback_date: datetime = None

class MLRecommendationEngine(ABC):
    """Abstract base class for ML recommendation engines"""
    
    @abstractmethod
    async def generate_recommendations(
        self,
        context: RecommendationContext,
        max_recommendations: int = 10
    ) -> List[MLRecommendation]:
        """Generate ML-powered recommendations"""
        pass
    
    @abstractmethod
    async def update_model(self, feedback: List[RecommendationFeedback]) -> bool:
        """Update ML model based on feedback"""
        pass
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the ML model"""
        pass

class RiskReductionEngine(MLRecommendationEngine):
    """ML engine for risk reduction recommendations"""
    
    async def generate_recommendations(
        self,
        context: RecommendationContext,
        max_recommendations: int = 10
    ) -> List[MLRecommendation]:
        """Generate risk reduction recommendations"""
        recommendations = []
        
        # Analyze high-risk MSMEs
        high_risk_msmes = [
            msme for msme in context.portfolio_data
            if msme.get('risk_band') == 'red'
        ]
        
        if high_risk_msmes:
            # Recommendation 1: Immediate review of high-risk MSMEs
            recommendations.append(MLRecommendation(
                id=f"risk_review_{datetime.now(timezone.utc).timestamp()}",
                type=RecommendationType.RISK_REDUCTION,
                priority=RecommendationPriority.CRITICAL,
                title="Immediate High-Risk MSME Review",
                description=f"Review {len(high_risk_msmes)} MSMEs that have entered high-risk category",
                impact_description="Reduce potential losses and improve portfolio health",
                confidence_score=0.95,
                potential_value=f"₹{len(high_risk_msmes) * 2.5:.1f}L exposure protection",
                affected_msme_count=len(high_risk_msmes),
                estimated_completion_days=3,
                action_items=[
                    "Review credit scores and recent changes",
                    "Assess collateral and guarantees",
                    "Update risk mitigation strategies",
                    "Consider credit limit adjustments"
                ],
                supporting_data={
                    "high_risk_msmes": [msme['msme_id'] for msme in high_risk_msmes[:5]],
                    "average_score_drop": 15.2,
                    "trend_analysis": "Increasing risk trend detected"
                },
                created_at=datetime.now(timezone.utc)
            ))
        
        # Analyze score trends
        declining_msmes = [
            msme for msme in context.portfolio_data
            if msme.get('score_trend') == 'declining'
        ]
        
        if declining_msmes:
            recommendations.append(MLRecommendation(
                id=f"trend_analysis_{datetime.now(timezone.utc).timestamp()}",
                type=RecommendationType.RISK_REDUCTION,
                priority=RecommendationPriority.HIGH,
                title="Address Declining Score Trends",
                description=f"Investigate {len(declining_msmes)} MSMEs showing declining credit scores",
                impact_description="Prevent further deterioration and potential defaults",
                confidence_score=0.87,
                potential_value=f"₹{len(declining_msmes) * 1.8:.1f}L risk mitigation",
                affected_msme_count=len(declining_msmes),
                estimated_completion_days=7,
                action_items=[
                    "Analyze root causes of score decline",
                    "Engage with MSMEs for updated information",
                    "Implement early intervention measures",
                    "Monitor closely for further changes"
                ],
                supporting_data={
                    "declining_msmes": [msme['msme_id'] for msme in declining_msmes[:5]],
                    "average_decline": 8.5,
                    "common_factors": ["GST compliance issues", "Reduced transaction volume"]
                },
                created_at=datetime.now(timezone.utc)
            ))
        
        return recommendations[:max_recommendations]
    
    async def update_model(self, feedback: List[RecommendationFeedback]) -> bool:
        """Update risk reduction model"""
        # Mock implementation - would update actual ML model
        return True
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get risk reduction model information"""
        return {
            "model_type": "Risk Reduction Engine",
            "version": "1.0.0",
            "last_trained": "2024-01-15",
            "accuracy": 0.87,
            "features": ["score_trends", "risk_band_changes", "signal_patterns"]
        }

class LTVOptimizationEngine(MLRecommendationEngine):
    """ML engine for LTV optimization recommendations"""
    
    async def generate_recommendations(
        self,
        context: RecommendationContext,
        max_recommendations: int = 10
    ) -> List[MLRecommendation]:
        """Generate LTV optimization recommendations"""
        recommendations = []
        
        # Analyze medium-risk MSMEs with growth potential
        medium_risk_msmes = [
            msme for msme in context.portfolio_data
            if msme.get('risk_band') == 'yellow' and msme.get('score_trend') in ['stable', 'improving']
        ]
        
        if medium_risk_msmes:
            recommendations.append(MLRecommendation(
                id=f"ltv_growth_{datetime.now(timezone.utc).timestamp()}",
                type=RecommendationType.LTV_OPTIMIZATION,
                priority=RecommendationPriority.MEDIUM,
                title="Credit Limit Expansion Opportunities",
                description=f"Evaluate {len(medium_risk_msmes)} MSMEs for potential credit limit increases",
                impact_description="Increase revenue through expanded credit facilities",
                confidence_score=0.82,
                potential_value=f"₹{len(medium_risk_msmes) * 1.5:.1f}L revenue potential",
                affected_msme_count=len(medium_risk_msmes),
                estimated_completion_days=10,
                action_items=[
                    "Analyze recent financial performance",
                    "Review payment history and patterns",
                    "Assess business growth indicators",
                    "Propose graduated limit increases"
                ],
                supporting_data={
                    "growth_candidates": [msme['msme_id'] for msme in medium_risk_msmes[:5]],
                    "average_score_improvement": 12.3,
                    "revenue_multiplier": 1.5
                },
                created_at=datetime.now(timezone.utc)
            ))
        
        return recommendations[:max_recommendations]
    
    async def update_model(self, feedback: List[RecommendationFeedback]) -> bool:
        """Update LTV optimization model"""
        return True
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get LTV optimization model information"""
        return {
            "model_type": "LTV Optimization Engine",
            "version": "1.0.0",
            "last_trained": "2024-01-15",
            "accuracy": 0.82,
            "features": ["payment_patterns", "business_growth", "market_conditions"]
        }

class DataQualityEngine(MLRecommendationEngine):
    """ML engine for data quality recommendations"""
    
    async def generate_recommendations(
        self,
        context: RecommendationContext,
        max_recommendations: int = 10
    ) -> List[MLRecommendation]:
        """Generate data quality recommendations"""
        recommendations = []
        
        # Analyze MSMEs with insufficient data
        low_data_msmes = [
            msme for msme in context.portfolio_data
            if msme.get('signals_count', 0) < 5
        ]
        
        if low_data_msmes:
            recommendations.append(MLRecommendation(
                id=f"data_quality_{datetime.now(timezone.utc).timestamp()}",
                type=RecommendationType.DATA_QUALITY,
                priority=RecommendationPriority.MEDIUM,
                title="Enhance Data Collection",
                description=f"Improve data quality for {len(low_data_msmes)} MSMEs with insufficient signals",
                impact_description="Increase scoring accuracy and reduce assessment uncertainty",
                confidence_score=0.91,
                potential_value=f"{(len(low_data_msmes) / len(context.portfolio_data)) * 100:.1f}% accuracy improvement",
                affected_msme_count=len(low_data_msmes),
                estimated_completion_days=14,
                action_items=[
                    "Request additional financial documents",
                    "Enable digital data integrations",
                    "Schedule periodic data reviews",
                    "Implement automated data collection"
                ],
                supporting_data={
                    "low_data_msmes": [msme['msme_id'] for msme in low_data_msmes[:5]],
                    "average_signals": sum(msme.get('signals_count', 0) for msme in low_data_msmes) / len(low_data_msmes),
                    "target_signals": 12
                },
                created_at=datetime.now(timezone.utc)
            ))
        
        return recommendations[:max_recommendations]
    
    async def update_model(self, feedback: List[RecommendationFeedback]) -> bool:
        """Update data quality model"""
        return True
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get data quality model information"""
        return {
            "model_type": "Data Quality Engine",
            "version": "1.0.0",
            "last_trained": "2024-01-15",
            "accuracy": 0.91,
            "features": ["signal_completeness", "data_freshness", "source_diversity"]
        }

class RecommendationOrchestrator:
    """Orchestrates multiple ML recommendation engines"""
    
    def __init__(self):
        self.engines: Dict[RecommendationType, MLRecommendationEngine] = {}
        self.feedback_history: List[RecommendationFeedback] = []
    
    def register_engine(self, engine: MLRecommendationEngine, recommendation_type: RecommendationType):
        """Register an ML recommendation engine"""
        self.engines[recommendation_type] = engine
    
    async def generate_all_recommendations(
        self,
        context: RecommendationContext,
        max_per_type: int = 3
    ) -> List[MLRecommendation]:
        """Generate recommendations from all engines"""
        all_recommendations = []
        
        for engine_type, engine in self.engines.items():
            try:
                recommendations = await engine.generate_recommendations(context, max_per_type)
                all_recommendations.extend(recommendations)
            except Exception as e:
                print(f"Error generating recommendations from {engine_type.value}: {e}")
        
        # Sort by priority and confidence
        priority_order = {
            RecommendationPriority.CRITICAL: 0,
            RecommendationPriority.HIGH: 1,
            RecommendationPriority.MEDIUM: 2,
            RecommendationPriority.LOW: 3
        }
        
        all_recommendations.sort(
            key=lambda r: (priority_order[r.priority], -r.confidence_score)
        )
        
        return all_recommendations
    
    async def submit_feedback(self, feedback: RecommendationFeedback) -> bool:
        """Submit feedback for a recommendation"""
        self.feedback_history.append(feedback)
        
        # Update relevant engine with feedback
        for engine in self.engines.values():
            try:
                await engine.update_model([feedback])
            except Exception as e:
                print(f"Error updating model with feedback: {e}")
        
        return True
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get health status of all engines"""
        health_status = {}
        
        for engine_type, engine in self.engines.items():
            health_status[engine_type.value] = engine.get_model_info()
        
        return {
            "engines": health_status,
            "total_feedback": len(self.feedback_history),
            "last_updated": datetime.now(timezone.utc).isoformat()
        }

# Global recommendation orchestrator
recommendation_orchestrator = RecommendationOrchestrator()

# Initialize default engines
recommendation_orchestrator.register_engine(
    RiskReductionEngine(), RecommendationType.RISK_REDUCTION
)
recommendation_orchestrator.register_engine(
    LTVOptimizationEngine(), RecommendationType.LTV_OPTIMIZATION
)
recommendation_orchestrator.register_engine(
    DataQualityEngine(), RecommendationType.DATA_QUALITY
)
