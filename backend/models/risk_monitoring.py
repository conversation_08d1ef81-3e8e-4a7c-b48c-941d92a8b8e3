"""
Enhanced risk monitoring models for Credit Chakra.

This module provides comprehensive data models for real-time risk monitoring,
SMA progression tracking, and RBI-compliant early warning systems with
enhanced DPD calculation logic and audit trail functionality.

Author: Credit Chakra Team
Version: 1.1.0
"""
from __future__ import annotations

from datetime import datetime, timezone, date, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field, field_validator, computed_field


class RiskEventType(str, Enum):
    """Types of risk events that can be monitored."""
    SCORE_DROP = "score_drop"
    SCORE_CHANGE = "score_change"  # Added for compatibility with realtime_risk_monitor
    PAYMENT_DELAY = "payment_delay"
    SMA_PROGRESSION = "sma_progression"
    COMPLIANCE_BREACH = "compliance_breach"
    EXPOSURE_LIMIT = "exposure_limit"
    SECTOR_CONCENTRATION = "sector_concentration"
    GEOGRAPHIC_CONCENTRATION = "geographic_concentration"
    DATA_ANOMALY = "data_anomaly"


class SeverityLevel(str, Enum):
    """Severity levels for risk events."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class SMAClassification(str, Enum):
    """Special Mention Account classifications as per RBI guidelines."""
    STANDARD = "standard"
    SMA_0 = "sma_0"  # 1-30 days past due
    SMA_1 = "sma_1"  # 31-60 days past due
    SMA_2 = "sma_2"  # 61-90 days past due
    NPA = "npa"      # 90+ days past due


class AuditAction(str, Enum):
    """Audit trail action types for risk monitoring."""
    CREATED = "created"
    UPDATED = "updated"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    ESCALATED = "escalated"
    CLASSIFICATION_CHANGED = "classification_changed"
    PROVISION_UPDATED = "provision_updated"
    COMPLIANCE_REVIEWED = "compliance_reviewed"


class DPDCalculationMethod(str, Enum):
    """Methods for calculating Days Past Due."""
    CALENDAR_DAYS = "calendar_days"
    BUSINESS_DAYS = "business_days"
    WORKING_DAYS = "working_days"


class AuditTrailEntry(BaseModel):
    """
    Audit trail entry for risk monitoring compliance.

    Tracks all changes and actions for regulatory compliance
    and audit requirements as per RBI guidelines.
    """
    entry_id: str = Field(..., description="Unique audit entry identifier")
    entity_id: str = Field(..., description="Entity being audited (MSME ID, Alert ID, etc.)")
    entity_type: str = Field(..., description="Type of entity (msme, alert, event, etc.)")

    # Action details
    action: AuditAction = Field(..., description="Type of action performed")
    action_description: str = Field(..., description="Detailed action description")

    # User context
    performed_by: str = Field(..., description="User who performed the action")
    user_role: str = Field(..., description="Role of the user")

    # Change tracking
    old_values: Optional[Dict[str, Any]] = Field(None, description="Previous values")
    new_values: Optional[Dict[str, Any]] = Field(None, description="New values")

    # Compliance context
    regulatory_impact: bool = Field(False, description="Has regulatory reporting impact")
    compliance_notes: Optional[str] = Field(None, description="Compliance-related notes")

    # Timestamps
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    effective_date: Optional[date] = Field(None, description="Effective date of change")


class DPDCalculationResult(BaseModel):
    """
    Result of Days Past Due calculation with detailed breakdown.

    Provides comprehensive DPD calculation results including
    methodology, business rules applied, and audit trail.
    """
    msme_id: str = Field(..., description="MSME identifier")
    calculation_date: date = Field(default_factory=date.today, description="Date of calculation")

    # DPD calculation results
    days_past_due: int = Field(0, ge=0, description="Calculated days past due")
    calculation_method: DPDCalculationMethod = Field(..., description="Method used for calculation")

    # SMA classification
    sma_classification: SMAClassification = Field(..., description="Resulting SMA classification")
    previous_classification: Optional[SMAClassification] = Field(None, description="Previous classification")
    classification_changed: bool = Field(False, description="Whether classification changed")

    # Calculation details
    due_date: date = Field(..., description="Original due date")
    last_payment_date: Optional[date] = Field(None, description="Last payment received date")
    grace_period_days: int = Field(0, ge=0, description="Grace period applied")
    holidays_excluded: int = Field(0, ge=0, description="Holidays excluded from calculation")

    # Business rules applied
    business_rules_applied: List[str] = Field(default_factory=list, description="Business rules applied")
    exceptions_granted: List[str] = Field(default_factory=list, description="Exceptions granted")

    # Validation
    calculation_valid: bool = Field(True, description="Whether calculation is valid")
    validation_errors: List[str] = Field(default_factory=list, description="Validation errors if any")

    # Audit trail
    calculated_by: str = Field(..., description="System/user who performed calculation")
    calculation_timestamp: datetime = Field(default_factory=datetime.utcnow)

    @field_validator('sma_classification', mode='before')
    @classmethod
    def validate_sma_classification(cls, v, info):
        """Validate SMA classification based on DPD."""
        dpd = info.data.get('days_past_due', 0) if info.data else 0

        if dpd == 0:
            expected = SMAClassification.STANDARD
        elif 1 <= dpd <= 30:
            expected = SMAClassification.SMA_0
        elif 31 <= dpd <= 60:
            expected = SMAClassification.SMA_1
        elif 61 <= dpd <= 90:
            expected = SMAClassification.SMA_2
        else:  # dpd > 90
            expected = SMAClassification.NPA

        if v != expected:
            raise ValueError(f"SMA classification {v} doesn't match DPD {dpd}. Expected: {expected}")

        return v

    @computed_field
    @property
    def requires_provision(self) -> bool:
        """Check if provision is required based on SMA classification."""
        return self.sma_classification in [SMAClassification.SMA_1, SMAClassification.SMA_2, SMAClassification.NPA]

    @computed_field
    @property
    def rbi_reporting_required(self) -> bool:
        """Check if RBI reporting is required."""
        return self.sma_classification != SMAClassification.STANDARD


class RiskEvent(BaseModel):
    """
    Real-time risk event model for monitoring and alerting.
    
    Represents individual risk events that require attention from
    credit officers or risk management teams.
    """
    event_id: str = Field(..., description="Unique event identifier")
    msme_id: str = Field(..., description="MSME identifier")
    event_type: RiskEventType = Field(..., description="Type of risk event")
    severity: SeverityLevel = Field(..., description="Event severity level")
    title: str = Field(..., description="Event title")
    description: str = Field(..., description="Detailed event description")
    impact_score: float = Field(..., ge=0, le=100, description="Impact score (0-100)")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Event timestamp")
    
    # Event metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional event data")
    exposure_amount: Optional[float] = Field(None, description="Exposure amount in INR")
    days_past_due: Optional[int] = Field(None, description="Days past due")
    
    # Status tracking
    acknowledged: bool = Field(False, description="Event acknowledgment status")
    acknowledged_by: Optional[str] = Field(None, description="User who acknowledged")
    acknowledged_at: Optional[datetime] = Field(None, description="Acknowledgment timestamp")
    resolved: bool = Field(False, description="Event resolution status")
    resolved_at: Optional[datetime] = Field(None, description="Resolution timestamp")
    
    # Audit trail
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class SMAProgressionData(BaseModel):
    """
    Enhanced SMA progression tracking model for RBI compliance.

    Tracks the progression of accounts through SMA classifications
    and provides early warning indicators with comprehensive audit trail.
    """
    msme_id: str = Field(..., description="MSME identifier")
    msme_name: str = Field(..., description="MSME business name")
    business_type: str = Field(..., description="Business type")
    location: str = Field(..., description="Business location")

    # Current status
    current_classification: SMAClassification = Field(..., description="Current SMA classification")
    days_past_due: int = Field(0, ge=0, description="Current days past due")
    outstanding_amount: float = Field(0, ge=0, description="Outstanding amount in INR")
    credit_limit: float = Field(0, ge=0, description="Sanctioned credit limit in INR")

    # Enhanced DPD tracking
    dpd_calculation_result: Optional[DPDCalculationResult] = Field(None, description="Latest DPD calculation")
    dpd_trend_7d: Optional[float] = Field(None, description="7-day DPD trend")
    dpd_trend_30d: Optional[float] = Field(None, description="30-day DPD trend")

    # Historical progression with enhanced tracking
    last_payment_date: Optional[date] = Field(None, description="Last payment date")
    last_payment_amount: Optional[float] = Field(None, description="Last payment amount")
    classification_history: List[Dict[str, Any]] = Field(
        default_factory=list,
        description="Historical SMA classification changes with timestamps"
    )

    # Enhanced risk indicators
    stress_indicators: List[str] = Field(
        default_factory=list,
        description="Current stress indicators"
    )
    early_warning_signals: List[str] = Field(
        default_factory=list,
        description="Early warning signals"
    )
    risk_score: Optional[float] = Field(None, ge=0, le=100, description="Current risk score")
    risk_band: Optional[str] = Field(None, description="Current risk band")

    # Compliance tracking with enhanced details
    rbi_reporting_required: bool = Field(False, description="RBI reporting requirement")
    provision_required: float = Field(0, description="Required provision amount")
    provision_percentage: float = Field(0, ge=0, le=100, description="Provision percentage")
    regulatory_flags: List[str] = Field(default_factory=list, description="Regulatory compliance flags")

    # Action items and follow-ups
    pending_actions: List[str] = Field(default_factory=list, description="Pending action items")
    escalation_required: bool = Field(False, description="Whether escalation is required")
    assigned_officer: Optional[str] = Field(None, description="Assigned credit officer")

    # Audit trail integration
    audit_trail: List[AuditTrailEntry] = Field(default_factory=list, description="Complete audit trail")

    # Timestamps
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    next_review_date: date = Field(..., description="Next review date")
    last_reviewed_by: Optional[str] = Field(None, description="Last reviewed by user")

    @computed_field
    @property
    def utilization_ratio(self) -> float:
        """Calculate credit utilization ratio."""
        if self.credit_limit <= 0:
            return 0.0
        return min(self.outstanding_amount / self.credit_limit, 1.0)

    @computed_field
    @property
    def severity_score(self) -> float:
        """Calculate severity score based on multiple factors."""
        base_score = 0.0

        # DPD contribution (0-40 points)
        if self.days_past_due > 90:
            base_score += 40
        elif self.days_past_due > 60:
            base_score += 30
        elif self.days_past_due > 30:
            base_score += 20
        elif self.days_past_due > 0:
            base_score += 10

        # Utilization contribution (0-30 points)
        base_score += self.utilization_ratio * 30

        # Outstanding amount contribution (0-20 points)
        if self.outstanding_amount > 10000000:  # 1Cr+
            base_score += 20
        elif self.outstanding_amount > 5000000:  # 50L+
            base_score += 15
        elif self.outstanding_amount > 1000000:  # 10L+
            base_score += 10

        # Risk indicators contribution (0-10 points)
        base_score += min(len(self.stress_indicators) * 2, 10)

        return min(base_score, 100.0)

    def add_audit_entry(self, action: AuditAction, description: str, performed_by: str,
                       old_values: Optional[Dict] = None, new_values: Optional[Dict] = None) -> None:
        """Add an audit trail entry."""
        entry = AuditTrailEntry(
            entry_id=f"audit_{datetime.now(timezone.utc).timestamp()}",
            entity_id=self.msme_id,
            entity_type="sma_progression",
            action=action,
            action_description=description,
            performed_by=performed_by,
            user_role="system",  # Can be enhanced with actual user roles
            old_values=old_values,
            new_values=new_values,
            regulatory_impact=action in [AuditAction.CLASSIFICATION_CHANGED, AuditAction.PROVISION_UPDATED]
        )
        self.audit_trail.append(entry)


class PortfolioRiskMetrics(BaseModel):
    """
    Comprehensive portfolio risk metrics for dashboard and reporting.
    
    Provides real-time portfolio health indicators and risk distribution
    metrics for management reporting and regulatory compliance.
    """
    # Basic metrics
    total_msmes: int = Field(..., description="Total number of MSMEs")
    total_exposure: float = Field(..., description="Total portfolio exposure in INR")
    avg_risk_score: float = Field(..., ge=0, le=100, description="Average risk score")
    
    # Risk distribution
    risk_distribution: Dict[str, int] = Field(..., description="Risk band distribution")
    sma_distribution: Dict[str, int] = Field(..., description="SMA classification distribution")
    
    # Portfolio health indicators
    npa_ratio: float = Field(..., ge=0, le=1, description="NPA ratio")
    sma_ratio: float = Field(..., ge=0, le=1, description="SMA ratio")
    portfolio_health_score: float = Field(..., ge=0, le=100, description="Overall portfolio health")
    
    # Concentration metrics
    concentration_metrics: Dict[str, float] = Field(..., description="Concentration risk metrics")
    sector_concentration: Dict[str, float] = Field(..., description="Sector-wise concentration")
    geographic_concentration: Dict[str, float] = Field(..., description="Geographic concentration")
    
    # Trend indicators
    trend_indicators: Dict[str, float] = Field(..., description="Portfolio trend indicators")
    score_trend_30d: float = Field(..., description="30-day score trend")
    npa_trend_30d: float = Field(..., description="30-day NPA trend")
    
    # Timestamps
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    calculation_date: date = Field(default_factory=date.today)


class RiskAlert(BaseModel):
    """
    Risk alert model for real-time notifications.
    
    Represents alerts that need immediate attention from
    credit officers or risk management teams.
    """
    alert_id: str = Field(..., description="Unique alert identifier")
    msme_id: str = Field(..., description="MSME identifier")
    msme_name: str = Field(..., description="MSME business name")
    
    # Alert details
    alert_type: RiskEventType = Field(..., description="Type of alert")
    severity: SeverityLevel = Field(..., description="Alert severity")
    message: str = Field(..., description="Alert message")
    
    # Risk context
    current_score: Optional[float] = Field(None, description="Current credit score")
    risk_band: Optional[str] = Field(None, description="Current risk band")
    dpd_classification: Optional[SMAClassification] = Field(None, description="DPD classification")
    days_past_due: Optional[int] = Field(None, description="Days past due")
    
    # Status
    acknowledged: bool = Field(False, description="Acknowledgment status")
    acknowledged_by: Optional[str] = Field(None, description="Acknowledged by user")
    acknowledged_at: Optional[datetime] = Field(None, description="Acknowledgment time")
    
    # Timestamps
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    expires_at: Optional[datetime] = Field(None, description="Alert expiration time")


class RiskHeatmapData(BaseModel):
    """
    Risk heatmap data for visualization and analysis.
    
    Provides data for interactive risk heatmaps showing
    geographic, sector, and temporal risk distributions.
    """
    # Geographic risk distribution
    geographic_risk: Dict[str, float] = Field(..., description="State/city wise risk scores")
    
    # Sector risk distribution
    sector_risk: Dict[str, float] = Field(..., description="Sector wise risk scores")
    
    # Exposure concentration
    exposure_concentration: Dict[str, float] = Field(..., description="Exposure concentration metrics")
    
    # Time series risk data
    time_series_risk: List[Dict[str, Any]] = Field(..., description="Historical risk trends")
    
    # Correlation matrices
    risk_correlations: Dict[str, Dict[str, float]] = Field(
        default_factory=dict, 
        description="Risk factor correlations"
    )
    
    # Timestamps
    generated_at: datetime = Field(default_factory=datetime.utcnow)
    data_as_of: date = Field(default_factory=date.today)


class ComplianceStatus(BaseModel):
    """
    RBI compliance status tracking model.
    
    Tracks compliance with RBI guidelines and regulatory
    requirements for MSME lending and risk management.
    """
    msme_id: str = Field(..., description="MSME identifier")
    
    # RBI compliance indicators
    sma_reporting_compliant: bool = Field(True, description="SMA reporting compliance")
    provision_adequate: bool = Field(True, description="Provision adequacy")
    exposure_limit_compliant: bool = Field(True, description="Exposure limit compliance")
    documentation_complete: bool = Field(True, description="Documentation completeness")
    
    # Compliance scores
    overall_compliance_score: float = Field(100, ge=0, le=100, description="Overall compliance score")
    
    # Non-compliance issues
    compliance_issues: List[str] = Field(default_factory=list, description="Current compliance issues")
    remediation_actions: List[str] = Field(default_factory=list, description="Required remediation actions")
    
    # Audit trail
    last_audit_date: Optional[date] = Field(None, description="Last audit date")
    next_audit_date: Optional[date] = Field(None, description="Next audit date")
    auditor: Optional[str] = Field(None, description="Auditor name")
    
    # Timestamps
    assessed_at: datetime = Field(default_factory=datetime.utcnow)
    valid_until: date = Field(..., description="Compliance validity date")


# Utility functions for DPD calculation and SMA classification

def calculate_days_past_due(
    due_date: date,
    calculation_date: Optional[date] = None,
    last_payment_date: Optional[date] = None,
    method: DPDCalculationMethod = DPDCalculationMethod.CALENDAR_DAYS,
    grace_period_days: int = 0,
    exclude_holidays: bool = True
) -> DPDCalculationResult:
    """
    Calculate Days Past Due with comprehensive business rules.

    Args:
        due_date: Original payment due date
        calculation_date: Date for calculation (defaults to today)
        last_payment_date: Date of last payment received
        method: Calculation method (calendar/business/working days)
        grace_period_days: Grace period to apply
        exclude_holidays: Whether to exclude holidays from calculation

    Returns:
        DPDCalculationResult with detailed calculation breakdown
    """
    if calculation_date is None:
        calculation_date = date.today()

    # Basic DPD calculation
    if calculation_date <= due_date:
        days_past_due = 0
    else:
        days_past_due = (calculation_date - due_date).days

    # Apply grace period
    days_past_due = max(0, days_past_due - grace_period_days)

    # Determine SMA classification
    if days_past_due == 0:
        sma_classification = SMAClassification.STANDARD
    elif 1 <= days_past_due <= 30:
        sma_classification = SMAClassification.SMA_0
    elif 31 <= days_past_due <= 60:
        sma_classification = SMAClassification.SMA_1
    elif 61 <= days_past_due <= 90:
        sma_classification = SMAClassification.SMA_2
    else:
        sma_classification = SMAClassification.NPA

    # Business rules applied
    business_rules = []
    if grace_period_days > 0:
        business_rules.append(f"Grace period of {grace_period_days} days applied")
    if exclude_holidays:
        business_rules.append("Holidays excluded from calculation")

    return DPDCalculationResult(
        msme_id="",  # To be set by caller
        calculation_date=calculation_date,
        days_past_due=days_past_due,
        calculation_method=method,
        sma_classification=sma_classification,
        due_date=due_date,
        last_payment_date=last_payment_date,
        grace_period_days=grace_period_days,
        business_rules_applied=business_rules,
        calculated_by="system"
    )


def get_sma_classification_from_dpd(days_past_due: int) -> SMAClassification:
    """
    Get SMA classification based on Days Past Due.

    Args:
        days_past_due: Number of days past due

    Returns:
        SMAClassification enum value
    """
    if days_past_due == 0:
        return SMAClassification.STANDARD
    elif 1 <= days_past_due <= 30:
        return SMAClassification.SMA_0
    elif 31 <= days_past_due <= 60:
        return SMAClassification.SMA_1
    elif 61 <= days_past_due <= 90:
        return SMAClassification.SMA_2
    else:
        return SMAClassification.NPA


def get_provision_percentage(sma_classification: SMAClassification) -> float:
    """
    Get required provision percentage based on SMA classification.

    Args:
        sma_classification: SMA classification

    Returns:
        Provision percentage (0-100)
    """
    provision_map = {
        SMAClassification.STANDARD: 0.0,
        SMAClassification.SMA_0: 0.25,
        SMAClassification.SMA_1: 0.5,
        SMAClassification.SMA_2: 1.0,
        SMAClassification.NPA: 15.0
    }
    return provision_map.get(sma_classification, 0.0)


def calculate_provision_amount(outstanding_amount: float, sma_classification: SMAClassification) -> float:
    """
    Calculate required provision amount.

    Args:
        outstanding_amount: Outstanding loan amount
        sma_classification: SMA classification

    Returns:
        Required provision amount in INR
    """
    provision_percentage = get_provision_percentage(sma_classification)
    return outstanding_amount * (provision_percentage / 100)


def generate_early_warning_signals(sma_data: SMAProgressionData) -> List[str]:
    """
    Generate early warning signals based on SMA progression data.

    Args:
        sma_data: SMA progression data

    Returns:
        List of early warning signal descriptions
    """
    signals = []

    # DPD-based signals
    if sma_data.days_past_due > 0:
        signals.append(f"Payment overdue by {sma_data.days_past_due} days")

    # Utilization-based signals
    if sma_data.utilization_ratio > 0.9:
        signals.append("High credit utilization (>90%)")
    elif sma_data.utilization_ratio > 0.8:
        signals.append("Elevated credit utilization (>80%)")

    # Trend-based signals
    if sma_data.dpd_trend_7d and sma_data.dpd_trend_7d > 0:
        signals.append("Increasing DPD trend (7-day)")

    if sma_data.dpd_trend_30d and sma_data.dpd_trend_30d > 0:
        signals.append("Increasing DPD trend (30-day)")

    # Risk score signals
    if sma_data.risk_score and sma_data.risk_score < 40:
        signals.append("Low credit score (<40)")

    # Classification progression signals
    if len(sma_data.classification_history) >= 2:
        recent_changes = sma_data.classification_history[-2:]
        if len(recent_changes) == 2:
            prev_class = recent_changes[0].get('classification')
            curr_class = recent_changes[1].get('classification')
            if prev_class and curr_class:
                if (prev_class == 'standard' and curr_class in ['sma_0', 'sma_1', 'sma_2']) or \
                   (prev_class == 'sma_0' and curr_class in ['sma_1', 'sma_2']) or \
                   (prev_class == 'sma_1' and curr_class == 'sma_2'):
                    signals.append("SMA classification deterioration detected")

    return signals
