'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Copilot Error Boundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error!} retry={this.handleRetry} />;
      }

      return (
        <div className="flex flex-col items-center justify-center h-full p-8 text-center">
          <div className="flex items-center gap-3 mb-4">
            <AlertTriangle className="h-8 w-8 text-red-500" />
            <h2 className="text-xl font-semibold text-foreground">Something went wrong</h2>
          </div>
          
          <p className="text-muted-foreground mb-6 max-w-md">
            The AI Copilot encountered an unexpected error. This has been logged and will be investigated.
          </p>
          
          <div className="space-y-3">
            <Button onClick={this.handleRetry} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Try Again
            </Button>
            
            <details className="text-left">
              <summary className="text-sm text-muted-foreground cursor-pointer hover:text-foreground">
                Technical Details
              </summary>
              <pre className="mt-2 p-3 bg-muted rounded text-xs overflow-auto max-w-md">
                {this.state.error?.message}
                {this.state.error?.stack && (
                  <>
                    {'\n\nStack Trace:\n'}
                    {this.state.error.stack}
                  </>
                )}
              </pre>
            </details>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Custom fallback component for copilot-specific errors
export const CopilotErrorFallback: React.FC<{ error: Error; retry: () => void }> = ({ 
  error, 
  retry 
}) => (
  <div className="flex flex-col items-center justify-center h-full p-8 text-center bg-background">
    <div className="flex items-center gap-3 mb-4">
      <AlertTriangle className="h-8 w-8 text-amber-500" />
      <h2 className="text-xl font-semibold text-foreground">AI Copilot Unavailable</h2>
    </div>
    
    <p className="text-muted-foreground mb-6 max-w-md">
      The AI Copilot is temporarily unavailable. You can still access your portfolio data through the main dashboard.
    </p>
    
    <div className="space-y-3">
      <Button onClick={retry} variant="default" className="flex items-center gap-2">
        <RefreshCw className="h-4 w-4" />
        Retry Connection
      </Button>
      
      <Button variant="outline" onClick={() => window.location.href = '/'}>
        Return to Dashboard
      </Button>
    </div>
    
    {process.env.NODE_ENV === 'development' && (
      <details className="mt-6 text-left">
        <summary className="text-sm text-muted-foreground cursor-pointer hover:text-foreground">
          Error Details (Development)
        </summary>
        <pre className="mt-2 p-3 bg-muted rounded text-xs overflow-auto max-w-md">
          {error.message}
          {error.stack && `\n\n${error.stack}`}
        </pre>
      </details>
    )}
  </div>
);

export default ErrorBoundary;
