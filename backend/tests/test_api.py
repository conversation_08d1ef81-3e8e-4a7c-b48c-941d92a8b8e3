#!/usr/bin/env python3
"""
Pytest test suite for Credit Chakra API endpoints
"""
import pytest
import json
from datetime import datetime

def test_health_check(client):
    """Test health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert "status" in data

def test_create_msme(client):
    """Test MSME creation"""
    msme_data = {
        "name": "Test MSME Business",
        "business_type": "retail",
        "location": "Mumbai, Maharashtra",
        "tags": ["retail", "electronics"]
    }

    response = client.post("/msme/", json=msme_data)
    assert response.status_code == 201
    msme = response.json()
    assert "msme_id" in msme
    assert msme["name"] == "Test MSME Business"

def test_add_signal(client, msme_id):
    """Test adding a signal"""
    signal_data = {
        "source": "gst",
        "value": 1500000,  # 15L monthly turnover
        "metadata": {"month": "2024-01", "verified": True}
    }

    response = client.post(f"/msme/{msme_id}/signals", json=signal_data)
    assert response.status_code == 201
    result = response.json()
    assert "signal_id" in result

def test_send_nudge(client, msme_id):
    """Test sending a nudge"""
    nudge_data = {
        "trigger_type": "score_improvement",
        "message": "Congratulations! Your credit score has improved.",
        "medium": "email",
        "metadata": {"campaign": "score_update"}
    }

    response = client.post(f"/msme/{msme_id}/nudge", json=nudge_data)
    assert response.status_code == 201
    nudge = response.json()
    assert "nudge_id" in nudge

def test_get_score(client, msme_id):
    """Test getting MSME health score"""
    response = client.get(f"/msme/{msme_id}/score")
    assert response.status_code == 200
    score_data = response.json()
    assert "current_score" in score_data
    assert "risk_band" in score_data

def test_portfolio_summary(client):
    """Test getting portfolio summary"""
    response = client.get("/dashboard/portfolio")
    assert response.status_code == 200
    portfolio = response.json()
    assert isinstance(portfolio, dict)
    assert "data" in portfolio
    assert isinstance(portfolio["data"], list)
    assert "pagination" in portfolio
    assert "total_count" in portfolio

def test_dashboard_analytics(client):
    """Test getting dashboard analytics"""
    response = client.get("/dashboard/analytics")
    assert response.status_code == 200
    analytics = response.json()
    assert "total_msmes" in analytics
    assert "risk_distribution" in analytics


