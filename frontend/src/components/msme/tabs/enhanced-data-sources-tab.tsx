'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Calendar,
  Database,
  TrendingUp,
  TrendingDown,
  CreditCard,
  FileText,
  Activity,
  Clock,
  Zap,
  Eye,
  Star,
  MapPin,
  Instagram,
  Phone
} from 'lucide-react';

interface DataSourceDetails {
  source: string;
  name: string;
  icon: React.ReactNode;
  status: 'connected' | 'disconnected' | 'error' | 'pending';
  lastUpdated: string;
  dataQuality: number;
  recordCount: number;
  healthScore: number;
  description: string;
  contributionPercentage: number;
  reliabilityScore: number;
  dataFreshness: 'real-time' | 'hourly' | 'daily' | 'weekly';
  details: {
    coverage: string;
    accuracy: number;
    completeness: number;
    timeliness: number;
    sources: string[];
    keyMetrics: { name: string; value: string; trend: 'up' | 'down' | 'stable' }[];
  };
}

interface EnhancedDataSourcesTabProps {
  msmeId: string;
}

export default function EnhancedDataSourcesTab({ msmeId }: EnhancedDataSourcesTabProps) {
  const [dataSources, setDataSources] = useState<DataSourceDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedSource, setSelectedSource] = useState<DataSourceDetails | null>(null);

  const handleSourceSelect = (source: DataSourceDetails) => {
    setSelectedSource(source);
  };

  useEffect(() => {
    fetchDataSourceStatus();
  }, [msmeId]);

  const fetchDataSourceStatus = async () => {
    try {
      setLoading(true);
      // Mock data - in production, this would call the actual API
      const mockData: DataSourceDetails[] = [
        {
          source: 'gst',
          name: 'GST & ITR Data',
          icon: <FileText className="h-5 w-5" />,
          status: 'connected',
          lastUpdated: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          dataQuality: 95.2,
          recordCount: 156,
          healthScore: 92.8,
          description: 'GST turnover, compliance, ITR filings, and payment data',
          contributionPercentage: 25.8,
          reliabilityScore: 94.5,
          dataFreshness: 'daily',
          details: {
            coverage: '24 months of GST data, 3 years ITR',
            accuracy: 96.2,
            completeness: 94.8,
            timeliness: 92.1,
            sources: ['GST Portal', 'ITR Database', 'CBDT Records'],
            keyMetrics: [
              { name: 'Monthly Turnover', value: '₹25.8L', trend: 'up' },
              { name: 'Filing Compliance', value: '92.5%', trend: 'stable' },
              { name: 'Payment Timeliness', value: '88.7%', trend: 'up' }
            ]
          }
        },
        {
          source: 'banking',
          name: 'Banking & UPI Data',
          icon: <CreditCard className="h-5 w-5" />,
          status: 'connected',
          lastUpdated: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          dataQuality: 88.7,
          recordCount: 2847,
          healthScore: 85.4,
          description: 'Bank statements, UPI transactions, and digital payment patterns',
          contributionPercentage: 22.3,
          reliabilityScore: 87.9,
          dataFreshness: 'real-time',
          details: {
            coverage: '18 months transaction history',
            accuracy: 89.4,
            completeness: 91.2,
            timeliness: 98.7,
            sources: ['Account Aggregator', 'UPI Networks', 'NPCI Data'],
            keyMetrics: [
              { name: 'Digital Adoption', value: '78.5%', trend: 'up' },
              { name: 'Transaction Volume', value: '₹18.2L', trend: 'up' },
              { name: 'Account Stability', value: '94.2%', trend: 'stable' }
            ]
          }
        },
        {
          source: 'reviews',
          name: 'Google Reviews & Ratings',
          icon: <Star className="h-5 w-5" />,
          status: 'connected',
          lastUpdated: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          dataQuality: 82.1,
          recordCount: 247,
          healthScore: 78.9,
          description: 'Customer reviews, ratings, and reputation analysis',
          contributionPercentage: 15.7,
          reliabilityScore: 81.3,
          dataFreshness: 'hourly',
          details: {
            coverage: '3 years review history',
            accuracy: 84.2,
            completeness: 79.8,
            timeliness: 95.4,
            sources: ['Google My Business', 'Maps API', 'Review Aggregators'],
            keyMetrics: [
              { name: 'Average Rating', value: '4.2/5', trend: 'up' },
              { name: 'Review Volume', value: '247 reviews', trend: 'up' },
              { name: 'Response Rate', value: '68.5%', trend: 'stable' }
            ]
          }
        },
        {
          source: 'social',
          name: 'Social Media Presence',
          icon: <Instagram className="h-5 w-5" />,
          status: 'connected',
          lastUpdated: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
          dataQuality: 75.8,
          recordCount: 89,
          healthScore: 72.1,
          description: 'Instagram, Facebook, and social media engagement metrics',
          contributionPercentage: 12.4,
          reliabilityScore: 74.6,
          dataFreshness: 'daily',
          details: {
            coverage: '2 years social activity',
            accuracy: 76.8,
            completeness: 74.2,
            timeliness: 89.3,
            sources: ['Instagram API', 'Facebook Graph', 'Social Analytics'],
            keyMetrics: [
              { name: 'Follower Growth', value: '+12.5%', trend: 'up' },
              { name: 'Engagement Rate', value: '3.8%', trend: 'stable' },
              { name: 'Post Frequency', value: '4.2/week', trend: 'down' }
            ]
          }
        },
        {
          source: 'justdial',
          name: 'JustDial & Directory',
          icon: <Phone className="h-5 w-5" />,
          status: 'connected',
          lastUpdated: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
          dataQuality: 79.3,
          recordCount: 34,
          healthScore: 76.2,
          description: 'Business directory listings and local search presence',
          contributionPercentage: 8.9,
          reliabilityScore: 78.1,
          dataFreshness: 'weekly',
          details: {
            coverage: 'Multi-platform directory presence',
            accuracy: 81.4,
            completeness: 76.8,
            timeliness: 72.1,
            sources: ['JustDial', 'Sulekha', 'IndiaMART', 'Local Directories'],
            keyMetrics: [
              { name: 'Listing Quality', value: '8.2/10', trend: 'stable' },
              { name: 'Search Visibility', value: '76.3%', trend: 'up' },
              { name: 'Contact Accuracy', value: '94.1%', trend: 'stable' }
            ]
          }
        },
        {
          source: 'maps',
          name: 'Maps & Location Data',
          icon: <MapPin className="h-5 w-5" />,
          status: 'connected',
          lastUpdated: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          dataQuality: 91.4,
          recordCount: 12,
          healthScore: 88.7,
          description: 'Location accuracy, foot traffic, and geographic insights',
          contributionPercentage: 14.9,
          reliabilityScore: 89.2,
          dataFreshness: 'real-time',
          details: {
            coverage: 'Real-time location analytics',
            accuracy: 93.7,
            completeness: 89.1,
            timeliness: 97.8,
            sources: ['Google Maps', 'Location Services', 'Foot Traffic Analytics'],
            keyMetrics: [
              { name: 'Location Accuracy', value: '99.2%', trend: 'stable' },
              { name: 'Foot Traffic', value: '+8.7%', trend: 'up' },
              { name: 'Peak Hours', value: '2-6 PM', trend: 'stable' }
            ]
          }
        }
      ];
      
      setDataSources(mockData);
    } catch (error) {
      console.error('Error fetching data source status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchDataSourceStatus();
    setRefreshing(false);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge variant="default" className="bg-green-100 text-green-800">Connected</Badge>;
      case 'disconnected':
        return <Badge variant="secondary" className="bg-gray-100 text-gray-800">Disconnected</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getHealthScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-yellow-600';
    if (score >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  const getFreshnessColor = (freshness: string) => {
    switch (freshness) {
      case 'real-time': return 'text-green-600';
      case 'hourly': return 'text-blue-600';
      case 'daily': return 'text-yellow-600';
      case 'weekly': return 'text-orange-600';
      default: return 'text-gray-600';
    }
  };

  const formatLastUpdated = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const calculateOverallHealth = () => {
    if (dataSources.length === 0) return 0;
    const totalScore = dataSources.reduce((sum, source) => sum + source.healthScore, 0);
    return totalScore / dataSources.length;
  };

  const getConnectedCount = () => {
    return dataSources.filter(source => source.status === 'connected').length;
  };

  const getTotalRecords = () => {
    return dataSources.reduce((sum, source) => sum + source.recordCount, 0);
  };

  const getTotalContribution = () => {
    return dataSources.reduce((sum, source) => sum + source.contributionPercentage, 0);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Refresh */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Data Sources Inventory</h2>
          <p className="text-muted-foreground">Comprehensive data source analysis with reliability metrics</p>
        </div>
        <Button 
          variant="outline" 
          onClick={handleRefresh} 
          disabled={refreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh All
        </Button>
      </div>

      {/* Enhanced Summary Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <Database className="h-5 w-5" />
              Data Sources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 mb-2">
              {getConnectedCount()}/{dataSources.length}
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-blue-600">Connected</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700">
              <TrendingUp className="h-5 w-5" />
              Overall Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 mb-2">
              {calculateOverallHealth().toFixed(0)}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Progress value={calculateOverallHealth()} className="h-2 flex-1" />
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <FileText className="h-5 w-5" />
              Total Records
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 mb-2">
              {getTotalRecords().toLocaleString()}
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-purple-600">Data Points</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <Zap className="h-5 w-5" />
              Score Contribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 mb-2">
              {getTotalContribution().toFixed(0)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-orange-600">Total Weight</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Data Source Details with Modal */}
      <div className="grid gap-6 md:grid-cols-2">
        {dataSources.map((source) => (
          <Card key={source.source} className="border-0 shadow-lg">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    {source.icon}
                  </div>
                  {source.name}
                </CardTitle>
                <div className="flex items-center gap-2">
                  {getStatusIcon(source.status)}
                  {getStatusBadge(source.status)}
                </div>
              </div>
              <CardDescription>{source.description}</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Health Score</span>
                    <span className={`font-medium ${getHealthScoreColor(source.healthScore)}`}>
                      {source.healthScore.toFixed(0)}/100
                    </span>
                  </div>
                  <Progress value={source.healthScore} className="h-2" />
                  
                  <div className="flex items-center justify-between text-sm">
                    <span>Contribution</span>
                    <span className="font-medium">{source.contributionPercentage.toFixed(1)}%</span>
                  </div>
                  <Progress value={source.contributionPercentage} className="h-2" />
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Reliability</span>
                    <span className="font-medium">{source.reliabilityScore.toFixed(0)}%</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span>Freshness</span>
                    <span className={`font-medium capitalize ${getFreshnessColor(source.dataFreshness)}`}>
                      {source.dataFreshness}
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <span>Last Updated</span>
                    <span className="font-medium">{formatLastUpdated(source.lastUpdated)}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center justify-between pt-2 border-t">
                <Button variant="outline" size="sm" className="flex items-center gap-2">
                  <RefreshCw className="h-3 w-3" />
                  Refresh
                </Button>
                
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="ghost" size="sm" className="flex items-center gap-2">
                      <Eye className="h-3 w-3" />
                      View Details
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle className="flex items-center gap-2">
                        {source.icon}
                        {source.name} - Detailed Analysis
                      </DialogTitle>
                      <DialogDescription>
                        Comprehensive data source metrics and performance indicators
                      </DialogDescription>
                    </DialogHeader>
                    
                    <div className="space-y-6">
                      {/* Coverage and Quality Metrics */}
                      <div className="grid gap-4 md:grid-cols-2">
                        <div className="space-y-3">
                          <h4 className="font-medium">Data Quality Metrics</h4>
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>Accuracy</span>
                              <span className="font-medium">{source.details.accuracy.toFixed(1)}%</span>
                            </div>
                            <Progress value={source.details.accuracy} className="h-2" />
                            
                            <div className="flex justify-between text-sm">
                              <span>Completeness</span>
                              <span className="font-medium">{source.details.completeness.toFixed(1)}%</span>
                            </div>
                            <Progress value={source.details.completeness} className="h-2" />
                            
                            <div className="flex justify-between text-sm">
                              <span>Timeliness</span>
                              <span className="font-medium">{source.details.timeliness.toFixed(1)}%</span>
                            </div>
                            <Progress value={source.details.timeliness} className="h-2" />
                          </div>
                        </div>
                        
                        <div className="space-y-3">
                          <h4 className="font-medium">Coverage Information</h4>
                          <p className="text-sm text-muted-foreground">{source.details.coverage}</p>
                          
                          <h4 className="font-medium">Data Sources</h4>
                          <div className="space-y-1">
                            {source.details.sources.map((src, idx) => (
                              <div key={idx} className="text-sm text-muted-foreground">
                                • {src}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                      
                      {/* Key Metrics */}
                      <div>
                        <h4 className="font-medium mb-3">Key Performance Metrics</h4>
                        <div className="grid gap-3 md:grid-cols-3">
                          {source.details.keyMetrics.map((metric, idx) => (
                            <div key={idx} className="p-3 border rounded-lg">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium">{metric.name}</span>
                                <div className="flex items-center gap-1">
                                  {metric.trend === 'up' && <TrendingUp className="h-3 w-3 text-green-500" />}
                                  {metric.trend === 'down' && <TrendingDown className="h-3 w-3 text-red-500" />}
                                  {metric.trend === 'stable' && <Activity className="h-3 w-3 text-blue-500" />}
                                </div>
                              </div>
                              <div className="text-lg font-bold mt-1">{metric.value}</div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* System Health Summary */}
      <Card className="bg-muted/30">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                System last checked: {new Date().toLocaleString()}
              </span>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground">Overall Status:</span>
                <Badge variant={getConnectedCount() === dataSources.length ? "default" : "secondary"}>
                  {getConnectedCount() === dataSources.length ? "All Systems Operational" : "Some Issues Detected"}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
