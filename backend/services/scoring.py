"""
Credit scoring service for MSME risk assessment.

This module provides comprehensive credit scoring functionality including:
- Signal normalization from multiple data sources
- Weighted scoring algorithms
- Risk band determination based on RBI guidelines
- Real-time score recalculation

Author: Credit Chakra Team
Version: 1.0.0
"""
from __future__ import annotations

from typing import Any, Dict, List

from firebase.init import get_firestore_client
from models.msme import RiskBand
from models.signal import SignalSource

# Signal normalization weights and rules based on banking industry standards
# These weights reflect the relative importance of each data source for credit assessment
SIGNAL_WEIGHTS: Dict[SignalSource, float] = {
    SignalSource.GST: 0.3,        # Highest weight - official government data
    SignalSource.UPI: 0.25,       # High weight - transaction behavior
    SignalSource.REVIEWS: 0.2,    # Medium weight - customer sentiment
    SignalSource.JUSTDIAL: 0.1,   # Low weight - business presence
    SignalSource.INSTAGRAM: 0.1,  # Low weight - social media presence
    SignalSource.MAPS: 0.05       # Lowest weight - location verification
}

def normalize_signal(source: SignalSource, value: Any) -> float:
    """
    Normalize signal value to a 0-1 scale based on source type.

    This function converts raw signal values from different sources into
    a standardized 0-1 scale for consistent scoring across all data sources.

    Args:
        source: The source of the signal (GST, UPI, REVIEWS, etc.)
        value: Raw signal value (format varies by source)

    Returns:
        Normalized score between 0.0 and 1.0

    Raises:
        None - Returns 0.0 for invalid inputs
    """
    try:
        if source == SignalSource.GST:
            # GST turnover normalization (assuming value is monthly turnover)
            if isinstance(value, (int, float)):
                # Handle negative values
                if value <= 0:
                    return 0.0
                # Normalize based on typical MSME turnover ranges
                # 0-10L = 0-0.3, 10L-50L = 0.3-0.7, 50L+ = 0.7-1.0
                if value <= 1000000:  # 10L
                    return min(0.3, value / 1000000 * 0.3)
                elif value <= 5000000:  # 50L
                    return 0.3 + (value - 1000000) / 4000000 * 0.4
                else:
                    return min(1.0, 0.7 + (value - 5000000) / 10000000 * 0.3)
            return 0.0
            
        elif source == SignalSource.UPI:
            # UPI transaction volume/frequency normalization
            if isinstance(value, dict):
                transaction_count = value.get('transaction_count', 0)
                volume = value.get('volume', 0)
                # Combine count and volume for score
                count_score = min(1.0, transaction_count / 1000)  # Normalize to 1000 transactions
                volume_score = min(1.0, volume / 500000)  # Normalize to 5L volume
                return (count_score + volume_score) / 2
            return 0.0
            
        elif source == SignalSource.REVIEWS:
            # Reviews rating normalization
            if isinstance(value, dict):
                rating = value.get('average_rating', 0)
                count = value.get('review_count', 0)
                # Weight rating by review count
                rating_score = rating / 5.0  # Normalize 5-star rating
                count_weight = min(1.0, count / 100)  # Weight by review count
                return rating_score * (0.5 + 0.5 * count_weight)
            return 0.0
            
        elif source == SignalSource.JUSTDIAL:
            # JustDial business listing normalization
            if isinstance(value, dict):
                listing_quality = value.get('listing_quality', 0)
                contact_responses = value.get('contact_responses', 0)
                # Average of quality metrics
                return (listing_quality + contact_responses) / 2
            return 0.0

        elif source in [SignalSource.INSTAGRAM, SignalSource.MAPS]:
            # Social/digital presence normalization
            if isinstance(value, dict):
                followers = value.get('followers', 0)
                engagement = value.get('engagement_rate', 0)
                # Combine followers and engagement
                follower_score = min(1.0, followers / 10000)  # Normalize to 10k followers
                return (follower_score + engagement) / 2
            return 0.0
            
        else:
            return 0.0
            
    except Exception:
        return 0.0

def calculate_msme_score(signals: List[Dict[str, Any]]) -> float:
    """
    Calculate overall MSME score based on all signals
    """
    if not signals:
        return 0.0
    
    # Group signals by source and get latest for each
    latest_signals = {}
    for signal in signals:
        source = signal.get('source')
        timestamp = signal.get('timestamp')
        
        if source not in latest_signals or timestamp > latest_signals[source].get('timestamp'):
            latest_signals[source] = signal
    
    # Calculate weighted score
    total_score = 0.0
    total_weight = 0.0
    
    for source_str, signal in latest_signals.items():
        try:
            source = SignalSource(source_str)
            weight = SIGNAL_WEIGHTS.get(source, 0.0)
            normalized = signal.get('normalized', 0.0)
            
            total_score += weight * normalized
            total_weight += weight
        except ValueError:
            continue
    
    # Convert to 0-1000 scale
    if total_weight > 0:
        # Count valid signals (those that contributed to the score)
        valid_signal_count = sum(1 for signal in latest_signals.values()
                               if signal.get('source') in SIGNAL_WEIGHTS)

        # For single valid signal, return weighted score directly
        # For multiple valid signals, normalize by total weight to get average
        if valid_signal_count == 1:
            return min(1000.0, total_score * 1000)
        else:
            return min(1000.0, (total_score / total_weight) * 1000)

    return 0.0

def determine_risk_band(score: float) -> RiskBand:
    """
    Determine risk band based on score
    """
    if score >= 700:
        return RiskBand.GREEN
    elif score >= 400:
        return RiskBand.YELLOW
    else:
        return RiskBand.RED

async def recalculate_msme_score(msme_id: str) -> Dict[str, Any]:
    """
    Recalculate MSME score based on all signals and update profile
    """
    try:
        db = get_firestore_client()
        
        # Get all signals for the MSME
        signals_ref = db.collection('msmes').document(msme_id).collection('signals')
        signals_docs = signals_ref.stream()
        
        signals = []
        for doc in signals_docs:
            signals.append(doc.to_dict())
        
        # Calculate new score
        new_score = calculate_msme_score(signals)
        new_risk_band = determine_risk_band(new_score)
        
        # Update MSME profile
        msme_ref = db.collection('msmes').document(msme_id)
        msme_ref.update({
            'score': new_score,
            'risk_band': new_risk_band.value
        })
        
        return {
            'msme_id': msme_id,
            'new_score': new_score,
            'new_risk_band': new_risk_band.value,
            'signals_processed': len(signals)
        }
        
    except Exception as e:
        return {
            'msme_id': msme_id,
            'error': f"Failed to recalculate MSME score: {str(e)}"
        }
