'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Building, 
  CheckCircle, 
  AlertCircle, 
  RefreshCw,
  Calendar,
  MapPin,
  Users,
  Factory,
  Award,
  FileText,
  Shield,
  TrendingUp,
  Info
} from 'lucide-react';

interface UdyamData {
  udyam_number: string;
  registration: {
    enterprise_name: string;
    pan_number: string;
    registration_date: string;
    category: string;
    sub_category: string;
    organization_type: string;
    social_category: string;
    gender: string;
    physically_handicapped: boolean;
    location: {
      state: string;
      district: string;
      address: string;
      pincode: string;
    };
    activity: {
      nic_code: string;
      nic_description: string;
      activity_description: string;
    };
    investment: {
      plant_machinery: number;
      land_building: number;
      total_investment: number;
    };
    employment: {
      male_employees: number;
      female_employees: number;
      total_employees: number;
    };
  };
  verification: {
    verification_status: string;
    verification_date: string;
    pan_verification: boolean;
    gstin_verification: boolean;
    bank_verification: boolean;
    identity_verification: boolean;
    verification_score: number;
  };
  compliance: {
    filing_status: string;
    last_filing_date: string;
    compliance_score: number;
    pending_requirements: string[];
  };
  analytics: {
    legitimacy_score: number;
    category_appropriateness: number;
    compliance_history_score: number;
    overall_udyam_score: number;
    risk_level: string;
    verification_confidence: number;
  };
  last_updated: string;
}

interface UdyamDataTabProps {
  msmeId: string;
}

export default function UdyamDataTab({ msmeId }: UdyamDataTabProps) {
  const [udyamData, setUdyamData] = useState<UdyamData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchUdyamData();
  }, [msmeId]);

  const fetchUdyamData = async () => {
    try {
      setLoading(true);
      // Mock data - in production, this would call the actual API
      const mockData: UdyamData = {
        udyam_number: "UDYAM-MH-12-0012345",
        registration: {
          enterprise_name: "Tech Solutions Pvt Ltd",
          pan_number: "**********",
          registration_date: "2022-03-15",
          category: "SMALL",
          sub_category: "MANUFACTURING",
          organization_type: "PRIVATE_LIMITED",
          social_category: "GENERAL",
          gender: "MALE",
          physically_handicapped: false,
          location: {
            state: "Maharashtra",
            district: "Mumbai",
            address: "123 Business Park, Andheri East",
            pincode: "400069"
          },
          activity: {
            nic_code: "62013",
            nic_description: "Computer programming activities",
            activity_description: "Software development and IT services"
          },
          investment: {
            plant_machinery: 8500000,
            land_building: 2500000,
            total_investment: ********
          },
          employment: {
            male_employees: 45,
            female_employees: 25,
            total_employees: 70
          }
        },
        verification: {
          verification_status: "VERIFIED",
          verification_date: "2022-03-20",
          pan_verification: true,
          gstin_verification: true,
          bank_verification: true,
          identity_verification: true,
          verification_score: 95.5
        },
        compliance: {
          filing_status: "COMPLIANT",
          last_filing_date: "2024-03-15",
          compliance_score: 88.2,
          pending_requirements: []
        },
        analytics: {
          legitimacy_score: 92.8,
          category_appropriateness: 96.5,
          compliance_history_score: 88.2,
          overall_udyam_score: 91.7,
          risk_level: "LOW",
          verification_confidence: 95.5
        },
        last_updated: new Date().toISOString()
      };
      
      setUdyamData(mockData);
    } catch (error) {
      console.error('Error fetching Udyam data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchUdyamData();
    setRefreshing(false);
  };

  const getVerificationStatusBadge = (status: string) => {
    switch (status) {
      case 'VERIFIED':
        return <Badge variant="default" className="bg-green-100 text-green-800">Verified</Badge>;
      case 'PENDING':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'REJECTED':
        return <Badge variant="destructive">Rejected</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getRiskBadge = (risk: string) => {
    switch (risk) {
      case 'LOW':
        return <Badge variant="default" className="bg-green-100 text-green-800">Low Risk</Badge>;
      case 'MEDIUM':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Medium Risk</Badge>;
      case 'HIGH':
        return <Badge variant="destructive">High Risk</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getCategoryBadge = (category: string) => {
    const colors = {
      'MICRO': 'bg-blue-100 text-blue-800',
      'SMALL': 'bg-green-100 text-green-800',
      'MEDIUM': 'bg-purple-100 text-purple-800'
    };
    return <Badge variant="outline" className={colors[category as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>{category}</Badge>;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!udyamData) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Udyam Data Available</h3>
        <p className="text-gray-500 mb-4">Udyam registration data is not available for this MSME.</p>
        <Button onClick={fetchUdyamData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Refresh */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Udyam Registration Data</h2>
          <p className="text-muted-foreground">Udyam Number: {udyamData.udyam_number}</p>
        </div>
        <Button 
          variant="outline" 
          onClick={handleRefresh} 
          disabled={refreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <Building className="h-5 w-5" />
              Category
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 mb-2">
              {udyamData.registration.category}
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getCategoryBadge(udyamData.registration.category)}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700">
              <Shield className="h-5 w-5" />
              Verification
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 mb-2">
              {udyamData.verification.verification_score.toFixed(1)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getVerificationStatusBadge(udyamData.verification.verification_status)}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <FileText className="h-5 w-5" />
              Compliance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 mb-2">
              {udyamData.compliance.compliance_score.toFixed(1)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              <span className="text-purple-600">{udyamData.compliance.filing_status}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <Award className="h-5 w-5" />
              Overall Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 mb-2">
              {udyamData.analytics.overall_udyam_score.toFixed(0)}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getRiskBadge(udyamData.analytics.risk_level)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="registration" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="registration">Registration</TabsTrigger>
          <TabsTrigger value="verification">Verification</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="registration" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Enterprise Details
                </CardTitle>
                <CardDescription>Basic enterprise information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Enterprise Name</span>
                    <span className="font-medium">{udyamData.registration.enterprise_name}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>PAN Number</span>
                    <span className="font-medium">{udyamData.registration.pan_number}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Registration Date</span>
                    <span className="font-medium">{new Date(udyamData.registration.registration_date).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Organization Type</span>
                    <span className="font-medium">{udyamData.registration.organization_type.replace('_', ' ')}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Social Category</span>
                    <span className="font-medium">{udyamData.registration.social_category}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Location Details
                </CardTitle>
                <CardDescription>Enterprise location information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>State</span>
                    <span className="font-medium">{udyamData.registration.location.state}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>District</span>
                    <span className="font-medium">{udyamData.registration.location.district}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Pincode</span>
                    <span className="font-medium">{udyamData.registration.location.pincode}</span>
                  </div>
                  <div className="text-sm">
                    <span className="text-muted-foreground">Address:</span>
                    <p className="font-medium mt-1">{udyamData.registration.location.address}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Factory className="h-5 w-5" />
                  Business Activity
                </CardTitle>
                <CardDescription>Primary business activity details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>NIC Code</span>
                    <span className="font-medium">{udyamData.registration.activity.nic_code}</span>
                  </div>
                  <div className="text-sm">
                    <span className="text-muted-foreground">NIC Description:</span>
                    <p className="font-medium mt-1">{udyamData.registration.activity.nic_description}</p>
                  </div>
                  <div className="text-sm">
                    <span className="text-muted-foreground">Activity Description:</span>
                    <p className="font-medium mt-1">{udyamData.registration.activity.activity_description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Employment & Investment
                </CardTitle>
                <CardDescription>Employment and investment details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Total Employees</span>
                    <span className="font-medium">{udyamData.registration.employment.total_employees}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Male Employees</span>
                    <span className="font-medium">{udyamData.registration.employment.male_employees}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Female Employees</span>
                    <span className="font-medium">{udyamData.registration.employment.female_employees}</span>
                  </div>
                  <Separator />
                  <div className="flex items-center justify-between text-sm">
                    <span>Total Investment</span>
                    <span className="font-medium">₹{(udyamData.registration.investment.total_investment / 100000).toFixed(1)}L</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="verification" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Verification Status
              </CardTitle>
              <CardDescription>Document and identity verification details</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Verification Components</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">PAN Verification</span>
                      {udyamData.verification.pan_verification ? 
                        <CheckCircle className="h-4 w-4 text-green-500" /> : 
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">GSTIN Verification</span>
                      {udyamData.verification.gstin_verification ? 
                        <CheckCircle className="h-4 w-4 text-green-500" /> : 
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Bank Verification</span>
                      {udyamData.verification.bank_verification ? 
                        <CheckCircle className="h-4 w-4 text-green-500" /> : 
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Identity Verification</span>
                      {udyamData.verification.identity_verification ? 
                        <CheckCircle className="h-4 w-4 text-green-500" /> : 
                        <AlertCircle className="h-4 w-4 text-red-500" />
                      }
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-medium">Verification Summary</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Overall Score</span>
                      <span className="font-medium">{udyamData.verification.verification_score.toFixed(1)}%</span>
                    </div>
                    <Progress value={udyamData.verification.verification_score} className="h-2" />
                    
                    <div className="flex items-center justify-between text-sm">
                      <span>Status</span>
                      {getVerificationStatusBadge(udyamData.verification.verification_status)}
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Verification Date</span>
                      <span className="font-medium">{new Date(udyamData.verification.verification_date).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Compliance Status
              </CardTitle>
              <CardDescription>Udyam compliance and filing status</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Filing Status</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Current Status</span>
                      <Badge variant={udyamData.compliance.filing_status === 'COMPLIANT' ? 'default' : 'destructive'}>
                        {udyamData.compliance.filing_status}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Last Filing Date</span>
                      <span className="font-medium">{new Date(udyamData.compliance.last_filing_date).toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Compliance Score</span>
                      <span className="font-medium">{udyamData.compliance.compliance_score.toFixed(1)}%</span>
                    </div>
                    <Progress value={udyamData.compliance.compliance_score} className="h-2" />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-medium">Requirements</h4>
                  <div className="space-y-3">
                    {udyamData.compliance.pending_requirements.length > 0 ? (
                      udyamData.compliance.pending_requirements.map((req, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm">
                          <AlertCircle className="h-4 w-4 text-orange-500" />
                          <span>{req}</span>
                        </div>
                      ))
                    ) : (
                      <div className="flex items-center gap-2 text-sm text-green-600">
                        <CheckCircle className="h-4 w-4" />
                        <span>All requirements fulfilled</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Udyam Analytics
              </CardTitle>
              <CardDescription>AI-powered insights and risk assessment</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Score Breakdown</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Legitimacy Score</span>
                      <span className="font-medium">{udyamData.analytics.legitimacy_score.toFixed(1)}/100</span>
                    </div>
                    <Progress value={udyamData.analytics.legitimacy_score} className="h-2" />
                    
                    <div className="flex items-center justify-between text-sm">
                      <span>Category Appropriateness</span>
                      <span className="font-medium">{udyamData.analytics.category_appropriateness.toFixed(1)}/100</span>
                    </div>
                    <Progress value={udyamData.analytics.category_appropriateness} className="h-2" />
                    
                    <div className="flex items-center justify-between text-sm">
                      <span>Compliance History</span>
                      <span className="font-medium">{udyamData.analytics.compliance_history_score.toFixed(1)}/100</span>
                    </div>
                    <Progress value={udyamData.analytics.compliance_history_score} className="h-2" />
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-medium">Risk Assessment</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span>Overall Score</span>
                      <span className="font-medium">{udyamData.analytics.overall_udyam_score.toFixed(1)}/100</span>
                    </div>
                    <Progress value={udyamData.analytics.overall_udyam_score} className="h-2" />
                    
                    <div className="flex items-center justify-between text-sm">
                      <span>Risk Level</span>
                      {getRiskBadge(udyamData.analytics.risk_level)}
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span>Verification Confidence</span>
                      <span className="font-medium">{udyamData.analytics.verification_confidence.toFixed(1)}%</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h4 className="font-medium">Key Insights</h4>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <div>
                      <p className="font-medium text-sm">Verified Registration</p>
                      <p className="text-xs text-muted-foreground">All documents verified successfully</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <Info className="h-4 w-4 text-blue-500" />
                    <div>
                      <p className="font-medium text-sm">Appropriate Category</p>
                      <p className="text-xs text-muted-foreground">Business size matches declared category</p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Data Quality Footer */}
      <Card className="bg-muted/30">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                Last updated: {new Date(udyamData.last_updated).toLocaleDateString()}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">Udyam Number:</span>
              <Badge variant="outline">{udyamData.udyam_number}</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
