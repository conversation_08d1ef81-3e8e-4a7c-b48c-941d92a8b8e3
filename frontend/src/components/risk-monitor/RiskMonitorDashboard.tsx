'use client';

/**
 * Enhanced Risk Monitor Dashboard for Credit Chakra
 * 
 * Comprehensive risk monitoring dashboard integrating SMA classification,
 * real-time risk events, and RBI compliance monitoring with existing
 * portfolio management features.
 * 
 * Features:
 * - Special Mention Accounts (SMA) heatmap and classification
 * - Real-time risk event streaming with 2-second load requirement
 * - Cross-component data synchronization with portfolio dashboard
 * - RBI compliance monitoring and regulatory alerts
 * - Emerald color palette consistency with existing UI patterns
 * - Responsive design with skeleton loading screens
 * 
 * @component
 * @example
 * ```tsx
 * <RiskMonitorDashboard />
 * ```
 * 
 * Author: Credit Chakra Team
 * Version: 1.0.0
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { RiskMonitorErrorBoundary } from './ErrorBoundary';

// Import SpecialMentionAccounts directly since it's a named export
import { SpecialMentionAccounts } from './SpecialMentionAccounts';
import {
  Shield,
  TrendingUp,
  AlertTriangle,
  Activity,
  RefreshCw,
  Bell,
  Eye,
  Clock,
  BarChart3,
  Users,
  FileText,
  Calendar,
  ArrowLeft,
  Search,
  Filter,
  Download,
  CheckCircle
} from 'lucide-react';

// Types for real-time risk events
interface RiskEvent {
  event_id: string;
  msme_id: string;
  event_type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  impact_score: number;
  timestamp: string;
  metadata: Record<string, any>;
  days_past_due?: number;
  exposure_amount?: number;
  acknowledged: boolean;
  resolved: boolean;
}

interface PortfolioMetrics {
  total_msmes: number;
  total_exposure: number;
  avg_risk_score: number;
  risk_distribution: Record<string, number>;
  npa_ratio: number;
  sma_ratio: number;
  portfolio_health_score: number;
  concentration_metrics: Record<string, number>;
  trend_indicators: Record<string, number>;
  last_updated: string;
}

interface RiskMonitorDashboardProps {
  onBackToPortfolio?: () => void;
}

export const RiskMonitorDashboard = React.memo(({ onBackToPortfolio }: RiskMonitorDashboardProps) => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('sma-monitor');
  const [riskEvents, setRiskEvents] = useState<RiskEvent[]>([]);
  const [portfolioMetrics, setPortfolioMetrics] = useState<PortfolioMetrics | null>(null);

  // Real-time events filtering and search state
  const [eventFilter, setEventFilter] = useState<'all' | 'critical' | 'high' | 'medium' | 'low'>('all');
  const [eventTypeFilter, setEventTypeFilter] = useState<string>('all');
  const [eventSearchQuery, setEventSearchQuery] = useState('');
  const [showAcknowledged, setShowAcknowledged] = useState(true);

  // Fetch real-time risk data
  const fetchRiskData = useCallback(async () => {
    try {
      const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

      const [eventsResponse, metricsResponse] = await Promise.all([
        fetch(`${API_BASE_URL}/api/dashboard/risk-monitor/real-time-events`),
        fetch(`${API_BASE_URL}/api/dashboard/risk-monitor/portfolio-metrics`)
      ]);

      if (!eventsResponse.ok || !metricsResponse.ok) {
        throw new Error('Failed to fetch risk monitoring data');
      }

      const [eventsData, metricsData] = await Promise.all([
        eventsResponse.json(),
        metricsResponse.json()
      ]);

      setRiskEvents(eventsData);
      setPortfolioMetrics(metricsData.data);
      setError(null);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch risk data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    fetchRiskData();
    
    // Set up real-time updates every 30 seconds
    const interval = setInterval(fetchRiskData, 30000);
    return () => clearInterval(interval);
  }, [fetchRiskData]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await fetchRiskData();
  }, [fetchRiskData]);

  // Handle acknowledging a risk event
  const handleAcknowledgeEvent = useCallback(async (eventId: string) => {
    try {
      // Optimistically update the UI first
      setRiskEvents(prevEvents =>
        prevEvents.map(event =>
          event.event_id === eventId
            ? { ...event, acknowledged: true }
            : event
        )
      );

      // In a real implementation, you would make an API call here
      // const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
      // await fetch(`${API_BASE_URL}/api/dashboard/risk-monitor/acknowledge-event/${eventId}`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' }
      // });

      console.log('Event acknowledged:', eventId);
    } catch (error) {
      // Revert the optimistic update on error
      setRiskEvents(prevEvents =>
        prevEvents.map(event =>
          event.event_id === eventId
            ? { ...event, acknowledged: false }
            : event
        )
      );
      console.error('Failed to acknowledge event:', error);
    }
  }, []);

  // Memoized helper functions for performance
  const getSeverityColor = useCallback((severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500 text-white';
      case 'high': return 'bg-orange-500 text-white';
      case 'medium': return 'bg-yellow-500 text-black';
      case 'low': return 'bg-emerald-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  }, []);

  const formatCurrency = useCallback((amount: number) => {
    if (!amount || isNaN(amount)) return '₹0';

    if (amount >= 10000000) {
      return `₹${(amount / 10000000).toFixed(1)}Cr`;
    } else if (amount >= 100000) {
      return `₹${(amount / 100000).toFixed(1)}L`;
    } else {
      return `₹${amount.toLocaleString()}`;
    }
  }, []);

  // Memoized risk event calculations
  const riskEventStats = useMemo(() => {
    if (!riskEvents.length) {
      return { critical: 0, high: 0, acknowledged: 0, total: 0, unacknowledged: 0 };
    }

    return {
      critical: riskEvents.filter(e => e.severity === 'critical').length,
      high: riskEvents.filter(e => e.severity === 'high').length,
      acknowledged: riskEvents.filter(e => e.acknowledged).length,
      total: riskEvents.length,
      unacknowledged: riskEvents.filter(e => !e.acknowledged).length
    };
  }, [riskEvents]);

  // Filtered events based on current filters
  const filteredEvents = useMemo(() => {
    let filtered = [...riskEvents];

    // Filter by severity
    if (eventFilter !== 'all') {
      filtered = filtered.filter(event => event.severity === eventFilter);
    }

    // Filter by event type
    if (eventTypeFilter !== 'all') {
      filtered = filtered.filter(event => event.event_type === eventTypeFilter);
    }

    // Filter by acknowledgment status
    if (!showAcknowledged) {
      filtered = filtered.filter(event => !event.acknowledged);
    }

    // Filter by search query
    if (eventSearchQuery.trim()) {
      const query = eventSearchQuery.toLowerCase();
      filtered = filtered.filter(event =>
        event.title.toLowerCase().includes(query) ||
        event.description.toLowerCase().includes(query) ||
        event.metadata.msme_name?.toLowerCase().includes(query) ||
        event.event_type.toLowerCase().includes(query)
      );
    }

    return filtered.sort((a, b) => b.impact_score - a.impact_score);
  }, [riskEvents, eventFilter, eventTypeFilter, showAcknowledged, eventSearchQuery]);

  // Get unique event types for filter dropdown
  const eventTypes = useMemo(() => {
    const types = [...new Set(riskEvents.map(event => event.event_type))];
    return types.sort();
  }, [riskEvents]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>
        
        <div className="grid gap-6 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="pb-3">
                <Skeleton className="h-6 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-4 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>
        
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Alert className="border-red-200 bg-red-50">
        <AlertTriangle className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-800">
          {error}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            className="ml-4"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="container mx-auto px-6 py-8 space-y-8">
      {/* Enhanced Header */}
      <div className="flex items-center justify-between border-b border-emerald-100 pb-6">
        <div className="flex items-center gap-6">
          {onBackToPortfolio && (
            <Button
              variant="outline"
              size="sm"
              onClick={onBackToPortfolio}
              className="border-emerald-200 text-emerald-700 hover:bg-emerald-50 transition-colors"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Portfolio
            </Button>
          )}
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-emerald-100 rounded-xl">
                <Shield className="h-8 w-8 text-emerald-700" />
              </div>
              <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-emerald-700 to-emerald-600 bg-clip-text text-transparent">
                Risk Monitor
              </h1>
            </div>
            <p className="text-lg text-muted-foreground ml-14">
              Real-time portfolio risk assessment and regulatory compliance
            </p>
          </div>
        </div>
        <Button
          onClick={handleRefresh}
          disabled={refreshing}
          variant="outline"
          className="border-emerald-200 text-emerald-700 hover:bg-emerald-50 transition-colors shadow-sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>



      {/* Risk Monitoring Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
        <TabsList className="grid w-full grid-cols-3 bg-emerald-50 border border-emerald-200 p-1 rounded-lg">
          <TabsTrigger
            value="sma-monitor"
            className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white transition-all duration-200"
          >
            SMA Monitor
          </TabsTrigger>
          <TabsTrigger
            value="real-time-events"
            className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white transition-all duration-200"
          >
            Real-time Events
          </TabsTrigger>
          <TabsTrigger
            value="compliance"
            className="data-[state=active]:bg-emerald-600 data-[state=active]:text-white transition-all duration-200"
          >
            Compliance
          </TabsTrigger>
        </TabsList>

        <TabsContent value="sma-monitor" className="mt-8">
          {/* Special Mention Accounts Component with Error Boundary */}
          <RiskMonitorErrorBoundary>
            <SpecialMentionAccounts />
          </RiskMonitorErrorBoundary>
        </TabsContent>

        <TabsContent value="real-time-events" className="mt-8 space-y-6">
          {/* Enhanced Risk Event Summary */}
          <div className="grid gap-6 md:grid-cols-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-red-700">
                  <AlertTriangle className="h-5 w-5" />
                  Critical Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-red-700 mb-2">
                  {riskEventStats.critical}
                </div>
                <p className="text-sm text-red-600">
                  Require immediate attention
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-orange-700">
                  <Clock className="h-5 w-5" />
                  High Priority
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-orange-700 mb-2">
                  {riskEventStats.high}
                </div>
                <p className="text-sm text-orange-600">
                  Need review within 24h
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-emerald-700">
                  <CheckCircle className="h-5 w-5" />
                  Acknowledged
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-emerald-700 mb-2">
                  {riskEventStats.acknowledged}
                </div>
                <p className="text-sm text-emerald-600">
                  Being handled
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-blue-700">
                  <Activity className="h-5 w-5" />
                  Total Events
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-700 mb-2">
                  {riskEventStats.total}
                </div>
                <p className="text-sm text-blue-600">
                  Active monitoring
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Enhanced Real-time Risk Events Display */}
          <Card>
            <CardHeader className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-emerald-600" />
                  <CardTitle>Real-time Risk Events</CardTitle>
                  <Badge variant="outline" className="ml-2">
                    {filteredEvents.length} events
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleRefresh}
                    disabled={refreshing}
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                  <Button size="sm" variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>

              {/* Integrated Filters */}
              <div className="grid gap-3 md:grid-cols-4">
                {/* Search Input */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search events..."
                    value={eventSearchQuery}
                    onChange={(e) => setEventSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Severity Filter */}
                <Select value={eventFilter} onValueChange={(value: any) => setEventFilter(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All severities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Severities</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>

                {/* Event Type Filter */}
                <Select value={eventTypeFilter} onValueChange={setEventTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {eventTypes.map(type => (
                      <SelectItem key={type} value={type}>
                        {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                {/* Show Acknowledged Toggle */}
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={showAcknowledged}
                    onCheckedChange={setShowAcknowledged}
                  />
                  <Label className="text-sm">Show acknowledged</Label>
                </div>
              </div>

              {/* Filter Summary */}
              {(eventSearchQuery || eventFilter !== 'all' || eventTypeFilter !== 'all' || !showAcknowledged) && (
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <span>Showing {filteredEvents.length} of {riskEventStats.total} events</span>
                    {eventSearchQuery && (
                      <Badge variant="outline" className="text-xs">
                        "{eventSearchQuery}"
                      </Badge>
                    )}
                    {eventFilter !== 'all' && (
                      <Badge variant="outline" className="text-xs">
                        {eventFilter}
                      </Badge>
                    )}
                    {eventTypeFilter !== 'all' && (
                      <Badge variant="outline" className="text-xs">
                        {eventTypeFilter.replace('_', ' ')}
                      </Badge>
                    )}
                  </div>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => {
                      setEventFilter('all');
                      setEventTypeFilter('all');
                      setEventSearchQuery('');
                      setShowAcknowledged(true);
                    }}
                    className="text-xs"
                  >
                    Clear filters
                  </Button>
                </div>
              )}

              <CardDescription>
                Live monitoring of portfolio risk events and alerts • Last updated: {new Date().toLocaleTimeString()}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[500px]">
                {filteredEvents.length === 0 ? (
                  <div className="text-center py-12 text-muted-foreground">
                    {riskEvents.length === 0 ? (
                      <>
                        <Bell className="h-16 w-16 mx-auto mb-4 opacity-50" />
                        <p className="text-lg font-medium mb-2">No risk events at this time</p>
                        <p className="text-sm">Your portfolio is looking healthy!</p>
                      </>
                    ) : (
                      <>
                        <Filter className="h-16 w-16 mx-auto mb-4 opacity-50" />
                        <p className="text-lg font-medium mb-2">No events match your filters</p>
                        <p className="text-sm">Try adjusting your search criteria or clearing filters</p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-4"
                          onClick={() => {
                            setEventFilter('all');
                            setEventTypeFilter('all');
                            setEventSearchQuery('');
                            setShowAcknowledged(true);
                          }}
                        >
                          Clear All Filters
                        </Button>
                      </>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {filteredEvents.map((event) => (
                      <div
                        key={event.event_id}
                        className={`p-5 border rounded-lg transition-all duration-200 ${
                          event.acknowledged
                            ? 'opacity-70 bg-muted/20 border-muted'
                            : 'hover:bg-muted/30 hover:shadow-md border-border'
                        }`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            {/* Event Header */}
                            <div className="flex items-center gap-2 mb-3">
                              <Badge className={getSeverityColor(event.severity)}>
                                {event.severity.toUpperCase()}
                              </Badge>
                              <Badge variant="outline">
                                Impact: {event.impact_score.toFixed(0)}
                              </Badge>
                              <Badge variant="outline">
                                {event.event_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </Badge>
                              {event.days_past_due && (
                                <Badge variant="outline" className="bg-orange-50 text-orange-700">
                                  {event.days_past_due} DPD
                                </Badge>
                              )}
                              {event.exposure_amount && (
                                <Badge variant="outline" className="bg-blue-50 text-blue-700">
                                  {formatCurrency(event.exposure_amount)}
                                </Badge>
                              )}
                              {event.acknowledged && (
                                <Badge className="bg-emerald-100 text-emerald-800">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Acknowledged
                                </Badge>
                              )}
                            </div>

                            {/* Event Content */}
                            <h4 className="font-semibold text-lg mb-2">{event.title}</h4>
                            <p className="text-sm text-muted-foreground mb-3 leading-relaxed">
                              {event.description}
                            </p>

                            {/* Event Metadata */}
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-muted-foreground bg-muted/30 p-3 rounded-lg">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                <span>{new Date(event.timestamp).toLocaleString()}</span>
                              </div>
                              {event.metadata.msme_name && (
                                <div className="flex items-center gap-1">
                                  <Users className="h-3 w-3" />
                                  <span>MSME: {event.metadata.msme_name}</span>
                                </div>
                              )}
                              {event.metadata.location && (
                                <div className="flex items-center gap-1">
                                  <BarChart3 className="h-3 w-3" />
                                  <span>Location: {event.metadata.location}</span>
                                </div>
                              )}
                              {event.metadata.business_type && (
                                <div className="flex items-center gap-1">
                                  <FileText className="h-3 w-3" />
                                  <span>Type: {event.metadata.business_type}</span>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Action Buttons */}
                          <div className="flex flex-col gap-2 ml-6">
                            <Button size="sm" variant="outline">
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </Button>
                            {!event.acknowledged ? (
                              <Button
                                size="sm"
                                className="bg-emerald-600 hover:bg-emerald-700"
                                onClick={() => handleAcknowledgeEvent(event.event_id)}
                              >
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Acknowledge
                              </Button>
                            ) : (
                              <Button size="sm" variant="outline" disabled>
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Acknowledged
                              </Button>
                            )}
                            {event.severity === 'critical' && (
                              <Button size="sm" variant="destructive">
                                <AlertTriangle className="h-4 w-4 mr-2" />
                                Escalate
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="compliance" className="mt-8 space-y-6">
          {/* RBI Compliance Overview Cards */}
          <div className="grid gap-6 md:grid-cols-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-emerald-700">
                  <Shield className="h-5 w-5" />
                  Overall Score
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-emerald-700 mb-2">
                  94%
                </div>
                <p className="text-sm text-emerald-600">
                  Excellent compliance
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-blue-700">
                  <FileText className="h-5 w-5" />
                  Active Reports
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-700 mb-2">
                  12
                </div>
                <p className="text-sm text-blue-600">
                  Pending submission
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-orange-700">
                  <Clock className="h-5 w-5" />
                  Overdue Items
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-orange-700 mb-2">
                  2
                </div>
                <p className="text-sm text-orange-600">
                  Require attention
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-purple-700">
                  <BarChart3 className="h-5 w-5" />
                  Audit Score
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-purple-700 mb-2">
                  A+
                </div>
                <p className="text-sm text-purple-600">
                  Last assessment
                </p>
              </CardContent>
            </Card>
          </div>

          {/* RBI Compliance Dashboard */}
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-emerald-600" />
                  RBI Compliance Status
                </CardTitle>
                <CardDescription>
                  Regulatory compliance monitoring and reporting
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                        <span className="font-medium">SMA Classification & Reporting</span>
                      </div>
                      <Badge className="bg-emerald-100 text-emerald-800">Compliant</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                        <span className="font-medium">Provision Coverage Ratio</span>
                      </div>
                      <Badge className="bg-emerald-100 text-emerald-800">98.5%</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                        <span className="font-medium">Exposure Concentration</span>
                      </div>
                      <Badge className="bg-emerald-100 text-emerald-800">Within Limits</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        <span className="font-medium">Documentation Review</span>
                      </div>
                      <Badge className="bg-yellow-100 text-yellow-800">In Progress</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                        <span className="font-medium">CRILC Reporting</span>
                      </div>
                      <Badge className="bg-emerald-100 text-emerald-800">Up to Date</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        <span className="font-medium">Stress Testing</span>
                      </div>
                      <Badge className="bg-red-100 text-red-800">Overdue</Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-emerald-600" />
                  Upcoming Compliance Deadlines
                </CardTitle>
                <CardDescription>
                  Critical regulatory reporting deadlines
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-4 border-l-4 border-red-500 bg-red-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <p className="font-medium text-red-900">Stress Testing Report</p>
                      <Badge variant="destructive">Overdue</Badge>
                    </div>
                    <p className="text-sm text-red-700">Annual stress testing submission</p>
                    <p className="text-xs text-red-600 mt-1">Due: 3 days ago</p>
                  </div>

                  <div className="p-4 border-l-4 border-orange-500 bg-orange-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <p className="font-medium text-orange-900">SMA Quarterly Report</p>
                      <Badge className="bg-orange-100 text-orange-800">Due Soon</Badge>
                    </div>
                    <p className="text-sm text-orange-700">Q3 SMA classification report</p>
                    <p className="text-xs text-orange-600 mt-1">Due: In 5 days</p>
                  </div>

                  <div className="p-4 border-l-4 border-yellow-500 bg-yellow-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <p className="font-medium text-yellow-900">CRILC Data Submission</p>
                      <Badge className="bg-yellow-100 text-yellow-800">Upcoming</Badge>
                    </div>
                    <p className="text-sm text-yellow-700">Monthly credit information report</p>
                    <p className="text-xs text-yellow-600 mt-1">Due: In 12 days</p>
                  </div>

                  <div className="p-4 border-l-4 border-blue-500 bg-blue-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <p className="font-medium text-blue-900">Provision Review</p>
                      <Badge className="bg-blue-100 text-blue-800">Scheduled</Badge>
                    </div>
                    <p className="text-sm text-blue-700">Quarterly provision adequacy review</p>
                    <p className="text-xs text-blue-600 mt-1">Due: In 25 days</p>
                  </div>

                  <div className="p-4 border-l-4 border-emerald-500 bg-emerald-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <p className="font-medium text-emerald-900">Risk Assessment Update</p>
                      <Badge className="bg-emerald-100 text-emerald-800">On Track</Badge>
                    </div>
                    <p className="text-sm text-emerald-700">Annual risk assessment review</p>
                    <p className="text-xs text-emerald-600 mt-1">Due: In 45 days</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Regulatory Guidelines & Actions */}
          <div className="grid gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-emerald-600" />
                  Key RBI Guidelines
                </CardTitle>
                <CardDescription>
                  Current regulatory requirements and updates
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Master Circular on Prudential Norms</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      Updated guidelines on asset classification and provisioning
                    </p>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">Updated: Oct 2024</Badge>
                      <Button size="sm" variant="outline">
                        View Details
                      </Button>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">MSME Lending Guidelines</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      Special provisions for MSME sector lending
                    </p>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">Effective: Sep 2024</Badge>
                      <Button size="sm" variant="outline">
                        View Details
                      </Button>
                    </div>
                  </div>

                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Digital Lending Guidelines</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      Compliance requirements for digital lending platforms
                    </p>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">New: Aug 2024</Badge>
                      <Button size="sm" variant="outline">
                        View Details
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-emerald-600" />
                  Compliance Actions
                </CardTitle>
                <CardDescription>
                  Required actions and recommendations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 border-l-4 border-red-500 bg-red-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-red-900">Critical Action Required</h4>
                      <Badge variant="destructive">High Priority</Badge>
                    </div>
                    <p className="text-sm text-red-700 mb-2">
                      Submit overdue stress testing report to RBI
                    </p>
                    <Button size="sm" className="bg-red-600 hover:bg-red-700">
                      Take Action
                    </Button>
                  </div>

                  <div className="p-4 border-l-4 border-orange-500 bg-orange-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-orange-900">Review Documentation</h4>
                      <Badge className="bg-orange-100 text-orange-800">Medium Priority</Badge>
                    </div>
                    <p className="text-sm text-orange-700 mb-2">
                      Update loan documentation for 15 MSME accounts
                    </p>
                    <Button size="sm" variant="outline">
                      Review Now
                    </Button>
                  </div>

                  <div className="p-4 border-l-4 border-blue-500 bg-blue-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-blue-900">System Enhancement</h4>
                      <Badge className="bg-blue-100 text-blue-800">Recommended</Badge>
                    </div>
                    <p className="text-sm text-blue-700 mb-2">
                      Implement automated CRILC reporting system
                    </p>
                    <Button size="sm" variant="outline">
                      Learn More
                    </Button>
                  </div>

                  <div className="p-4 border-l-4 border-emerald-500 bg-emerald-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-emerald-900">Training Update</h4>
                      <Badge className="bg-emerald-100 text-emerald-800">Scheduled</Badge>
                    </div>
                    <p className="text-sm text-emerald-700 mb-2">
                      Complete RBI compliance training for credit team
                    </p>
                    <Button size="sm" variant="outline">
                      Schedule
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
});
