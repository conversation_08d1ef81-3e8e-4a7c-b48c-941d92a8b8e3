apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: credit-chakra-backend
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      annotations:
        run.googleapis.com/cpu-throttling: "false"
        run.googleapis.com/memory: "1Gi"
        run.googleapis.com/cpu: "1000m"
        run.googleapis.com/max-scale: "10"
        run.googleapis.com/min-scale: "0"
    spec:
      containerConcurrency: 80
      timeoutSeconds: 300
      containers:
      - image: gcr.io/PROJECT_ID/credit-chakra-backend:latest
        ports:
        - name: http1
          containerPort: 8000
        env:
        - name: PORT
          value: "8000"
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: DEBUG
          value: "false"
        - name: ALLOWED_ORIGINS
          value: "https://credit-chakra-frontend.web.app,https://credit-chakra.com"
        resources:
          limits:
            cpu: 1000m
            memory: 1Gi
          requests:
            cpu: 500m
            memory: 512Mi
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 2
