"""
Reports service for Credit Chakra MSME credit monitoring dashboard.

This service handles the business logic for generating all types of reports including:
- Portfolio Summary Report
- Risk Exposure Report
- Compliance & Regulatory Report
- Performance Trends Report
- Detailed MSME Profile Report

Author: Credit Chakra Team
Version: 1.0.0
"""
from __future__ import annotations

import uuid
from datetime import datetime, timezone, date, timedelta, UTC
from typing import Any, Dict, List, Optional
import logging

from models.reports import (
    ReportType, ReportStatus, BaseReportMetadata,
    PortfolioSummaryReport, RiskDistribution, SMAAccountsSummary, BusinessTypeDistribution,
    RiskExposureReport, GeographicRiskDistribution, SectorExposure, HighRiskMSME, 
    RiskTrendData, RegulatoryThresholdAlert,
    ComplianceRegulatoryReport, GSTComplianceStatus, UdyamRegistrationStatus, ComplianceMetric,
    PerformanceTrendsReport, MonthlyPerformanceMetric, SignalGenerationMetrics, NudgeEffectivenessMetrics,
    DetailedMSMEProfileReport, MSMEParameterScore, SignalHistoryEntry, NudgeHistoryEntry, 
    CashFlowAnalysis, AuditTrailEntry
)
from services.data_service import data_service
# from services.compliance_service import ComplianceService  # Not needed for now
from firebase.init import get_firestore_client

logger = logging.getLogger(__name__)


class ReportsService:
    """Service for generating comprehensive reports"""
    
    def __init__(self):
        self.db = get_firestore_client()
    
    def _generate_report_metadata(self, report_type: ReportType, start_date: date, end_date: date, 
                                generated_by: str = "system") -> BaseReportMetadata:
        """Generate base metadata for reports"""
        return BaseReportMetadata(
            report_id=str(uuid.uuid4()),
            report_type=report_type,
            generated_at=datetime.now(UTC),
            generated_by=generated_by,
            reporting_period_start=start_date,
            reporting_period_end=end_date,
            status=ReportStatus.GENERATING
        )
    
    async def generate_portfolio_summary_report(self, start_date: date, end_date: date, 
                                              generated_by: str = "system") -> PortfolioSummaryReport:
        """Generate Portfolio Summary Report"""
        try:
            logger.info(f"Generating Portfolio Summary Report for period {start_date} to {end_date}")
            
            # Generate metadata
            metadata = self._generate_report_metadata(ReportType.PORTFOLIO_SUMMARY, start_date, end_date, generated_by)
            
            # Get portfolio data
            portfolio_data = [msme.__dict__ for msme in data_service.get_all_msmes()]
            analytics_data = data_service.get_portfolio_analytics()
            
            # Calculate risk distribution
            total_msmes = len(portfolio_data)
            high_risk = len([m for m in portfolio_data if m.get('risk_band') == 'red'])
            medium_risk = len([m for m in portfolio_data if m.get('risk_band') == 'yellow'])
            low_risk = len([m for m in portfolio_data if m.get('risk_band') == 'green'])
            
            risk_distribution = RiskDistribution(
                high_risk_count=high_risk,
                high_risk_percentage=round((high_risk / total_msmes) * 100, 2) if total_msmes > 0 else 0,
                medium_risk_count=medium_risk,
                medium_risk_percentage=round((medium_risk / total_msmes) * 100, 2) if total_msmes > 0 else 0,
                low_risk_count=low_risk,
                low_risk_percentage=round((low_risk / total_msmes) * 100, 2) if total_msmes > 0 else 0
            )
            
            # Calculate SMA accounts summary
            sma_0 = len([m for m in portfolio_data if m.get('sma_classification') == 'SMA-0'])
            sma_1 = len([m for m in portfolio_data if m.get('sma_classification') == 'SMA-1'])
            sma_2 = len([m for m in portfolio_data if m.get('sma_classification') == 'SMA-2'])
            standard = total_msmes - sma_0 - sma_1 - sma_2
            
            total_overdue = sum([m.get('outstanding_amount', 0) for m in portfolio_data if m.get('days_past_due', 0) > 0])
            total_provision = sum([m.get('provision_required', 0) for m in portfolio_data])
            
            sma_summary = SMAAccountsSummary(
                standard_accounts=standard,
                sma_0_count=sma_0,
                sma_1_count=sma_1,
                sma_2_count=sma_2,
                total_overdue_amount=total_overdue,
                provision_required=total_provision
            )
            
            # Calculate business type distribution
            business_types = {}
            for msme in portfolio_data:
                btype = msme.get('business_type', 'unknown')
                business_types[btype] = business_types.get(btype, 0) + 1
            
            business_distribution = BusinessTypeDistribution(
                retail_count=business_types.get('retail', 0),
                retail_percentage=round((business_types.get('retail', 0) / total_msmes) * 100, 2) if total_msmes > 0 else 0,
                manufacturing_count=business_types.get('manufacturing', 0),
                manufacturing_percentage=round((business_types.get('manufacturing', 0) / total_msmes) * 100, 2) if total_msmes > 0 else 0,
                services_count=business_types.get('services', 0),
                services_percentage=round((business_types.get('services', 0) / total_msmes) * 100, 2) if total_msmes > 0 else 0,
                b2b_count=business_types.get('b2b', 0),
                b2b_percentage=round((business_types.get('b2b', 0) / total_msmes) * 100, 2) if total_msmes > 0 else 0
            )
            
            # Calculate portfolio health metrics
            avg_score = sum([m.get('score', 0) for m in portfolio_data]) / total_msmes if total_msmes > 0 else 0
            portfolio_health = 85.0 - (high_risk * 2) - (medium_risk * 0.5)  # Simple health calculation
            
            # Generate insights and recommendations
            high_risk_percentage = risk_distribution.high_risk_percentage
            insights = [
                f"Portfolio contains {total_msmes} MSMEs with {high_risk_percentage}% high risk exposure",
                f"Average credit score is {avg_score:.1f} with {low_risk} MSMEs in low risk category",
                f"SMA classification shows {sma_0 + sma_1 + sma_2} accounts requiring attention"
            ]
            
            recommendations = [
                "Focus on high-risk MSMEs for immediate intervention",
                "Implement proactive monitoring for medium-risk accounts",
                "Leverage successful low-risk MSME patterns for portfolio improvement"
            ]
            
            # Update metadata
            metadata.status = ReportStatus.COMPLETED
            metadata.total_records = total_msmes
            
            # Create report
            report = PortfolioSummaryReport(
                metadata=metadata,
                total_msmes=total_msmes,
                active_msmes=total_msmes,  # Assuming all are active for now
                inactive_msmes=0,
                risk_distribution=risk_distribution,
                sma_accounts_summary=sma_summary,
                business_type_distribution=business_distribution,
                overall_portfolio_health_score=portfolio_health,
                average_credit_score=avg_score,
                portfolio_growth_rate=5.2,  # Mock growth rate
                key_insights=insights,
                recommendations=recommendations
            )
            
            logger.info(f"Successfully generated Portfolio Summary Report: {metadata.report_id}")
            return report
            
        except Exception as e:
            logger.error(f"Error generating Portfolio Summary Report: {str(e)}")
            raise
    
    async def generate_risk_exposure_report(self, start_date: date, end_date: date, 
                                          generated_by: str = "system") -> RiskExposureReport:
        """Generate Risk Exposure Report"""
        try:
            logger.info(f"Generating Risk Exposure Report for period {start_date} to {end_date}")
            
            # Generate metadata
            metadata = self._generate_report_metadata(ReportType.RISK_EXPOSURE, start_date, end_date, generated_by)
            
            # Get portfolio data
            portfolio_data = [msme.__dict__ for msme in data_service.get_all_msmes()]
            
            # Calculate geographic risk distribution
            geographic_data = {}
            for msme in portfolio_data:
                location = msme.get('location', 'Unknown')
                if location not in geographic_data:
                    geographic_data[location] = {
                        'total': 0, 'high_risk': 0, 'exposure': 0
                    }
                geographic_data[location]['total'] += 1
                if msme.get('risk_band') == 'red':
                    geographic_data[location]['high_risk'] += 1
                geographic_data[location]['exposure'] += msme.get('outstanding_amount', 100000)  # Mock exposure
            
            geographic_distribution = []
            for state, data in geographic_data.items():
                geographic_distribution.append(GeographicRiskDistribution(
                    state=state,
                    msme_count=data['total'],
                    high_risk_count=data['high_risk'],
                    total_exposure=data['exposure'],
                    risk_concentration=round((data['high_risk'] / data['total']) * 100, 2) if data['total'] > 0 else 0
                ))
            
            # Calculate sector exposure
            sector_data = {}
            for msme in portfolio_data:
                sector = msme.get('business_type', 'Unknown')
                if sector not in sector_data:
                    sector_data[sector] = {
                        'count': 0, 'exposure': 0, 'scores': []
                    }
                sector_data[sector]['count'] += 1
                sector_data[sector]['exposure'] += msme.get('outstanding_amount', 100000)
                sector_data[sector]['scores'].append(msme.get('score', 50))
            
            sector_exposure = []
            total_exposure = sum([data['exposure'] for data in sector_data.values()])
            
            for sector, data in sector_data.items():
                avg_score = sum(data['scores']) / len(data['scores']) if data['scores'] else 0
                concentration = (data['exposure'] / total_exposure) * 100 if total_exposure > 0 else 0
                
                sector_exposure.append(SectorExposure(
                    sector=sector,
                    msme_count=data['count'],
                    total_exposure=data['exposure'],
                    average_risk_score=avg_score,
                    concentration_risk=concentration,
                    regulatory_limit=25.0,  # Mock regulatory limit
                    breach_status=concentration > 25.0
                ))
            
            # Get top 10 high risk MSMEs
            high_risk_msmes = [m for m in portfolio_data if m.get('risk_band') == 'red']
            high_risk_msmes.sort(key=lambda x: x.get('score', 0))  # Sort by lowest score first
            
            top_10_high_risk = []
            for msme in high_risk_msmes[:10]:
                top_10_high_risk.append(HighRiskMSME(
                    msme_id=msme.get('msme_id', ''),
                    name=msme.get('name', ''),
                    business_type=msme.get('business_type', ''),
                    location=msme.get('location', ''),
                    current_score=msme.get('score', 0),
                    risk_factors=["Low GST compliance", "Irregular payments", "High debt ratio"],
                    exposure_amount=msme.get('outstanding_amount', 100000),
                    days_past_due=msme.get('days_past_due', 0),
                    last_review_date=date.today() - timedelta(days=30)
                ))
            
            # Generate mock risk trends (6 months)
            risk_trends = []
            for i in range(6):
                month_date = date.today() - timedelta(days=30 * i)
                risk_trends.append(RiskTrendData(
                    period=month_date.strftime("%Y-%m"),
                    average_risk_score=65.0 + (i * 2),
                    high_risk_count=len(high_risk_msmes) + i,
                    new_defaults=2 + (i % 3),
                    recovered_accounts=1 + (i % 2)
                ))
            
            # Generate regulatory threshold alerts
            threshold_alerts = []
            for sector_exp in sector_exposure:
                if sector_exp.breach_status:
                    threshold_alerts.append(RegulatoryThresholdAlert(
                        threshold_type=f"Sector Concentration - {sector_exp.sector}",
                        current_value=sector_exp.concentration_risk,
                        threshold_limit=sector_exp.regulatory_limit,
                        breach_percentage=sector_exp.concentration_risk - sector_exp.regulatory_limit,
                        severity="high" if sector_exp.concentration_risk > 30 else "medium",
                        action_required="Reduce sector exposure or increase diversification"
                    ))
            
            # Update metadata
            metadata.status = ReportStatus.COMPLETED
            metadata.total_records = len(portfolio_data)
            
            # Create report
            report = RiskExposureReport(
                metadata=metadata,
                geographic_distribution=geographic_distribution,
                top_risk_states=[state for state, _ in sorted(geographic_data.items(), 
                                key=lambda x: x[1]['high_risk'], reverse=True)[:5]],
                sector_exposure=sector_exposure,
                concentration_risks=[f"High concentration in {s.sector}" for s in sector_exposure if s.concentration_risk > 20],
                top_10_high_risk_msmes=top_10_high_risk,
                risk_trends_6_months=risk_trends,
                regulatory_threshold_alerts=threshold_alerts,
                total_portfolio_exposure=total_exposure,
                weighted_average_risk=sum([m.get('score', 0) for m in portfolio_data]) / len(portfolio_data) if portfolio_data else 0,
                diversification_index=75.5  # Mock diversification index
            )
            
            logger.info(f"Successfully generated Risk Exposure Report: {metadata.report_id}")
            return report
            
        except Exception as e:
            logger.error(f"Error generating Risk Exposure Report: {str(e)}")
            raise


    async def generate_compliance_regulatory_report(self, start_date: date, end_date: date,
                                                  generated_by: str = "system") -> ComplianceRegulatoryReport:
        """Generate Compliance & Regulatory Report"""
        try:
            logger.info(f"Generating Compliance & Regulatory Report for period {start_date} to {end_date}")

            # Generate metadata
            metadata = self._generate_report_metadata(ReportType.COMPLIANCE_REGULATORY, start_date, end_date, generated_by)

            # Get portfolio data
            portfolio_data = [msme.__dict__ for msme in data_service.get_all_msmes()]

            # Calculate SMA classification breakdown (reuse from portfolio summary)
            sma_0 = len([m for m in portfolio_data if m.get('sma_classification') == 'SMA-0'])
            sma_1 = len([m for m in portfolio_data if m.get('sma_classification') == 'SMA-1'])
            sma_2 = len([m for m in portfolio_data if m.get('sma_classification') == 'SMA-2'])
            standard = len(portfolio_data) - sma_0 - sma_1 - sma_2

            sma_breakdown = SMAAccountsSummary(
                standard_accounts=standard,
                sma_0_count=sma_0,
                sma_1_count=sma_1,
                sma_2_count=sma_2,
                total_overdue_amount=sum([m.get('outstanding_amount', 0) for m in portfolio_data if m.get('days_past_due', 0) > 0]),
                provision_required=sum([m.get('provision_required', 0) for m in portfolio_data])
            )

            # Calculate GST compliance status
            total_with_gst = len([m for m in portfolio_data if m.get('gst_number')])
            gst_compliant = len([m for m in portfolio_data if m.get('gst_compliance', 0) >= 80])
            gst_non_compliant = len([m for m in portfolio_data if m.get('gst_compliance', 0) < 60])
            gst_pending = total_with_gst - gst_compliant - gst_non_compliant

            gst_status = GSTComplianceStatus(
                total_msmes_with_gst=total_with_gst,
                compliant_msmes=gst_compliant,
                non_compliant_msmes=gst_non_compliant,
                pending_verification=gst_pending,
                compliance_rate=round((gst_compliant / total_with_gst) * 100, 2) if total_with_gst > 0 else 0
            )

            # Calculate Udyam registration status
            total_msmes = len(portfolio_data)
            verified_udyam = int(total_msmes * 0.85)  # Mock 85% verification rate
            pending_udyam = int(total_msmes * 0.12)   # Mock 12% pending
            invalid_udyam = total_msmes - verified_udyam - pending_udyam

            udyam_status = UdyamRegistrationStatus(
                total_msmes=total_msmes,
                verified_registrations=verified_udyam,
                pending_verification=pending_udyam,
                invalid_registrations=invalid_udyam,
                verification_rate=round((verified_udyam / total_msmes) * 100, 2)
            )

            # Generate regulatory readiness checklist
            readiness_checklist = [
                ComplianceMetric(
                    metric_name="RBI SMA Classification",
                    current_value=95.0,
                    target_value=100.0,
                    compliance_status="compliant",
                    last_updated=datetime.now(UTC)
                ),
                ComplianceMetric(
                    metric_name="GST Compliance Rate",
                    current_value=gst_status.compliance_rate,
                    target_value=90.0,
                    compliance_status="compliant" if gst_status.compliance_rate >= 90 else "warning",
                    last_updated=datetime.now(UTC)
                ),
                ComplianceMetric(
                    metric_name="Account Aggregator Integration",
                    current_value=78.0,
                    target_value=85.0,
                    compliance_status="warning",
                    last_updated=datetime.now(UTC)
                )
            ]

            # Calculate overall compliance score
            compliance_scores = [metric.current_value for metric in readiness_checklist]
            overall_compliance = sum(compliance_scores) / len(compliance_scores) if compliance_scores else 0

            # Update metadata
            metadata.status = ReportStatus.COMPLETED
            metadata.total_records = total_msmes

            # Create report
            report = ComplianceRegulatoryReport(
                metadata=metadata,
                sma_classification_breakdown=sma_breakdown,
                early_warning_signals_summary={
                    "payment_delays": sma_0 + sma_1 + sma_2,
                    "gst_non_compliance": gst_non_compliant,
                    "irregular_transactions": int(total_msmes * 0.15)
                },
                gst_compliance_status=gst_status,
                aa_data_compliance={
                    "connected_accounts": int(total_msmes * 0.78),
                    "data_quality_score": 85.5,
                    "consent_compliance": 92.3
                },
                udyam_registration_status=udyam_status,
                regulatory_readiness_checklist=readiness_checklist,
                overall_compliance_score=overall_compliance,
                compliance_trend="improving"
            )

            logger.info(f"Successfully generated Compliance & Regulatory Report: {metadata.report_id}")
            return report

        except Exception as e:
            logger.error(f"Error generating Compliance & Regulatory Report: {str(e)}")
            raise

    async def generate_performance_trends_report(self, start_date: date, end_date: date,
                                               generated_by: str = "system") -> PerformanceTrendsReport:
        """Generate Performance Trends Report"""
        try:
            logger.info(f"Generating Performance Trends Report for period {start_date} to {end_date}")

            # Generate metadata
            metadata = self._generate_report_metadata(ReportType.PERFORMANCE_TRENDS, start_date, end_date, generated_by)

            # Get portfolio data
            portfolio_data = [msme.__dict__ for msme in data_service.get_all_msmes()]
            total_msmes = len(portfolio_data)

            # Generate monthly performance metrics (last 12 months)
            monthly_performance = []
            for i in range(12):
                month_date = date.today() - timedelta(days=30 * i)
                monthly_performance.append(MonthlyPerformanceMetric(
                    month=month_date.strftime("%Y-%m"),
                    total_msmes=total_msmes + (i * 2),  # Mock growth
                    new_onboarding=5 + (i % 3),
                    attrition_count=2 + (i % 2),
                    average_score=650 + (i * 5),
                    score_improvement_count=8 + (i % 4),
                    score_deterioration_count=3 + (i % 2)
                ))

            # Generate signal generation metrics
            signal_metrics = SignalGenerationMetrics(
                total_signals_generated=total_msmes * 15,  # Average 15 signals per MSME
                signals_by_source={
                    "GST": total_msmes * 5,
                    "Banking": total_msmes * 4,
                    "UPI": total_msmes * 3,
                    "Reviews": total_msmes * 2,
                    "Manual": total_msmes * 1
                },
                average_resolution_time=24.5,  # Hours
                resolution_rate=87.3
            )

            # Generate nudge effectiveness metrics
            nudge_metrics = NudgeEffectivenessMetrics(
                total_nudges_sent=total_msmes * 8,  # Average 8 nudges per MSME
                delivery_rate=94.2,
                response_rate=68.7,
                effectiveness_by_medium={
                    "WhatsApp": 72.5,
                    "Email": 45.3,
                    "SMS": 38.9
                },
                average_response_time=4.2  # Hours
            )

            # Update metadata
            metadata.status = ReportStatus.COMPLETED
            metadata.total_records = total_msmes

            # Create report
            report = PerformanceTrendsReport(
                metadata=metadata,
                monthly_performance=monthly_performance,
                quarterly_summary={
                    "Q1_2024": {"growth": 12.5, "avg_score": 675},
                    "Q2_2024": {"growth": 8.3, "avg_score": 682},
                    "Q3_2024": {"growth": 15.7, "avg_score": 695},
                    "Q4_2024": {"growth": 11.2, "avg_score": 708}
                },
                onboarding_vs_attrition_trends={
                    "onboarding": [15, 18, 22, 19, 25, 28],
                    "attrition": [5, 7, 4, 6, 8, 5]
                },
                score_improvement_patterns={
                    "retail": 15.2,
                    "manufacturing": 12.8,
                    "services": 18.5,
                    "b2b": 14.1
                },
                score_deterioration_patterns={
                    "retail": 8.3,
                    "manufacturing": 12.1,
                    "services": 6.7,
                    "b2b": 9.4
                },
                signal_generation_metrics=signal_metrics,
                nudge_effectiveness_metrics=nudge_metrics,
                yoy_portfolio_growth=22.5,
                yoy_score_improvement=8.7,
                key_performance_indicators={
                    "portfolio_health_score": 85.3,
                    "risk_adjusted_return": 12.8,
                    "customer_satisfaction": 4.2,
                    "operational_efficiency": 78.9
                }
            )

            logger.info(f"Successfully generated Performance Trends Report: {metadata.report_id}")
            return report

        except Exception as e:
            logger.error(f"Error generating Performance Trends Report: {str(e)}")
            raise

    async def generate_detailed_msme_profile_report(self, msme_id: str, start_date: date, end_date: date,
                                                  generated_by: str = "system") -> DetailedMSMEProfileReport:
        """Generate Detailed MSME Profile Report"""
        try:
            logger.info(f"Generating Detailed MSME Profile Report for MSME {msme_id}")

            # Generate metadata
            metadata = self._generate_report_metadata(ReportType.DETAILED_MSME_PROFILE, start_date, end_date, generated_by)

            # Get MSME data
            portfolio_data = [msme.__dict__ for msme in data_service.get_all_msmes()]
            msme_data = next((m for m in portfolio_data if m.get('msme_id') == msme_id), None)

            if not msme_data:
                raise ValueError(f"MSME with ID {msme_id} not found")

            # Generate parameter scores breakdown
            parameter_scores = [
                MSMEParameterScore(
                    parameter_name="GST Compliance",
                    current_score=msme_data.get('gst_compliance', 75),
                    weight_percentage=18.0,
                    contribution_to_score=msme_data.get('gst_compliance', 75) * 0.18,
                    trend="improving",
                    last_updated=datetime.now(timezone.utc)
                ),
                MSMEParameterScore(
                    parameter_name="Banking Health",
                    current_score=msme_data.get('banking_health', 82),
                    weight_percentage=15.0,
                    contribution_to_score=msme_data.get('banking_health', 82) * 0.15,
                    trend="stable",
                    last_updated=datetime.now(timezone.utc)
                ),
                MSMEParameterScore(
                    parameter_name="Digital Payment Score",
                    current_score=msme_data.get('digital_score', 68),
                    weight_percentage=12.0,
                    contribution_to_score=msme_data.get('digital_score', 68) * 0.12,
                    trend="improving",
                    last_updated=datetime.now(timezone.utc)
                )
            ]

            # Generate mock signal history
            signal_history = [
                SignalHistoryEntry(
                    signal_id=f"sig_{i}",
                    signal_type="payment_delay" if i % 2 == 0 else "gst_compliance",
                    source="banking" if i % 2 == 0 else "gst_api",
                    generated_at=datetime.now(timezone.utc) - timedelta(days=i*7),
                    severity="medium" if i % 3 == 0 else "low",
                    status="resolved" if i % 2 == 0 else "active",
                    resolution_notes="Resolved through customer contact" if i % 2 == 0 else None
                ) for i in range(10)
            ]

            # Generate mock nudge history
            nudge_history = [
                NudgeHistoryEntry(
                    nudge_id=f"nudge_{i}",
                    nudge_type="payment_reminder" if i % 2 == 0 else "compliance_alert",
                    medium="whatsapp" if i % 3 == 0 else "email",
                    sent_at=datetime.now(timezone.utc) - timedelta(days=i*5),
                    delivery_status="delivered",
                    response_status="responded" if i % 2 == 0 else "no_response",
                    effectiveness_score=0.8 if i % 2 == 0 else 0.3
                ) for i in range(8)
            ]

            # Generate cash flow analysis
            cash_flow = CashFlowAnalysis(
                monthly_inflow=msme_data.get('monthly_turnover', 500000),
                monthly_outflow=msme_data.get('monthly_turnover', 500000) * 0.85,
                net_cash_flow=msme_data.get('monthly_turnover', 500000) * 0.15,
                cash_flow_volatility=0.25,
                seasonal_patterns={
                    "Q1": 0.9, "Q2": 1.1, "Q3": 1.2, "Q4": 0.8
                }
            )

            # Generate SMA classification timeline
            sma_timeline = [
                {
                    "date": (date.today() - timedelta(days=i*30)).isoformat(),
                    "classification": "standard" if i < 2 else "SMA-0",
                    "days_past_due": 0 if i < 2 else min(i*10, 30)
                } for i in range(6)
            ]

            # Generate audit trail
            audit_trail = [
                AuditTrailEntry(
                    timestamp=datetime.now(timezone.utc) - timedelta(days=i*7),
                    action_type="score_update" if i % 2 == 0 else "signal_generated",
                    performed_by="system" if i % 3 == 0 else "credit_manager",
                    details=f"Credit score updated to {msme_data.get('score', 650) + i}" if i % 2 == 0 else "Payment delay signal generated",
                    impact="positive" if i % 2 == 0 else "neutral"
                ) for i in range(15)
            ]

            # Update metadata
            metadata.status = ReportStatus.COMPLETED
            metadata.total_records = 1

            # Create report
            report = DetailedMSMEProfileReport(
                metadata=metadata,
                msme_id=msme_id,
                msme_name=msme_data.get('name', ''),
                business_type=msme_data.get('business_type', ''),
                location=msme_data.get('location', ''),
                overall_credit_score=msme_data.get('score', 0),
                risk_band=msme_data.get('risk_band', 'green'),
                parameter_scores=parameter_scores,
                signal_history=signal_history,
                nudge_history=nudge_history,
                cash_flow_analysis=cash_flow,
                upi_transaction_patterns={
                    "monthly_transactions": 450,
                    "average_transaction_value": 1250,
                    "peak_transaction_hours": ["10-12", "15-17"],
                    "transaction_growth": 15.3
                },
                sma_classification_timeline=sma_timeline,
                overdue_payment_tracking={
                    "current_overdue": msme_data.get('outstanding_amount', 0),
                    "days_past_due": msme_data.get('days_past_due', 0),
                    "payment_history_score": 85.2
                },
                audit_trail=audit_trail,
                risk_mitigation_recommendations=[
                    "Implement automated payment reminders",
                    "Improve GST compliance monitoring",
                    "Enhance digital payment adoption"
                ],
                improvement_opportunities=[
                    "Increase digital transaction volume",
                    "Diversify revenue streams",
                    "Strengthen cash flow management"
                ]
            )

            logger.info(f"Successfully generated Detailed MSME Profile Report: {metadata.report_id}")
            return report

        except Exception as e:
            logger.error(f"Error generating Detailed MSME Profile Report: {str(e)}")
            raise


# Create singleton instance
reports_service = ReportsService()
