'use client';

import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { QuickActions } from './quick-actions';
import { InsightsFeed } from './insights-feed';
import { QuerySuggestions } from './query-suggestions';
import { MessageList } from './message-list';
import ErrorBoundary, { CopilotErrorFallback } from './error-boundary';
import { ArrowLeft, Sparkles, Brain, Send, Loader2, AlertTriangle, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { api } from '@/lib/api';
import {
  ChatMessage,
  ConversationContext,
  CopilotPageProps,
  LoadingState,
  CopilotError
} from '@/types/copilot';
import {
  usePerformanceMonitor,
  useOptimizedMessages,
  useOptimizedScroll,
  useThrottledApiCall,
  useDebouncedState,
  useMemoryMonitor
} from '@/lib/performance';
import { useInputValidation } from '@/hooks/useValidation';
import { validateChatMessage } from '@/lib/validation';
import Link from 'next/link';

export function CopilotPage({
  initialMessages = [],
  defaultQuickActionsOpen = false,
  defaultInsightsOpen = false
}: CopilotPageProps = {}) {
  // Performance monitoring
  const performanceMetrics = usePerformanceMonitor('CopilotPage');
  const memoryUsage = useMemoryMonitor();

  // Enhanced state management with performance optimizations
  const [showQuickActions, setShowQuickActions] = useState(defaultQuickActionsOpen);
  const [showInsights, setShowInsights] = useState(defaultInsightsOpen);
  const [rawMessages, setRawMessages] = useState<ChatMessage[]>(initialMessages);
  const [loadingState, setLoadingState] = useState<LoadingState>('idle');
  const [isTyping, setIsTyping] = useState(false);
  const [conversationContext, setConversationContext] = useState<ConversationContext>({});
  const [error, setError] = useState<CopilotError | null>(null);

  // Memory management: Limit message history to prevent memory leaks
  const MAX_MESSAGES = 100;
  const trimMessages = useCallback((messages: ChatMessage[]) => {
    if (messages.length > MAX_MESSAGES) {
      return messages.slice(-MAX_MESSAGES);
    }
    return messages;
  }, []);

  // Debounced input for better performance
  const [inputValue, setInputValueImmediate] = useDebouncedState('', 150);

  // Input validation for security
  const chatValidation = useInputValidation('chatMessage', { required: true, maxLength: 1000 });

  // Optimized message processing
  const messages = useOptimizedMessages(rawMessages);

  // Refs for performance optimization
  const inputRef = useRef<HTMLInputElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Optimized scroll behavior - memoize dependencies to prevent infinite loops
  const scrollDependencies = useMemo(() => [messages.length, isTyping], [messages.length, isTyping]);
  useOptimizedScroll(scrollAreaRef as React.RefObject<HTMLDivElement>, scrollDependencies);

  // Cleanup resources on unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  // Throttled API call for better performance
  const throttledApiCall = useThrottledApiCall(api.askCopilot, 1000);

  // Optimized focus handler
  const focusInput = useCallback((text?: string) => {
    if (inputRef.current) {
      inputRef.current.focus();
      if (text) {
        inputRef.current.setSelectionRange(text.length, text.length);
      }
    }
  }, []);

  // Handler for when a query is selected from Quick Actions
  const handleQuerySelect = useCallback((query: string) => {
    setInputValueImmediate(query);
    setTimeout(() => focusInput(query), 100);
  }, [focusInput, setInputValueImmediate]);

  // Handler for when a suggestion is clicked
  const handleSuggestionClick = useCallback((suggestion: string) => {
    setInputValueImmediate(suggestion);
    setTimeout(() => focusInput(suggestion), 100);
  }, [focusInput, setInputValueImmediate]);

  // Enhanced message sending with performance optimizations and validation
  const handleSendMessage = useCallback(async (messageText?: string) => {
    const rawText = messageText || inputValue.trim();
    if (!rawText || loadingState === 'loading') return;

    // Validate and sanitize input for security using direct validation
    const validationResult = validateChatMessage(rawText);

    if (!validationResult.isValid) {
      setError({
        code: 'INVALID_INPUT',
        message: 'Invalid message format',
        details: validationResult.errors.join(', '),
        timestamp: new Date(),
        recoverable: true,
        suggested_actions: ['Please check your message and try again']
      });
      return;
    }

    const queryText = validationResult.sanitizedValue;

    // Additional safety check to prevent empty queries
    if (!queryText || queryText.trim().length === 0) {
      setError({
        code: 'EMPTY_QUERY',
        message: 'Please enter a message',
        details: 'Query cannot be empty',
        timestamp: new Date(),
        recoverable: true,
        suggested_actions: ['Enter a message and try again']
      });
      return;
    }

    // Performance: Batch state updates
    const startTime = performance.now();

    // Clear any previous errors
    setError(null);

    const userMessage: ChatMessage = {
      id: `user_${Date.now()}`,
      type: 'user',
      content: queryText,
      timestamp: new Date(),
    };

    // Optimized state updates with memory management
    setRawMessages(prev => trimMessages([...prev, userMessage]));
    if (!messageText) setInputValueImmediate('');
    setLoadingState('loading');
    setIsTyping(true);

    try {
      // Setup abort controller for timeout
      abortControllerRef.current = new AbortController();
      timeoutRef.current = setTimeout(() => {
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
      }, 30000);

      // Prepare context for API call
      const cleanMessages = messages.slice(-5).map(msg => ({
        id: msg.id,
        type: msg.type,
        content: msg.content,
        timestamp: msg.timestamp.toISOString()
      }));

      const enhancedContext = {
        conversation_history: cleanMessages,
        user_intent: 'analysis' as const,
        timestamp: new Date().toISOString(),
        conversation_context: conversationContext as Record<string, unknown>,
        user_id: 'default_user',
        session_id: `session_${Date.now()}`,
        query_metadata: {
          query_length: queryText.length,
          query_type: queryText.includes('?') ? 'question' as const : 'command' as const,
          contains_numbers: /\d/.test(queryText),
          urgency_indicators: ['urgent', 'immediate', 'asap', 'critical'].some(word =>
            queryText.toLowerCase().includes(word)
          )
        }
      };

      // Use throttled API call for better performance
      const response = await throttledApiCall({
        query: queryText,
        context: enhancedContext
      });

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
      setIsTyping(false);

      const assistantMessage: ChatMessage = {
        id: response.query_id,
        type: 'assistant',
        content: response.response,
        timestamp: new Date(response.timestamp),
        data: response.data ? {
          ...response.data,
          processing_time_ms: 0,
          response_type: (response.data as Record<string, unknown>).response_type as string || 'general',
          confidence: (response.data as Record<string, unknown>).confidence as number || 0.8,
          topics: (response.data as Record<string, unknown>).topics as string[] || []
        } as import('@/types/copilot').CopilotResponseData : undefined
      };

      // Performance: Add processing time to metrics
      const processingTime = performance.now() - startTime;
      console.log(`Message processed in ${processingTime.toFixed(2)}ms`);

      setRawMessages(prev => trimMessages([...prev, assistantMessage]));

      // Update conversation context
      setConversationContext(prev => ({
        ...prev,
        last_query: queryText,
        last_response_type: response.data?.response_type || 'general',
        topics_discussed: [
          ...(prev.topics_discussed || []),
          ...(response.data?.topics || [])
        ].slice(-10),
        user_satisfaction: response.data?.confidence || 0.8
      }));

    } catch (error) {
      console.error('Chat API Error:', error);
      setIsTyping(false);
      setLoadingState('error');

      // Enhanced error handling with structured error objects
      let copilotError: CopilotError;
      let errorContent = 'I apologize, but I encountered an error processing your request.';

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          copilotError = {
            code: 'TIMEOUT',
            message: 'Request timed out',
            details: 'The AI service took too long to respond',
            timestamp: new Date(),
            recoverable: true,
            suggested_actions: ['Try a simpler query', 'Use quick action buttons', 'Check your connection']
          };
          errorContent = 'The request timed out. Please try a more focused query or use the quick action buttons.';
        } else if (error.message.includes('fetch') || error.message.includes('network')) {
          copilotError = {
            code: 'NETWORK_ERROR',
            message: 'Network connection failed',
            details: error.message,
            timestamp: new Date(),
            recoverable: true,
            suggested_actions: ['Check your internet connection', 'Refresh the page', 'Try again in a moment']
          };
          errorContent = 'Unable to connect to the AI service. Please check your connection and try again.';
        } else {
          copilotError = {
            code: 'UNKNOWN_ERROR',
            message: 'An unexpected error occurred',
            details: error.message,
            timestamp: new Date(),
            recoverable: true,
            suggested_actions: ['Try again', 'Use quick actions', 'Contact support if the issue persists']
          };
        }
      } else {
        copilotError = {
          code: 'UNKNOWN_ERROR',
          message: 'An unexpected error occurred',
          timestamp: new Date(),
          recoverable: true,
          suggested_actions: ['Try again', 'Use quick actions']
        };
      }

      setError(copilotError);

      const errorMessage: ChatMessage = {
        id: `error_${Date.now()}`,
        type: 'assistant',
        content: errorContent,
        timestamp: new Date(),
      };
      setRawMessages(prev => trimMessages([...prev, errorMessage]));
    } finally {
      setLoadingState('idle');
      setIsTyping(false);
      abortControllerRef.current = null;
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    }
  }, [inputValue, loadingState, messages, conversationContext, trimMessages, throttledApiCall, setInputValueImmediate, chatValidation, setError]);

  // Enhanced keyboard handlers with better accessibility
  const handleKeyPress = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
    if (e.key === 'Escape') {
      setInputValueImmediate('');
      setError(null); // Clear any errors when user starts fresh
    }
  }, [handleSendMessage, setInputValueImmediate, setError]);

  // Handler for clearing chat with state reset
  const handleClearChat = useCallback(() => {
    setRawMessages([]);
    setInputValueImmediate('');
    setConversationContext({});
    setError(null);
    setLoadingState('idle');
    focusInput();
  }, [focusInput, setInputValueImmediate]);

  // Optimized send button handler with loading state check
  const handleSendClick = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (loadingState !== 'loading') {
      handleSendMessage();
    }
  }, [handleSendMessage, loadingState]);

  // Retry handler for failed messages
  const handleRetry = useCallback((messageId: string) => {
    const message = messages.find(m => m.id === messageId);
    if (message && message.type === 'user') {
      handleSendMessage(message.content);
    }
  }, [messages, handleSendMessage]);

  return (
    <ErrorBoundary fallback={CopilotErrorFallback}>
      <div className="h-full bg-background overflow-hidden flex flex-col">
        {/* Enhanced Header with Loading State */}
        <div className="flex items-center justify-between px-8 py-6 border-b border-border/40 flex-shrink-0">
          <div>
            <h1 className="text-xl font-medium text-foreground">AI Copilot</h1>
            <p className="text-sm text-muted-foreground mt-1">
              {loadingState === 'loading' || isTyping
                ? (isTyping ? 'AI is thinking...' : 'Processing your request...')
                : error
                  ? 'Ready to help - try again'
                  : 'Ask questions about your portfolio'
              }
            </p>
          </div>

          <div className="flex items-center gap-3">
            {(loadingState === 'loading' || isTyping) && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-sm">
                  {isTyping ? 'AI analyzing...' : 'Loading...'}
                </span>
              </div>
            )}

            {error && (
              <div className="flex items-center gap-2 text-red-500">
                <AlertTriangle className="h-4 w-4" />
                <span className="text-sm">Error occurred</span>
              </div>
            )}

            {/* Performance monitoring (development only) */}
            {process.env.NODE_ENV === 'development' && (performanceMetrics || memoryUsage) && (
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                {performanceMetrics && (
                  <span>Render: {performanceMetrics.render_time_ms.toFixed(1)}ms</span>
                )}
                {memoryUsage && (
                  <span>Memory: {memoryUsage.used.toFixed(1)}MB ({memoryUsage.percentage.toFixed(1)}%)</span>
                )}
              </div>
            )}

            <Link href="/">
              <Button variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Portfolio
              </Button>
            </Link>
          </div>
        </div>

      {/* Enhanced Layout with Chat Interface and Collapsible Panels */}
      <div className="flex flex-1 overflow-hidden">
        {/* Enhanced Collapsible Quick Actions */}
        {showQuickActions && (
          <div className="w-80 border-r border-border/40 bg-gradient-to-b from-emerald-50/30 to-background flex flex-col overflow-hidden shadow-sm">
            <div className="flex items-center justify-between p-3 border-b border-emerald-200/40 flex-shrink-0 bg-emerald-50/50">
              <div className="flex items-center gap-2">
                <div className="p-1 rounded-md bg-emerald-100">
                  <Sparkles className="h-3 w-3 text-emerald-600" />
                </div>
                <h3 className="font-medium text-emerald-900 text-sm">Quick Actions</h3>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowQuickActions(false)}
                className="text-emerald-600 hover:text-emerald-700 hover:bg-emerald-100 h-6 w-6 p-0 rounded-full"
                aria-label="Close Quick Actions"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-emerald-200 scrollbar-track-transparent">
              <QuickActions onQuerySelect={handleQuerySelect} />
            </div>
          </div>
        )}

        {/* Central Chat Interface */}
        <div className="flex-1 flex flex-col overflow-hidden bg-background">
          <div className="flex-1 flex flex-col overflow-hidden">
            <div className="h-full max-w-4xl mx-auto px-6 py-4 flex flex-col">
              {/* Enhanced Toggle Buttons with Emerald Theme - Fixed height */}
              <div className="flex items-center justify-between flex-shrink-0 mb-6">
                <div className="flex items-center gap-3">
                  <Button
                    variant={showQuickActions ? "default" : "outline"}
                    size="sm"
                    onClick={() => setShowQuickActions(!showQuickActions)}
                    className={cn(
                      "transition-all duration-200 border-emerald-200",
                      showQuickActions
                        ? "bg-emerald-600 hover:bg-emerald-700 text-white shadow-md border-emerald-600"
                        : "text-emerald-700 hover:text-emerald-800 hover:bg-emerald-50 hover:border-emerald-300"
                    )}
                  >
                    <Sparkles className="h-4 w-4" />
                    <span className="ml-2 text-sm font-medium">Quick Actions</span>
                  </Button>

                  <Button
                    variant={showInsights ? "default" : "outline"}
                    size="sm"
                    onClick={() => setShowInsights(!showInsights)}
                    className={cn(
                      "transition-all duration-200 border-emerald-200",
                      showInsights
                        ? "bg-emerald-600 hover:bg-emerald-700 text-white shadow-md border-emerald-600"
                        : "text-emerald-700 hover:text-emerald-800 hover:bg-emerald-50 hover:border-emerald-300"
                    )}
                  >
                    <Brain className="h-4 w-4" />
                    <span className="ml-2 text-sm font-medium">Live Insights</span>
                  </Button>
                </div>

                {/* Clear Chat Button - only show when there are messages */}
                {messages.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearChat}
                    className="text-muted-foreground hover:text-foreground"
                  >
                    Clear Chat
                  </Button>
                )}
              </div>

              {/* Chat Messages or Welcome Content - Flexible height with proper constraints */}
              <div className="flex-1 flex flex-col min-h-0">
                {messages.length === 0 ? (
                  // Welcome Content
                  <div className="flex-1 flex flex-col justify-center">
                    <div className="text-center space-y-3 mb-8">
                      <h2 className="text-2xl font-light text-foreground">
                        How can I help you today?
                      </h2>
                      <p className="text-sm text-muted-foreground max-w-md mx-auto">
                        Ask me about your portfolio, compliance status, risk analysis, or any insights you need.
                      </p>
                    </div>
                    <QuerySuggestions onSuggestionClick={handleSuggestionClick} />
                  </div>
                ) : (
                  // Enhanced Chat Messages with proper scrolling constraints
                  <div
                    className="flex-1 overflow-y-auto py-4 scrollbar-thin scrollbar-thumb-emerald-200 scrollbar-track-emerald-50 scroll-smooth min-h-0"
                    ref={scrollAreaRef}
                    style={{ scrollBehavior: 'smooth' }}
                  >
                    <MessageList
                      messages={messages}
                      isLoading={loadingState === 'loading'}
                      onRetry={handleRetry}
                    />
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Enhanced Chat Input Area - Always Visible at Bottom */}
          <div className="border-t border-border/40 bg-background flex-shrink-0">
            <div className="max-w-4xl mx-auto px-6 py-4">
              {/* Enhanced Quick Suggestions with Emerald Theme */}
              <div className="mb-4 flex flex-wrap items-center gap-2">
                {loadingState !== 'loading' && !isTyping && inputValue.length === 0 && (
                  <>
                    <div className="h-3 w-px bg-emerald-200"></div>
                    <span className="text-xs text-emerald-600 font-medium">Quick suggestions:</span>
                    {['High-risk MSMEs', 'Compliance', 'Trends'].map((suggestion) => (
                      <button
                        key={suggestion}
                        onClick={() => setInputValueImmediate(`Show ${suggestion.toLowerCase()}`)}
                        className="text-xs px-3 py-1.5 rounded-full bg-emerald-50 text-emerald-700 hover:bg-emerald-100 hover:text-emerald-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-1 border border-emerald-200 hover:border-emerald-300"
                        type="button"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </>
                )}

                {/* Enhanced Error indicator */}
                {error && (
                  <div className="flex items-center gap-2 px-3 py-1.5 rounded-full bg-red-50 border border-red-200">
                    <AlertTriangle className="h-3 w-3 text-red-500" />
                    <span className="text-xs text-red-600 font-medium">Error - please try again</span>
                  </div>
                )}
              </div>

              <div className="flex gap-3 items-end">
                <div className="flex-1">
                  <Input
                    ref={inputRef}
                    value={inputValue}
                    onChange={(e) => {
                      setInputValueImmediate(e.target.value);
                      chatValidation.validate(e.target.value);
                    }}
                    onKeyPress={handleKeyPress}
                    placeholder={
                      loadingState === 'loading' || isTyping
                        ? "AI is processing..."
                        : error
                          ? "Try asking again..."
                          : "Ask me about portfolio risks, compliance, trends, or specific MSMEs..."
                    }
                    className={cn(
                      "h-12 text-sm transition-all duration-200 bg-white border-2",
                      error || (chatValidation.touched && !chatValidation.isValid)
                        ? "border-red-300 focus:border-red-500 focus:ring-red-200"
                        : "border-emerald-200 focus:border-emerald-500 focus:ring-emerald-200 hover:border-emerald-300",
                      "focus:ring-2 focus:ring-opacity-20"
                    )}
                    disabled={loadingState === 'loading' || isTyping}
                    aria-label="Chat input"
                    aria-invalid={chatValidation.touched && !chatValidation.isValid}
                    aria-describedby="chat-input-error"
                  />
                  {chatValidation.touched && !chatValidation.isValid && (
                    <div id="chat-input-error" className="mt-1 text-xs text-red-600">
                      {chatValidation.errors.join(', ')}
                    </div>
                  )}
                </div>
                <Button
                  onClick={handleSendClick}
                  disabled={!inputValue.trim() || loadingState === 'loading' || isTyping}
                  className={cn(
                    "h-12 px-6 transition-all duration-200 shadow-md",
                    "bg-emerald-600 hover:bg-emerald-700 active:bg-emerald-800",
                    "disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none",
                    "focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2"
                  )}
                  aria-label="Send message"
                >
                  {loadingState === 'loading' || isTyping ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Send className="h-4 w-4" />
                  )}
                </Button>
              </div>

              <div className="mt-2 text-xs text-muted-foreground">
                Try: &ldquo;Which MSMEs need attention?&rdquo;, &ldquo;Show compliance status&rdquo;, or &ldquo;Predict portfolio trends&rdquo;
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Collapsible Insights */}
        {showInsights && (
          <div className="w-80 border-l border-border/40 bg-gradient-to-b from-emerald-50/30 to-background flex flex-col overflow-hidden shadow-sm">
            <div className="flex items-center justify-between p-3 border-b border-emerald-200/40 flex-shrink-0 bg-emerald-50/50">
              <div className="flex items-center gap-2">
                <div className="p-1 rounded-md bg-emerald-100">
                  <Brain className="h-3 w-3 text-emerald-600" />
                </div>
                <h3 className="font-medium text-emerald-900 text-sm">Live Insights</h3>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowInsights(false)}
                className="text-emerald-600 hover:text-emerald-700 hover:bg-emerald-100 h-6 w-6 p-0 rounded-full"
                aria-label="Close Live Insights"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
            <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-emerald-200 scrollbar-track-transparent">
              <InsightsFeed />
            </div>
          </div>
        )}
      </div>
    </div>
    </ErrorBoundary>
  );
}
