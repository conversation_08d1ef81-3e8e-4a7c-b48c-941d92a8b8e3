'use client';

import React, { useState, useMemo, memo } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Target,
  Zap,
  ChevronDown,
  ChevronUp,
  Brain,
  Lightbulb
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  EnhancedMessageProps
} from '@/types/copilot';

// Utility function to safely access nested properties
const safeGet = (obj: Record<string, unknown>, path: string, defaultValue: unknown = null): unknown => {
  return path.split('.').reduce((current: unknown, key: string) => {
    return current && typeof current === 'object' && current !== null && key in (current as Record<string, unknown>)
      ? (current as Record<string, unknown>)[key]
      : defaultValue;
  }, obj as unknown);
};

const EnhancedMessageComponent = memo(function EnhancedMessage({ content, data, timestamp, type }: EnhancedMessageProps) {
  const [showDetails, setShowDetails] = useState(false);

  // Memoized timestamp processing
  const messageTimestamp = useMemo(() => {
    return timestamp instanceof Date ? timestamp : new Date(timestamp);
  }, [timestamp]);

  // Enhanced data validation and sanitization
  const sanitizedData = useMemo(() => {
    if (!data || typeof data !== 'object') return null;

    // Check if data looks like a React event object
    if ('_reactName' in data || 'nativeEvent' in data || 'currentTarget' in data) {
      console.warn('Detected React event object in message data, filtering out');
      return null;
    }

    // Deep clone to prevent mutations
    try {
      return JSON.parse(JSON.stringify(data));
    } catch (error) {
      console.warn('Failed to serialize message data:', error);
      return null;
    }
  }, [data]);

  // Memoized time formatting
  const formattedTime = useMemo(() => {
    return messageTimestamp.toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
  }, [messageTimestamp]);

  // Enhanced content formatting function
  const renderFormattedContent = useMemo(() => {
    const ContentFormatter = (content: string) => {
      // Split content into lines for processing
      const lines = content.split('\n');
      const formattedLines: React.ReactNode[] = [];

      lines.forEach((line, index) => {
        let formattedLine: React.ReactNode = line;

        // Format headers (lines starting with ##)
        if (line.startsWith('## ')) {
          formattedLine = (
            <h3 key={index} className="text-lg font-semibold text-foreground mt-4 mb-2 first:mt-0">
              {line.replace('## ', '')}
            </h3>
          );
        }
        // Format subheaders (lines starting with **text:**)
        else if (line.match(/^\*\*.*:\*\*$/)) {
          formattedLine = (
            <h4 key={index} className="text-base font-medium text-foreground mt-3 mb-1">
              {line.replace(/\*\*/g, '')}
            </h4>
          );
        }
        // Format bullet points
        else if (line.startsWith('• ')) {
          formattedLine = (
            <div key={index} className="flex items-start gap-2 my-1">
              <span className="text-emerald-600 mt-1">•</span>
              <span className="flex-1">{line.replace('• ', '')}</span>
            </div>
          );
        }
        // Format numbered lists
        else if (line.match(/^\d+\.\s/)) {
          const match = line.match(/^(\d+)\.\s(.*)$/);
          if (match) {
            formattedLine = (
              <div key={index} className="flex items-start gap-2 my-1">
                <span className="text-emerald-600 font-medium min-w-[20px]">{match[1]}.</span>
                <span className="flex-1">{match[2]}</span>
              </div>
            );
          }
        }
        // Format bold text (**text**)
        else if (line.includes('**')) {
          const parts = line.split(/(\*\*.*?\*\*)/);
          formattedLine = (
            <div key={index} className="my-1">
              {parts.map((part, partIndex) => {
                if (part.startsWith('**') && part.endsWith('**')) {
                  return (
                    <strong key={partIndex} className="font-semibold text-foreground">
                      {part.replace(/\*\*/g, '')}
                    </strong>
                  );
                }
                return part;
              })}
            </div>
          );
        }
        // Format currency and percentages with highlighting
        else if (line.match(/[₹$]\d+|[\d.]+%|\d+\/\d+/)) {
          const formattedText = line
            .replace(/(₹[\d,]+(?:Cr|L|K)?)/g, '<span class="font-semibold text-emerald-600">$1</span>')
            .replace(/([\d.]+%)/g, '<span class="font-semibold text-blue-600">$1</span>')
            .replace(/(\d+\/\d+)/g, '<span class="font-semibold text-purple-600">$1</span>');

          formattedLine = (
            <div
              key={index}
              className="my-1"
              dangerouslySetInnerHTML={{ __html: formattedText }}
            />
          );
        }
        // Regular text
        else if (line.trim()) {
          formattedLine = (
            <div key={index} className="my-1">
              {line}
            </div>
          );
        }
        // Empty lines for spacing
        else {
          formattedLine = <div key={index} className="h-2" />;
        }

        formattedLines.push(formattedLine);
      });

      return <div className="space-y-0">{formattedLines}</div>;
    };
    ContentFormatter.displayName = 'ContentFormatter';
    return ContentFormatter;
  }, []);

  // Enhanced user message rendering with animation
  if (type === 'user') {
    return (
      <div className="flex justify-end mb-4 message-user">
        <div className="max-w-[80%] bg-emerald-600 text-white rounded-lg px-4 py-2 shadow-md hover:shadow-lg transition-shadow duration-200">
          <p className="text-sm leading-relaxed">{content}</p>
          <p className="text-xs text-emerald-100 mt-1 opacity-75">
            {formattedTime}
          </p>
        </div>
      </div>
    );
  }

  // Enhanced assistant message rendering with animation
  return (
    <div className="flex justify-start mb-6 message-assistant">
      <div className="max-w-[90%] space-y-3">
        {/* Enhanced main response with better formatting */}
        <div className="bg-muted/50 rounded-lg px-4 py-3 shadow-sm border border-border/20">
          <div className="prose prose-sm max-w-none">
            <div className="whitespace-pre-wrap text-foreground leading-relaxed">
              {renderFormattedContent(content)}
            </div>
          </div>
          <p className="text-xs text-muted-foreground mt-2 opacity-75">
            {formattedTime}
          </p>
        </div>

        {/* Enhanced data visualization with type safety */}
        {sanitizedData && typeof sanitizedData === 'object' && !Array.isArray(sanitizedData) && (
          <div className="space-y-3">
            {/* AI Analytics Overview with improved type safety */}
            {sanitizedData.ai_analytics && (
              <Card className="border-emerald-200 bg-emerald-50/30 shadow-sm">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Brain className="h-4 w-4 text-emerald-600" />
                    AI Portfolio Analytics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 rounded-lg bg-emerald-50">
                      <div className="text-2xl font-bold text-emerald-600">
                        {Math.round(Number(safeGet(sanitizedData, 'ai_analytics.portfolio_health_score', 0)))}%
                      </div>
                      <div className="text-xs text-muted-foreground">Portfolio Health</div>
                      <div className="text-xs text-emerald-600 mt-1">
                        {Number(safeGet(sanitizedData, 'ai_analytics.portfolio_health_score', 0)) >= 80 ? 'Excellent' :
                         Number(safeGet(sanitizedData, 'ai_analytics.portfolio_health_score', 0)) >= 60 ? 'Good' : 'Needs Attention'}
                      </div>
                    </div>
                    <div className="text-center p-3 rounded-lg bg-blue-50">
                      <div className="text-2xl font-bold text-blue-600">
                        {Number(safeGet(sanitizedData, 'ai_analytics.total_msmes', 0)).toLocaleString()}
                      </div>
                      <div className="text-xs text-muted-foreground">Total MSMEs</div>
                      <div className="text-xs text-blue-600 mt-1">
                        Active Portfolio
                      </div>
                    </div>
                  </div>

                  {/* Enhanced Risk Distribution with visual indicators */}
                  {Boolean(safeGet(sanitizedData, 'ai_analytics.risk_distribution')) && (
                    <div className="space-y-3">
                      <div className="text-xs font-medium text-muted-foreground">Risk Distribution</div>
                      <div className="space-y-2">
                        {[
                          { key: 'high', label: 'High Risk', color: 'red', bgColor: 'bg-red-50', textColor: 'text-red-600' },
                          { key: 'medium', label: 'Medium Risk', color: 'yellow', bgColor: 'bg-yellow-50', textColor: 'text-yellow-600' },
                          { key: 'low', label: 'Low Risk', color: 'green', bgColor: 'bg-green-50', textColor: 'text-green-600' }
                        ].map(({ key, label, bgColor, textColor }) => {
                          const percentage = Math.round(Number(safeGet(sanitizedData, `ai_analytics.risk_distribution.${key}`, 0)) * 100);
                          return (
                            <div key={key} className={`flex items-center justify-between p-2 rounded ${bgColor}`}>
                              <div className="flex items-center gap-2">
                                <div className={`w-3 h-3 rounded-full ${key === 'high' ? 'bg-red-500' : key === 'medium' ? 'bg-yellow-500' : 'bg-green-500'}`}></div>
                                <span className="text-xs font-medium">{label}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <div className={`text-sm font-bold ${textColor}`}>
                                  {percentage}%
                                </div>
                                <div className="w-16 h-2 bg-white rounded-full overflow-hidden">
                                  <div
                                    className={`h-full ${key === 'high' ? 'bg-red-500' : key === 'medium' ? 'bg-yellow-500' : 'bg-green-500'}`}
                                    style={{ width: `${percentage}%` }}
                                  ></div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Predictive Insights */}
            {sanitizedData.predictive_insights && Array.isArray(sanitizedData.predictive_insights) && sanitizedData.predictive_insights.length > 0 && (
              <Card className="border-blue-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Brain className="h-4 w-4 text-blue-600" />
                    AI Predictive Insights
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {sanitizedData.predictive_insights.slice(0, 3).map((insight: Record<string, unknown>, index: number) => (
                    <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-blue-50/50">
                      <div className="w-2 h-2 rounded-full bg-blue-500 mt-2"></div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-foreground mb-1">
                          {String(insight.title || '')}
                        </div>
                        <div className="text-xs text-muted-foreground mb-2">
                          {String(insight.description || '')}
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">
                            {Math.round((insight.confidence as number) * 100)}% confidence
                          </Badge>
                          {Boolean(insight.actionable) && (
                            <Badge variant="outline" className="text-xs">
                              Actionable
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Growth Opportunities */}
            {sanitizedData.opportunities && Array.isArray(sanitizedData.opportunities) && sanitizedData.opportunities.length > 0 && (
              <Card className="border-green-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Lightbulb className="h-4 w-4 text-green-600" />
                    Growth Opportunities
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  {sanitizedData.opportunities.slice(0, 2).map((opportunity: Record<string, unknown>, index: number) => (
                    <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-green-50/50">
                      <Target className="h-4 w-4 text-green-600 mt-1" />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-foreground mb-1">
                          {String(opportunity.title || '')}
                        </div>
                        <div className="text-xs text-muted-foreground mb-2">
                          {String(opportunity.description || '')}
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="text-xs">
                            {Math.round((opportunity.confidence as number) * 100)}% confidence
                          </Badge>
                          <Badge variant="outline" className="text-xs text-green-700">
                            Priority: {String(opportunity.priority_score || '')}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* Portfolio Summary */}
            {sanitizedData.portfolio_summary && (
              <Card className="border-emerald-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <BarChart3 className="h-4 w-4 text-emerald-600" />
                    Portfolio Overview
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-foreground">
                        {sanitizedData.portfolio_summary.total_msmes}
                      </div>
                      <div className="text-xs text-muted-foreground">Total MSMEs</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-foreground">
                        {sanitizedData.portfolio_summary.avg_score?.toFixed(1)}
                      </div>
                      <div className="text-xs text-muted-foreground">Avg Score</div>
                    </div>
                  </div>
                  
                  {/* Risk Distribution */}
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-muted-foreground">Risk Distribution</div>
                    <div className="space-y-1">
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Green</span>
                        <Badge variant="outline" className="text-green-600 border-green-200">
                          {sanitizedData.portfolio_summary.risk_distribution?.green || 0}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Yellow</span>
                        <Badge variant="outline" className="text-yellow-600 border-yellow-200">
                          {sanitizedData.portfolio_summary.risk_distribution?.yellow || 0}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs">Red</span>
                        <Badge variant="outline" className="text-red-600 border-red-200">
                          {sanitizedData.portfolio_summary.risk_distribution?.red || 0}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Predictive Analytics */}
            {sanitizedData.forecast && (
              <Card className="border-blue-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Target className="h-4 w-4 text-blue-600" />
                    Predictive Forecast
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Trend Direction</span>
                    <div className="flex items-center gap-1">
                      {sanitizedData.forecast.trend_direction === 'improving' ? (
                        <TrendingUp className="h-4 w-4 text-green-600" />
                      ) : sanitizedData.forecast.trend_direction === 'declining' ? (
                        <TrendingDown className="h-4 w-4 text-red-600" />
                      ) : (
                        <div className="h-4 w-4 rounded-full bg-yellow-400" />
                      )}
                      <span className="text-sm capitalize">{sanitizedData.forecast.trend_direction}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Model Accuracy</span>
                    <div className="flex items-center gap-2">
                      <Progress
                        value={sanitizedData.forecast.model_accuracy * 100}
                        className="w-16 h-2"
                      />
                      <span className="text-sm">{(sanitizedData.forecast.model_accuracy * 100).toFixed(0)}%</span>
                    </div>
                  </div>

                  {sanitizedData.forecast.risk_factors && sanitizedData.forecast.risk_factors.length > 0 && (
                    <div className="space-y-1">
                      <div className="text-xs font-medium text-muted-foreground">Risk Factors</div>
                      {sanitizedData.forecast.risk_factors.slice(0, 3).map((factor: string, index: number) => (
                        <div key={index} className="flex items-center gap-2">
                          <AlertTriangle className="h-3 w-3 text-amber-500" />
                          <span className="text-xs">{factor}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Intelligent Alerts */}
            {sanitizedData.intelligent_alerts && Array.isArray(sanitizedData.intelligent_alerts) && sanitizedData.intelligent_alerts.length > 0 && (
              <Card className="border-orange-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Zap className="h-4 w-4 text-orange-600" />
                    AI-Powered Alerts
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {sanitizedData.intelligent_alerts.slice(0, 3).map((alert: Record<string, unknown>, index: number) => (
                    <div key={index} className="flex items-start gap-2 p-2 rounded border">
                      <div className={cn(
                        "h-2 w-2 rounded-full mt-1.5",
                        alert.severity === 'high' ? 'bg-red-500' :
                        alert.severity === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                      )} />
                      <div className="flex-1 min-w-0">
                        <div className="text-xs font-medium truncate">{String(alert.title || '')}</div>
                        <div className="text-xs text-muted-foreground">{String(alert.description || '')}</div>
                        {Boolean(alert.auto_actionable) && (
                          <Badge variant="outline" className="text-xs mt-1">
                            Auto-actionable
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  {sanitizedData.intelligent_alerts.length > 3 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowDetails(!showDetails)}
                      className="w-full text-xs"
                    >
                      {showDetails ? (
                        <>Show Less <ChevronUp className="h-3 w-3 ml-1" /></>
                      ) : (
                        <>Show {sanitizedData.intelligent_alerts.length - 3} More <ChevronDown className="h-3 w-3 ml-1" /></>
                      )}
                    </Button>
                  )}

                  {showDetails && sanitizedData.intelligent_alerts.slice(3).map((alert: Record<string, unknown>, index: number) => (
                    <div key={index + 3} className="flex items-start gap-2 p-2 rounded border">
                      <div className={cn(
                        "h-2 w-2 rounded-full mt-1.5",
                        alert.severity === 'high' ? 'bg-red-500' :
                        alert.severity === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                      )} />
                      <div className="flex-1 min-w-0">
                        <div className="text-xs font-medium truncate">{String(alert.title || '')}</div>
                        <div className="text-xs text-muted-foreground">{String(alert.description || '')}</div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            )}

            {/* AI Recommendations */}
            {sanitizedData.ai_recommendations && Array.isArray(sanitizedData.ai_recommendations) && sanitizedData.ai_recommendations.length > 0 && (
              <Card className="border-emerald-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-emerald-600" />
                    AI Recommendations
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {sanitizedData.ai_recommendations.map((recommendation: unknown, index: number) => {
                    // Ensure recommendation is a string, not an object or event
                    const recommendationText = typeof recommendation === 'string'
                      ? recommendation
                      : String((recommendation as Record<string, unknown>)?.text || (recommendation as Record<string, unknown>)?.description || 'Invalid recommendation');

                    return (
                      <div key={index} className="flex items-start gap-2">
                        <div className="h-1.5 w-1.5 rounded-full bg-emerald-500 mt-2" />
                        <span className="text-xs text-foreground">{recommendationText}</span>
                      </div>
                    );
                  })}
                </CardContent>
              </Card>
            )}

            {/* Compliance Status */}
            {sanitizedData.compliance_health && (
              <Card className="border-purple-200">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Clock className="h-4 w-4 text-purple-600" />
                    Compliance Health
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Overall Score</span>
                    <div className="flex items-center gap-2">
                      <Progress
                        value={sanitizedData.compliance_health.health_score}
                        className="w-16 h-2"
                      />
                      <span className="text-sm">{sanitizedData.compliance_health.health_score}%</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-lg font-bold text-red-600">
                        {sanitizedData.compliance_health.overdue_tasks}
                      </div>
                      <div className="text-xs text-muted-foreground">Overdue</div>
                    </div>
                    <div>
                      <div className="text-lg font-bold text-yellow-600">
                        {sanitizedData.compliance_health.upcoming_tasks}
                      </div>
                      <div className="text-xs text-muted-foreground">Upcoming</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </div>
  );
});

// Export the memoized component
export { EnhancedMessageComponent as EnhancedMessage };
