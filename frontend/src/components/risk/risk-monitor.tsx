'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { MSME, Analytics } from '@/types';
import { api } from '@/lib/api';
import {
  Shield,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Activity,
  Users,
  BarChart3,
  <PERSON>,
  Filter,
  <PERSON>fresh<PERSON><PERSON>,
  Bell,
  Eye,
  Building2,
  MapPin,
  Calendar
} from 'lucide-react';

interface RiskAlert {
  id: string;
  msme_id: string;
  msme_name: string;
  type: 'score_drop' | 'high_risk' | 'no_activity' | 'signal_anomaly';
  severity: 'low' | 'medium' | 'high';
  message: string;
  timestamp: string;
  acknowledged: boolean;
}

interface TrendingMSME {
  msme: MSME;
  trend_type: 'improving' | 'declining';
  score_change: number;
  period: string;
}

export function RiskMonitor() {
  const [msmes, setMsmes] = useState<MSME[]>([]);
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [riskAlerts, setRiskAlerts] = useState<RiskAlert[]>([]);
  const [trendingMsmes, setTrendingMsmes] = useState<TrendingMSME[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedRiskFilter, setSelectedRiskFilter] = useState<string>('all');
  const [selectedBusinessType, setSelectedBusinessType] = useState<string>('all');
  const router = useRouter();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      const [portfolioData, analyticsData] = await Promise.all([
        api.getPortfolio(),
        api.getAnalytics()
      ]);
      
      setMsmes(portfolioData);
      setAnalytics(analyticsData);
      
      // Generate mock risk alerts
      generateMockRiskAlerts(portfolioData);
      
      // Generate trending MSMEs
      generateTrendingMsmes(portfolioData);
      
    } catch (error) {
      console.error('Failed to fetch risk monitor data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const generateMockRiskAlerts = (msmeData: MSME[]) => {
    const alerts: RiskAlert[] = [];
    
    // Generate alerts for high-risk MSMEs
    msmeData
      .filter(msme => msme.risk_band === 'red')
      .slice(0, 3)
      .forEach((msme, index) => {
        alerts.push({
          id: `alert-${msme.msme_id}-${index}`,
          msme_id: msme.msme_id,
          msme_name: msme.name,
          type: 'high_risk',
          severity: 'high',
          message: `${msme.name} has entered high-risk category with score ${msme.current_score}`,
          timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString(),
          acknowledged: false
        });
      });

    // Generate score drop alerts
    msmeData
      .filter(msme => msme.score_trend === 'declining')
      .slice(0, 2)
      .forEach((msme, index) => {
        alerts.push({
          id: `score-drop-${msme.msme_id}-${index}`,
          msme_id: msme.msme_id,
          msme_name: msme.name,
          type: 'score_drop',
          severity: 'medium',
          message: `${msme.name} credit score has declined by 15 points in the last week`,
          timestamp: new Date(Date.now() - Math.random() * 12 * 60 * 60 * 1000).toISOString(),
          acknowledged: Math.random() > 0.5
        });
      });

    setRiskAlerts(alerts.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()));
  };

  const generateTrendingMsmes = (msmeData: MSME[]) => {
    const trending: TrendingMSME[] = [];
    
    // Improving MSMEs
    msmeData
      .filter(msme => msme.score_trend === 'improving')
      .slice(0, 3)
      .forEach(msme => {
        trending.push({
          msme,
          trend_type: 'improving',
          score_change: Math.floor(Math.random() * 30) + 10,
          period: '7 days'
        });
      });

    // Declining MSMEs
    msmeData
      .filter(msme => msme.score_trend === 'declining')
      .slice(0, 3)
      .forEach(msme => {
        trending.push({
          msme,
          trend_type: 'declining',
          score_change: -(Math.floor(Math.random() * 25) + 5),
          period: '7 days'
        });
      });

    setTrendingMsmes(trending);
  };

  const acknowledgeAlert = (alertId: string) => {
    setRiskAlerts(prev => 
      prev.map(alert => 
        alert.id === alertId ? { ...alert, acknowledged: true } : alert
      )
    );
  };

  const getRiskBadgeVariant = (risk: string) => {
    switch (risk) {
      case 'green': return 'default';
      case 'yellow': return 'secondary';
      case 'red': return 'destructive';
      default: return 'outline';
    }
  };

  const getRiskLabel = (risk: string) => {
    switch (risk) {
      case 'green': return 'Low';
      case 'yellow': return 'Medium';
      case 'red': return 'High';
      default: return 'Unknown';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'high_risk': return <AlertTriangle className="h-4 w-4" />;
      case 'score_drop': return <TrendingDown className="h-4 w-4" />;
      case 'no_activity': return <Clock className="h-4 w-4" />;
      case 'signal_anomaly': return <Activity className="h-4 w-4" />;
      default: return <Bell className="h-4 w-4" />;
    }
  };

  const getAlertSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const filteredMsmes = msmes.filter(msme => {
    const matchesRisk = selectedRiskFilter === 'all' || msme.risk_band === selectedRiskFilter;
    const matchesBusinessType = selectedBusinessType === 'all' || msme.business_type === selectedBusinessType;
    return matchesRisk && matchesBusinessType;
  });

  const unacknowledgedAlerts = riskAlerts.filter(alert => !alert.acknowledged);

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="space-y-6">
          <div className="h-8 bg-gray-200 rounded animate-pulse"></div>
          <div className="grid gap-6 md:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div className="space-y-2">
          <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text">
            Risk Monitor
          </h1>
          <p className="text-muted-foreground">
            Real-time risk assessment and portfolio health monitoring
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {unacknowledgedAlerts.length > 0 && (
            <Badge variant="destructive" className="animate-pulse">
              {unacknowledgedAlerts.length} alerts
            </Badge>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Risk Overview Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-red-50 to-red-100 dark:from-red-950/20 dark:to-red-900/20">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-red-700 dark:text-red-400">
              <div className="p-2 bg-red-500/10 rounded-lg">
                <AlertTriangle className="h-5 w-5" />
              </div>
              High Risk
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-red-700 dark:text-red-400 mb-2">
              {analytics?.risk_distribution.red || 0}
            </div>
            <p className="text-sm text-red-600 dark:text-red-500">
              {analytics ? Math.round((analytics.risk_distribution.red / analytics.total_msmes) * 100) : 0}% of portfolio
            </p>
            <Progress 
              value={analytics ? (analytics.risk_distribution.red / analytics.total_msmes) * 100 : 0} 
              className="mt-3 h-2" 
            />
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-950/20 dark:to-yellow-900/20">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-yellow-700 dark:text-yellow-400">
              <div className="p-2 bg-yellow-500/10 rounded-lg">
                <Shield className="h-5 w-5" />
              </div>
              Medium Risk
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-yellow-700 dark:text-yellow-400 mb-2">
              {analytics?.risk_distribution.yellow || 0}
            </div>
            <p className="text-sm text-yellow-600 dark:text-yellow-500">
              {analytics ? Math.round((analytics.risk_distribution.yellow / analytics.total_msmes) * 100) : 0}% of portfolio
            </p>
            <Progress 
              value={analytics ? (analytics.risk_distribution.yellow / analytics.total_msmes) * 100 : 0} 
              className="mt-3 h-2" 
            />
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100 dark:from-green-950/20 dark:to-green-900/20">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700 dark:text-green-400">
              <div className="p-2 bg-green-500/10 rounded-lg">
                <Shield className="h-5 w-5" />
              </div>
              Low Risk
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-700 dark:text-green-400 mb-2">
              {analytics?.risk_distribution.green || 0}
            </div>
            <p className="text-sm text-green-600 dark:text-green-500">
              {analytics ? Math.round((analytics.risk_distribution.green / analytics.total_msmes) * 100) : 0}% of portfolio
            </p>
            <Progress 
              value={analytics ? (analytics.risk_distribution.green / analytics.total_msmes) * 100 : 0} 
              className="mt-3 h-2" 
            />
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-950/20 dark:to-blue-900/20">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700 dark:text-blue-400">
              <div className="p-2 bg-blue-500/10 rounded-lg">
                <Activity className="h-5 w-5" />
              </div>
              Active Alerts
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-700 dark:text-blue-400 mb-2">
              {unacknowledgedAlerts.length}
            </div>
            <p className="text-sm text-blue-600 dark:text-blue-500">
              Require attention
            </p>
            <Button
              variant="outline"
              size="sm"
              className="mt-3 w-full"
              onClick={() => {
                // Scroll to alerts section or open alerts modal
                document.getElementById('alerts-section')?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              View Alerts
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="alerts">Risk Alerts</TabsTrigger>
          <TabsTrigger value="trending">Trending</TabsTrigger>
          <TabsTrigger value="portfolio">Portfolio View</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Portfolio Health */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Portfolio Health Score
                </CardTitle>
                <CardDescription>
                  Overall portfolio risk assessment
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Overall Health</span>
                    <span className="text-2xl font-bold text-green-600">
                      {analytics ? Math.round(((analytics.risk_distribution.green * 100) + (analytics.risk_distribution.yellow * 50)) / analytics.total_msmes) : 0}%
                    </span>
                  </div>
                  <Progress
                    value={analytics ? ((analytics.risk_distribution.green * 100) + (analytics.risk_distribution.yellow * 50)) / analytics.total_msmes : 0}
                    className="h-3"
                  />
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="text-center">
                      <div className="font-semibold text-green-600">{analytics?.risk_distribution.green || 0}</div>
                      <div className="text-muted-foreground">Low Risk</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-yellow-600">{analytics?.risk_distribution.yellow || 0}</div>
                      <div className="text-muted-foreground">Medium Risk</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-red-600">{analytics?.risk_distribution.red || 0}</div>
                      <div className="text-muted-foreground">High Risk</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Recent Risk Changes
                </CardTitle>
                <CardDescription>
                  Latest risk level changes in your portfolio
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-48">
                  <div className="space-y-3">
                    {trendingMsmes.slice(0, 5).map((trending, index) => (
                      <div key={index} className="flex items-center justify-between p-2 rounded-lg border">
                        <div className="flex items-center gap-3">
                          <div className={`p-1 rounded ${trending.trend_type === 'improving' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}>
                            {trending.trend_type === 'improving' ?
                              <TrendingUp className="h-3 w-3" /> :
                              <TrendingDown className="h-3 w-3" />
                            }
                          </div>
                          <div>
                            <p className="text-sm font-medium">{trending.msme.name}</p>
                            <p className="text-xs text-muted-foreground">{trending.period}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className={`text-sm font-medium ${trending.score_change > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {trending.score_change > 0 ? '+' : ''}{trending.score_change}
                          </div>
                          <div className="text-xs text-muted-foreground">points</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="alerts" id="alerts-section" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Risk Alerts
                {unacknowledgedAlerts.length > 0 && (
                  <Badge variant="destructive" className="ml-2">
                    {unacknowledgedAlerts.length} new
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                Monitor and manage risk alerts across your portfolio
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                <div className="space-y-3">
                  {riskAlerts.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <Bell className="h-12 w-12 mx-auto mb-3 opacity-50" />
                      <p>No risk alerts at this time</p>
                      <p className="text-sm">Your portfolio is looking healthy!</p>
                    </div>
                  ) : (
                    riskAlerts.map((alert) => (
                      <Alert
                        key={alert.id}
                        className={`${getAlertSeverityColor(alert.severity)} ${alert.acknowledged ? 'opacity-60' : ''}`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-3">
                            {getAlertIcon(alert.type)}
                            <div className="flex-1">
                              <AlertTitle className="text-sm font-medium">
                                {alert.msme_name}
                                {alert.acknowledged && (
                                  <Badge variant="outline" className="ml-2 text-xs">
                                    Acknowledged
                                  </Badge>
                                )}
                              </AlertTitle>
                              <AlertDescription className="text-sm mt-1">
                                {alert.message}
                              </AlertDescription>
                              <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                                <span>{new Date(alert.timestamp).toLocaleString()}</span>
                                <Badge variant="outline" className="text-xs">
                                  {alert.severity} severity
                                </Badge>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(`/msme/${alert.msme_id}`)}
                            >
                              <Eye className="h-3 w-3 mr-1" />
                              View
                            </Button>
                            {!alert.acknowledged && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => acknowledgeAlert(alert.id)}
                              >
                                Acknowledge
                              </Button>
                            )}
                          </div>
                        </div>
                      </Alert>
                    ))
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trending" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Improving MSMEs */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-600">
                  <TrendingUp className="h-5 w-5" />
                  Improving MSMEs
                </CardTitle>
                <CardDescription>
                  MSMEs with positive score trends
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-64">
                  <div className="space-y-3">
                    {trendingMsmes
                      .filter(t => t.trend_type === 'improving')
                      .map((trending, index) => (
                        <div key={index} className="p-3 border rounded-lg hover:bg-green-50 transition-colors">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm">{trending.msme.name}</h4>
                            <Badge variant="outline" className="text-green-600 border-green-200">
                              +{trending.score_change} points
                            </Badge>
                          </div>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <Building2 className="h-3 w-3" />
                              {trending.msme.business_type}
                            </span>
                            <span className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {trending.msme.location}
                            </span>
                            <span className="flex items-center gap-1">
                              <BarChart3 className="h-3 w-3" />
                              {trending.msme.current_score}
                            </span>
                          </div>
                          <div className="mt-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(`/msme/${trending.msme.msme_id}`)}
                              className="w-full"
                            >
                              View Details
                            </Button>
                          </div>
                        </div>
                      ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Declining MSMEs */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-600">
                  <TrendingDown className="h-5 w-5" />
                  Declining MSMEs
                </CardTitle>
                <CardDescription>
                  MSMEs requiring attention
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-64">
                  <div className="space-y-3">
                    {trendingMsmes
                      .filter(t => t.trend_type === 'declining')
                      .map((trending, index) => (
                        <div key={index} className="p-3 border rounded-lg hover:bg-red-50 transition-colors">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-sm">{trending.msme.name}</h4>
                            <Badge variant="destructive" className="text-xs">
                              {trending.score_change} points
                            </Badge>
                          </div>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <Building2 className="h-3 w-3" />
                              {trending.msme.business_type}
                            </span>
                            <span className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {trending.msme.location}
                            </span>
                            <span className="flex items-center gap-1">
                              <BarChart3 className="h-3 w-3" />
                              {trending.msme.current_score}
                            </span>
                          </div>
                          <div className="mt-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(`/msme/${trending.msme.msme_id}`)}
                              className="w-full"
                            >
                              View Details
                            </Button>
                          </div>
                        </div>
                      ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="portfolio" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Filtered Portfolio View
              </CardTitle>
              <CardDescription>
                Filter and view MSMEs by risk level and business type
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-4 mb-6">
                <div className="flex-1">
                  <label className="text-sm font-medium mb-2 block">Risk Level</label>
                  <Select value={selectedRiskFilter} onValueChange={setSelectedRiskFilter}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Risk Levels</SelectItem>
                      <SelectItem value="red">High Risk</SelectItem>
                      <SelectItem value="yellow">Medium Risk</SelectItem>
                      <SelectItem value="green">Low Risk</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex-1">
                  <label className="text-sm font-medium mb-2 block">Business Type</label>
                  <Select value={selectedBusinessType} onValueChange={setSelectedBusinessType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Business Types</SelectItem>
                      <SelectItem value="retail">Retail</SelectItem>
                      <SelectItem value="b2b">B2B</SelectItem>
                      <SelectItem value="manufacturing">Manufacturing</SelectItem>
                      <SelectItem value="services">Services</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">
                    Showing {filteredMsmes.length} of {msmes.length} MSMEs
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push('/portfolio')}
                  >
                    View Full Portfolio
                  </Button>
                </div>

                <ScrollArea className="h-96">
                  <div className="space-y-2">
                    {filteredMsmes.map((msme) => (
                      <div
                        key={msme.msme_id}
                        className="p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                        onClick={() => router.push(`/msme/${msme.msme_id}`)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-medium">{msme.name}</h4>
                              <Badge variant={getRiskBadgeVariant(msme.risk_band)} className="text-xs">
                                {getRiskLabel(msme.risk_band)} Risk
                              </Badge>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <Building2 className="h-3 w-3" />
                                {msme.business_type}
                              </span>
                              <span className="flex items-center gap-1">
                                <MapPin className="h-3 w-3" />
                                {msme.location}
                              </span>
                              <span className="flex items-center gap-1">
                                <BarChart3 className="h-3 w-3" />
                                Score: {msme.current_score}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {new Date(msme.last_signal_date).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                          <Button variant="outline" size="sm">
                            View Details
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
