/**
 * Performance optimization hooks for Credit Chakra frontend
 * Provides memoization, caching, and efficient data fetching patterns
 *
 * @fileoverview Optimized data fetching hooks with caching, pagination, and performance monitoring
 * <AUTHOR> Chakra Team
 * @version 1.0.0
 */
import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { MSME, Analytics } from '@/types';
import { api } from '@/lib/api';

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

class DataCache {
  private cache = new Map<string, CacheEntry<any>>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

  set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + ttl
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  clear(): void {
    this.cache.clear();
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }
}

// Global cache instance
const dataCache = new DataCache();

interface UseOptimizedPortfolioOptions {
  enableCache?: boolean;
  cacheTTL?: number;
  enablePagination?: boolean;
  pageSize?: number;
}

interface UseOptimizedPortfolioReturn {
  msmes: MSME[];
  analytics: Analytics | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  // Pagination
  currentPage: number;
  totalPages: number;
  paginatedMsmes: MSME[];
  setCurrentPage: (page: number) => void;
  // Filtering
  filteredMsmes: MSME[];
  setFilter: (filter: Partial<MSME>) => void;
  clearFilter: () => void;
}

/**
 * Optimized portfolio data fetching hook with caching and pagination
 *
 * @param options - Configuration options for the hook
 * @param options.enableCache - Whether to enable client-side caching (default: true)
 * @param options.cacheTTL - Cache time-to-live in milliseconds (default: 5 minutes)
 * @param options.enablePagination - Whether to enable pagination (default: true)
 * @param options.pageSize - Number of items per page (default: 10)
 *
 * @returns Portfolio data with pagination, filtering, and caching capabilities
 *
 * @example
 * ```tsx
 * const {
 *   msmes,
 *   analytics,
 *   loading,
 *   error,
 *   paginatedMsmes,
 *   setCurrentPage
 * } = useOptimizedPortfolio({
 *   enableCache: true,
 *   pageSize: 20
 * });
 * ```
 */
export function useOptimizedPortfolio(
  options: UseOptimizedPortfolioOptions = {}
): UseOptimizedPortfolioReturn {
  const {
    enableCache = true,
    cacheTTL = 5 * 60 * 1000,
    enablePagination = true,
    pageSize = 10
  } = options;

  const [msmes, setMsmes] = useState<MSME[]>([]);
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [filter, setFilterState] = useState<Partial<MSME>>({});
  
  const abortControllerRef = useRef<AbortController | null>(null);

  const fetchData = useCallback(async () => {
    // Cancel previous request if still pending
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();
    
    try {
      setLoading(true);
      setError(null);

      // Check cache first
      const portfolioCacheKey = 'portfolio_data';
      const analyticsCacheKey = 'analytics_data';

      let portfolioData: MSME[] | null = null;
      let analyticsData: Analytics | null = null;

      if (enableCache) {
        portfolioData = dataCache.get<MSME[]>(portfolioCacheKey);
        analyticsData = dataCache.get<Analytics>(analyticsCacheKey);
      }

      // Fetch from API if not in cache
      if (!portfolioData || !analyticsData) {
        const [fetchedPortfolio, fetchedAnalytics] = await Promise.all([
          // Use legacy endpoint for now to maintain compatibility
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}/api/dashboard/portfolio/legacy`)
            .then(res => res.json()),
          api.getAnalytics()
        ]);

        portfolioData = fetchedPortfolio;
        analyticsData = fetchedAnalytics;

        // Cache the results
        if (enableCache) {
          dataCache.set(portfolioCacheKey, portfolioData, cacheTTL);
          dataCache.set(analyticsCacheKey, analyticsData, cacheTTL);
        }
      }

      setMsmes(portfolioData || []);
      setAnalytics(analyticsData);
    } catch (err) {
      if (err instanceof Error && err.name !== 'AbortError') {
        setError(err.message);
      }
    } finally {
      setLoading(false);
    }
  }, [enableCache, cacheTTL]);

  useEffect(() => {
    fetchData();
    
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchData]);

  // Memoized filtered MSMEs
  const filteredMsmes = useMemo(() => {
    if (Object.keys(filter).length === 0) return msmes;

    return msmes.filter(msme => {
      return Object.entries(filter).every(([key, value]) => {
        if (value === undefined || value === null || value === '') return true;
        return msme[key as keyof MSME] === value;
      });
    });
  }, [msmes, filter]);

  // Memoized pagination
  const { paginatedMsmes, totalPages } = useMemo(() => {
    if (!enablePagination) {
      return { paginatedMsmes: filteredMsmes, totalPages: 1 };
    }

    const total = Math.ceil(filteredMsmes.length / pageSize);
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginated = filteredMsmes.slice(startIndex, endIndex);

    return { paginatedMsmes: paginated, totalPages: total };
  }, [filteredMsmes, currentPage, pageSize, enablePagination]);

  const setFilter = useCallback((newFilter: Partial<MSME>) => {
    setFilterState(newFilter);
    setCurrentPage(1); // Reset to first page when filtering
  }, []);

  const clearFilter = useCallback(() => {
    setFilterState({});
    setCurrentPage(1);
  }, []);

  const refetch = useCallback(async () => {
    // Clear cache and refetch
    if (enableCache) {
      dataCache.clear();
    }
    await fetchData();
  }, [fetchData, enableCache]);

  return {
    msmes,
    analytics,
    loading,
    error,
    refetch,
    currentPage,
    totalPages,
    paginatedMsmes,
    setCurrentPage,
    filteredMsmes,
    setFilter,
    clearFilter
  };
}

interface UseOptimizedMSMEOptions {
  enableCache?: boolean;
  cacheTTL?: number;
}

export function useOptimizedMSME(msmeId: string, options: UseOptimizedMSMEOptions = {}) {
  const { enableCache = true, cacheTTL = 5 * 60 * 1000 } = options;
  
  const [msme, setMsme] = useState<MSME | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchMSME = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const cacheKey = `msme_${msmeId}`;
      let msmeData: MSME | null = null;

      if (enableCache) {
        msmeData = dataCache.get<MSME>(cacheKey);
      }

      if (!msmeData) {
        msmeData = await api.getMSME(msmeId);
        
        if (enableCache) {
          dataCache.set(cacheKey, msmeData, cacheTTL);
        }
      }

      setMsme(msmeData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch MSME');
    } finally {
      setLoading(false);
    }
  }, [msmeId, enableCache, cacheTTL]);

  useEffect(() => {
    if (msmeId) {
      fetchMSME();
    }
  }, [fetchMSME, msmeId]);

  const refetch = useCallback(async () => {
    if (enableCache) {
      dataCache.clear();
    }
    await fetchMSME();
  }, [fetchMSME, enableCache]);

  return { msme, loading, error, refetch };
}

// Export cache for manual management if needed
export { dataCache };
