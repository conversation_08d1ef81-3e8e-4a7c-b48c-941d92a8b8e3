'use client';

import React from 'react';
import { Signal, SignalSource } from '@/types';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Receipt, 
  CreditCard, 
  Star, 
  Phone, 
  Instagram, 
  MapPin,
  TrendingUp,
  TrendingDown,
  Minus,
  Clock
} from 'lucide-react';

interface SignalTimelineProps {
  signals: Signal[];
  loading?: boolean;
}

const getSignalIcon = (source: SignalSource) => {
  const iconProps = { className: "h-4 w-4" };
  
  switch (source) {
    case 'gst':
      return <Receipt {...iconProps} />;
    case 'upi':
      return <CreditCard {...iconProps} />;
    case 'reviews':
      return <Star {...iconProps} />;
    case 'justdial':
      return <Phone {...iconProps} />;
    case 'instagram':
      return <Instagram {...iconProps} />;
    case 'maps':
      return <MapPin {...iconProps} />;
    default:
      return <Clock {...iconProps} />;
  }
};

const getSignalColor = (source: SignalSource) => {
  switch (source) {
    case 'gst':
      return 'bg-blue-500/10 text-blue-500 border-blue-200';
    case 'upi':
      return 'bg-green-500/10 text-green-500 border-green-200';
    case 'reviews':
      return 'bg-yellow-500/10 text-yellow-500 border-yellow-200';
    case 'justdial':
      return 'bg-purple-500/10 text-purple-500 border-purple-200';
    case 'instagram':
      return 'bg-pink-500/10 text-pink-500 border-pink-200';
    case 'maps':
      return 'bg-red-500/10 text-red-500 border-red-200';
    default:
      return 'bg-gray-500/10 text-gray-500 border-gray-200';
  }
};

const formatSignalTitle = (signal: Signal): string => {
  const { source, value, metadata } = signal;
  
  switch (source) {
    case 'gst':
      if (typeof value === 'number') {
        return `GST Turnover: ₹${(value / 100000).toFixed(1)}L`;
      }
      return 'GST Data Updated';
      
    case 'upi':
      if (typeof value === 'object' && value) {
        const txnCount = value.transaction_count || 0;
        const volume = value.volume || 0;
        return `UPI Activity: ${txnCount} transactions, ₹${(Number(volume) / 1000).toFixed(0)}K`;
      }
      return 'UPI Data Updated';
      
    case 'reviews':
      if (typeof value === 'object' && value) {
        const rating = value.average_rating || 0;
        const count = value.review_count || 0;
        return `Reviews: ${Number(rating).toFixed(1)}★ (${count} reviews)`;
      }
      return 'Reviews Updated';
      
    case 'justdial':
      return 'JustDial Profile Updated';
      
    case 'instagram':
      if (typeof value === 'object' && value) {
        const followers = value.followers || 0;
        return `Instagram: ${followers} followers`;
      }
      return 'Instagram Profile Updated';
      
    case 'maps':
      return 'Google Maps Listing Updated';
      
    default:
      return 'Signal Updated';
  }
};

const getSignalDescription = (signal: Signal): string => {
  const { source, metadata } = signal;
  
  if (metadata && Object.keys(metadata).length > 0) {
    switch (source) {
      case 'gst':
        if (metadata.month) {
          return `Data for ${metadata.month}`;
        }
        break;
      case 'upi':
        if (metadata.period) {
          return `${metadata.period} activity summary`;
        }
        break;
      default:
        break;
    }
  }
  
  return `${source.toUpperCase()} signal recorded`;
};

const getImpactIndicator = (normalized: number) => {
  if (normalized >= 0.7) {
    return <TrendingUp className="h-4 w-4 text-green-500" />;
  } else if (normalized >= 0.4) {
    return <Minus className="h-4 w-4 text-yellow-500" />;
  } else {
    return <TrendingDown className="h-4 w-4 text-red-500" />;
  }
};

const formatDate = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
  
  if (diffInHours < 1) {
    return 'Just now';
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  } else if (diffInHours < 168) { // 7 days
    const days = Math.floor(diffInHours / 24);
    return `${days}d ago`;
  } else {
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  }
};

export function SignalTimeline({ signals, loading = false }: SignalTimelineProps) {
  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="w-6 h-6 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (signals.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Clock className="h-12 w-12 mx-auto mb-3 text-gray-400" />
          <p className="text-gray-500 mb-2">No signals recorded yet</p>
          <p className="text-sm text-gray-400">
            Signals will appear here as data is collected from various sources
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {signals.map((signal, index) => (
        <Card key={signal.signal_id} className="border-l-4 border-l-transparent hover:border-l-blue-500 transition-colors">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className={`p-2 rounded-lg border ${getSignalColor(signal.source)}`}>
                {getSignalIcon(signal.source)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-sm truncate">
                    {formatSignalTitle(signal)}
                  </h4>
                  <Badge variant="outline" className="text-xs">
                    {signal.source.toUpperCase()}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <span>{formatDate(signal.timestamp)}</span>
                  <span>•</span>
                  <span>{getSignalDescription(signal)}</span>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                {getImpactIndicator(signal.normalized)}
                <span className="text-xs text-gray-500">
                  {Math.round(signal.normalized * 100)}%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
