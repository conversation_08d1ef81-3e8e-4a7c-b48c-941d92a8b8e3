"""
Credit Chakra MSME Mobile App Backend.

FastAPI backend specifically designed for the MSME mobile application,
providing authentication, CCR scoring, nudges, training, and government schemes.

Author: Credit Chakra Team
Version: 1.0.0
"""
import logging
import os
import time
from datetime import datetime

from dotenv import load_dotenv
from fastapi import Fast<PERSON><PERSON>, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.security import HTT<PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
from firebase_admin import auth

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('msme_app.log') if not os.getenv("CLOUD_RUN") else logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="Credit Chakra MSME App API",
    description="Mobile API for MSME Credit Readiness, Training, and Government Schemes",
    version="1.0.0",
    docs_url="/docs" if os.getenv("DEBUG", "False").lower() == "true" else None,
    redoc_url="/redoc" if os.getenv("DEBUG", "False").lower() == "true" else None
)

# Security
security = HTTPBearer()

# Add security middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "testserver", "*.creditchakra.com", "*.msme-app.com"]
)

# CORS middleware for mobile app
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "https://*.creditchakra.com",
        "https://*.msme-app.com"
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Log all incoming requests with timing"""
    start_time = time.time()
    
    # Log request
    logger.info(f"Request: {request.method} {request.url}")
    
    # Process request
    response = await call_next(request)
    
    # Log response
    process_time = time.time() - start_time
    logger.info(f"Response: {response.status_code} - {process_time:.3f}s")
    
    return response

# Authentication dependency
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify Firebase JWT token and return user info"""
    try:
        # Verify the token
        decoded_token = auth.verify_id_token(credentials.credentials)
        user_id = decoded_token['uid']
        
        # Get additional user info if needed
        user_record = auth.get_user(user_id)
        
        return {
            "uid": user_id,
            "email": user_record.email,
            "phone": user_record.phone_number,
            "verified": user_record.email_verified,
            "custom_claims": decoded_token.get('custom_claims', {})
        }
    except Exception as e:
        logger.error(f"Authentication failed: {str(e)}")
        raise HTTPException(status_code=401, detail="Invalid authentication token")

# Optional authentication (for public endpoints)
async def get_current_user_optional(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Optional authentication - returns None if no valid token"""
    try:
        return await get_current_user(credentials)
    except HTTPException:
        return None

# Initialize Firebase
try:
    from firebase.init import firebase_client
    logger.info("Firebase initialized successfully")
except Exception as e:
    logger.warning(f"Firebase initialization failed: {e}")

# Health check endpoints
@app.get("/", tags=["System"])
async def root():
    """Root endpoint providing basic API information"""
    return {
        "message": "Credit Chakra MSME App API is running",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat()
    }

@app.get("/health", tags=["System"])
async def health_check():
    """Health check endpoint for monitoring and load balancers"""
    return {
        "status": "healthy",
        "service": "credit-chakra-msme-app",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat()
    }

@app.get("/health/detailed", tags=["System"])
async def detailed_health_check():
    """Detailed health check including dependencies"""
    health_status = {
        "status": "healthy",
        "service": "credit-chakra-msme-app",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat(),
        "dependencies": {}
    }
    
    # Check Firebase connection
    try:
        from firebase.init import get_firestore_client
        db = get_firestore_client()
        # Simple test query
        test_ref = db.collection('health_check').limit(1)
        list(test_ref.stream())
        health_status["dependencies"]["firestore"] = "healthy"
    except Exception as e:
        health_status["dependencies"]["firestore"] = f"unhealthy: {str(e)}"
        health_status["status"] = "degraded"
    
    return health_status

# Import and include routers
try:
    from routes import auth, ccr, nudges, training, schemes
    
    # Include routers with proper prefixes
    app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
    app.include_router(ccr.router, prefix="/api/ccr", tags=["CCR Score"])
    app.include_router(nudges.router, prefix="/api/nudges", tags=["Nudges & Reminders"])
    app.include_router(training.router, prefix="/api/training", tags=["Training & Courses"])
    app.include_router(schemes.router, prefix="/api/schemes", tags=["Government Schemes"])
    
    logger.info("All routers loaded successfully")
    
except ImportError as e:
    logger.warning(f"Some routers could not be loaded: {e}")

# Error handlers
@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    return {
        "error": "Not Found",
        "message": f"The requested endpoint {request.url.path} was not found",
        "status_code": 404
    }

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    logger.error(f"Internal server error: {str(exc)}")
    return {
        "error": "Internal Server Error",
        "message": "An unexpected error occurred",
        "status_code": 500
    }

if __name__ == "__main__":
    import uvicorn
    
    # Development server configuration
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", 8001)),
        reload=os.getenv("DEBUG", "False").lower() == "true",
        log_level="info"
    )
