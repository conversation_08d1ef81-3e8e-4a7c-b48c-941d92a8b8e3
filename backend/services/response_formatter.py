"""
Enhanced Response Formatter for AI Copilot
Provides intelligent formatting for numerical data, risk assessments, and recommendations
"""

import json
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
import re

class ResponseFormatter:
    """Enhanced response formatter with contextual understanding"""
    
    def __init__(self):
        self.risk_colors = {
            'high': '🔴',
            'medium': '🟡', 
            'low': '🟢'
        }
        
        self.trend_indicators = {
            'improving': '📈',
            'declining': '📉',
            'stable': '➡️'
        }
        
        self.priority_indicators = {
            'critical': '🚨',
            'high': '⚠️',
            'medium': '📋',
            'low': '📝'
        }

    def format_currency(self, amount: Union[int, float], currency: str = '₹') -> str:
        """Format currency with Indian numbering system"""
        if amount >= 10000000:  # 1 crore
            return f"{currency}{amount/10000000:.1f}Cr"
        elif amount >= 100000:  # 1 lakh
            return f"{currency}{amount/100000:.1f}L"
        elif amount >= 1000:  # 1 thousand
            return f"{currency}{amount/1000:.1f}K"
        else:
            return f"{currency}{amount:,.0f}"

    def format_percentage(self, value: Union[int, float], decimal_places: int = 1) -> str:
        """Format percentage with appropriate precision"""
        return f"{value:.{decimal_places}f}%"

    def format_score(self, score: Union[int, float], max_score: int = 100) -> str:
        """Format credit score with risk indication"""
        risk_level = self.get_risk_level_from_score(score)
        risk_icon = self.risk_colors.get(risk_level, '')
        return f"{risk_icon} {score:.0f}/{max_score}"

    def get_risk_level_from_score(self, score: Union[int, float]) -> str:
        """Determine risk level from credit score"""
        if score >= 75:
            return 'low'
        elif score >= 50:
            return 'medium'
        else:
            return 'high'

    def format_trend(self, current: float, previous: float, period: str = "month") -> str:
        """Format trend with direction and magnitude"""
        change = current - previous
        change_pct = (change / previous * 100) if previous != 0 else 0
        
        if abs(change_pct) < 1:
            direction = 'stable'
            trend_text = f"➡️ Stable ({change_pct:+.1f}%)"
        elif change_pct > 0:
            direction = 'improving'
            trend_text = f"📈 Up {change_pct:.1f}%"
        else:
            direction = 'declining'
            trend_text = f"📉 Down {abs(change_pct):.1f}%"
        
        return f"{trend_text} vs last {period}"

    def format_risk_distribution(self, distribution: Dict[str, int]) -> str:
        """Format risk distribution with visual indicators"""
        total = sum(distribution.values())
        if total == 0:
            return "No data available"
        
        formatted_parts = []
        for risk_level in ['low', 'medium', 'high']:
            count = distribution.get(risk_level, 0)
            percentage = (count / total) * 100
            icon = self.risk_colors.get(risk_level, '')
            formatted_parts.append(f"{icon} {count} ({percentage:.1f}%)")
        
        return " | ".join(formatted_parts)

    def format_compliance_status(self, status: str, deadline: Optional[datetime] = None) -> str:
        """Format compliance status with urgency indicators"""
        status_icons = {
            'compliant': '✅',
            'non_compliant': '❌',
            'pending': '⏳',
            'warning': '⚠️',
            'overdue': '🚨'
        }
        
        icon = status_icons.get(status.lower(), '📋')
        formatted_status = f"{icon} {status.title()}"
        
        if deadline:
            days_until = (deadline - datetime.now()).days
            if days_until < 0:
                formatted_status += f" (Overdue by {abs(days_until)} days)"
            elif days_until <= 7:
                formatted_status += f" (Due in {days_until} days)"
        
        return formatted_status

    def format_msme_summary(self, msme_data: Dict[str, Any]) -> str:
        """Format MSME summary with key metrics"""
        name = msme_data.get('business_name', 'Unknown Business')
        score = msme_data.get('credit_score', 0)
        risk_level = self.get_risk_level_from_score(score)
        exposure = msme_data.get('exposure_amount', 0)
        
        summary = f"**{name}**\n"
        summary += f"• Score: {self.format_score(score)}\n"
        summary += f"• Exposure: {self.format_currency(exposure)}\n"
        summary += f"• Risk Level: {self.risk_colors.get(risk_level, '')} {risk_level.title()}"
        
        return summary

    def format_portfolio_health(self, health_score: float, total_msmes: int, 
                              risk_distribution: Dict[str, int]) -> str:
        """Format comprehensive portfolio health summary"""
        health_icon = "🟢" if health_score >= 80 else "🟡" if health_score >= 60 else "🔴"
        
        summary = f"## {health_icon} Portfolio Health: {health_score:.1f}/100\n\n"
        summary += f"**📊 Overview:**\n"
        summary += f"• Total MSMEs: {total_msmes:,}\n"
        summary += f"• Risk Distribution: {self.format_risk_distribution(risk_distribution)}\n"
        
        # Add health insights
        high_risk_count = risk_distribution.get('high', 0)
        high_risk_pct = (high_risk_count / total_msmes * 100) if total_msmes > 0 else 0
        
        if high_risk_pct > 20:
            summary += f"\n⚠️ **Alert:** {high_risk_pct:.1f}% of portfolio is high-risk"
        elif high_risk_pct < 5:
            summary += f"\n✅ **Good:** Low risk exposure ({high_risk_pct:.1f}%)"
        
        return summary

    def format_recommendations(self, recommendations: List[str], 
                             priority: str = 'medium') -> str:
        """Format AI recommendations with priority indicators"""
        if not recommendations:
            return ""
        
        priority_icon = self.priority_indicators.get(priority, '📋')
        formatted = f"\n## {priority_icon} AI Recommendations:\n\n"
        
        for i, rec in enumerate(recommendations, 1):
            formatted += f"{i}. {rec}\n"
        
        return formatted

    def format_predictive_insights(self, insights: List[Dict[str, Any]]) -> str:
        """Format predictive insights with confidence levels"""
        if not insights:
            return ""
        
        formatted = "\n## 🔮 Predictive Insights:\n\n"
        
        for insight in insights[:3]:  # Limit to top 3
            title = insight.get('title', 'Insight')
            description = insight.get('description', '')
            confidence = insight.get('confidence', 0.5)
            impact = insight.get('impact', 'medium')
            
            confidence_bar = "█" * int(confidence * 10) + "░" * (10 - int(confidence * 10))
            impact_icon = self.priority_indicators.get(impact, '📋')
            
            formatted += f"**{title}** {impact_icon}\n"
            formatted += f"• {description}\n"
            formatted += f"• Confidence: {confidence_bar} {confidence*100:.0f}%\n\n"
        
        return formatted

    def format_risk_alerts(self, alerts: List[Dict[str, Any]]) -> str:
        """Format risk alerts with severity and urgency"""
        if not alerts:
            return ""
        
        formatted = "\n## 🚨 Risk Alerts:\n\n"
        
        # Sort by severity and exposure
        sorted_alerts = sorted(alerts, 
                             key=lambda x: (x.get('severity_score', 0), x.get('exposure_amount', 0)), 
                             reverse=True)
        
        for alert in sorted_alerts[:5]:  # Top 5 alerts
            msme_name = alert.get('msme_name', 'Unknown MSME')
            risk_type = alert.get('risk_type', 'general')
            description = alert.get('description', '')
            severity = alert.get('severity_score', 0)
            exposure = alert.get('exposure_amount', 0)
            
            severity_icon = "🚨" if severity >= 80 else "⚠️" if severity >= 60 else "📋"
            
            formatted += f"{severity_icon} **{msme_name}** ({risk_type.title()})\n"
            formatted += f"• {description}\n"
            formatted += f"• Exposure: {self.format_currency(exposure)} | Severity: {severity}/100\n\n"
        
        return formatted

    def enhance_response_with_context(self, base_response: str, 
                                    context_data: Dict[str, Any]) -> str:
        """Enhance base response with contextual data formatting"""
        enhanced_response = base_response
        
        # Add portfolio health if available
        if 'portfolio_health' in context_data:
            health_data = context_data['portfolio_health']
            enhanced_response += "\n" + self.format_portfolio_health(
                health_data.get('score', 75),
                health_data.get('total_msmes', 0),
                health_data.get('risk_distribution', {})
            )
        
        # Add recommendations if available
        if 'recommendations' in context_data:
            enhanced_response += self.format_recommendations(
                context_data['recommendations'],
                context_data.get('priority', 'medium')
            )
        
        # Add predictive insights if available
        if 'predictive_insights' in context_data:
            enhanced_response += self.format_predictive_insights(
                context_data['predictive_insights']
            )
        
        # Add risk alerts if available
        if 'risk_alerts' in context_data:
            enhanced_response += self.format_risk_alerts(
                context_data['risk_alerts']
            )
        
        return enhanced_response

    def format_query_specific_response(self, query: str, data: Dict[str, Any]) -> str:
        """Generate query-specific formatted responses"""
        query_lower = query.lower()
        
        # Risk-related queries
        if any(word in query_lower for word in ['risk', 'alert', 'danger', 'problem']):
            return self._format_risk_response(data)
        
        # Compliance-related queries
        elif any(word in query_lower for word in ['compliance', 'deadline', 'filing', 'regulation']):
            return self._format_compliance_response(data)
        
        # Performance/trend queries
        elif any(word in query_lower for word in ['trend', 'performance', 'analysis', 'growth']):
            return self._format_performance_response(data)
        
        # Portfolio overview queries
        elif any(word in query_lower for word in ['portfolio', 'overview', 'summary', 'dashboard']):
            return self._format_portfolio_response(data)
        
        # Default enhanced response
        else:
            return self._format_general_response(data)

    def _format_risk_response(self, data: Dict[str, Any]) -> str:
        """Format risk-focused response"""
        response = "## 🎯 Risk Analysis Report\n\n"
        
        # Add risk distribution
        if 'risk_distribution' in data:
            response += f"**Risk Distribution:**\n{self.format_risk_distribution(data['risk_distribution'])}\n\n"
        
        # Add high-risk MSMEs
        if 'high_risk_msmes' in data:
            response += "**🚨 High-Risk MSMEs Requiring Attention:**\n\n"
            for msme in data['high_risk_msmes'][:3]:
                response += f"• {self.format_msme_summary(msme)}\n\n"
        
        return response

    def _format_compliance_response(self, data: Dict[str, Any]) -> str:
        """Format compliance-focused response"""
        response = "## 📋 Compliance Status Report\n\n"
        
        if 'compliance_summary' in data:
            compliance = data['compliance_summary']
            response += f"**Overall Compliance Rate:** {self.format_percentage(compliance.get('rate', 0))}\n\n"
            
            if 'upcoming_deadlines' in compliance:
                response += "**📅 Upcoming Deadlines:**\n"
                for deadline in compliance['upcoming_deadlines'][:5]:
                    response += f"• {deadline.get('description', 'Unknown')} - {deadline.get('date', 'TBD')}\n"
        
        return response

    def _format_performance_response(self, data: Dict[str, Any]) -> str:
        """Format performance-focused response"""
        response = "## 📈 Performance Analysis\n\n"
        
        if 'performance_metrics' in data:
            metrics = data['performance_metrics']
            avg_score = metrics.get('avg_score', 0)
            prev_score = metrics.get('prev_avg_score', avg_score)
            
            response += f"**Average Portfolio Score:** {self.format_score(avg_score)}\n"
            response += f"**Trend:** {self.format_trend(avg_score, prev_score)}\n\n"
        
        return response

    def _format_portfolio_response(self, data: Dict[str, Any]) -> str:
        """Format portfolio overview response"""
        response = "## 📊 Portfolio Overview\n\n"
        
        total_msmes = data.get('total_msmes', 0)
        avg_score = data.get('avg_score', 0)
        
        response += f"**Portfolio Size:** {total_msmes:,} MSMEs\n"
        response += f"**Average Score:** {self.format_score(avg_score)}\n"
        
        if 'sector_distribution' in data:
            response += "\n**📈 Sector Distribution:**\n"
            for sector, count in data['sector_distribution'].items():
                pct = (count / total_msmes * 100) if total_msmes > 0 else 0
                response += f"• {sector}: {count} ({pct:.1f}%)\n"
        
        return response

    def _format_general_response(self, data: Dict[str, Any]) -> str:
        """Format general response with available data"""
        response = "## 🤖 AI Analysis Results\n\n"
        
        # Add whatever data is available in a structured format
        if 'summary' in data:
            response += f"{data['summary']}\n\n"
        
        return response

# Global formatter instance
response_formatter = ResponseFormatter()
