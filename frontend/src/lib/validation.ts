/**
 * Input validation and sanitization utilities for security
 * Prevents XSS, injection attacks, and ensures data integrity
 */

// Common validation patterns
export const VALIDATION_PATTERNS = {
  // Basic text with alphanumeric, spaces, and common punctuation
  SAFE_TEXT: /^[a-zA-Z0-9\s\-\.\,\(\)\'\"]+$/,
  
  // Business names (more permissive but safe)
  BUSINESS_NAME: /^[a-zA-Z0-9\s\-\.\&\(\)\'\"]+$/,
  
  // Email validation
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  
  // Phone number (Indian format)
  PHONE: /^(\+91[\-\s]?)?[0]?(91)?[6789]\d{9}$/,
  
  // GST number format
  GST: /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}[Z]{1}[0-9A-Z]{1}$/,
  
  // Numeric values
  NUMERIC: /^\d+(\.\d+)?$/,
  
  // Currency amounts (Indian format)
  CURRENCY: /^\d{1,3}(,\d{3})*(\.\d{2})?$/,
  
  // Safe search terms (no special characters that could be used for injection)
  SEARCH_TERM: /^[a-zA-Z0-9\s\-\.]+$/,
  
  // Chat message content (allows more characters but blocks dangerous ones)
  CHAT_MESSAGE: /^[a-zA-Z0-9\s\-\.\,\?\!\(\)\'\"\:\;\&\%\@\#\+\=\[\]]+$/
};

// Input length limits
export const INPUT_LIMITS = {
  BUSINESS_NAME: { min: 1, max: 100 },
  DESCRIPTION: { min: 0, max: 500 },
  CONTACT_NAME: { min: 1, max: 50 },
  EMAIL: { min: 5, max: 100 },
  PHONE: { min: 10, max: 15 },
  GST: { min: 15, max: 15 },
  SEARCH_TERM: { min: 0, max: 100 },
  CHAT_MESSAGE: { min: 1, max: 1000 },
  NUDGE_MESSAGE: { min: 10, max: 1000 },
  LOCATION: { min: 1, max: 100 }
};

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  sanitizedValue: string;
  errors: string[];
}

/**
 * Sanitize input by removing potentially dangerous characters
 */
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') {
    return '';
  }
  
  return input
    .trim()
    // Remove HTML tags
    .replace(/<[^>]*>/g, '')
    // Remove script tags and their content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    // Remove javascript: protocol
    .replace(/javascript:/gi, '')
    // Remove on* event handlers
    .replace(/\bon\w+\s*=/gi, '')
    // Remove null bytes
    .replace(/\0/g, '')
    // Normalize whitespace
    .replace(/\s+/g, ' ');
}

/**
 * Validate business name
 */
export function validateBusinessName(name: string): ValidationResult {
  const sanitized = sanitizeInput(name);
  const errors: string[] = [];
  
  if (!sanitized) {
    errors.push('Business name is required');
  } else if (sanitized.length < INPUT_LIMITS.BUSINESS_NAME.min) {
    errors.push('Business name is too short');
  } else if (sanitized.length > INPUT_LIMITS.BUSINESS_NAME.max) {
    errors.push('Business name is too long');
  } else if (!VALIDATION_PATTERNS.BUSINESS_NAME.test(sanitized)) {
    errors.push('Business name contains invalid characters');
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}

/**
 * Validate email address
 */
export function validateEmail(email: string): ValidationResult {
  const sanitized = sanitizeInput(email);
  const errors: string[] = [];
  
  if (sanitized && !VALIDATION_PATTERNS.EMAIL.test(sanitized)) {
    errors.push('Invalid email format');
  }
  
  if (sanitized.length > INPUT_LIMITS.EMAIL.max) {
    errors.push('Email is too long');
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}

/**
 * Validate phone number
 */
export function validatePhone(phone: string): ValidationResult {
  const sanitized = sanitizeInput(phone);
  const errors: string[] = [];
  
  if (sanitized && !VALIDATION_PATTERNS.PHONE.test(sanitized)) {
    errors.push('Invalid phone number format');
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}

/**
 * Validate GST number
 */
export function validateGST(gst: string): ValidationResult {
  const sanitized = sanitizeInput(gst).toUpperCase();
  const errors: string[] = [];
  
  if (sanitized && !VALIDATION_PATTERNS.GST.test(sanitized)) {
    errors.push('Invalid GST number format');
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}

/**
 * Validate search term
 */
export function validateSearchTerm(term: string): ValidationResult {
  const sanitized = sanitizeInput(term);
  const errors: string[] = [];
  
  if (sanitized.length > INPUT_LIMITS.SEARCH_TERM.max) {
    errors.push('Search term is too long');
  }
  
  if (sanitized && !VALIDATION_PATTERNS.SEARCH_TERM.test(sanitized)) {
    errors.push('Search term contains invalid characters');
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}

/**
 * Validate chat message
 */
export function validateChatMessage(message: string): ValidationResult {
  const sanitized = sanitizeInput(message);
  const errors: string[] = [];
  
  if (!sanitized) {
    errors.push('Message is required');
  } else if (sanitized.length < INPUT_LIMITS.CHAT_MESSAGE.min) {
    errors.push('Message is too short');
  } else if (sanitized.length > INPUT_LIMITS.CHAT_MESSAGE.max) {
    errors.push('Message is too long');
  } else if (!VALIDATION_PATTERNS.CHAT_MESSAGE.test(sanitized)) {
    errors.push('Message contains invalid characters');
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}

/**
 * Validate nudge message
 */
export function validateNudgeMessage(message: string): ValidationResult {
  const sanitized = sanitizeInput(message);
  const errors: string[] = [];
  
  if (!sanitized) {
    errors.push('Message is required');
  } else if (sanitized.length < INPUT_LIMITS.NUDGE_MESSAGE.min) {
    errors.push('Message must be at least 10 characters');
  } else if (sanitized.length > INPUT_LIMITS.NUDGE_MESSAGE.max) {
    errors.push('Message must be less than 1000 characters');
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}

/**
 * Validate numeric input
 */
export function validateNumeric(value: string, min?: number, max?: number): ValidationResult {
  const sanitized = sanitizeInput(value);
  const errors: string[] = [];
  
  if (sanitized && !VALIDATION_PATTERNS.NUMERIC.test(sanitized)) {
    errors.push('Must be a valid number');
  }
  
  const numValue = parseFloat(sanitized);
  if (!isNaN(numValue)) {
    if (min !== undefined && numValue < min) {
      errors.push(`Value must be at least ${min}`);
    }
    if (max !== undefined && numValue > max) {
      errors.push(`Value must be at most ${max}`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}

/**
 * Generic text validation
 */
export function validateText(text: string, minLength: number = 0, maxLength: number = 500): ValidationResult {
  const sanitized = sanitizeInput(text);
  const errors: string[] = [];
  
  if (minLength > 0 && sanitized.length < minLength) {
    errors.push(`Text must be at least ${minLength} characters`);
  }
  
  if (sanitized.length > maxLength) {
    errors.push(`Text must be less than ${maxLength} characters`);
  }
  
  if (sanitized && !VALIDATION_PATTERNS.SAFE_TEXT.test(sanitized)) {
    errors.push('Text contains invalid characters');
  }
  
  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}
