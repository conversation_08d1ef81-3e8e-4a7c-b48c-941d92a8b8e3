/**
 * Enhanced TypeScript types for AI Copilot components
 * Provides comprehensive type safety for chat interface, messages, and AI responses
 */

import { LucideIcon } from 'lucide-react';

// Core message types
export interface ChatMessage {
  id: string;
  content: string;
  type: 'user' | 'assistant';
  timestamp: Date;
  data?: CopilotResponseData;
}

// Enhanced conversation context
export interface ConversationContext {
  last_query?: string;
  last_response_type?: ResponseType;
  topics_discussed?: string[];
  user_satisfaction?: number;
  session_metadata?: SessionMetadata;
}

export interface SessionMetadata {
  session_id: string;
  user_id: string;
  start_time: Date;
  message_count: number;
  last_activity: Date;
}

// Query and response types
export type ResponseType = 'general' | 'analytics' | 'risk' | 'compliance' | 'insights' | 'error';

export interface QueryMetadata {
  query_length: number;
  query_type: 'question' | 'command';
  contains_numbers: boolean;
  urgency_indicators: boolean;
  complexity_score?: number;
  estimated_response_time?: number;
}

// AI Analytics types
export interface RiskDistribution {
  high: number;
  medium: number;
  low: number;
}

export interface AIAnalytics {
  portfolio_health_score: number;
  total_msmes: number;
  risk_distribution?: RiskDistribution;
  trend_analysis?: TrendAnalysis;
  performance_metrics?: PerformanceMetrics;
}

export interface TrendAnalysis {
  direction: 'improving' | 'declining' | 'stable';
  change_percentage: number;
  time_period: string;
  key_factors: string[];
}

export interface PerformanceMetrics {
  avg_score: number;
  score_variance: number;
  compliance_rate: number;
  risk_adjusted_return: number;
}

// Predictive insights
export interface PredictiveInsight {
  id: string;
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  timeline: string;
  category: 'risk' | 'opportunity' | 'compliance' | 'performance';
  actionable: boolean;
  recommended_actions?: string[];
}

// Risk alerts
export interface RiskAlert {
  msme_id: string;
  msme_name: string;
  risk_level: 'high' | 'medium' | 'low';
  risk_type: 'credit' | 'operational' | 'compliance' | 'market';
  description: string;
  severity_score: number;
  detected_at: Date;
  requires_action: boolean;
  suggested_actions?: string[];
}

// Compliance information
export interface ComplianceInfo {
  status: 'compliant' | 'non_compliant' | 'pending' | 'warning';
  deadline?: Date;
  requirements: string[];
  completion_percentage: number;
  critical_items: string[];
}

// Enhanced response data structure
export interface CopilotResponseData {
  response_type: ResponseType;
  confidence: number;
  processing_time_ms: number;
  topics: string[];
  ai_analytics?: AIAnalytics;
  predictive_insights?: PredictiveInsight[];
  ai_recommendations?: string[];
  risk_alerts?: RiskAlert[];
  compliance_info?: ComplianceInfo[];
  msme_data?: Array<{
    id: string;
    name: string;
    score: number;
    risk_level: string;
    last_updated: Date;
  }>;
  charts_data?: ChartData[];
  summary_stats?: SummaryStats;
}

// Chart data for visualizations
export interface ChartData {
  type: 'bar' | 'line' | 'pie' | 'scatter' | 'heatmap';
  title: string;
  data: Array<{
    label: string;
    value: number;
    color?: string;
    metadata?: Record<string, unknown>;
  }>;
  config?: {
    x_axis_label?: string;
    y_axis_label?: string;
    show_legend?: boolean;
    color_scheme?: string[];
  };
}

// Summary statistics
export interface SummaryStats {
  total_count: number;
  high_risk_count: number;
  medium_risk_count: number;
  low_risk_count: number;
  avg_score: number;
  score_trend: 'up' | 'down' | 'stable';
  compliance_rate: number;
  last_updated: Date;
}

// Quick Actions types
export interface QuickAction {
  id: string;
  title: string;
  query: string;
  icon: LucideIcon;
  count?: string;
  priority: 'high' | 'medium' | 'low';
  category: 'risk' | 'compliance' | 'analytics' | 'insights';
  aiPowered: boolean;
  description: string;
  estimated_time?: string;
  requires_confirmation?: boolean;
}

// Insights Feed types
export interface Insight {
  id: string;
  type: 'alert' | 'trend' | 'compliance' | 'geographic' | 'prediction' | 'opportunity';
  severity: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  timestamp: Date;
  actionable: boolean;
  dismissed?: boolean;
  priority_score: number;
  exposure_amount?: number;
  metadata?: Record<string, unknown>;
  ai_generated: boolean;
  confidence?: number;
  source: 'ai_model' | 'rule_engine' | 'manual' | 'external_api';
}

// Error handling types
export interface CopilotError {
  code: string;
  message: string;
  details?: string;
  timestamp: Date;
  recoverable: boolean;
  suggested_actions?: string[];
}

// Component prop types
export interface CopilotPageProps {
  initialMessages?: ChatMessage[];
  defaultQuickActionsOpen?: boolean;
  defaultInsightsOpen?: boolean;
}

export interface MessageListProps {
  messages: ChatMessage[];
  isLoading?: boolean;
  onRetry?: (messageId: string) => void;
}

export interface EnhancedMessageProps {
  content: string;
  data?: CopilotResponseData;
  timestamp: Date;
  type: 'user' | 'assistant';
  isLoading?: boolean;
  error?: CopilotError;
}

export interface QuickActionsProps {
  onQuerySelect: (query: string) => void;
  actions?: QuickAction[];
  isLoading?: boolean;
}

export interface InsightsFeedProps {
  insights?: Insight[];
  onInsightDismiss?: (insightId: string) => void;
  onInsightAction?: (insightId: string, action: string) => void;
  isLoading?: boolean;
}

export interface QuerySuggestionsProps {
  onSuggestionClick: (suggestion: string) => void;
  suggestions?: string[];
  category?: 'general' | 'risk' | 'compliance' | 'analytics';
}

// API response types
export interface CopilotApiResponse {
  success: boolean;
  data?: CopilotResponseData;
  error?: CopilotError;
  metadata?: {
    request_id: string;
    processing_time_ms: number;
    model_version: string;
    cache_hit: boolean;
  };
}

// Performance monitoring types
export interface PerformanceMetrics {
  component_name: string;
  render_time_ms: number;
  memory_usage_mb: number;
  api_call_count: number;
  cache_hit_rate: number;
  error_count: number;
  user_interactions: number;
  timestamp: Date;
}

// Utility types
export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'error' | 'retry';
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';
export type ViewMode = 'chat' | 'analytics' | 'insights' | 'actions';

// Type guards
export const isChatMessage = (obj: unknown): obj is ChatMessage => {
  return typeof obj === 'object' && obj !== null && 
         'id' in obj && 'content' in obj && 'type' in obj && 'timestamp' in obj;
};

export const isAIAnalytics = (obj: unknown): obj is AIAnalytics => {
  return typeof obj === 'object' && obj !== null && 
         'portfolio_health_score' in obj && 'total_msmes' in obj;
};

export const isPredictiveInsight = (obj: unknown): obj is PredictiveInsight => {
  return typeof obj === 'object' && obj !== null && 
         'title' in obj && 'description' in obj && 'confidence' in obj;
};
