'use client';

import { memo, useCallback } from 'react';
import { EnhancedMessage } from './enhanced-message';
import { ChatMessage, MessageListProps } from '@/types/copilot';

// Enhanced error boundary for individual messages
const MessageErrorBoundary = memo(function MessageErrorBoundary({
  children,
  messageId,
  onRetry
}: {
  children: React.ReactNode;
  messageId: string;
  onRetry?: (messageId: string) => void;
}) {
  // Use parameters to avoid unused variable warnings
  const handleRetry = () => onRetry?.(messageId);

  return (
    <div className="message-container" data-message-id={messageId}>
      {children}
      {onRetry && (
        <button
          onClick={handleRetry}
          className="sr-only"
          aria-label="Retry message"
        />
      )}
    </div>
  );
});

const MessageListComponent = memo(function MessageList({
  messages,
  isLoading = false,
  onRetry
}: MessageListProps) {

  // Memoized message validation function
  const validateMessage = useCallback((message: ChatMessage): ChatMessage => {
    // Ensure timestamp is a Date object
    const validatedMessage: ChatMessage = {
      ...message,
      timestamp: message.timestamp instanceof Date ? message.timestamp : new Date(message.timestamp)
    };

    // Validate and sanitize data to prevent React event objects
    if (validatedMessage.data && typeof validatedMessage.data === 'object') {
      const data = validatedMessage.data as unknown as Record<string, unknown>;
      if ('_reactName' in data || 'nativeEvent' in data || 'currentTarget' in data) {
        console.warn('Detected React event object in message data, filtering out:', message.id);
        validatedMessage.data = undefined;
      }
    }

    return validatedMessage;
  }, []);

  // Memoized message renderer with error boundary
  const renderMessage = useCallback((message: ChatMessage) => {
    try {
      const validatedMessage = validateMessage(message);

      return (
        <MessageErrorBoundary
          key={validatedMessage.id}
          messageId={validatedMessage.id}
          onRetry={onRetry}
        >
          <EnhancedMessage
            content={validatedMessage.content}
            data={validatedMessage.data}
            timestamp={validatedMessage.timestamp}
            type={validatedMessage.type}
          />
        </MessageErrorBoundary>
      );
    } catch (error) {
      console.error('Error rendering message:', error, message);

      return (
        <div key={message.id} className="p-4 border border-red-200 rounded-lg bg-red-50 shadow-sm">
          <div className="flex items-start gap-3">
            <div className="w-2 h-2 rounded-full bg-red-500 mt-2 flex-shrink-0"></div>
            <div className="flex-1">
              <p className="text-sm font-medium text-red-600 mb-1">Message Error</p>
              <p className="text-xs text-red-500 mb-2">{message.content}</p>
              <p className="text-xs text-red-400">
                {error instanceof Error ? error.message : 'Unknown rendering error'}
              </p>
              {onRetry && (
                <button
                  onClick={() => onRetry(message.id)}
                  className="mt-2 text-xs text-red-600 hover:text-red-700 underline"
                >
                  Retry
                </button>
              )}
            </div>
          </div>
        </div>
      );
    }
  }, [validateMessage, onRetry]);

  // Enhanced loading state with emerald theme
  if (isLoading && messages.length === 0) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="animate-pulse-emerald">
            <div className="flex justify-start mb-4">
              <div className="max-w-[80%] bg-emerald-50 rounded-lg px-4 py-3 border border-emerald-100">
                <div className="h-4 bg-emerald-200 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-emerald-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {messages.map(renderMessage)}
      {isLoading && (
        <div className="flex justify-start mb-4">
          <div className="max-w-[80%] bg-muted/50 rounded-lg px-4 py-3 animate-pulse">
            <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
            <div className="h-4 bg-muted rounded w-1/2"></div>
          </div>
        </div>
      )}
    </div>
  );
});

// Export the memoized component
export { MessageListComponent as MessageList };
