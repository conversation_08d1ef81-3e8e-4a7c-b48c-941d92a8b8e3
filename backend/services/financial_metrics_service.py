import asyncio
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta, date
import statistics

from models.financial_metrics import (
    FinancialMetric, LiquidityMetrics, ProfitabilityMetrics, LeverageMetrics,
    EfficiencyMetrics, ComprehensiveFinancialMetrics, FinancialMetricsRequest,
    FinancialMetricsResponse, MetricCategory, MetricTrend, RiskLevel
)
from models.cash_flow import CashFlowStatement, CashFlowAnalytics
from models.gst_data import GSTAnalytics
from firebase.init import get_firestore_client

logger = logging.getLogger(__name__)

class FinancialMetricsService:
    """Service for calculating comprehensive financial metrics"""
    
    def __init__(self):
        self.db = get_firestore_client()
        
        # Industry benchmarks (mock data - in practice, would come from external sources)
        self.industry_benchmarks = {
            'current_ratio': 2.0,
            'quick_ratio': 1.0,
            'cash_ratio': 0.2,
            'debt_to_equity': 0.5,
            'debt_service_coverage': 1.25,
            'gross_profit_margin': 25.0,
            'net_profit_margin': 10.0,
            'return_on_assets': 8.0,
            'return_on_equity': 15.0,
            'asset_turnover': 1.5,
            'inventory_turnover': 6.0,
            'days_sales_outstanding': 45.0
        }
    
    async def calculate_comprehensive_metrics(self, msme_id: str, 
                                            calculation_date: Optional[date] = None) -> Optional[ComprehensiveFinancialMetrics]:
        """Calculate comprehensive financial metrics"""
        try:
            if not calculation_date:
                calculation_date = date.today()
            
            # Get required data
            financial_data = await self._get_financial_data(msme_id, calculation_date)
            cash_flow_data = await self._get_cash_flow_data(msme_id)
            gst_data = await self._get_gst_data(msme_id)
            
            if not financial_data:
                logger.warning(f"No financial data found for MSME {msme_id}")
                return None
            
            # Calculate component metrics
            liquidity_metrics = self._calculate_liquidity_metrics(msme_id, financial_data, cash_flow_data, calculation_date)
            profitability_metrics = self._calculate_profitability_metrics(msme_id, financial_data, gst_data, calculation_date)
            leverage_metrics = self._calculate_leverage_metrics(msme_id, financial_data, calculation_date)
            efficiency_metrics = self._calculate_efficiency_metrics(msme_id, financial_data, calculation_date)
            
            # Create comprehensive metrics
            comprehensive_metrics = ComprehensiveFinancialMetrics(
                msme_id=msme_id,
                calculation_date=calculation_date,
                liquidity_metrics=liquidity_metrics,
                profitability_metrics=profitability_metrics,
                leverage_metrics=leverage_metrics,
                efficiency_metrics=efficiency_metrics
            )
            
            # Calculate overall scores and assessments
            comprehensive_metrics.overall_financial_score = self._calculate_overall_financial_score(comprehensive_metrics)
            comprehensive_metrics.financial_grade = self._determine_financial_grade(comprehensive_metrics.overall_financial_score)
            comprehensive_metrics.overall_risk_level = self._assess_overall_risk_level(comprehensive_metrics)
            comprehensive_metrics.financial_trend = await self._analyze_financial_trend(msme_id)
            comprehensive_metrics.risk_factors = self._identify_risk_factors(comprehensive_metrics)
            comprehensive_metrics.improvement_areas = self._identify_improvement_areas(comprehensive_metrics)
            comprehensive_metrics.action_items = self._generate_action_items(comprehensive_metrics)
            comprehensive_metrics.industry_percentile = self._calculate_industry_percentile(comprehensive_metrics)
            comprehensive_metrics.data_completeness = self._calculate_data_completeness(financial_data)
            comprehensive_metrics.calculation_confidence = self._calculate_confidence_level(financial_data, cash_flow_data)
            
            # Store metrics
            await self._store_comprehensive_metrics(comprehensive_metrics)
            
            return comprehensive_metrics
            
        except Exception as e:
            logger.error(f"Error calculating comprehensive metrics: {str(e)}")
            return None
    
    def _calculate_liquidity_metrics(self, msme_id: str, financial_data: Dict[str, Any], 
                                   cash_flow_data: Optional[CashFlowAnalytics], 
                                   calculation_date: date) -> LiquidityMetrics:
        """Calculate liquidity metrics"""
        metrics = LiquidityMetrics(msme_id=msme_id, calculation_date=calculation_date)
        
        # Current Ratio
        current_assets = financial_data.get('current_assets', 0)
        current_liabilities = financial_data.get('current_liabilities', 1)  # Avoid division by zero
        metrics.current_ratio = current_assets / current_liabilities
        metrics.current_ratio_score = self._score_current_ratio(metrics.current_ratio)
        
        # Quick Ratio
        inventory = financial_data.get('inventory', 0)
        quick_assets = current_assets - inventory
        metrics.quick_ratio = quick_assets / current_liabilities
        metrics.quick_ratio_score = self._score_quick_ratio(metrics.quick_ratio)
        
        # Cash Ratio
        cash_and_equivalents = financial_data.get('cash_and_equivalents', 0)
        metrics.cash_ratio = cash_and_equivalents / current_liabilities
        metrics.cash_ratio_score = self._score_cash_ratio(metrics.cash_ratio)
        
        # Working Capital
        metrics.working_capital = current_assets - current_liabilities
        metrics.working_capital_score = self._score_working_capital(metrics.working_capital, financial_data.get('revenue', 1))
        
        # Operating Cash Flow Ratio
        if cash_flow_data:
            operating_cash_flow = financial_data.get('operating_cash_flow', 0)
            metrics.operating_cash_flow_ratio = operating_cash_flow / current_liabilities
            metrics.operating_cash_flow_ratio_score = self._score_operating_cash_flow_ratio(metrics.operating_cash_flow_ratio)
        
        # Overall liquidity score
        scores = [
            metrics.current_ratio_score,
            metrics.quick_ratio_score,
            metrics.cash_ratio_score,
            metrics.working_capital_score,
            metrics.operating_cash_flow_ratio_score
        ]
        metrics.overall_liquidity_score = sum(scores) / len(scores)
        metrics.liquidity_risk_level = self._determine_liquidity_risk_level(metrics.overall_liquidity_score)
        
        return metrics
    
    def _calculate_profitability_metrics(self, msme_id: str, financial_data: Dict[str, Any], 
                                       gst_data: Optional[GSTAnalytics], 
                                       calculation_date: date) -> ProfitabilityMetrics:
        """Calculate profitability metrics"""
        metrics = ProfitabilityMetrics(msme_id=msme_id, calculation_date=calculation_date)
        
        revenue = financial_data.get('revenue', 1)  # Avoid division by zero
        cogs = financial_data.get('cost_of_goods_sold', 0)
        operating_income = financial_data.get('operating_income', 0)
        net_income = financial_data.get('net_income', 0)
        total_assets = financial_data.get('total_assets', 1)
        shareholders_equity = financial_data.get('shareholders_equity', 1)
        
        # Gross Profit Margin
        gross_profit = revenue - cogs
        metrics.gross_profit_margin = (gross_profit / revenue) * 100
        metrics.gross_profit_margin_score = self._score_profit_margin(metrics.gross_profit_margin, 'gross')
        
        # Net Profit Margin
        metrics.net_profit_margin = (net_income / revenue) * 100
        metrics.net_profit_margin_score = self._score_profit_margin(metrics.net_profit_margin, 'net')
        
        # Operating Profit Margin
        metrics.operating_profit_margin = (operating_income / revenue) * 100
        metrics.operating_profit_margin_score = self._score_profit_margin(metrics.operating_profit_margin, 'operating')
        
        # EBITDA Margin
        ebitda = financial_data.get('ebitda', operating_income)
        metrics.ebitda_margin = (ebitda / revenue) * 100
        metrics.ebitda_margin_score = self._score_profit_margin(metrics.ebitda_margin, 'ebitda')
        
        # Return on Assets (ROA)
        metrics.return_on_assets = (net_income / total_assets) * 100
        metrics.roa_score = self._score_return_ratio(metrics.return_on_assets, 'roa')
        
        # Return on Equity (ROE)
        metrics.return_on_equity = (net_income / shareholders_equity) * 100
        metrics.roe_score = self._score_return_ratio(metrics.return_on_equity, 'roe')
        
        # Return on Investment (ROI)
        total_investment = financial_data.get('total_investment', total_assets)
        metrics.return_on_investment = ((net_income - total_investment) / total_investment) * 100
        metrics.roi_score = self._score_return_ratio(metrics.return_on_investment, 'roi')
        
        # Overall profitability score
        scores = [
            metrics.gross_profit_margin_score,
            metrics.net_profit_margin_score,
            metrics.operating_profit_margin_score,
            metrics.ebitda_margin_score,
            metrics.roa_score,
            metrics.roe_score,
            metrics.roi_score
        ]
        metrics.overall_profitability_score = sum(scores) / len(scores)
        metrics.profitability_trend = self._determine_profitability_trend(metrics)
        
        return metrics
    
    def _calculate_leverage_metrics(self, msme_id: str, financial_data: Dict[str, Any], 
                                  calculation_date: date) -> LeverageMetrics:
        """Calculate leverage/debt metrics"""
        metrics = LeverageMetrics(msme_id=msme_id, calculation_date=calculation_date)
        
        total_debt = financial_data.get('total_debt', 0)
        total_equity = financial_data.get('shareholders_equity', 1)
        total_assets = financial_data.get('total_assets', 1)
        ebit = financial_data.get('ebit', 0)
        interest_expense = financial_data.get('interest_expense', 1)
        operating_income = financial_data.get('operating_income', 0)
        total_debt_service = financial_data.get('total_debt_service', 1)
        depreciation = financial_data.get('depreciation', 0)
        
        # Debt-to-Equity Ratio
        metrics.debt_to_equity_ratio = total_debt / total_equity
        metrics.debt_to_equity_score = self._score_debt_ratio(metrics.debt_to_equity_ratio, 'debt_to_equity')
        
        # Debt-to-Assets Ratio
        metrics.debt_to_assets_ratio = total_debt / total_assets
        metrics.debt_to_assets_score = self._score_debt_ratio(metrics.debt_to_assets_ratio, 'debt_to_assets')
        
        # Interest Coverage Ratio
        metrics.interest_coverage_ratio = ebit / interest_expense
        metrics.interest_coverage_score = self._score_coverage_ratio(metrics.interest_coverage_ratio, 'interest')
        
        # Debt Service Coverage Ratio (DSCR)
        metrics.debt_service_coverage_ratio = operating_income / total_debt_service
        metrics.dscr_score = self._score_coverage_ratio(metrics.debt_service_coverage_ratio, 'debt_service')
        
        # Cash Coverage Ratio
        metrics.cash_coverage_ratio = (ebit + depreciation) / interest_expense
        metrics.cash_coverage_score = self._score_coverage_ratio(metrics.cash_coverage_ratio, 'cash')
        
        # Equity Multiplier
        metrics.equity_multiplier = total_assets / total_equity
        metrics.equity_multiplier_score = self._score_equity_multiplier(metrics.equity_multiplier)
        
        # Overall leverage score
        scores = [
            metrics.debt_to_equity_score,
            metrics.debt_to_assets_score,
            metrics.interest_coverage_score,
            metrics.dscr_score,
            metrics.cash_coverage_score,
            metrics.equity_multiplier_score
        ]
        metrics.overall_leverage_score = sum(scores) / len(scores)
        metrics.leverage_risk_level = self._determine_leverage_risk_level(metrics.overall_leverage_score)
        
        return metrics
    
    def _calculate_efficiency_metrics(self, msme_id: str, financial_data: Dict[str, Any], 
                                    calculation_date: date) -> EfficiencyMetrics:
        """Calculate efficiency/activity metrics"""
        metrics = EfficiencyMetrics(msme_id=msme_id, calculation_date=calculation_date)
        
        revenue = financial_data.get('revenue', 1)
        cogs = financial_data.get('cost_of_goods_sold', 1)
        avg_total_assets = financial_data.get('average_total_assets', financial_data.get('total_assets', 1))
        avg_inventory = financial_data.get('average_inventory', financial_data.get('inventory', 1))
        avg_receivables = financial_data.get('average_receivables', financial_data.get('accounts_receivable', 1))
        avg_payables = financial_data.get('average_payables', financial_data.get('accounts_payable', 1))
        avg_working_capital = financial_data.get('average_working_capital', 1)
        
        # Asset Turnover
        metrics.asset_turnover = revenue / avg_total_assets
        metrics.asset_turnover_score = self._score_turnover_ratio(metrics.asset_turnover, 'asset')
        
        # Inventory Turnover
        metrics.inventory_turnover = cogs / avg_inventory
        metrics.inventory_turnover_score = self._score_turnover_ratio(metrics.inventory_turnover, 'inventory')
        
        # Receivables Turnover
        metrics.receivables_turnover = revenue / avg_receivables
        metrics.receivables_turnover_score = self._score_turnover_ratio(metrics.receivables_turnover, 'receivables')
        
        # Payables Turnover
        metrics.payables_turnover = cogs / avg_payables
        metrics.payables_turnover_score = self._score_turnover_ratio(metrics.payables_turnover, 'payables')
        
        # Working Capital Turnover
        metrics.working_capital_turnover = revenue / avg_working_capital
        metrics.working_capital_turnover_score = self._score_turnover_ratio(metrics.working_capital_turnover, 'working_capital')
        
        # Days ratios
        metrics.days_sales_outstanding = (avg_receivables / revenue) * 365
        metrics.dso_score = self._score_days_ratio(metrics.days_sales_outstanding, 'dso')
        
        metrics.days_inventory_outstanding = (avg_inventory / cogs) * 365
        metrics.dio_score = self._score_days_ratio(metrics.days_inventory_outstanding, 'dio')
        
        metrics.days_payable_outstanding = (avg_payables / cogs) * 365
        metrics.dpo_score = self._score_days_ratio(metrics.days_payable_outstanding, 'dpo')
        
        # Cash Conversion Cycle
        metrics.cash_conversion_cycle = metrics.days_sales_outstanding + metrics.days_inventory_outstanding - metrics.days_payable_outstanding
        metrics.cash_conversion_cycle_score = self._score_cash_conversion_cycle(metrics.cash_conversion_cycle)
        
        # Overall efficiency score
        scores = [
            metrics.asset_turnover_score,
            metrics.inventory_turnover_score,
            metrics.receivables_turnover_score,
            metrics.working_capital_turnover_score,
            metrics.dso_score,
            metrics.dio_score,
            metrics.cash_conversion_cycle_score
        ]
        metrics.overall_efficiency_score = sum(scores) / len(scores)
        metrics.efficiency_trend = self._determine_efficiency_trend(metrics)
        
        return metrics

    # Overall assessment methods
    def _calculate_overall_financial_score(self, metrics: ComprehensiveFinancialMetrics) -> float:
        """Calculate overall financial health score"""
        weights = {
            'liquidity': 0.25,
            'profitability': 0.30,
            'leverage': 0.25,
            'efficiency': 0.20
        }

        overall_score = (
            metrics.liquidity_metrics.overall_liquidity_score * weights['liquidity'] +
            metrics.profitability_metrics.overall_profitability_score * weights['profitability'] +
            metrics.leverage_metrics.overall_leverage_score * weights['leverage'] +
            metrics.efficiency_metrics.overall_efficiency_score * weights['efficiency']
        )

        return round(overall_score, 2)

    def _determine_financial_grade(self, score: float) -> str:
        """Determine financial grade based on score"""
        if score >= 90:
            return "A+"
        elif score >= 85:
            return "A"
        elif score >= 80:
            return "A-"
        elif score >= 75:
            return "B+"
        elif score >= 70:
            return "B"
        elif score >= 65:
            return "B-"
        elif score >= 60:
            return "C+"
        elif score >= 55:
            return "C"
        elif score >= 50:
            return "C-"
        elif score >= 40:
            return "D"
        else:
            return "F"

    def _assess_overall_risk_level(self, metrics: ComprehensiveFinancialMetrics) -> RiskLevel:
        """Assess overall risk level"""
        risk_factors = 0

        # Liquidity risk
        if metrics.liquidity_metrics.overall_liquidity_score < 60:
            risk_factors += 1
        if metrics.liquidity_metrics.current_ratio < 1.0:
            risk_factors += 1

        # Profitability risk
        if metrics.profitability_metrics.overall_profitability_score < 50:
            risk_factors += 1
        if metrics.profitability_metrics.net_profit_margin < 5:
            risk_factors += 1

        # Leverage risk
        if metrics.leverage_metrics.debt_to_equity_ratio > 1.0:
            risk_factors += 1
        if metrics.leverage_metrics.debt_service_coverage_ratio < 1.25:
            risk_factors += 1

        # Efficiency risk
        if metrics.efficiency_metrics.cash_conversion_cycle > 90:
            risk_factors += 1

        if risk_factors >= 5:
            return RiskLevel.CRITICAL
        elif risk_factors >= 3:
            return RiskLevel.HIGH
        elif risk_factors >= 1:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW

    def _identify_risk_factors(self, metrics: ComprehensiveFinancialMetrics) -> List[str]:
        """Identify key risk factors"""
        risk_factors = []

        # Liquidity risks
        if metrics.liquidity_metrics.current_ratio < 1.0:
            risk_factors.append("Current ratio below 1.0 indicates liquidity stress")
        if metrics.liquidity_metrics.cash_ratio < 0.1:
            risk_factors.append("Low cash ratio may indicate cash flow problems")

        # Profitability risks
        if metrics.profitability_metrics.net_profit_margin < 5:
            risk_factors.append("Low net profit margin indicates profitability concerns")
        if metrics.profitability_metrics.return_on_assets < 5:
            risk_factors.append("Low return on assets indicates inefficient asset utilization")

        # Leverage risks
        if metrics.leverage_metrics.debt_to_equity_ratio > 1.0:
            risk_factors.append("High debt-to-equity ratio indicates high financial leverage")
        if metrics.leverage_metrics.debt_service_coverage_ratio < 1.25:
            risk_factors.append("Low DSCR indicates potential debt servicing difficulties")

        # Efficiency risks
        if metrics.efficiency_metrics.cash_conversion_cycle > 90:
            risk_factors.append("Long cash conversion cycle ties up working capital")
        if metrics.efficiency_metrics.days_sales_outstanding > 60:
            risk_factors.append("High DSO indicates collection issues")

        return risk_factors

    def _identify_improvement_areas(self, metrics: ComprehensiveFinancialMetrics) -> List[str]:
        """Identify areas for improvement"""
        improvement_areas = []

        # Check each category
        if metrics.liquidity_metrics.overall_liquidity_score < 70:
            improvement_areas.append("Liquidity Management")
        if metrics.profitability_metrics.overall_profitability_score < 70:
            improvement_areas.append("Profitability Enhancement")
        if metrics.leverage_metrics.overall_leverage_score < 70:
            improvement_areas.append("Debt Management")
        if metrics.efficiency_metrics.overall_efficiency_score < 70:
            improvement_areas.append("Operational Efficiency")

        return improvement_areas

    def _generate_action_items(self, metrics: ComprehensiveFinancialMetrics) -> List[str]:
        """Generate specific action items"""
        action_items = []

        # Liquidity actions
        if metrics.liquidity_metrics.current_ratio < 1.5:
            action_items.append("Improve current ratio by reducing short-term debt or increasing current assets")

        # Profitability actions
        if metrics.profitability_metrics.gross_profit_margin < 20:
            action_items.append("Focus on cost reduction or pricing optimization to improve gross margins")

        # Leverage actions
        if metrics.leverage_metrics.debt_to_equity_ratio > 0.8:
            action_items.append("Consider debt reduction or equity infusion to improve leverage ratios")

        # Efficiency actions
        if metrics.efficiency_metrics.days_sales_outstanding > 45:
            action_items.append("Implement better collection processes to reduce DSO")
        if metrics.efficiency_metrics.inventory_turnover < 4:
            action_items.append("Optimize inventory management to improve turnover")

        return action_items

    def _calculate_industry_percentile(self, metrics: ComprehensiveFinancialMetrics) -> float:
        """Calculate industry percentile ranking (mock implementation)"""
        # In practice, this would compare against industry database
        overall_score = metrics.overall_financial_score

        if overall_score >= 85:
            return 90.0
        elif overall_score >= 75:
            return 75.0
        elif overall_score >= 65:
            return 60.0
        elif overall_score >= 55:
            return 45.0
        else:
            return 25.0

    def _calculate_data_completeness(self, financial_data: Dict[str, Any]) -> float:
        """Calculate data completeness percentage"""
        required_fields = [
            'revenue', 'cost_of_goods_sold', 'operating_income', 'net_income',
            'current_assets', 'current_liabilities', 'total_assets', 'total_debt',
            'shareholders_equity', 'inventory', 'accounts_receivable', 'accounts_payable'
        ]

        available_fields = sum(1 for field in required_fields if financial_data.get(field) is not None)
        return (available_fields / len(required_fields)) * 100

    def _calculate_confidence_level(self, financial_data: Dict[str, Any],
                                  cash_flow_data: Optional[CashFlowAnalytics]) -> float:
        """Calculate calculation confidence level"""
        confidence_factors = []

        # Data completeness
        completeness = self._calculate_data_completeness(financial_data)
        confidence_factors.append(completeness)

        # Data recency
        data_age = financial_data.get('data_age_days', 30)
        if data_age <= 30:
            confidence_factors.append(95.0)
        elif data_age <= 90:
            confidence_factors.append(80.0)
        else:
            confidence_factors.append(60.0)

        # Cash flow data availability
        if cash_flow_data:
            confidence_factors.append(90.0)
        else:
            confidence_factors.append(70.0)

        return sum(confidence_factors) / len(confidence_factors)
