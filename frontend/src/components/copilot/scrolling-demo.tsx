'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface DemoMessage {
  id: string;
  content: string;
  timestamp: Date;
  type: 'user' | 'ai';
}

export function ScrollingDemo() {
  const [messages, setMessages] = useState<DemoMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTo({
        top: scrollAreaRef.current.scrollHeight,
        behavior: 'smooth'
      });
    }
  }, [messages]);

  const addMessage = (content: string, type: 'user' | 'ai' = 'user') => {
    const newMessage: DemoMessage = {
      id: `${type}_${Date.now()}`,
      content,
      timestamp: new Date(),
      type
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const handleSendMessage = () => {
    if (inputValue.trim()) {
      addMessage(inputValue, 'user');
      setInputValue('');
      
      // Simulate AI response
      setTimeout(() => {
        addMessage(`AI response to: "${inputValue}"`, 'ai');
      }, 1000);
    }
  };

  const addMultipleMessages = () => {
    for (let i = 1; i <= 10; i++) {
      setTimeout(() => {
        addMessage(`Demo message ${i} - This is a longer message to test scrolling behavior when content overflows the visible area.`, 'user');
      }, i * 200);
    }
  };

  const clearMessages = () => {
    setMessages([]);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto h-[600px] flex flex-col">
      <CardHeader className="flex-shrink-0">
        <CardTitle>AI Copilot Scrolling Demo</CardTitle>
        <div className="flex gap-2">
          <Button onClick={addMultipleMessages} variant="outline" size="sm">
            Add 10 Messages
          </Button>
          <Button onClick={clearMessages} variant="outline" size="sm">
            Clear Messages
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col min-h-0 p-6">
        {/* Chat Messages Area - Scrollable */}
        <div className="flex-1 flex flex-col min-h-0">
          {messages.length === 0 ? (
            <div className="flex-1 flex flex-col justify-center items-center text-center">
              <h3 className="text-lg font-medium mb-2">No messages yet</h3>
              <p className="text-muted-foreground">Send a message or add demo messages to test scrolling</p>
            </div>
          ) : (
            <div
              ref={scrollAreaRef}
              className="flex-1 overflow-y-auto py-4 scrollbar-thin scrollbar-thumb-emerald-200 scrollbar-track-emerald-50 scroll-smooth min-h-0"
              style={{ scrollBehavior: 'smooth' }}
            >
              <div className="space-y-4">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-[80%] rounded-lg px-4 py-3 ${
                        message.type === 'user'
                          ? 'bg-emerald-600 text-white'
                          : 'bg-muted text-foreground'
                      }`}
                    >
                      <p className="text-sm">{message.content}</p>
                      <p className="text-xs opacity-70 mt-1">
                        {message.timestamp.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Chat Input - Always Visible at Bottom */}
        <div className="border-t border-border/40 pt-4 flex-shrink-0">
          <div className="flex gap-3 items-end">
            <div className="flex-1">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleSendMessage();
                  }
                }}
                placeholder="Type a message to test scrolling..."
                className="h-12 text-sm border-emerald-200 focus:border-emerald-500 focus:ring-emerald-200"
              />
            </div>
            <Button
              onClick={handleSendMessage}
              disabled={!inputValue.trim()}
              className="h-12 px-6 bg-emerald-600 hover:bg-emerald-700"
            >
              Send
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
