'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  TrendingUp, 
  TrendingDown, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  RefreshCw,
  DollarSign,
  Calendar,
  BarChart3,
  Target,
  Zap
} from 'lucide-react';

interface GSTData {
  gstin: string;
  turnover_data: any[];
  compliance_records: any[];
  payment_patterns: any[];
  analytics: {
    avg_monthly_turnover: number;
    turnover_growth_rate: number;
    turnover_volatility: number;
    filing_compliance_rate: number;
    payment_compliance_rate: number;
    avg_filing_delay: number;
    avg_payment_delay: number;
    itc_utilization_rate: number;
    itc_efficiency: number;
    compliance_risk_score: number;
    payment_risk_score: number;
    overall_gst_score: number;
    turnover_trend: string;
    compliance_trend: string;
    data_quality_score: number;
  };
  last_updated: string;
}

interface GSTDataTabProps {
  msmeId: string;
}

export default function GSTDataTab({ msmeId }: GSTDataTabProps) {
  const [gstData, setGstData] = useState<GSTData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchGSTData();
  }, [msmeId]);

  const fetchGSTData = async () => {
    try {
      setLoading(true);
      // Mock data - in production, this would call the actual API
      const mockData: GSTData = {
        gstin: "27AABCU9603R1ZX",
        turnover_data: [],
        compliance_records: [],
        payment_patterns: [],
        analytics: {
          avg_monthly_turnover: 2500000,
          turnover_growth_rate: 15.5,
          turnover_volatility: 12.3,
          filing_compliance_rate: 92.5,
          payment_compliance_rate: 88.7,
          avg_filing_delay: 2.1,
          avg_payment_delay: 4.3,
          itc_utilization_rate: 85.2,
          itc_efficiency: 78.9,
          compliance_risk_score: 15.2,
          payment_risk_score: 22.1,
          overall_gst_score: 82.4,
          turnover_trend: "increasing",
          compliance_trend: "stable",
          data_quality_score: 94.5
        },
        last_updated: new Date().toISOString()
      };
      
      setGstData(mockData);
    } catch (error) {
      console.error('Error fetching GST data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchGSTData();
    setRefreshing(false);
  };

  const getComplianceStatusColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600';
    if (rate >= 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getComplianceStatusBadge = (rate: number) => {
    if (rate >= 90) return <Badge variant="default" className="bg-green-100 text-green-800">Excellent</Badge>;
    if (rate >= 80) return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Good</Badge>;
    return <Badge variant="destructive">Needs Improvement</Badge>;
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing':
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'decreasing':
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <BarChart3 className="h-4 w-4 text-blue-500" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 rounded animate-pulse"></div>
          ))}
        </div>
        <div className="h-64 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  if (!gstData) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No GST Data Available</h3>
        <p className="text-gray-500 mb-4">GST data integration is not set up for this MSME.</p>
        <Button onClick={fetchGSTData}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Refresh */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">GST Data Analysis</h2>
          <p className="text-muted-foreground">GSTIN: {gstData.gstin}</p>
        </div>
        <Button 
          variant="outline" 
          onClick={handleRefresh} 
          disabled={refreshing}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-6 md:grid-cols-4">
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <DollarSign className="h-5 w-5" />
              Monthly Turnover
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-700 mb-2">
              ₹{(gstData.analytics.avg_monthly_turnover / 100000).toFixed(1)}L
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getTrendIcon(gstData.analytics.turnover_trend)}
              <span className={gstData.analytics.turnover_growth_rate > 0 ? 'text-green-600' : 'text-red-600'}>
                {gstData.analytics.turnover_growth_rate > 0 ? '+' : ''}{gstData.analytics.turnover_growth_rate}%
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-green-700">
              <FileText className="h-5 w-5" />
              Filing Compliance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-700 mb-2">
              {gstData.analytics.filing_compliance_rate.toFixed(1)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              {getComplianceStatusBadge(gstData.analytics.filing_compliance_rate)}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-purple-700">
              <Target className="h-5 w-5" />
              ITC Efficiency
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-700 mb-2">
              {gstData.analytics.itc_efficiency.toFixed(1)}%
            </div>
            <div className="flex items-center gap-1 text-sm">
              <CheckCircle className="h-3 w-3 text-purple-500" />
              <span className="text-purple-600">Utilization: {gstData.analytics.itc_utilization_rate.toFixed(1)}%</span>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg bg-gradient-to-br from-orange-50 to-orange-100">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-orange-700">
              <Zap className="h-5 w-5" />
              GST Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-700 mb-2">
              {gstData.analytics.overall_gst_score.toFixed(0)}/100
            </div>
            <div className="flex items-center gap-1 text-sm">
              <Progress value={gstData.analytics.overall_gst_score} className="h-2 flex-1" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analysis Tabs */}
      <Tabs defaultValue="compliance" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
          <TabsTrigger value="turnover">Turnover Analysis</TabsTrigger>
          <TabsTrigger value="payments">Payment Patterns</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>

        <TabsContent value="compliance" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Filing Compliance
                </CardTitle>
                <CardDescription>GST return filing performance</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Filing Rate</span>
                  <span className={`text-sm font-semibold ${getComplianceStatusColor(gstData.analytics.filing_compliance_rate)}`}>
                    {gstData.analytics.filing_compliance_rate.toFixed(1)}%
                  </span>
                </div>
                <Progress value={gstData.analytics.filing_compliance_rate} className="h-2" />
                
                <Separator />
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Average Filing Delay</span>
                    <span className="font-medium">{gstData.analytics.avg_filing_delay.toFixed(1)} days</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Compliance Trend</span>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(gstData.analytics.compliance_trend)}
                      <span className="font-medium capitalize">{gstData.analytics.compliance_trend}</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Risk Score</span>
                    <Badge variant={gstData.analytics.compliance_risk_score < 20 ? "default" : gstData.analytics.compliance_risk_score < 40 ? "secondary" : "destructive"}>
                      {gstData.analytics.compliance_risk_score.toFixed(1)}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Payment Compliance
                </CardTitle>
                <CardDescription>GST payment performance</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Payment Rate</span>
                  <span className={`text-sm font-semibold ${getComplianceStatusColor(gstData.analytics.payment_compliance_rate)}`}>
                    {gstData.analytics.payment_compliance_rate.toFixed(1)}%
                  </span>
                </div>
                <Progress value={gstData.analytics.payment_compliance_rate} className="h-2" />
                
                <Separator />
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span>Average Payment Delay</span>
                    <span className="font-medium">{gstData.analytics.avg_payment_delay.toFixed(1)} days</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span>Payment Risk Score</span>
                    <Badge variant={gstData.analytics.payment_risk_score < 20 ? "default" : gstData.analytics.payment_risk_score < 40 ? "secondary" : "destructive"}>
                      {gstData.analytics.payment_risk_score.toFixed(1)}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="turnover" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Turnover Analysis
              </CardTitle>
              <CardDescription>Business turnover trends and patterns</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-3">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600 mb-1">
                    ₹{(gstData.analytics.avg_monthly_turnover / 100000).toFixed(1)}L
                  </div>
                  <div className="text-sm text-blue-600">Average Monthly</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    {gstData.analytics.turnover_growth_rate > 0 ? '+' : ''}{gstData.analytics.turnover_growth_rate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-green-600">Growth Rate</div>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600 mb-1">
                    {gstData.analytics.turnover_volatility.toFixed(1)}%
                  </div>
                  <div className="text-sm text-purple-600">Volatility</div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h4 className="font-medium">Turnover Insights</h4>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                    {getTrendIcon(gstData.analytics.turnover_trend)}
                    <div>
                      <p className="font-medium text-sm">Trend Direction</p>
                      <p className="text-xs text-muted-foreground capitalize">{gstData.analytics.turnover_trend}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                    <BarChart3 className="h-4 w-4 text-blue-500" />
                    <div>
                      <p className="font-medium text-sm">Stability</p>
                      <p className="text-xs text-muted-foreground">
                        {gstData.analytics.turnover_volatility < 15 ? 'Stable' : 
                         gstData.analytics.turnover_volatility < 25 ? 'Moderate' : 'Volatile'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Payment Patterns
              </CardTitle>
              <CardDescription>GST payment behavior analysis</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Payment Timeliness</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">On-time Payments</span>
                      <span className="font-semibold">{gstData.analytics.payment_compliance_rate.toFixed(1)}%</span>
                    </div>
                    <Progress value={gstData.analytics.payment_compliance_rate} className="h-2" />
                    
                    <div className="flex items-center justify-between text-sm">
                      <span>Average Delay</span>
                      <span className="font-medium">{gstData.analytics.avg_payment_delay.toFixed(1)} days</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-medium">ITC Utilization</h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Utilization Rate</span>
                      <span className="font-semibold">{gstData.analytics.itc_utilization_rate.toFixed(1)}%</span>
                    </div>
                    <Progress value={gstData.analytics.itc_utilization_rate} className="h-2" />
                    
                    <div className="flex items-center justify-between text-sm">
                      <span>Efficiency Score</span>
                      <span className="font-medium">{gstData.analytics.itc_efficiency.toFixed(1)}/100</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                GST Insights & Recommendations
              </CardTitle>
              <CardDescription>AI-powered insights and improvement suggestions</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium text-green-700">Strengths</h4>
                  <div className="space-y-2">
                    {gstData.analytics.filing_compliance_rate >= 90 && (
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Excellent filing compliance</span>
                      </div>
                    )}
                    {gstData.analytics.turnover_growth_rate > 10 && (
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Strong turnover growth</span>
                      </div>
                    )}
                    {gstData.analytics.itc_efficiency >= 75 && (
                      <div className="flex items-center gap-2 text-sm">
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span>Efficient ITC utilization</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-medium text-orange-700">Improvement Areas</h4>
                  <div className="space-y-2">
                    {gstData.analytics.avg_filing_delay > 5 && (
                      <div className="flex items-center gap-2 text-sm">
                        <AlertCircle className="h-4 w-4 text-orange-500" />
                        <span>Reduce filing delays</span>
                      </div>
                    )}
                    {gstData.analytics.payment_compliance_rate < 85 && (
                      <div className="flex items-center gap-2 text-sm">
                        <AlertCircle className="h-4 w-4 text-orange-500" />
                        <span>Improve payment timeliness</span>
                      </div>
                    )}
                    {gstData.analytics.turnover_volatility > 20 && (
                      <div className="flex items-center gap-2 text-sm">
                        <AlertCircle className="h-4 w-4 text-orange-500" />
                        <span>Stabilize business turnover</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-4">
                <h4 className="font-medium">Overall Assessment</h4>
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">GST Health Score</span>
                      <span className="text-sm font-semibold">{gstData.analytics.overall_gst_score.toFixed(0)}/100</span>
                    </div>
                    <Progress value={gstData.analytics.overall_gst_score} className="h-3" />
                  </div>
                  <Badge variant={gstData.analytics.overall_gst_score >= 80 ? "default" : gstData.analytics.overall_gst_score >= 60 ? "secondary" : "destructive"}>
                    {gstData.analytics.overall_gst_score >= 80 ? "Excellent" : 
                     gstData.analytics.overall_gst_score >= 60 ? "Good" : "Needs Improvement"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Data Quality Footer */}
      <Card className="bg-muted/30">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">
                Last updated: {new Date(gstData.last_updated).toLocaleDateString()}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">Data Quality:</span>
              <Badge variant="outline">{gstData.analytics.data_quality_score.toFixed(0)}%</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
